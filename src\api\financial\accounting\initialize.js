import request from '@/utils/request'

/**
 * @name:账套余额初始化列表
 * @param {*} booksId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listInitItemBalanceTre(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/listInitItemBalanceTree',
    method: 'post',
    data: data,
    withCredentials: true
  })
}


//获取源科目忽略余额科目
export function sourceIgnoreItemBalance(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/sourceIgnoreItemBalance',
    method: 'post',
    data: data,
    withCredentials: true
  })
}


// 来源科目List
export function sourceItemBalanceTree(data, params) {
  return request({
    url: '/financial/accounting/accountingItemBalance/sourceItemBalanceTree',
    method: 'post',
    data: data,
    params: params,
    // ContentType: 'application/json',
    payload: true,
    withCredentials: true
  })
}


// 科目迁移
export function transfer(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/transfer',
    method: 'post',
    data: data,
    payload: true,
    withCredentials: true
  })
}


//科目删除
export function transferDelete(params) {
  return request({
    url: '/financial/accounting/accountingItemBalance/transferDelete',
    method: 'post',
    params: params,
    withCredentials: true
  })
}






/**
 * @name:账套余额初始化列表
 * @param {*} booksId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getCashierFlag(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/getCashierFlag',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:试算平衡
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function trialBalance(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/trialBalance',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:账套初始化
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function init(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/init',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:撤销初始化
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unInit(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/unInit',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:保存账套余额修改
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveList(data, id) {
  return request({
    url: '/financial/accounting/accountingItemBalance/saveList?booksId=' + id,
    method: 'post',
    data: data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:科目余额导入
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveImportTemp(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/saveImportTemp',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:出纳余额取数
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function importCashier(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/importCashier',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:辅助核算详情
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getItemAssist(data) {
  return request({
    url: '/financial/books/basedata/accountingItem/getItemAssist',
    method: 'get',
    params: data,
    withCredentials: true
  })
}
export function getAssistType(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/getAssistType',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:辅助核算项目查询
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getAssistPrj(data) {
  return request({
    url: '/financial/books/basedata/assistProject/getAssistPrj',
    method: 'get',
    params: data,
    withCredentials: true
  })
}
export function getAssistProject(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/getAssistProject',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:账套余额-辅助核算-明细导入
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveImportAssist(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/saveImportAssist',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:账套余额-辅助核算-明细列表
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectAssist(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/selectAssist',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

/**
 * @name:账套余额-辅助核算-明细列表保存
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveAssist(data, booksId, itemId) {
  return request({
    url: '/financial/accounting/accountingItemBalance/saveAssist?booksId=' + booksId + '&itemId=' + itemId,
    method: 'post',
    data: data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:账套余额-辅助核算列表
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listAssistSum(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/listAssistSum',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:账套余额-辅助核算列表
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listInitItemBalance(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/listInitItemBalance',
    method: 'post',
    params: data,
    withCredentials: true
  })
}
/**
 * @name:获取单位名称
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function inits(data) {
  return request({
    url: `/financial/accounting/accountingItemBalance/print/init?booksId=${data}`,
    method: 'get'
  })
}

// 账套余额初始化上传附件
export function saveAccountingItemCompareFile(data) {
  return request({
    url: '/financial/accounting/accountingItemBalance/saveAccountingItemCompareFile',
    method: 'post',
    params: data,
    withCredentials: true
  })
}

/**
 * 重置科目余额（注意：还包括重置非标准科目）
 * @param {*} data 
 * @returns 
 */
export function resetBalance(data){
  return request({
    url: '/financial/accounting/accountingItemBalance/resetBalance',
    method: 'post',
    params: data,
    withCredentials: true
  })
}