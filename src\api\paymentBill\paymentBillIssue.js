/*
 * @Description: 
 * @Version: 
 * @Author: luozc
 * @Date: 2023-09-27 16:01:39
 * @LastEditors: luozc
 * @LastEditTime: 2023-10-09 18:35:37
 */
import request from '@/utils/request'

/**
 * 查询支付票据开出列表
 * @param {*} data 
 * @returns 
 */
export function getPaymentBillIssueList(data){
    return request({
        url: '/paymentbill/paymentBillIssue/page',
        method: 'post',
        data
    })
}

/**
 * 查询支付票据开出详情
 * @param {*} id 
 * @returns 
 */
export function getIssueDetail(id){
    return request({
        url: `/paymentbill/paymentBillIssue/get/${id}`,
        method: 'get'
    })
}

/**
 * 作废
 * @param {*} params 
 * @returns 
 */
export function cancel(params){
    return request({
        url: '/paymentbill/paymentBillIssue/cancel',
        method: 'post',
        params
    })
}

/**
 * 补打
 */
export function rePrint(id){
    return request({
        url: `/paymentbill/paymentBillIssue/rePrint?issueId=${id}`,
        method: 'post'
    })
}

/**
 * 查询支付票据凭证列表
 * @param {*} data 
 * @returns 
 */
 export function getPaymentVoucherList(data) {
    return request({
        url: '/paymentbill/paymentBillIssue/pageList',
        method: 'post',
        data
    })
}

/**
 * 模拟功能（开出）接口
 * @param {*} data 
 * @returns 
 */
export function invoice(data) {
    return request({
        url: '/paymentbill/paymentBillIssue/invoice',
        method: 'post',
        data
    })
}
export function payInvoice(data) {
    return request({
        url: '/payment/manager/payOrder/invoicePaymentBill',
        method: 'post',
        data
    })
}

/**
 * 模拟功能（打印）接口
 * @param {*} id 
 * @returns 
 */
export function print(id) {
    return request({
        url: `/paymentbill/paymentBillIssue/print?issueId=${id}`,
        method: 'post'
    })
}