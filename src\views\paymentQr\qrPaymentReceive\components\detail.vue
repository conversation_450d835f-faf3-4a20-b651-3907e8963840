<template>
  <div v-loading="loading" class="gever-form detail" style="height: 100%">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="170px"
      :disabled="type == 'view'"
    >
    <div class="gever-title">{{ $t('基本信息') }}</div>
		<div class="form-db">
			<el-form-item :label="$t('组织机构')" prop="orgName">
				<gever-input v-model="contentForm.orgName" />
			</el-form-item>
      <el-form-item :label="$t('订单号')" prop="orderNo">
				<gever-input v-model="contentForm.orderNo" />
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('合计总额')" prop="totalMoney">
				<gever-input v-model="contentForm.totalMoney" />
			</el-form-item>
      <el-form-item :label="$t('交易状态')" prop="status">
        <el-select v-model="contentForm.status" clearable  placeholder="请选择">
          <el-option
            v-for="item in optionsMap.payStatus"
            :key="item.id"
            :label="item.text"
            :value="item.id">
          </el-option>
        </el-select>
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('收款账号名称')" prop="payeeAccountName">
				<gever-input v-model="contentForm.payeeAccountName" />
			</el-form-item>
			<el-form-item :label="$t('收款账号')" prop="payeeAccountNo">
				<gever-input v-model="contentForm.payeeAccountNo" />
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('收款方开户行')" prop="payeeBank">
				<gever-input v-model="contentForm.payeeBank" />
			</el-form-item>
			<el-form-item :label="$t('创建人')" prop="createUsername">
				<gever-input v-model="contentForm.createUsername" />
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('创建时间')" prop="createTime">
				<gever-input v-model="contentForm.createTime" />
			</el-form-item>
		</div>


    <div class="form-sg">
			<el-form-item :label="$t('订单明细')" prop="faileReason">
				<el-table :data="details" row-key="id" border max-height="250" :loading="detailsLoading"
        show-summary
        :summary-method="getSummary"
        >
          <el-table-column type="index" label="序号" width="50" align="center" :row-class-name="handleRowClassName">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="receiveItemName" label="收款项目"  min-width="100" resizable="true" header-align="center" align="center">
            <template slot-scope="scope">
                <span >{{ scope.row.receiveItemName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="money" label="收款金额" min-width="100" resizable="true" header-align="center" align="center">
            <template slot-scope="scope">
                <span >{{ scope.row.money }}</span>
            </template>
          </el-table-column>
        </el-table>
			</el-form-item>
		</div>

    <div class="form-db">
			<el-form-item :label="$t('付款方')" prop="payerAccountName">
				<gever-input v-model="contentForm.payerAccountName" />
			</el-form-item>
		</div>

    <div class="form-sg">
      <el-form-item :label="$t('备注')" prop="remark">
				<el-input type="textarea" :rows="3" v-model="contentForm.remark" />
			</el-form-item>
    </div>
    
    <div class="gever-title">{{ $t('支付信息') }}</div>
		
		<div class="form-db">
			<el-form-item :label="$t('交易凭证号')" prop="voucherNo">
				<gever-input v-model="contentForm.voucherNo" />
			</el-form-item>
			<el-form-item :label="$t('交易时间')" prop="payTime">
				<gever-input v-model="contentForm.payTime" />
			</el-form-item>
		</div>
		<!-- <div class="form-sg">
			<el-form-item :label="$t('失败原因')" prop="faileReason">
        <el-input type="textarea" :rows="3" v-model="contentForm.faileReason" />
			</el-form-item>
		</div> -->
    </el-form>
  </div>
</template>

<script>
import {
  loadQrPaymentReceiveInfoApi,loadQrPaymentReceiveInfoByNoApi,saveQrPaymentReceiveApi,updateQrPaymentReceiveApi,loadQrPaymentReceiveDetailOptionsApi
} from '@/api/paymentQr/qrPaymentReceive.js'
import { getDictionary } from '@/api/gever/common.js' // 引入获取字典的方法

export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    orderNo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      detailsLoading: false,
      contentForm: {},
      details: [],
      rules: {},
      optionsMap: {
        payStatus: [] // 添加 payStatus 字段
      }
    }
  },
  async created() {
    const { data: payStatus = [] } = await getDictionary('/financial/qrPayment/status/')
    this.optionsMap.payStatus = payStatus
    this.initPage()
  },
  methods: {
    initPage() {
      if (this.type === 'add') {
        // 新增页面初始化
      } else if (this.type === 'view' || this.type === 'edit') {
        // 查看、编辑页面初始化
        this.loading = true
        if(this.id){
          loadQrPaymentReceiveInfoApi(this.id).then(res => {
            this.contentForm = res.data
          }).finally(() => {
            this.loading = false
          })
        }else if(this.orderNo){
          loadQrPaymentReceiveInfoByNoApi(this.orderNo).then(res => {
            this.contentForm = res.data
          }).finally(() => {
            this.loading = false
          })
        }

        this.detailsLoading = true
        loadQrPaymentReceiveDetailOptionsApi({receiveId:this.id}).then(res => {
          this.details = res.data
        }).finally(() => {
          this.detailsLoading = false
        })
      }
    },
    handleBatchIdChange(systemBatchId) {
      this.contentForm.systemBatchId = systemBatchId
    },
    save() {
      this.$refs['_geverFormRef'].validate(valid => {
        if (!valid) {
          return this.$message.warning(this.$t('请完善表单信息'))
        }
        this.loading = true
        if (this.contentForm.id) {
          updateQrPaymentReceiveApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        } else {
          saveQrPaymentReceiveApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    getSummary(param) {
      const { columns, data } = param
      const sums = []

      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }

        const values = data.map(item => Number(item[column.property]))
        if (!isNaN(values[0])) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            return !isNaN(value) ? prev + curr : prev
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    handleRowClassName({ row, rowIndex }) {
      if (rowIndex === this.details.length - 1) {
        return 'summary-row'
      }
      return ''
    },
  }
}
</script>

<style scoped>

::v-deep .el-table__footer-wrapper td{
  background-color: #f8f8f9 !important; /* 灰色背景 */
  font-weight: bold;
}

</style>