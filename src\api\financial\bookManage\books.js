/*
 * @Descripttion:账套维护接口
 * @version:
 * @Author: 未知
 * @Date: 2021-11-01 13:50:29
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-16 09:33:42
 */

import request from '@/utils/request'

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/books/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取账套维护详情
 * @param {String} id 账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id = '' }) {
  return request({
    url: `/financial/books/load/${id}`,
    method: 'get',
    data: { },
    withCredentials: true
  })
}

/**
 * @name:保存账套维护
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveBooks(data) {
  return request({
    url: '/financial/books/saveBooks',
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:删除账套维护
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/books/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:启用账套
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function enable(data) {
  return request({
    url: `/financial/books/enable`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:停用账套
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function disable(data) {
  return request({
    url: `/financial/books/disable`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:初始化账套
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function checkInit(data) {
  return request({
    url: `/financial/books/checkInit`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取初始化的模块
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function initType(data) {
  return request({
    url: `/financial/books/initType`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:设置初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function initBooks(data) {
  return request({
    url: `/financial/books/initBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取账套信息
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBooksInfo(data) {
  return request({
    url: `/financial/books/getBooksInfo`,
    method: 'post',
    data,
    withCredentials: true
  })
}

