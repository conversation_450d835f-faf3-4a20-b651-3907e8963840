<!--
 * @Description: 专项资金沉淀统计表
 * @Author: liuwf
 * @Date: 2025-05-28 15:17:03
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-14 11:41:52
-->
<template>
  <div style="height: 100% ;">
    <el-form inline @submit.native.prevent>
      <el-form-item :label="$t('统计年月')">
        <el-date-picker
          v-model="queryParams.yearMonth"
          type="month"
          value-format="yyyy-MM"
          format="yyyy-MM"
          placeholder="选择年"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('沉淀年数')">
        <el-select v-model="queryParams.yearNum" placeholder="请选择" class="w100">
          <el-option v-for="item in 10" :key="item+''" :label="item" :value="item+''"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('专项资金名称')">
        <el-input v-model="queryParams.receiptName" type="text" class="w200" clearable />
      </el-form-item>
      <el-form-item :label="$t('专项资金编号')">
        <el-input v-model="queryParams.receiptCode" type="text" class="w200" clearable />
      </el-form-item>
      <el-form-item :label="$t('专项资金来源')">
        <gever-select
          v-model="queryParams.specialSourceList"
          class="w200"
          multiple
          collapse-tags
          path="/financial/specialFund/receipt/specialSource/"
          placeholder="请选择"
        />
      </el-form-item>
      <el-form-item :label="$t('专项资金类别')">
        <categoryTreeSelect v-model="queryParams.categoryId" class="w200" placeholder="请选择"></categoryTreeSelect>
      </el-form-item>

      <el-form-item :label="$t('')">
        <el-checkbox v-model="queryParams.isBalance">沉淀金额大于0</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button
          plain
          icon="el-icon-search"
          type="primary"
          round
          @click="handleSearch"
        >{{ $t('搜索') }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-customIcon-baocun"
          type="primary"
          round
          :loading="exportloading"
          @click="handleExport"
        >{{ $t('导出') }}</el-button>
      </el-form-item>
    </el-form>
    <div style="text-align: center;font-size: 24px;font-weight: bold;margin-bottom: 10px;">
      <span>专项资金沉淀统计表</span>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: 10px;">
      <div>
        <span>单位名称：{{ orgName }}</span>
      </div>
      <div>
        <span>统计时间：{{ statisticalTime }}</span>
      </div>
    </div>
    <div style="height: 82%;">
      <gever-table
        ref="_geverTableRef"
        :loading="tableLoading"
        :columns="table.columns"
        :data="table.tableList"
        :total="table.total"
        :pagi="queryParams"
        :page-sizes="pageSizes"
        :row-class-name="getRowClassName"
        @pagination-change="getList"
      >
        <template
          #categoryCode="{ row }"
        >{{ row.orgName!='总合计'?row.categoryCode + " "+row.categoryName:'' }}</template>
      </gever-table>
      <div style="display: flex;justify-content:space-between">
        <div>
          说明：
          <div>此报表的每行数据为每笔“已入账”的专项资金，以下说明以统计期间2025年5月，沉淀2年为例子。</div>
          <div>1、2023年5月余额（统计期间推前X年当月余额）：专项资金在2023年5月前入账并发生支出后的余额，即专项资金要在2023年5月前入账，该列才有金额，否则为零；</div>
          <div>2、收入金额：专项资金的入账金额，专项资金需要在2023年6月到2025年5月之间入账，该列才有金额，否则为零；</div>
          <div>3、支出金额：专项资金在2023年6月到2025年5月之间发生的支出；</div>
          <div>4、2025年5月余额（统计期间当月余额）：收入金额 - 支出金额；</div>
          <div>5、沉淀金额：2023年5月余额 - 支出金额，若沉淀金额少于等于零，为无沉淀，若沉淀金额大于零，为有沉淀。</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { accumulatedFundsReport } from '@/api/specialFund/receipt.js'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
import categoryTreeSelect from '../../receipt/components/categoryTreeSelect'
import { getToken } from '@/utils/auth'
export default {
  name: 'Receipt',
  components: {
    categoryTreeSelect
  },
  data () {
    return {
      pageSizes: [20, 100, 150, 200, 300, 400, 500],
      exportloading: false,
      tableLoading: false,
      queryParams: {
        page: 1,
        rows: 100,
        yearMonth: '',
        yearNum: '2',
        isBalance: false,
        specialSourceList: []
      },
      table: {
        columns: [
          { type: 'index', label: '序号', width: '50', fixed: 'left' },
          { label: '镇（街）名称', prop: 'townName', width: '150' },
          { label: '村名称', prop: 'villageName', width: '150' },
          { label: '单位名称', prop: 'orgName', width: '300' },
          { label: '专项资金编号', prop: 'receiptCode', width: '150' },
          { label: '专项资金名称', prop: 'receiptName', width: '150' },
          { label: '专项资金类别', prop: 'categoryCode', width: '240' },
          { label: '专项资金来源', prop: 'specialSource', width: '150' },
          { label: '入账日期', prop: 'arrivalDate', width: '150' },
          { label: '余额', prop: 'previousBalance', width: '150', filter: 'money' },
          { label: '收入金额', prop: 'income', width: '150', filter: 'money' },
          { label: '支出金额', prop: 'expenses', width: '150', filter: 'money' },
          { label: '余额', prop: 'balance', width: '150', filter: 'money' },
          { label: '沉淀金额', prop: 'accumulation', width: '150', filter: 'money' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
      ],
      optionsMap: {
        zero: [
          {
            text: '是',
            id: '1'
          },
          {
            text: '否',
            id: '0'
          }
        ],
        bankAccountType: []
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      dateStart: '',
      dateEnd: '',
      orgName: '',
      statisticalTime: '',
      currentDate: '',
      yearMonthStart: '',
      yearMonthEnd: ''
    }
  },
  computed: {
    ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler () {
        if (this.queryParams.yearMonth == null || this.queryParams.yearMonth == '' || this.queryParams.yearMonth == undefined) {
          return
        }
        if (this.queryParams.yearNum == null || this.queryParams.yearNum == '' || this.queryParams.yearNum == undefined) {
          return
        }
        this.getList()
      }
    }
  },
  created () {
    this.getCurrentDate()
  },
  methods: {
    getTableColumns () {
      this.table.columns = [
        { prop: 'index', label: '序号', width: '50', fixed: 'left' },
        { label: '镇（街）名称', prop: 'townName', width: '150' },
        { label: '村名称', prop: 'villageName', width: '150' },
        { label: '单位名称', prop: 'orgName', width: '300' },
        { label: '专项资金编号', prop: 'receiptCode', width: '150' },
        { label: '专项资金名称', prop: 'receiptName', width: '150' },
        { label: '专项资金类别', prop: 'categoryCode', width: '150' },
        { label: '专项资金来源', prop: 'specialSource', width: '150' },
        { label: '入账日期', prop: 'arrivalDate', width: '150' },
        { label: this.getPreviousBalanceLabel(), prop: 'previousBalance', width: '150', filter: 'money' },
        { label: '收入金额', prop: 'income', width: '150', filter: 'money' },
        { label: '支出金额', prop: 'expenses', width: '150', filter: 'money' },
        { label: this.getBalanceLabel(), prop: 'balance', width: '150', filter: 'money' },
        { label: '沉淀金额', prop: 'accumulation', width: '150', filter: 'money' }
      ]
      this.statisticalTime = this.yearMonthStart + ' 至 ' + this.queryParams.yearMonth
    },
    getCurrentDate () {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      // const day = String(now.getDate()).padStart(2, '0')
      this.queryParams.yearMonth = `${year}-${month}`
      this.adjustYearMonth(this.queryParams.yearMonth, this.queryParams.yearNum)
    },
    adjustYearMonth (originalDate, yearsToAdjust) {
      const [year, month] = originalDate.split('-').map(Number)
      const date = new Date(year, month - 1, 1)
      date.setFullYear(date.getFullYear() - yearsToAdjust)
      const newYear = date.getFullYear()
      const newMonth = String(date.getMonth() + 1).padStart(2, '0')
      this.yearMonthStart = `${newYear}-${newMonth}`
      this.getTableColumns()
    },
    getPreviousBalanceLabel () {
      return this.yearMonthStart != undefined ? (this.yearMonthStart.replace('-', '年') + '月余额') : ''
    },
    getBalanceLabel () {
      return this.queryParams.yearMonth != undefined ? (this.queryParams.yearMonth.replace('-', '年') + '月余额') : ''
    },
    handleSizeChange (val) {
      this.queryParams.rows = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.queryParams.page = val
      this.getList()
    },
    getList () {
      if (this.areaLogin.type == 'Organization') {
        this.queryParams.orgId = this.areaLogin.id
        this.queryParams.orgName = this.areaLogin.fname
        this.queryParams.areaCode = ''
      } else {
        this.queryParams.orgId = ''
        this.queryParams.orgName = ''
        this.queryParams.areaCode = this.areaLogin.code
      }
      if (this.queryParams.yearMonth == null || this.queryParams.yearMonth == '' || this.queryParams.yearMonth == undefined) {
        this.$message.warning('请选择查询年月')
        return
      }
      if (this.queryParams.yearNum == null || this.queryParams.yearNum == '' || this.queryParams.yearNum == undefined) {
        this.$message.warning('请选择沉淀年数')
        return
      }
      this.adjustYearMonth(this.queryParams.yearMonth, this.queryParams.yearNum)
      this.tableLoading = true
      this.orgName = this.areaLogin.fname
      accumulatedFundsReport(this.queryParams).then(res => {
        this.table.tableList = res.data.records
        this.table.total = res.data.total
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleSearch () {
      this.getList()
    },
    getRowClassName ({ row, rowIndex }) {
      if (row.orgName == '总合计') { // 例如，让第一行的字体加粗
        return 'row-bold'
      }
      return ''
    },
    handleExport () {
      if (this.areaLogin.type == 'Organization') {
        this.queryParams.orgId = this.areaLogin.id
        this.queryParams.orgName = this.areaLogin.fname
        this.queryParams.areaCode = ''
      } else {
        this.$message.warning('请选择机构')
        return
      }
      if (this.queryParams.yearMonth == null || this.queryParams.yearMonth == '' || this.queryParams.yearMonth == undefined) {
        this.$message.warning('请选择查询年月')
        return
      }
      if (this.queryParams.yearNum == null || this.queryParams.yearNum == '' || this.queryParams.yearNum == undefined) {
        this.$message.warning('请选择沉淀年数')
        return
      }
      let error = false
      try {
        // 拼接参数
        const params = new URLSearchParams()
        Object.keys(this.queryParams).forEach(key => {
          const value = this.queryParams[key]
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value)
          }
        })

        // 获取 token 和 tenant_id
        const accessToken = getToken()
        const tenantId = this.$Cookies.get('X-tenant-id-header')

        // 构建完整 URL
        const api = '/specialFund/reconciliation/report/accumulatedFundsExport'
        const url = `${process.env.VUE_APP_BASE_API}${api}?${params.toString()}&access_token=${accessToken}&tenant_id=${tenantId}`
        this.exportloading = true
        // 创建 <a> 标签并触发下载
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', '') // 强制浏览器下载（而不是预览）
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (err) {
        error = true // 捕获到异常，标记为错误
        this.$message.error(this.$t('导出失败')) // 显示错误消息
      } finally {
        this.exportloading = false
        if (!error) {
          // this.$message.success(this.$t('导出成功')) // 没有错误才显示成功消息
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .table-text {
  width: 100%;
  text-align: right;
}
::v-deep .row-bold {
  font-weight: bold !important;
  background-color: #f8f8f9;
}
</style>
