import request from "@/utils/request";

//初始角色权限
export function getList() {
    return request({
        url: "/system/workbench/config/user/getList",
        method: "post",
    });
}

//个人中心用户信息
export function current(data) {
    return request({
        url: "/system/user/current",
        method: "get",
        params: data
    })
}


//常用功能设置
export function resourceTree(data) {
    return request({
        url: "/system/workbench/config/user/resourceTree",
        method: "get",
        params: data
    });
}

//消息盒子角标

export function countMessage(data) {
    return request({
        url: "/system/workbench/config/user/countMessage",
        method: "get",
        params: data
    });
}

//消息已读
export function updateUserMessageRead(data) {
    return request({
        url: "/system/messageBox/updateUserMessageRead",
        method: "post",
        data: data,

    });
}

//消息盒子列表
export function pageMessage(data) {
    return request({
        url: "/system/workbench/config/user/pageMessage",
        method: "post",
        data: data,
        payload: true,
    });
}
//监控预警

export function warmingPage(data) {
    return request({
        url: "/leaderview/riskWarming/warmingPage",
        method: "post",
        data: data,
    });
}


//指标分析
// export function loadData() {
//     return request({
//         url: "/system/workbench/config/user/loadData",
//         method: "post",
//     });
// }

//指标删除
export function deleteZb(data) {
    return request({
        url: "/system/workbench/config/user/delete",
        method: "post",
        data: data
    });
}

//指标菜单设置
export function page(data) {
    return request({
        url: "/system/workbench/index/tree",
        method: "get",
        data: data
    });
}

//指标菜单选择保存
export function saveZb(data) {
    return request({
        url: "/system/workbench/config/user/save",
        method: "post",
        data: data,
        payload: true,
    });
}



//账套进度

export function booksProgress() {
    return request({
        url: "/financial/books/basedata/accountingperiod/booksProgress",
        method: "get",
    });
}


//到期合同

export function expirationContract() {
    return request({
        url: "/weixun/contract/expirationContract",
        method: "get",
    });
}


//支出统计

export function expenseStatistics() {
    return request({
        url: "/financial/cashier/cashierVoucher/expenseStatistics",
        method: "get",
    });
}


//预算行政账

export function getBudgetXZ() {
    return request({
        url: "/financial/budget/budgetPlait/getBudgetXZ",
        method: "post",
    });
}

//票据未收款

export function getBillAmount() {
    return request({
        url: "/bill/billDetail/getBillAmount",
        method: "post",
    });
}

//银行存款余额

export function getBankBalance() {
    return request({
        url: "/financial/cashier/cashierItemBalance/getBankBalance",
        method: "post",
    });
}


//农村集体经济组织


export function getBudgetJT() {
    return request({
        url: "/financial/budget/budgetPlait/getBudgetJT",
        method: "post",
    });
}


//指标统一接口
export function loadData(data) {
    return request({
        url: "/system/workbench/config/user/loadData",
        method: "post",
        data: data,
        payload: true,
    });
}
export function expenseStatisticsDetails(data) {
    return request({
        url: "/financial/cashier/cashierVoucher/expenseStatisticsDetails",
        method: "get",
        params: data,
        payload: true,
    });
}







