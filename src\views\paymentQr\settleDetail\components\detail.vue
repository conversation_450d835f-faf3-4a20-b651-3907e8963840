<template>
  <div v-loading="loading" class="gever-form detail" style="height: 100%">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="170px"
      :disabled="type == 'view'"
    >
      <div class="gever-title">{{ $t('基本信息') }}</div>
      <div class="form-db">
        <el-form-item :label="$t('商户号')" prop="merchantNo">
          <gever-input v-model="contentForm.merchantNo" />
        </el-form-item>
        <el-form-item :label="$t('交易号')" prop="tradeNo">
          <gever-input v-model="contentForm.tradeNo" />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('交易金额')" prop="tradeAmount">
          <gever-input v-model="contentForm.tradeAmount" />
        </el-form-item>
        <el-form-item :label="$t('交易状态')" prop="status">
          <el-select v-model="contentForm.status" clearable placeholder="请选择">
            <el-option
              v-for="item in optionsMap.payStatus"
              :key="item.id"
              :label="item.text"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('客户账号')" prop="customerAccount">
          <gever-input v-model="contentForm.customerAccount" />
        </el-form-item>
        <el-form-item :label="$t('说明')" prop="remark">
          <gever-input v-model="contentForm.remark" />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('交易时间')" prop="tradeTime">
          <gever-input v-model="contentForm.tradeTime" />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getDictionary } from '@/api/gever/common.js' // 引入获取字典的方法

export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      contentForm: {},
      rules: {},
      optionsMap: {
        payStatus: [] // 添加 payStatus 字段
      }
    }
  },
  async created() {
    // 获取支付状态字典数据
    const { data: payStatus = [] } = await getDictionary('/financial/qrPayment/status/')
    this.optionsMap.payStatus = payStatus
  }
}
</script>

<style scoped>
.detail {
  padding: 24px;
}
</style> 