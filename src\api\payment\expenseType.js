
import request from '@/utils/request'

/**
 * @name:支出类型树结构（懒加载）
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function expenTypeTree(params) {
  return request({
    url: '/payment/paymentExpenditureType/tree',
    method: 'get',
    params,
    withCredentials: true
  })
}
/**
 * @name:获取数据页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/payment/paymentExpenditureType/page',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 新增初始化
export function init(params) {
  return request({
    url: '/payment/paymentExpenditureType/add/init',
    method: 'get',
    params,
    withCredentials: true
  })
}
// 保存
export function save(data) {
  return request({
    url: '/payment/paymentExpenditureType/save',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 详情
export function load(id) {
  return request({
    url: `/payment/paymentExpenditureType/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}
// 删除
export function postDeletePost(data) {
  return request({
    url: '/payment/paymentExpenditureType/delete',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 支出类型树结构
export function paymentExpenditureTypeTree(params) {
  return request({
    url: '/payment/paymentExpenditureType/validateLeafList',
    // url: '/payment/paymentExpenditureType/tree',
    method: 'get',
    params,
    withCredentials: true
  })
}
