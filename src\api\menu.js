/*
 * @Descripttion: 
 * @version: 
 * @Author: 未知
 * @Date: 2023-08-24 11:44:54
 * @LastEditors: Andy
 * @LastEditTime: 2023-08-24 11:44:58
 */
import request from '@base/src/utils/request'

// 获取路由
export function getRouters(params) {
  return request({
    url: '/system/resource/getRouters',
    method: 'get',
    withCredentials: true,
    params: params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

export const getAuth = (data) => {
  return request({
    url: '/system/resource/authResourcesVer2',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params: data
  })
}

export const getImg = (url) => {
  return request({
    url: url,
    method: 'get',
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
