<template>
  <div>
    <fold-box right-title="审批查询">
      <template #right>
        <div class="right-box">
          <el-form ref="_formRef" inline :model="queryParams">
            <el-form-item :label="$t('审批事项名称')" prop="matter">
              <el-input v-model="queryParams.matter" class="w100" />
            </el-form-item>
            <el-form-item :label="$t('资金用途')" prop="fundsUse">
              <el-input v-model="queryParams.fundsUse" class="w100" />
            </el-form-item>
            <el-form-item :label="$t('审批状态')" prop="approvalStatus">
              <el-select
                v-model="queryParams.approvalStatus"
                clearable
                class="w100"
              >
                <el-option
                  v-for="item in approvalstatusOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('支付状态')" prop="approvalStatus">
              <el-select
                v-model="queryParams.paymentState"
                clearable
                class="w100"
              >
                <el-option
                  v-for="item in paymentStateOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('支出类型')" prop="expenditureType">
              <el-select
                v-model="queryParams.expenditureType"
                clearable
                class="w100"
              >
                <el-option
                  v-for="item in expendTypeOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('申请类型')" prop="applicationType">
              <el-select
                v-model="queryParams.applicationType"
                clearable
                class="w100"
              >
                <el-option
                  v-for="item in applicationTypeOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('结算方式')" prop="expenditureType">
              <el-select
                v-model="queryParams.settlementMethod"
                clearable
                class="w100"
              >
                <el-option
                  v-for="item in settlementMethodOptions"
                  :key="item.id"
                  :label="item.settleName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('机构类型')" prop="orgType">
              <el-select v-model="queryParams.orgType" clearable class="w100">
                <el-option
                  v-for="item in orgTypeOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('银行账户类型')" prop="accountType">
              <el-select
                v-model="queryParams.accountType"
                clearable
                class="w100"
              >
                <el-option
                  v-for="item in accountTypeOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <div class="inline-block">
              <el-form-item :label="$t('金额(元)')">
                <el-input-number
                  v-model="queryParams.startAmount"
                  :precision="2"
                  :controls="false"
                  class="w100"
                />
                {{ $t('至') }}
                <el-input-number
                  v-model="queryParams.endAmount"
                  :precision="2"
                  :controls="false"
                  class="w100"
                />
              </el-form-item>
            </div>
            <div class="inline-block">
              <el-form-item :label="$t('开始日期')" prop="startCreationTime">
                <el-date-picker
                  v-model="queryParams.startCreationTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  type="date"
                  :placeholder="$t('选择日期')"
                />
              </el-form-item>
              <el-form-item :label="$t('结束日期')" prop="endCreationTime">
                <el-date-picker
                  v-model="queryParams.endCreationTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  type="date"
                  :placeholder="$t('选择日期')"
                />
              </el-form-item>
<!--              <el-form-item :label="$t('乡村振兴类别')" prop="revitalizationType">
                <gever-select
                  v-model="queryParams.revitalizationType"
                  :options="revitalizationTypeOptions"
                  clearable
                  class="w150"
                />
              </el-form-item>-->
            </div>
            <el-form-item>
              <el-button
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplicationSearch.list'
                "
                type="primary"
                plain
                round
                icon="el-icon-search"
                @click="loadList"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            v-loading="loading"
            :columns="tableColumns"
            :data="table.dataList"
            :total="table.total"
            :pagi="queryParams"
            :pageSizes="[5, 10, 20, 30, 40, 50, 100, 500, 1000]"
            @p-current-change="loadList"
            @size-change="loadList"
          >
            <!-- <template #expenditureType="{ row }">
              {{ getExpenditureType(row.expenditureType) }}
            </template> -->
            <template #amount="{ row }">
              {{ getAmount(row.amount) }}
            </template>
            <template #approvalStatus="{ row }">
              {{ getApprovalStatus(row.approvalStatus) }}
            </template>
            <template #creationTime="{ row }">
              {{ getCreationTime(row.creationTime) }}
            </template>
            <template #paymentState="{ row }">
              {{ getPaymentState(row.paymentState) }}
            </template>
            <template #orgType="{ row }">
              {{ getOrgTypePayment(row.orgType) }}
            </template>
            <template #accountType="{ row }">
              {{ getAccountType(row.accountType) }}
            </template>
            <template #revitalizationType="{ row }">
              {{ convertRevitalizationType(row.revitalizationType) }}
            </template>
            <template #settlementMethod="{ row }">
              {{ convertSettlementMethod(row.settlementMethod) }}
            </template>
            <template #operation="{ row }">
              <el-button
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplicationSearch.view'
                "
                type="text"
                @click="handleView(row)"
              >
                {{ $t('查看') }}
              </el-button>
              <el-button
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplicationSearch.view'
                "
                type="text"
                @click="handleTrace(row)"
              >
                {{ $t('跟踪') }}
              </el-button>
              <el-button type="text" @click="handlePreview(row)">
                {{ $t('预览PDF') }}
              </el-button>
            </template>
            <template #footer-left>
              <p style="color: #46a6ff">
                {{ $t('本次支付合计：') }}
                <span style="color: #333; font-weight: bold">
                  {{ $t(totalAmount) }}
                </span>
              </p>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <public-drawer
      :visible.sync="drawerVisible"
      :title="$t('查看用款申请')"
      :size="70"
    >
      <application
        v-if="drawerVisible"
        ref="_appRef"
        class="application"
        :receiver="receiverList"
        :application="currentApplication"
        :tree-data="treeData"
        load-type="view"
        :expend-type-options="expendTypeOptions"
      />
    </public-drawer>

    <public-drawer
      :visible.sync="processVisible"
      :title="$t('流程跟踪')"
      :size="70"
    >
      <process-trace
        v-if="processVisible"
        :url="processUrl"
        :show-process-log="true"
        :process-ins-id="processInsId"
        :bussiness-key="bussinessKey"
      />
    </public-drawer>
  </div>
</template>

<script>
import { formatMoney } from '@/utils/index.js'
import {
  loadApprovalSearchList,
  getApplication
} from '@/api/payment/approval.js'
import { comboJson, authTreeDataAll } from '@/api/gever/common.js'
import Application from '../approvalApplication/applicationInfo.vue'
import { getToken } from '@/utils/auth'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { paymentExpenditureTypeTree as expenseTypePage } from '@/api/payment/expenseType'
import { page as billingMethodPage } from '@/api/payment/billingMethod'

export default {
  name: 'ApprovalSearch',
  components: { Application },
  data() {
    return {
      loading: false,
      queryParams: {
        revitalizationType: '',
        region: '',
        codes: '',
        orgCode: '',
        orgType: ' ',
        accountType: ' ',
        matter: '',
        fundsUse: '',
        approvalStatus: ' ',
        paymentState: ' ',
        expenditureType: ' ',
        applicationType: ' ',
        startAmount: undefined,
        endAmount: undefined,
        startCreationTime: '',
        endCreationTime: '',
        settlementMethod: '',
        page: 1,
        rows: 20
      },
      table: {
        total: 0,
        dataList: []
      },
      paymentStateOptions: [
        { id: ' ', text: this.$t('全部') },
        { id: '0', text: this.$t('未支付') },
        { id: '1', text: this.$t('支付中') },
        { id: '2', text: this.$t('支付成功') },
        { id: '3', text: this.$t('部分成功') },
        { id: '5', text: this.$t('退款成功') },
        { id: '-1', text: this.$t('支付失败') },
        { id: '-9', text: this.$t('已退回') },
        { id: '-10', text: this.$t('交易终止') },
        { id: '99', text: this.$t('状态未知') }
      ],
      applicationTypeOptions: [
        { id: ' ', text: this.$t('全部') },
        { id: 0, text: '常规用款' },
        { id: 1, text: '收支相抵' },
        { id: 2, text: '冲减收入' },
        { id: 3, text: '非预算项目支出' },
        { id: 4, text: '银行代扣' },
        { id: 5, text: '扣减预算' },
        { id: 6, text: '非预算收支相抵' }
      ],
      approvalstatusOptions: [{ id: ' ', text: this.$t('全部') }],
      orgTypeOptions: [{ id: ' ', text: this.$t('全部') }],
      accountTypeOptions: [{ id: ' ', text: this.$t('全部') }],
      expendTypeOptions: [{ id: ' ', text: this.$t('全部') }],
      revitalizationTypeOptions: [{ id: '', text: this.$t('全部') }],
      tableColumns: [
        { type: 'selection', width: '50' },
        { label: '序号', type: 'index', index: this.indexMethod, width: '80' },
        {
          label: '申请单位',
          prop: 'orgName',
          minWidth: '120',
          resizable: true
        },
        // { label: '机构类型', slotName: 'orgType', width: '100' },
        // { label: '银行账户类型', slotName: 'accountType', width: '100' },
        { label: '审批事项名称', prop: 'matter', minWidth: '200' },
        { label: '申请编号', prop: 'serialNumber', minWidth: '200' },
        { label: '资金用途', prop: 'fundsUse', minWidth: '200' },
        { label: '申请详情', prop: 'details', width: '150' },
        { label: '结算方式', prop: 'settlementMethod', width: '100' },
        { label: '支出类型', prop: 'expenditureTypeZh', width: '100' },
        { label: '金额（元）', slotName: 'amount', width: '120' },
        // { label: '乡村振兴类别', slotName: 'revitalizationType', width: '120' },
        { label: '审批状态', slotName: 'approvalStatus', width: '100' },
        { label: '当前环节', prop: 'nodeName', width: '100' },
        { label: '创建日期', slotName: 'creationTime', width: '100' },
        { label: '申请人', prop: 'submitUserName', width: '100' },
        { label: '支付状态', slotName: 'paymentState', width: '100' },
        { label: '操作', slotName: 'operation', fixed: 'right', width: '220' }
      ],
      settlementMethodOptions: [{ id: '', settleName: this.$t('全部') }],
      processInsId: '',
      bussinessKey: '',
      processUrl: '',
      processVisible: false,
      drawerVisible: false,
      currentApplication: {},
      receiverList: [],
      treeData: []
    }
  },
  computed: {
    ...mapState(['areaLogin']),
    totalAmount() {
      const total = this.table.dataList.reduce((t, cur) => {
        return (t += +(cur.amount || 0))
      }, 0)

      return formatMoney(total)
    }
  },
  watch: {
    areaLogin: {
      /** 监听到变化后就触发一次handler 相当与created */
      immediate: true,
      handler(val) {
        if (val) {
          this.queryParams.region = val.code || ''
          this.loadList()
        }
      },
      deep: true
    }
  },
  created() {
    this.queryParams.region = this.areaLogin.code || ''
    this.loadAllComboJson()
    this.loadList()
    authTreeDataAll().then((res) => {
      this.treeData = res.data
    })
  },
  methods: {
    indexMethod(index) {
      return (this.queryParams.page - 1) * this.queryParams.rows + index + 1
    },
    getToken,
    loadAllComboJson() {
      comboJson({ path: '/payment/approvalstatus' }).then((res) => {
        this.approvalstatusOptions.push(...res.data)
      })
      comboJson({ path: '/system/organization_type/' }).then((res) => {
        this.orgTypeOptions.push(...res.data)
      })
      comboJson({ path: '/financial/bank_account_type/' }).then((res) => {
        this.accountTypeOptions.push(...res.data)
      })
      comboJson({ path: '/payment/revitalization_type' }).then((res) => {
        this.revitalizationTypeOptions.push(...res.data)
      })
      expenseTypePage({ page: 1, rows: 100 }).then((res) => {
        const data = res.data.filter((cur) => cur.id !== '0')
        this.expendTypeOptions.push(...data)
      })
      // 结算方式
      billingMethodPage({ page: 1, rows: 100 }).then((res) => {
        this.settlementMethodOptions.push(...res.data.rows)
      })
    },
    loadList() {
      const start = this.queryParams.startCreationTime
        ? new Date(this.queryParams.startCreationTime).getTime()
        : 0
      const end = this.queryParams.endCreationTime
        ? new Date(this.queryParams.endCreationTime).getTime()
        : 0
      if (start && end) {
        if (start > end) {
          return this.$message.warning('开始日期不能大于结束日期')
        }
      }
      this.loading = true

      const params = JSON.parse(JSON.stringify(this.queryParams))
      for (const key in params) {
        if (params[key] && typeof params[key] === 'string') {
          params[key] = params[key].trim()
        }
        if ((key === 'startAmount' || key === 'endAmount') && !params[key]) {
          params[key] = ''
        }
      }
      loadApprovalSearchList(params)
        .then((res) => {
          this.table.dataList = res.data.rows
          this.table.total = res.data.total
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleView(row) {
      // approvalApplicationSearch
      getApplication(row.id).then((res) => {
        this.drawerVisible = true
        this.currentApplication = res.data[0]
        this.receiverList = res.data[1]
      })
    },
    handleTrace(row) {
      if (!row.procInstId) {
        return this.$message.warning(this.$t('只有已提交的申请单才能进行跟踪'))
      }
      this.processInsId = row.procInstId
      this.bussinessKey = row.id
      this.processUrl =
        '/' +
        process.env.VUE_APP_REAL +
        '/workflow/genProcessDiagram?processInsId=' +
        this.processInsId +
        '&bussinessKey=' +
        this.bussinessKey +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      this.processVisible = true
    },
    handlePreview(row) {
      if (row.approvalStatus == 1) {
        return this.$message.warning(
          this.$t('审批状态为未提交的记录不能预览pdf')
        )
      }
      const url =
        process.env.VUE_APP_BASE_API +
        '/payment/approval/approvalApplication/export_pdf?id=' +
        row.id +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.open(url)
    },
    getExpenditureType(val) {
      if (!val) return ''
      const type = this.expendTypeOptions.find((item) => item.id == val)
      return type ? type.text : ''
    },
    getAmount(val) {
      if (val === undefined) return ''
      return Number(val).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    },
    getApprovalStatus(val) {
      if (val === undefined) return ''
      const status = this.approvalstatusOptions.find((item) => item.id == val)
      return status ? status.text : ''
    },
    getCreationTime(val) {
      if (val === undefined) return ''
      const date = new Date(val)
      return (
        date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
      )
    },
    getPaymentState(val) {
      if (val === undefined) return ''
      const state = this.paymentStateOptions.find((item) => item.id == val + '')
      return state ? state.text : ''
    },
    getOrgTypePayment(val) {
      if (val === undefined) return ''
      const state = this.orgTypeOptions.find((item) => item.id == val + '')
      return state ? state.text : ''
    },
    convertRevitalizationType(val) {
      const revitalizationType = this.revitalizationTypeOptions.find(
        (item) => item.id === val + ''
      )
      return revitalizationType ? revitalizationType.text : '/'
    },
    convertSettlementMethod(val) {
      const settlementMethod = this.settlementMethodOptions.find(
        (item) => item.id === val + ''
      )
      return settlementMethod ? settlementMethod.settleName : ''
    },
    getAccountType(val) {
      if (val === undefined) return ''
      const state = this.accountTypeOptions.find((item) => item.id == val + '')
      return state ? state.text : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.inline-block {
  display: inline-block;
}
</style>
