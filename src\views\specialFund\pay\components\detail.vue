<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-28 11:27:39
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-23 16:06:16
-->
<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-15 16:06:46
 * @LastEditors: liuwf
 * @LastEditTime: 2025-05-28 10:52:36
-->
<template>
  <div v-loading="loading" class="gever-form detail">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="170px"
    >
      <div class="gever-title label-title">{{ $t('支出明细') }}</div>
      <div class="form-db">
        <el-form-item :label="$t('单位名称')" prop="orgName">
          <gever-input v-model="form.orgName" disabled />
        </el-form-item>
        <el-form-item :label="$t('专项资金编号')" prop="receiptCode">
          <gever-input v-model="form.specialFundCode" disabled />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('专项资金名称')" prop="categoryId">
          <gever-input v-model="form.specialFundName" disabled />
        </el-form-item>
        <el-form-item :label="$t('专项资金类别')" prop="specialSource">
          <gever-input v-model="form.specialFundCategoryCode" disabled />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('专项资金来源')" prop="receiptName">
          <gever-input v-model="form.specialFundSourceText" disabled />
        </el-form-item>
        <el-form-item :label="$t('专项资金支出类型')" prop="bankAccountId">
          <gever-input v-model="form.paymentTypeText" disabled />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('支出申请单编号')" prop="amount">
          <gever-input v-model="form.serialNumber" disabled />
        </el-form-item>
        <el-form-item :label="$t('支出申请金额（元）')" prop="bankAccountName">
          <gever-input v-model="form.payAmount" disabled />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('实际支出金额')" prop="amount">
          <gever-input v-model="form.actualPayAmount" disabled />
        </el-form-item>
        <el-form-item :label="$t('支出申请人')" prop="amount">
          <gever-input v-model="form.submitUserName" disabled />
        </el-form-item>

      </div>
      <div class="form-db">
        <el-form-item :label="$t('支出申请日期')" prop="bankAccountName">
          <gever-input v-model="form.creationTime" disabled />
        </el-form-item>
        <el-form-item :label="$t('支付日期')" prop="bankAccountName">
          <gever-input v-model="form.actualPayTime" disabled />
        </el-form-item>

      </div>
      <div class="form-db">
        <el-form-item :label="$t('出纳日期')" prop="amount">
          <gever-input v-model="form.cashierDate" disabled />
        </el-form-item>
        <el-form-item :label="$t('付款银行账号')" prop="amount">
          <gever-input v-model="form.payAccountNumber" disabled />
        </el-form-item>

      </div>
      <div class="form-db">
        <el-form-item :label="$t('收款方')" prop="bankAccountName">
          <gever-input v-model="form.receiverAccount" disabled />
        </el-form-item>
        <el-form-item :label="$t('支出审批状态')" prop="amount">
          <gever-input v-model="form.approvalStatusText" disabled />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('支出支付状态')" prop="bankAccountName">
          <gever-input v-model="form.payStatusText" disabled />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('支出用途')" prop="purpose">
          <gever-input v-model="form.fundsUse" disabled />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('支出申请详情')" prop="purpose">
          <gever-input v-model="form.details" disabled />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  components: {
  },
  props: {
    contentForm: {
      type: Object,
      default: () => { }
    }
  },
  data () {
    return {
      form: this.contentForm,
      loading: false,
      rules: {
      },
      accountOptions: [],
      treeNodeData: {},
      areaCode: '',
      restaurants: [],
      timeout: null
    }
  },
  created () {
    this.form.cashierDate = this.form.cashierDate ? this.form.cashierDate.substring(0, 10) : ''
    this.form.creationTime = this.form.creationTime ? this.form.creationTime.substring(0, 10) : ''
    this.form.actualPayTime = this.form.actualPayTime ? this.form.actualPayTime.substring(0, 10) : ''
    this.form.approvalTime = this.form.approvalTime ? this.form.approvalTime.substring(0, 10) : ''
    this.form.payAmount = this.form.payAmount ? this.formatMoney(this.form.payAmount) : ''
    this.form.actualPayAmount = this.form.actualPayAmount ? this.formatMoney(this.form.actualPayAmount) : ''
  },
  methods: {
      formatMoney(num) {
        if (typeof num !== 'number') num = parseFloat(num) || 0
        return new Intl.NumberFormat('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(num)
      }
  }
}
</script>
<style lang="scss" scoped>
.label-title {
  color: #1890ff;
}
</style>
