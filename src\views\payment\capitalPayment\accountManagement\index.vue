<template>
  <div>
    <fold-box right-title="银行账户管理">
      <template #right>
        <div class="right-box">
          <el-form inline>
            <el-form-item :label="$t('户主名称')">
              <el-input v-model="queryParams.ownerName" />
            </el-form-item>
            <el-form-item
              :label="$t('是否签订监管协议')"
              prop="hasRegulatoryAgreement"
            >
              <el-select
                v-model="queryParams.hasRegulatoryAgreement"
                clearable
                class="w150"
              >
                <el-option
                  v-for="item in hasRegulatoryAgreementOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('是否已禁用')" prop="state">
              <el-select
                v-model="queryParams.state"
                clearable
                class="w150"
              >
                <el-option
                  v-for="item in isValidOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('所属银行')" prop="bankType">
              <gever-select
                v-model="queryParams.bankType"
                :options="bankTypeOptions"
                clearable
                class="w150"
              />
            </el-form-item>
            <el-form-item :label="$t('是否授权银农直连')" prop="isAuthorized">
              <gever-select
                v-model="queryParams.isAuthorized"
                :options="whetherOptions"
                clearable
                class="w150"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                v-hasPermi="'financial.payment.manager.bankAccount.list'"
                type="primary"
                plain
                round
                icon="el-icon-search"
                @click="loadList"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <div style="float: right">
              <el-form-item>
                <el-button
                  plain
                  round
                  icon="el-icon-download"
                  @click="handleExport"
                >
                  {{ $t('导出') }}
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.payment.manager.bankAccount.add'"
                  icon="el-icon-plus"
                  type="primary"
                  :disabled="paymentLogin.type !== 'Organization'"
                  round
                  @click="handleAdd"
                >
                  {{ $t('新增') }}
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.payment.manager.bankAccount.add'"
                  icon="el-icon-edit"
                  type="primary"
                  plain
                  round
                  :disabled="paymentLogin.type !== 'Organization'"
                  @click="handleEditRegulatoryAgreement"
                >
                  {{ $t('设置签订监管协议') }}
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.payment.manager.bankAccount.add'"
                  icon="el-icon-money"
                  type="primary"
                  plain
                  round
                  :disabled="paymentLogin.type !== 'Organization'"
                  @click="handleOpenEntrustType"
                >
                  {{ $t('设置委托业务类型') }}
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.payment.manager.bankAccount.authorize'"
                  icon="el-icon-refresh"
                  type="primary"
                  plain
                  round
                  :disabled="paymentLogin.type !== 'Organization'"
                  @click="handleUpdateAuthorize"
                >
                  {{ $t('更新授权') }}
                </el-button>
              </el-form-item>
            </div>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            :columns="tableColumns"
            :data="table.dataList"
            :total="table.total"
            :pagi="queryParams"
            @p-current-change="loadList"
            @size-change="loadList"
          >
            <template #bankType="{ row }">
              {{ getBankType(row.bankType) }}
            </template>
            <template #accountType="{ row }">
              {{ getAccountType(row.accountType) }}
            </template>
            <template #depositType="{ row }">
              {{ getDepositType(row.depositType) }}
            </template>
            <template #state="{ row }">
              {{ row.state ? $t('启用') : $t('禁用') }}
            </template>
            <template #hasRegulatoryAgreement="{ row }">
              {{ row.hasRegulatoryAgreement === 1 ? $t('是') : $t('否') }}
            </template>
            <template #operation="{ row }">
              <el-link
                v-hasPermi="'financial.payment.manager.bankAccount.view'"
                :underline="false"
                type="primary"
                @click="handleView(row)"
              >
                {{ $t('查看') }}
              </el-link>
              <el-link
                v-hasPermi="'financial.payment.manager.bankAccount.edit'"
                :underline="false"
                type="primary"
                :disabled="paymentLogin.type !== 'Organization'"
                @click="handleEdit(row)"
              >
                {{ $t('编辑') }}
              </el-link>
              <el-link
                v-hasPermi="'financial.payment.manager.bankAccount.delete'"
                :underline="false"
                type="primary"
                :disabled="paymentLogin.type !== 'Organization'"
                @click="handleRemove(row)"
              >
                {{ $t('删除') }}
              </el-link>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <public-drawer
      :title="drawerTitle"
      :visible.sync="drawerVisible"
      :buttons="isView ? [] : buttons"
    >
      <bank-account
        v-if="drawerVisible"
        ref="_bankAccountRef"
        :bank-account="bankAccount"
        :disabled="isView"
        :type="type"
        :org-info="paymentLogin"
      />
    </public-drawer>

    <!-- 设置是否签订监管协议 -->
    <gever-dialog
      v-loading="loading"
      :visible.sync="regulatoryAgreementVisible"
      title="设置是否签订监管协议"
      :perch-height="400"
      width="50%"
      :buttons="[
        {
          type: '',
          text: '关 闭',
          buttonStatus: true,
          callback: () => {
            regulatoryAgreementVisible = false
          }
        },
        {
          text: '保存',
          type: 'primary',
          buttonStatus: true,
          disabled: loading === true,
          callback: saveRegulatoryAgreement
        }
      ]"
      @close="regulatoryAgreementClose"
    >
      <el-form ref="_regulatoryAgreementFormRef" inline class="gever-form" :model="bankAccount" label-position="top">
        <div class="gever-title">
          {{ $t('银行账号信息') }}
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('银行账号：')" prop="accountNumber">
            <el-input v-model.trim="bankAccount.accountNumber" disabled />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('所属银行：')" prop="bankType">
            <gever-select
              v-model="bankAccount.bankType"
              path="/payment/pay_bank_type"
              disabled
            />
          </el-form-item>
        </div>
        <div class="form-db">
            <el-form-item :label="$t('是否签订监管协议：')" prop="hasRegulatoryAgreement">
              <el-select v-model="bankAccount.hasRegulatoryAgreement">
                <el-option
                  v-for="item in whetherOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="gever-title">
            <span v-if="bankAccount.hasRegulatoryAgreement == 1" style="color: red">*</span>
            {{ $t('附件信息') }}
            <span class="red" style="font-weight: 400;">
              (附件类型仅支持jpg, jpeg, png, doc, xls, txt, pdf, docx, xlsx, zip)
            </span>
          </div>
          <div class="form-sg">
            <el-form-item :label="$t('')" prop="fileBatchId">
              <gever-upload
                ref = "_evidenceIdRef"
                :photographic="true"
                :accept="'.jpg,.jpeg,.png,.doc,.xls,.docx,.xlsx,.pdf,.txt,.zip'"
                :default-batch-id="bankAccount.fileBatchId"
                :business-id="bankAccount.id"
                file-classification="bankAccountFile"
                :list-type="'form-list'"
                :limit="10"
                @batch-change="changeFileBatch"
              />
            </el-form-item>
          </div>
      </el-form>
    </gever-dialog>
    <!-- 设置委托业务类型 -->
    <gever-dialog
      :title="$t('设置委托业务类型')"
      :visible.sync="entrustTypeVisible"
      width="850px"
      :before-close="handleCloseEntrustType"
      custom-class="accredit-dialog"
    >
      <div>
        <el-transfer
          v-model="entrustTypeValue"
          filterable
          :data="entrustTypeOptions"
          :props="{ key: 'id', label: 'fullName' }"
          :titles="[$t('待选类型'), $t('已选类型')]"
        >
          <span slot-scope="{ option }">
            <div :title="option.fullName">{{ option.fullName }}</div>
          </span>
        </el-transfer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-t="'关 闭'" plain @click="entrustTypeVisible = false" />
        <el-button v-t="'保 存'" type="primary" @click="handleSaveEntrustType" />
      </span>
    </gever-dialog>
  </div>
</template>

<script>
import {
  loadBankAccountList,
  saveBankAccount,
  loadBankAccountInfo,
  removeBankAccount,
  updateRegulatoryAgreement,
  updateBankAccountAuthorize,
  listEntrustType,
  setEntrustType,
  listBankEntrustType
} from '@/api/payment/capital.js'
import { comboJson } from '@/api/gever/common.js'
import BankAccount from './bankAccount.vue'
import { getToken } from '@/utils/auth'
// import { createNamespacedHelpers } from 'vuex'
// const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'AccountManagement',
  components: { BankAccount },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      queryParams: {
        nodeId: '',
        ownerName: '',
        hasRegulatoryAgreement: '',
        state: '1',
        page: 1,
        rows: 20,
        region: ''
      },
      whetherOptions: [
        { id: 1, text: '是' },
        { id: 0, text: '否' }
      ],
      hasRegulatoryAgreementOptions: [
        { id: '', text: this.$t('全部') },
        { id: '1', text: this.$t('是') },
        { id: '0', text: this.$t('否') }
      ],
      isValidOptions: [
        { id: '', text: this.$t('全部') },
        { id: '0', text: this.$t('禁用') },
        { id: '1', text: this.$t('启用') }
      ],
      table: {
        total: 0,
        dataList: []
      },
      depositTypeOptions: [],
      bankTypeOptions: [],
      accountTypeOptions: [],
      tableColumns: [
        { type: 'selection', width: '50' },
        { label: '序号', type: 'index', index: this.indexMethod, width: '80' },
        { label: '组织机构', prop: 'orgName', minWidth: '200' },
        { label: '户主名称', prop: 'ownerName', minWidth: '120' },
        { label: '账户类型', prop: 'accountType', minWidth: '120' },
        { label: '银行账号', prop: 'accountNumber', minWidth: '200' },
        { label: '所属银行', slotName: 'bankType', minWidth: '200' },
        { label: '存款类型', slotName: 'depositType', minWidth: '160' },
        { label: '是否已授权银农直连', prop: 'authorized', width: '160' },
        { label: '是否签订监管协议', prop: 'hasRegulatoryAgreement', width: '150' },
        { label: '是否已禁用', slotName: 'state', width: '120' },
        { label: '创建时间', prop: 'createTime', width: '150' },
        { label: '修改时间', prop: 'modifiedTime', width: '150' },
        { label: '操作', slotName: 'operation', width: '200', fixed: 'right' }
      ],
      drawerTitle: '',
      drawerVisible: false,
      regulatoryAgreementVisible: false,
      isView: true,
      type: '',
      bankAccount: {},
      buttons: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: () => {
            this.drawerVisible = false
          }
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ],
      loading: false,
      entrustTypeVisible: false,
      entrustTypeOptions: [],
      // entrustTypeList: [],
      entrustTypeValue: []
    }
  },
  computed: {
    // ...mapState(['paymentLogin']),
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  watch: {
    paymentLogin: {
      /** 监听到变化后就触发一次handler 相当与created */
      immediate: true,
      handler(val) {
        if (val) {
          this.queryParams.region = val.code || ''
          this.loadList()
        }
      },
      deep: true
    }
  },
  created() {
    this.loadAllComboJson()
    this.loadList()
  },
  methods: {
    getToken,
    indexMethod(index) {
      return (this.queryParams.page - 1) * this.queryParams.rows + index + 1
    },
    loadAllComboJson() {
      comboJson({ path: '/payment/pay_bank_type/' }).then((res) => {
        this.bankTypeOptions = res.data
      })
      comboJson({ path: '/financial/bank_account_type/' }).then((res) => {
        this.accountTypeOptions = res.data
      })
      comboJson({ path: '/payment/depositType/' }).then((res) => {
        this.depositTypeOptions = res.data
      })
    },
    loadList() {
      loadBankAccountList(this.queryParams).then((res) => {
        this.table.dataList = res.data.rows
        this.table.total = res.data.total
      })
    },
    // 导出银行账户
    handleExport() {
      let str = ''
      const obj = { ...this.queryParams }
      delete obj.page
      delete obj.rows
      for (var i = 0; i < Object.keys(obj).length; i++) {
        if (str) {
          str += '&'
        }
        str += Object.keys(obj)[i] + '=' + Object.values(obj)[i]
      }
      const url =
        this.baseUrl +
        `/payment/manager/bankAccount/export?` +
        str +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.location.href = url
    },
    handleAdd() {
      this.isView = false
      this.type = 'add'
      this.drawerTitle = '新增银行账户'
      this.bankAccount = {}
      this.drawerVisible = true
    },
    handleUpdateAuthorize() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (selectedRows.length < 1) {
        return this.$message.warning(this.$t('请选择需要更新授权的数据'))
      }
      const ids = []
      selectedRows.forEach((item) => {
        ids.push(item.id)
      })
      updateBankAccountAuthorize(ids).then((res) => {
        this.$message.success(res.message)
        this.loadList()
      })
    },
    handleView(row) {
      loadBankAccountInfo(row.id).then((res) => {
        this.isView = true
        this.type = 'view'
        this.drawerTitle = '查看银行账户'
        this.bankAccount = res
        this.drawerVisible = true
      })
    },
    handleEdit(row) {
      loadBankAccountInfo(row.id).then((res) => {
        this.isView = false
        this.type = 'edit'
        this.drawerTitle = '编辑银行账户'
        this.bankAccount = res
        this.drawerVisible = true
      })
    },
    handleRemove(row) {
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          removeBankAccount({ id: row.id }).then((res) => {
            this.$message.success(res.message)
            this.queryParams.page = 1
            this.loadList()
          })
        })
        .catch(() => {})
    },
    handleSave() {
      this.$refs['_bankAccountRef'].$refs['_formRef'].validate((valid) => {
        if (valid) {
          const params = this.$refs['_bankAccountRef'].currentBankAccount
          delete params.authorized
          console.log(params, '============')
          saveBankAccount(params).then((res) => {
            this.$message.success(res.message)
            this.drawerVisible = false
            this.loadList()
          })
        }
      })
    },
    handleEditRegulatoryAgreement() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (selectedRows.length != 1) {
        return this.$message.warning(this.$t('请选择1条需要设置是否签订监管协议的数据'))
      }
      loadBankAccountInfo(selectedRows[0].id).then((res) => {
        this.isView = false
        this.type = 'edit'
        this.bankAccount = res
        this.regulatoryAgreementVisible = true
      })
    },
    regulatoryAgreementClose() {
      this.regulatoryAgreementVisible = false
    },
    saveRegulatoryAgreement() {
      this.loading = true
      if (this.bankAccount.hasRegulatoryAgreement === 1
        && this.$refs._evidenceIdRef.fileList.length === 0) {
        this.loading = false
        this.$message.error(this.$t('“是否签订监管协议”选择是时，必须上传附件'))
        return
      }
      const params = this.bankAccount
      delete params.authorized
      updateRegulatoryAgreement(params).then((res) => {
        this.$message.success(res.message)
        this.loading = false
        this.regulatoryAgreementVisible = false
        this.loadList()
      })
      this.loading = false
    },
    handleCloseEntrustType() {
      this.entrustTypeVisible = false
    },
    handleOpenEntrustType() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (selectedRows.length != 1) {
        return this.$message.warning(this.$t('请选择1条需要设置是否签订监管协议的数据'))
      }
      listBankEntrustType({ bankType: selectedRows[0].bankType }).then((res) => {
        this.entrustTypeOptions = res.data
      })
      this.entrustTypeValue = []
      // this.entrustTypeList = []
      listEntrustType(selectedRows[0].id).then((res) => {
        // 获取已关联的数据列表
        const entrusTypeRel = res.data
        // const relIds = new Set()
        for (const item of entrusTypeRel) {
          this.entrustTypeValue.push(item.entrustType)
        }
        this.entrustTypeVisible = true
      })
    },
    handleSaveEntrustType() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      const params = {}
      params.id = selectedRows[0].id
      console.log(this.entrustTypeValue)
      params.entrustType = this.entrustTypeValue.join(',')
      this.loading = true
      setEntrustType(params).then((res) => {
        this.$message.success(res.message)
        this.loading = false
        this.$refs['_geverTableRef'].$refs['elTable'].clearSelection()
        this.entrustTypeVisible = false
        this.loadList()
      })
      this.loading = false
    },
    changeFileBatch(batchId) {
      this.bankAccount.fileBatchId = batchId
    },
    getBankType(val) {
      const type = this.bankTypeOptions.find((item) => item.id === val)
      return type ? type.text : ''
    },
    getDepositType(val) {
      const type = this.depositTypeOptions.find((item) => item.id == val)
      return type ? type.text : ''
    },
    getAccountType(val) {
      const type = this.accountTypeOptions.find((item) => item.id === val)
      return type ? type.text : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.el-link {
  margin-left: 8px;
}
::v-deep .el-transfer-panel {
  width: 280px !important;  // 添加!important提高优先级
}
</style>
