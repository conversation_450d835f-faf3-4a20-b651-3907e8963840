import request from '@/utils/request'

/**
 * 获取预算分类列表
 */
export function budgetTypeList(data) {
  return request({
    url: '/financial/budget/budgetType/page',
    method: 'post',
    data: data
  })
}

/**
 * 保存预算分类
 */
export function saveBudgetType(data) {
  return request({
    url: '/financial/budget/budgetType/save',
    method: 'post',
    data: data
  })
}

/**
 * 获取预算分类
 */
export function loadBudgetType(id) {
  return request({
    url: '/financial/budget/budgetType/load/' + id,
    method: 'get'
  })
}

/**
 * 获取预算分类
 */
export function removeBudgetType(data) {
  return request({
    url: '/financial/budget/budgetType/delete',
    method: 'post',
    data: data
  })
}

/**
 * 获取budgetItem
 */
export function budgetItemList(data) {
  return request({
    url: '/financial/budget/budgetItem/page',
    method: 'post',
    data: data
  })
}

/**
 * 获取budgetItem的预算科目
 */
export function getItemList(data) {
  return request({
    url: '/financial/budget/budgetItem/getItemList',
    method: 'post',
    data: data
  })
}

/**
 * 获取会计科目
 */
export function getAccountingItemType(data) {
  return request({
    url: '/financial/books/basedata/accountingItemType/listByBooksId',
    method: 'post',
    data: data
  })
}

/**
 * 保存预算科目
 */
export function saveBudgetItem(data) {
  return request({
    url: '/financial/budget/budgetItem/saveByItemType',
    method: 'post',
    data: data
  })
}

/**
 * 检测是否存在已编制的预算
 */
export function checkExistsBudgetData(data) {
  return request({
    url: '/financial/budget/budgetItem/checkExistsBudgetData',
    method: 'post',
    data: data
  })
}

/**
 * 删除预算科目
 */
export function removeBudgetItem(data) {
  return request({
    url: '/financial/budget/budgetItem/deleteAccountingItem',
    method: 'post',
    data: data
  })
}

/**
 * 获取账套编制信息
 */
export function getPlanningMainInfo(data) {
  return request({
    url: '/financial/budget/budgetPlanning/getPlanningMain',
    method: 'get',
    params: data
  })
}

/**
 * 获取编制列表
 */
export function loadPlanningList(data) {
  return request({
    url: '/financial/budget/budgetPlanning/page',
    method: 'post',
    data: data
  })
}

/**
 * 获取编制详情
 */
export function loadPlanningData(data) {
  return request({
    url: '/financial/budget/budgetPlanning/getPlanningData',
    method: 'post',
    data: data
  })
}

/**
 * 保存编制
 */
export function savePlanningData(params, data) {
  return request({
    url: '/financial/budget/budgetPlanning/save',
    method: 'post',
    params: params,
    data: data,
    payload: true
  })
}

/**
 * 提交预算编制
 */
export function submitPlanning(data) {
  return request({
    url: '/financial/budget/budgetPlanning/submit',
    method: 'post',
    data: data
  })
}

/**
 * 撤销提交预算编制
 */
export function unSubmitPlanning(data) {
  return request({
    url: '/financial/budget/budgetPlanning/unSubmit',
    method: 'post',
    data: data
  })
}

/**
 * 预算调整列表
 */
export function loadAdjustmentList(data) {
  return request({
    url: '/financial/budget/budgetAdjustment/page',
    method: 'post',
    data: data
  })
}

/**
 * 检测能都添加预算调整
 */
export function checkCanAddAdjustment(params) {
  return request({
    url: '/financial/budget/budgetAdjustment/checkCanAdd',
    method: 'get',
    params: params
  })
}

/**
 * 删除预算调整
 */
export function removeBudgetAdjustment(data) {
  return request({
    url: '/financial/budget/budgetAdjustment/delete',
    method: 'post',
    data: data
  })
}

/**
 * 提交预算调整
 */
export function submitBudgetAdjustment(data) {
  return request({
    url: '/financial/budget/budgetAdjustment/submit',
    method: 'post',
    data: data
  })
}

/**
 * 撤销提交预算调整
 */
export function unSubmitBudgetAdjustment(data) {
  return request({
    url: '/financial/budget/budgetAdjustment/unSubmit',
    method: 'post',
    data: data
  })
}

/**
 * 获取预算分类
 */
export function getAdjustState(data) {
  return request({
    url: '/financial/budget/budgetAdjustment/getAdjustState',
    method: 'post',
    data: data
  })
}

/**
 * 获取预算调整的数据
 */
export function getAdjustmentData(data) {
  return request({
    url: '/financial/budget/budgetAdjustment/getAdjustmentData',
    method: 'post',
    data: data
  })
}

/**
 * 保存预算调整的数据
 */
export function saveAdjustmentData(data, params) {
  return request({
    url: '/financial/budget/budgetAdjustment/save',
    method: 'post',
    data: data,
    params: params,
    payload: true
  })
}

/**
 * 获取预算待办事项
 */
export function getTodoList(data) {
  return request({
    url: '/workflow/getTodoList?path=/standard/financial/budget&subArea=true',
    method: 'post',
    data: data
  })
}
/**
 * 获取预算已办事项
 */
export function getHasDoneTaskList(data) {
  return request({
    url: '/workflow/getHasDoneTaskList?path=/standard/financial/budget&subArea=true',
    method: 'post',
    data: data
  })
}

/**
 * 获取预算分类列表
 */
export function budgetTypeListInVerify(data) {
  return request({
    url: '/financial/budget/budgetPlanning/verify/budgetTypeList',
    method: 'post',
    data: data
  })
}

/**
 * 预算审批
 */
export function budgetPlanningVerify(data) {
  return request({
    url: '/financial/budget/budgetPlanning/verify',
    method: 'post',
    data: data
  })
}
/**
 * 预算调整
 */
export function budgetAdjustmentVerify(data) {
  return request({
    url: '/financial/budget/budgetAdjustment/verify',
    method: 'post',
    data: data
  })
}

/**
 * 获取报告列表
 */
export function loadReportList(data) {
  return request({
    url: '/financial/report/reportList',
    method: 'post',
    data: data
  })
}

/**
 * 获取报告
 */
export function getReport(data) {
  return request({
    url: '/financial/report/get',
    method: 'post',
    data: data
  })
}

/**
 * 获取导出文件信息
 */
export function saveExcelTempFile(data) {
  return request({
    url: '/financial/report/saveExcelTempFile',
    method: 'post',
    data: data,
    payload: true,
    timeout: 180000
  })
}

/**
 * 预算调整审批信息初始化
 */
export function budgetVerifyInit(params) {
  return request({
    url: '/financial/budget/budgetAdjustment/verify/init',
    method: 'get',
    params: params
  })
}

/**
 * 预算编制审批信息初始化
 */
export function budgetPlanningVerifyInit(params) {
  return request({
    url: '/financial/budget/budgetPlanning/verify/init',
    method: 'get',
    params: params
  })
}
