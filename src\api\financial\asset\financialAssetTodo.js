import request from '@/utils/request'

/**
 * 资产待办列表
 */
export function getTodoList(data) {
  return request({
    url: '/financial/asset/assetTodo/getTodoList',
    method: 'post',
    data: data
  })
}

/**
 * 资产已办列表
 */
 export function getDoneList(data) {
    return request({
      url: '/financial/asset/assetTodo/getHasDoneList',
      method: 'post',
      data: data
    })
}

/**
 * 资产变动办理
 */
 export function assetAdjustApproval(data) {
    return request({
      url: '/financial/asset/assetAdjust/approval',
      method: 'post',
      data: data
    })
}

/**
 * 资产处置办理
 */
 export function assetDisposeApproval(data) {
    return request({
      url: '/financial/asset/assetDispose/approval',
      method: 'post',
      data: data
    })
}