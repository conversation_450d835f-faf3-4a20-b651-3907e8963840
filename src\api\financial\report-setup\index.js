import request from "@/utils/request";

//报表类型
// reportType({ path: '/financial/settings/reportSettings/reportType/' }) 参数为写死的
// export function reportType(params) {
//   return request({
//     url: '/system/dictionary/combotree',
//     method: "get",
//     params,
//     withCredentials: true,
//   })
// }

export function reportType() {
  return request({
    url: '/financial/report/getReportTypeTreeList',
    method: "post",
    withCredentials: true,
  })
}
export function selectCreateYear() {
  return request({
    url: '/financial/report/reportSetting/selectCreateYear',
    method: "post",
    withCredentials: true,
  })
}


//组织机构
export function getAuthAreaTree(params) {
  return request({
    url: '/financial/report/reportSetting/getAuthAreaTree',
    method: "get",
    params,
    withCredentials: true,
  })
}

//报表列表
export function page(data) {
  return request({
    url: '/financial/report/reportSetting/page',
    method: "post",
    data,
    withCredentials: true,
  })
}

//落款人列表
export function signer(id) {
  return request({
    url: `/financial/settings/reportSettings/signer/${id}`,
    method: "get",
    withCredentials: true,
  })
}

//新增报表名称
export function getReportNameList(data) {
  return request({
    url: '/financial/report/getReportNameList',
    method: "post",
    data,
    withCredentials: true,
  })
}

//保存
export function save(data) {
  return request({
    url: '/financial/report/reportSetting/save',
    method: "post",
    data,
    withCredentials: true,
  })
}

//编辑

export function getSigner(id) {
  return request({
    url: `/financial/report/reportSetting/get/${id}`,
    method: "get",
    withCredentials: true,
  })
}

//删除

export function deleteSigner(data) {
  return request({
    url: `/financial/report/reportSetting/delete`,
    method: "post",
    data,
    withCredentials: true,
  })
}

export function copyToNext(data) {
  return request({
    url: `/financial/report/reportSetting/copyToNext`,
    method: "post",
    data,
    withCredentials: true,
  })
}
