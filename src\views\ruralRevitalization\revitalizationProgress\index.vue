<template>
  <div>
    <fold-box right-title="乡村振兴进度">
      <template #right>
        <div class="right-box">
          <el-form inline :model="queryParams">
            <el-form-item :label="$t('付款账户类型')">
              <gever-select
                v-model="queryParams.accountTypes"
                clearable
                multiple
                class="w200"
                :options="accountTypeOptions"
              />
            </el-form-item>
            <el-form-item :label="$t('年份')" required>
              <el-date-picker
                v-model="queryParams.year"
                type="year"
                :placeholder="$t('选择年份')"
                class="w150"
                value-format="yyyy"
                :clearable="false"
                :default-value="new Date()"
              />
            </el-form-item>
            <el-form-item :label="$t('期间范围')" required>
              <el-select
                v-model="queryParams.startMonth"
                class="w100"
                :clearable="false"
              >
                <el-option
                  v-for="item in monthOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              {{ $t('至') }}
              <el-select
                v-model="queryParams.endMonth"
                class="w100"
                :clearable="false"
              >
                <el-option
                  v-for="item in monthOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
              <el-form-item :label="$t('是否乡村振兴')">
                <gever-select
                  v-model="queryParams.isSet"
                  clearable
                  class="w150"
                  :options="whetherOptions"
                />
              </el-form-item>
            <el-form-item>
              <el-button
                round
                plain
                type="primary"
                icon="el-icon-search"
                @click="getList"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <el-button
              plain
              round
              icon="el-icon-download"
              @click="handleExport"
            >
              {{ $t('导出') }}
            </el-button>
          </el-form>

          <gever-table
            ref="_geverTableRef"
            v-loading="tableLoading"
            :columns="tableColumns"
            :data="tableList"
            pagination
            :total="total"
            :pagi="queryParams"
            @p-current-change="getList"
            @size-change="getList"
          >
            <template #accountType="{ row }">
              {{ convertAccountType(row.accountType) }}
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
  </div>
</template>

<script>
import moment from 'moment'
import { getToken } from '@/utils/auth'
import { selectRevitalizationProgress } from '@/api/payment/approval.js'
import { comboJson } from '@/api/gever/common.js'

export default {
  name: 'RevitalizationProgress',
  data() {
    return {
      queryParams: {
        page: 1,
        rows: 20,
        queryType: 'area', // area: 地区, org: 单位
        accountTypes: ['2', '1', '4', '7', '8', '9'],
        year: moment().format('YYYY'),
        startMonth: '1',
        endMonth: moment().format('M')
      },
      accountTypeOptions: [
      ],
      monthOptions: Array.from({ length: 12 }, (_, i) => ({
        value: String(i + 1),
        label: this.$t(String(i + 1) + '月')
      })),
      tableLoading: false,
      tableList: [],
      total: 0,
      tableColumns: [
        { type: 'index', label: '序号', width: '80', fixed: 'left' },
        {
          label: '镇名称',
          prop: 'townName',
          minWidth: '120',
          showOverflowTooltip: true
        },
        {
          label: '村名称',
          prop: 'villagerName',
          minWidth: '120',
          showOverflowTooltip: true
        },
        {
          label: '单位名称',
          prop: 'orgName',
          minWidth: '250',
          showOverflowTooltip: true
        },
        {
          label: '付款账号',
          prop: 'accountNumber',
          minWidth: '180',
          showOverflowTooltip: true
        },
        {
          label: '账户类型',
          prop: 'accountType',
          width: '120',
          showOverflowTooltip: true
        },
        {
          label: '已支付笔数',
          prop: 'payCount',
          width: '120',
          align: 'right'
        },
        {
          label: '已设置乡村振兴笔数',
          prop: 'revitalizationCount',
          width: '150',
          align: 'right'
        },
        {
          label: '其中已关联出纳笔数',
          prop: 'cashierCount',
          width: '150',
          align: 'right'
        }
      ],
      whetherOptions: [
        { id: 0, text: '否' },
        { id: 1, text: '是' }
      ]
    }
  },
  computed: {
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  watch: {
    paymentLogin: {
      /** 监听到变化后就触发一次handler 相当与created */
      handler(val) {
        if (val.type !== 'Organization') {
          if (val.id) {
            this.queryParams.areaCode = val.code
            this.queryParams.orgCode = ''
            this.getList()
          }
        } else {
          if (val.id) {
            this.queryParams.areaCode = ''
            this.queryParams.orgCode = val.code
            this.getList()
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // 设置默认查询月份范围
    this.initDefaultMonths()
    this.loadAllJson()
  },
  methods: {
    getToken,
    initDefaultMonths() {
      const currentMonth = moment().format('M')
      this.queryParams.startMonth = '1'
      this.queryParams.endMonth = currentMonth
    },
    handleQueryTypeChange() {
      // 切换查询类型时重置相关参数
      this.getList()
    },
    async getList() {
      if (!this.queryParams.year || !this.queryParams.startMonth || !this.queryParams.endMonth) {
        return this.$message.warning(this.$t('请选择完整的查询期间!'))
      }

      if (Number(this.queryParams.endMonth) < Number(this.queryParams.startMonth)) {
        return this.$message.warning(this.$t('结束月份不能小于开始月份!'))
      }

      this.tableLoading = true
      try {
        const { data } = await selectRevitalizationProgress(this.queryParams)
        this.tableList = data.records
        this.total = data.total
      } finally {
        this.tableLoading = false
      }
    },
    handleExport() {
      if (this.paymentLogin.areaLevel < 3) {
        return this.$message.warning(
          this.$t('地区只能选择区、镇、村或机构进行导出，不能选择市或区')
        )
      }
      let str = ''
      const obj = { ...this.queryParams }
      delete obj.page
      delete obj.rows
      for (var i = 0; i < Object.keys(obj).length; i++) {
        if (str) {
          str += '&'
        }
        str += Object.keys(obj)[i] + '=' + Object.values(obj)[i]
      }
      const url =
        process.env.VUE_APP_BASE_API +
        `/payment/approval/approvalReceiver/exportRevitalizationProgress?` +
        str +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.location.href = url
    },
    getOrgOptionsForBatchSet() {
      this.curSetRevitalizationOrgOptions = []
      if (this.paymentLogin.type == 'Area') {
        this.getOrgOptionsForSet(this.paymentLogin.code)
      } else {
        this.getOrgOptionsForSet(this.paymentLogin.areaCode)
      }
    },
    loadAllJson() {
      comboJson({ path: '/financial/bank_account_type/' }).then((res) => {
        this.accountTypeOptions.push(...res.data)
      })
    },
    convertAccountType(val) {
      const accountType = this.accountTypeOptions.find(
        (item) => item.id == val
      )
      return accountType ? accountType.text : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100px;
}
.w150 {
  width: 150px;
}
</style>
