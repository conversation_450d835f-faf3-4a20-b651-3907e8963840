import request from '@/utils/request'

/**
 * 凭证查询
 */
export function accountingVoucherQuery(data) {
  return request({
    url: '/financial/accounting/accountingVoucherQuery/pageVoucherQuery',
    method: 'post',
    data: data
  })
}

/**
 * 凭证id查询
 */
export function voucherIdQuery(data) {
  return request({
    url: '/financial/accounting/accountingVoucherQuery/voucherIdQuery',
    method: 'post',
    data: data
  })
}

/**
 * 凭证id查询
 */
export function loadPrintData(params) {
  return request({
    url: '/financial/accounting/accountingVoucher/loadPrintData',
    method: 'post',
    data: params
  })
}

/**
 * 获取打印设置
 */
export function loadPrintSettings(params) {
  return request({
    url: '/financial/accounting/accountingVoucher/accountingVoucherPrintSettings/loadByBooksId',
    method: 'get',
    params: params
  })
}

/**
 * 保存打印设置
 */
export function savePrintSettings(data) {
  return request({
    url: '/financial/accounting/accountingVoucher/accountingVoucherPrintSettings/save',
    method: 'post',
    data: data
  })
}

/**
 * 获取url配置
 */
export function getUrlCfg() {
  return request({
    url: '/filesystem/fileInfo/getUrlCfg',
    method: 'get'
  })
}

/**
 * 保存导入的打印模板
 */
export function saveImportTemp(data) {
  return request({
    url: '/financial/accounting/accountingVoucher/accountingVoucherPrintSettings/saveImportTemp',
    method: 'post',
    data: data
  })
}

/**
 * 删除上传的打印模板
 */
export function deleteFile(data) {
  return request({
    url: '/filesystem/fileInfo/deleteFile',
    method: 'post',
    data: data
  })
}

/**
 * 根据凭证id查询凭证来源基本信息
 */
export function getBasicSourceInfo(id) {
  return request({
    url: '/financial/accounting/accountingBusinessRelation/loadView/' + id,
    method: 'get'
  })
}
export function getCashierSourceInfo(params) {
  return request({
    url: '/financial/business/base/loadView' ,
    method: 'get',
    params: params
  })
}

/**
 * 根据凭证id以及basicInfo中的url查询凭证来源详细信息
 */
export function getDetailSourceInfo(url, data) {
  return request({
    url: url + '/loadView',
    method: 'post',
    data: data
  })
}

/**
 * 查看凭证中的流水详情
 */
export function loadCashierInfo(id) {
  return request({
    url: '/financial/cashier/cashierVoucher/load/' + id,
    method: 'get'

  })
}

/**
 * 获取互转科目
 */
export function loadTransferItemCombo(params) {
  return request({
    url: '/financial/cashier/cashierVoucher/transferItemComboTree',
    method: 'post',
    params: params
  })
}

/**
 * 获取资金常用信息
 */
export function loadCashierCommonInfo(params) {
  return request({
    url: '/financial/cashier/cashierCommonInfo/combotree',
    method: 'post',
    params: params
  })
}

/**
 * 获取支出类型
 */
export function loadCashierExpenseType(params) {
  return request({
    url: '/financial/books/basedata/cashierexpensetype/combotree',
    method: 'get',
    params: params
  })
}

/**
 * 获取收入类型
 */
export function loadCashierIncomeType(params) {
  return request({
    url: '/financial/books/basedata/cashierincometype/combotree',
    method: 'get',
    params: params
  })
}

/**
 * 获取结算方式
 */
export function loadCashierSettlementType(params) {
  console.log(params);
  return request({
    url: '/financial/books/basedata/cashiersettlementtype/combotree',
    method: 'get',
    params: params
  })
}

