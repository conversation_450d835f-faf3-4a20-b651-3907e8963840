<template>
  <div>
    <fold-box right-title="待支付">
      <template #right>
        <div class="right-box">
          <el-form ref="_formRef" inline :model="queryParams">
            <div style="display: flex; justify-content: space-between">
              <div>
                <el-form-item :label="$t('支付单号')" prop="paymentCode">
                  <el-input v-model="queryParams.paymentCode" class="w150" />
                </el-form-item>
                <el-form-item
                  :label="$t('申请单编号')"
                  prop="approvalApplicationSerialNumber"
                >
                  <el-input
                    v-model="queryParams.approvalApplicationSerialNumber"
                    class="w150"
                  />
                </el-form-item>
                <el-form-item :label="$t('资金用途')" prop="fundsUsed">
                  <el-input v-model="queryParams.fundsUsed" class="w150" />
                </el-form-item>
                <el-form-item :label="$t('金额(元)')">
                  <el-input-number
                    v-model="queryParams.startAmount"
                    :precision="2"
                    :controls="false"
                    class="w100"
                  />
                  {{ $t('至') }}
                  <el-input-number
                    v-model="queryParams.endAmount"
                    :precision="2"
                    :controls="false"
                    class="w100"
                  />
                </el-form-item>
                <el-form-item :label="$t('支付状态')" prop="payStatus">
                  <el-select
                    v-model="queryParams.payStatus"
                    clearable
                    class="w150"
                  >
                    <el-option
                      v-for="item in paymentStateOptions"
                      :key="item.id"
                      :label="item.text"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('结算方式')" prop="orgType">
                  <el-select v-model="queryParams.settlementMethod" clearable class="w150">
                    <el-option
                      v-for="item in mapOptins.settlementMethod"
                      :key="item.id"
                      :label="item.settleName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('结算号')">
                  <el-input v-model="queryParams.settlementNo" />
                </el-form-item>
                <el-form-item :label="$t('付款账号')">
                  <el-input v-model="queryParams.payAccountNumber" />
                </el-form-item>
                <el-form-item>
                  <el-button
                    v-hasPermi="'financial.payment.manager.payOrder.list'"
                    type="primary"
                    round
                    icon="el-icon-search"
                    @click="loadList"
                  >
                    {{ $t('搜索') }}
                  </el-button>
                </el-form-item>
              </div>
              <div>
                <el-form-item>
                  <el-button
                    v-hasPermi="'financial.payment.manager.payOrder.payLog'"
                    type="primary"
                    round
                    @click="handleViewLog"
                  >
                    {{ $t('日志') }}
                  </el-button>
                </el-form-item>
              </div>
            </div>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            v-loading="loading"
            :columns="tableColumns"
            :data="table.dataList"
            :total="table.total"
            :pagi="queryParams"
            :map-optins="mapOptins"
            @p-current-change="loadList"
            @size-change="loadList"
          >
            <template #countOrName="{ row }">
              <span v-if="orderType(row)">{{ row.countOrName }}</span>
              <el-link
                v-else
                :underline="false"
                type="primary"
                @click="handleView(row)"
              >
                {{ row.countOrName + $t('个收款方') }}
              </el-link>
            </template>
            <template #returnFlag="{ row }">
              <span>{{ row.returnFlag ? '是' : '否' }}</span>
            </template>
            <template #businessType="{ row }">
              <span>{{ getText(row.businessType, 'businessType') }}</span>
            </template>
            <template #approvalApplicationSerialNumber="{ row }">
              <span class="name-detailSpan" @click="handleViewApplicationForm(row)">{{ row.approvalApplicationSerialNumber }}</span>
            </template>
            <template #operation="{ row }">
              <el-button
                v-for="(l, i) in getButtons(row)['buttons1']"
                :key="i"
                v-hasPermi="l.hasPermi"
                type="text"
                @click="l.callback(row)"
              >
                {{ $t(l.text) }}
              </el-button>
              <el-dropdown
                v-if="getButtons(row)['buttons2'].length"
                trigger="click"
                style="margin-left: 5px"
              >
                <span class="el-dropdown-link">
                  <i class="el-icon-more" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="(l, i) in getButtons(row)['buttons2']"
                    :key="i"
                    v-hasPermi="l.hasPermi"
                    @click.native="l.callback(row)"
                  >
                    {{ $t(l.text) }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <template #createTime="{ row }">
              {{ settingDate(row.createTime) }}
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <el-dialog :title="$t('查看支付申请单日志')" :visible.sync="logVisible">
      <order-log v-if="logVisible" :order-id="currentOrderId" />
    </el-dialog>

    <public-drawer
      :visible.sync="drawerVisible"
      :title="$t(drawerTitle)"
      :size="70"
    >
      <order-info
        v-if="drawerVisible"
        :business-type="businessType"
        :map-optins="mapOptins"
        :order-id="currentOrderId"
      />
    </public-drawer>

    <el-dialog
      :title="$t('终止支付')"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <el-form>
        <el-form-item :label="$t('终止原因')" required>
          <el-input
            v-model="payResult"
            type="textarea"
            resize="none"
            :maxlength="250"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('关 闭') }}</el-button>
        <el-button type="primary" @click="handleConfirmTerminate">
          {{ $t('保 存') }}
        </el-button>
      </span>
    </el-dialog>

    <PaymentAccount
      v-if="paymentAccountVisible"
      :visible="paymentAccountVisible"
      :order-id="currentOrderId"
      @handleAccountSelect="handleAccountSelect"
      @close="paymentAccountVisible = false"
    />

    <sms
      v-if="smsVisible"
      ref="_smsRef"
      :payment-title="paymentTitle"
      :sms-visible="smsVisible"
      :callback="callback"
      :account-id="selectedAccountId"
      :order-id="currentOrderId"
      :bank-type="bankType"
      @closeSms="closeSms"
      @pay="pay"
      @repay="repay"
      @repay2="repay2"
      @changeRetryPayeeFun="changeRetryPayeeFun"
    />
    <!-- @directPay="directPay" -->

    <public-drawer
      :visible.sync="paidDrawerVisible"
      :title="paymentTitle"
      :size="70"
      :buttons="isPayable ? (isRepay ? buttons2 : buttons1) : []"
    >
      <OrderToPaid
        v-if="paidDrawerVisible"
        ref="_orderRef"
        :payment-title="paymentTitle"
        :allow-modify="allowModify"
        :order-id="currentOrderId"
        :selected-account-id="selectedAccountId"
        :bank-type="handleBankType"
        :business-type="businessType"
        @changeIsPayable="changeIsPayable"
        @getOrder="getOrder"
        @flagChange="changedFlag = true"
      />
    </public-drawer>
    <!-- 办理支付 -->
    <PayDrawer
      v-if="payVisible"
      ref="_payDrawerRef"
      :is-pay="isPay"
      :current-order-id="currentOrderId"
      :map-optins="mapOptins"
      :prop-visible.sync="payVisible"
      @successFn="loadList"
      @handleConfirmPay="handleConfirmPay"
      @confirmThePaymentResult="confirmThePaymentResult"
    />
    <!-- 确认支付结果 -->
    <ComfirmResDrawer
      v-if="comfirmResVisible"
      :current-order-id="currentOrderId"
      :map-optins="mapOptins"
      :prop-visible.sync="comfirmResVisible"
      @successFn="loadList"
    />
    <!--    支付申请查看-->
    <public-drawer
      ref="_appDrawerRef"
      v-loading="loading"
      :visible.sync="applicationDrawerVisible"
      :title="'支出申请查看'"
      :size="70"
      :is-before-close="false"
      @close="handleDrawerClose"
    >
      <application
        v-if="applicationDrawerVisible"
        :id="currentApplication.id || ''"
        ref="_appRef"
        :application="currentApplication"
        :receiver="receiverList"
        :load-type="'view'"
        :show-process-btn="true"
        :visible.sync="applicationDrawerVisible"
      />
    </public-drawer>
  </div>
</template>

<script>
import {
  loadPayOrderList,
  terminatePayOrder,
  pay,
  repay2,
  repay,
  ignoreRepay,
  querySMS,
  tempSaveRepay,
  changeRetryPayee,
  queryPaymentResult
} from '@/api/payment/capital.js'
// import { loadSettingByCode } from '@/api/system/setting.js'
import OrderInfo from '../order/index.vue'
import OrderToPaid from '../order/index1.vue'
import OrderLog from '../orderLog/index.vue'
import PaymentAccount from './choosePaymentAccount.vue'
import PayDrawer from './components/payDrawer.vue'
import ComfirmResDrawer from './components/comfirmResDrawer.vue'
import Sms from './sms.vue'
import { Loading } from 'element-ui'
import { getToken } from '@/utils/auth'
// import { createNamespacedHelpers } from 'vuex'
import { comboJson } from '@/api/gever/common.js'
import { page as billingMethodPage } from '@/api/payment/billingMethod'
import { listBankEntrustType } from '../../../../api/payment/capital'
import Application from '@/views/payment/approval/approvalApplication/applicationInfo.vue'
import { getApplication } from '@/api/payment/approval.js'

// const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'OrderToBePaid',
  components: {
    OrderInfo,
    OrderToPaid,
    OrderLog,
    PaymentAccount,
    Sms,
    PayDrawer,
    ComfirmResDrawer,
    Application
  },
  data() {
    return {
      currentOrderIdtemp: {},
      bankType: '',
      isPay: true,
      mapOptins: {
        businessType: [],
        settlementMethod: [{ id: '', settleName: this.$t('全部') }],
        payMethod: [],
        interOrOutOptions: [
          { id: 1, text: '同行' },
          { id: 2, text: '跨行' }
        ],
        entrustTypeOptions: []
      },
      loading: false,
      payVisible: false,
      comfirmResVisible: false,
      order: {},
      queryParams: {
        toBePaid: 'y',
        paymentCode: '',
        fundsUsed: '',
        startAmount: undefined,
        endAmount: undefined,
        payStatus: ' ',
        approvalApplicationSerialNumber: '',
        page: 1,
        rows: 20,
        region: '',
        settlementMethod: ''
      },
      table: {
        total: 0,
        dataList: []
      },
      paymentStateOptions: [
        { id: ' ', text: this.$t('全部') },
        { id: '0', text: this.$t('未支付') },
        { id: '1', text: this.$t('支付中') },
        // { id: '2', text: this.$t('支付成功') },
        { id: '3', text: this.$t('部分成功') },
        { id: '-1', text: this.$t('支付失败') },
        { id: '-9', text: this.$t('已退回') }
        // { id: '-10', text: this.$t('交易终止') },
        // { id: '99', text: this.$t('状态未知') }
      ],
      tableColumns: [
        { type: 'selection', width: '50', fixed: 'left' },
        { label: '序号', type: 'index', index: this.indexMethod, width: '80', fixed: 'left' },
        { label: '支付单号', prop: 'paymentCode', minWidth: '120' },
        {
          label: '申请单编号',
          prop: 'approvalApplicationSerialNumber',
          minWidth: '120'
        },
        { label: '申请单位', prop: 'orgName', minWidth: '250' },
        {
          label: '申请类型',
          prop: 'applicationType',
          minWidth: '100',
          convert: {
            options: [
              { id: 0, text: '常规用款' },
              { id: 1, text: '收支相抵' },
              { id: 2, text: '冲减收入' },
              { id: 3, text: '非预算项目支出' },
              { id: 4, text: '银行代扣' },
              { id: 5, text: '扣减预算' },
              { id: 6, text: '非预算收支相抵' }
            ]
          }
        },
        { label: '资金用途', prop: 'fundsUsed', minWidth: '200' },
        { label: '金额（元）', prop: 'payAmount', minWidth: '120' },
        { label: '结算号', prop: 'settlementNoName', minWidth: '120' },
        { label: '支付申请收款方账户', slotName: 'countOrName', width: '200' },
        { label: '申请人', prop: 'applyUserName', width: '120' },
        { label: '申请时间', prop: 'createTime', width: '200' },
        { label: '支付发起人/终止人', prop: 'payUserName', minWidth: '200' },
        {
          label: '实际支付时间/终止时间',
          prop: 'actualPayTime',
          minWidth: '200'
        },
        { label: '支付状态', prop: 'payStatus', width: '120' },
        { label: '支付结果说明', prop: 'payResult', width: '120' },
        { label: '申请单来源', prop: 'sourceText', width: '120' },
        { label: '结算方式', prop: 'settlementMethodName', width: '120' },
        { label: '业务类型', prop: 'businessType', width: '120' },
        { label: '是否退款', prop: 'returnFlag', width: '120' },
        { label: '操作', slotName: 'operation', width: '250', fixed: 'right' }
      ],
      currentOrderId: '',
      drawerVisible: false,
      drawerTitle: '',
      dialogVisible: false,
      payResult: '',
      logVisible: false,

      paidDrawerVisible: false,
      paymentAccountVisible: false,
      selectedAccountId: null,
      paymentTitle: '',
      allowModify: false, // 重新支付时允许修改收款人账号、收款人开户行
      // 用于判断是否显示按钮
      isPayable: false,
      // 用于判显示哪种按钮
      isRepay: false,
      buttons1: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: () => {
            this.paidDrawerVisible = false
          }
        },
        {
          type: 'primary',
          text: this.$t('支 付'),
          buttonStatus: true,
          callback: this.handleConfirmPay
        }
      ],
      buttons2: [
        {
          type: '',
          text: this.$t('暂 存'),
          buttonStatus: true,
          callback: this.handleRepayTempSave
        },
        {
          type: 'primary',
          text: this.$t('支 付'),
          buttonStatus: true,
          callback: this.handleConfirmPay
        }
      ],

      smsVisible: false,
      callback: '',

      smsConfig: '',

      changedFlag: false, // repay2中的收款人信息是否修改

      loadingInstance: null,
      businessType: 1,
      applicationDrawerVisible: false,
      currentApplication: {},
      receiverList: []
    }
  },
  computed: {
    orderType() {
      return function (row) {
        if (row.orderType === '单笔支付' || isNaN(Number(row.countOrName))) {
          return true
        } else {
          return false
        }
      }
    },
    // ...mapState(['paymentLogin']),
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  watch: {
    paymentLogin: {
      /** 监听到变化后就触发一次handler 相当与created */
      immediate: true,
      handler(val) {
        if (val) {
          this.queryParams.region = val.code || ''
          this.loadList()
        }
      },
      deep: true
    }
  },
  created() {
    // this.loadList()
    this.loadAllComboJson()
    this.payResult = ''
  },
  methods: {
    loadAllComboJson() {
      comboJson({ path: '/payment/business_type/' }).then((res) => {
        this.mapOptins.businessType = res.data
      })
      comboJson({ path: '/payment/payMethod/' }).then((res) => {
        this.mapOptins.payMethod = res.data
      })
      // 结算方式
      billingMethodPage({ page: 1, rows: 100, parentId: '0' }).then((res) => {
        this.mapOptins.settlementMethod = res.data.rows
      })
    },
    getText(id, optionsData) {
      const cur = this.mapOptins[optionsData].find((item) => item.id == id)
      return cur?.text ?? ''
    },
    handleBankType(temp) {
      this.bankType = temp
    },
    indexMethod(index) {
      return (this.queryParams.page - 1) * this.queryParams.rows + index + 1
    },
    // eslint-disable-next-line complexity
    getButtons(row) {
      const arr = [
        {
          text: '查看',
          hasPermi: 'financial.payment.manager.payOrder.view',
          callback: (v) => this.handleView(v),
          show: true
        },
        // {
        //   text: '支付',
        //   hasPermi: 'financial.payment.manager.payOrder.pay',
        //   callback: (v) => this.handleRepay2(v),
        //   show: row.payStatusCode === 0 && row.retryType === 2
        // },
        // {
        //   text: '支付',
        //   hasPermi: 'financial.payment.manager.payOrder.pay',
        //   callback: (v) => this.handlePay(v),
        //   show: row.payStatusCode === 0 && row.retryType !== 2
        // },
        {
          text: '办理支付',
          hasPermi: 'financial.payment.manager.payOrder.pay',
          // callback: (v) => this.confirmThePaymentResult(v),
          callback: (v) => this.handlePayment(v, true),
          show: row.payStatusCode === 0 && row.tempFlag
        },
        {
          text: '终止支付',
          hasPermi: 'financial.payment.manager.payOrder.terminate',
          callback: (v) => this.handleTerminate(v),
          show: row.payStatusCode === 0
        },
        // {
        //   text: '重新支付',
        //   hasPermi: 'financial.payment.manager.payOrder.pay',
        //   callback: (v) => this.handleRepay(v),
        //   // show: (row.payStatusCode === -9 || row.payStatusCode === -1 || row.payStatusCode === 3) && row.retryType === 0
        //   show:
        //     row.businessType === 1 &&
        //     [-9, -1, 3].includes(row.payStatusCode) &&
        //     row.retryType === 0
        // },
        {
          text: '重新开票',
          hasPermi: 'financial.payment.manager.payOrder.confirmPayment',
          callback: (v) => this.handlePayment(v),
          show:
            [
              '79baddcb5c5d11eebaa00242ac130002',
              '9797d83952d711eea8160242ac120002',
              '979ad04c52d711eea8160242ac120002'
            ].includes(row.settlementMethod) &&
            row.payStatusCode === 1 &&
            row.businessType !== 1
        },
        {
          text: '确认支付结果',
          hasPermi: 'financial.payment.manager.payOrder.confirmPayment',
          callback: (v) => this.confirmThePaymentResult(v),
          show: row.payStatusCode === 1 && row.businessType !== 1
        },
/*        {
          text: '忽略',
          hasPermi: '',
          callback: (v) => this.handleIgnoreRepay(v),
          // show: (row.payStatusCode === -9 || row.payStatusCode === -1 || row.payStatusCode === 3) && row.retryType === 0
          show:
            row.businessType === 1 &&
            [-9, -1, 3].includes(row.payStatusCode) &&
            row.retryType === 0
        },*/
        {
          text: row.payMethod == 2 ? '导出委托明细' : '导出明细',
          hasPermi: 'financial.payment.manager.payOrder.export',
          callback: (v) => this.handleExportDetail(v),
          show:
            row.orderType !== '单笔支付' &&
            (row.payStatusCode === 1 ||
              row.payStatusCode === 2 ||
              row.payStatusCode === 3 ||
              row.payStatusCode === -1)
        }
      ]
      const buttons = arr.filter((l) => l.show)
      const result = {
        buttons1: [],
        buttons2: []
      }
      if (buttons.length > 3) {
        buttons.forEach((l, i) => {
          if (i <= 2) {
            result.buttons1.push(l)
          } else {
            result.buttons2.push(l)
          }
        })
      } else {
        result.buttons1 = buttons
      }
      return result
    },
    // 时间截取
    settingDate(value) {
      return value.substring(0, 10)
    },
    getToken,
    getOrder(data) {
      this.order = data
      console.log(data, 'data')
    },
    loadList() {
      this.loading = true
      const params = JSON.parse(JSON.stringify(this.queryParams))
      for (const key in params) {
        if (typeof params[key] === 'string') {
          params[key] = params[key].trim()
        }
      }
      loadPayOrderList(params)
        .then((res) => {
          this.table.dataList = res.data.rows
          this.table.total = res.data.total
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleViewLog() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (selectedRows.length !== 1) {
        return this.$message.warning(this.$t('请先选择其中一条记录查看'))
      }
      this.currentOrderId = selectedRows[0].id
      this.logVisible = true
    },
    handleView(row) {
      this.currentOrderId = row.id
      this.drawerTitle = '查看支付申请单'
      this.drawerVisible = true
    },
    closeSms(val = false) {
      this.smsVisible = false
      // if (val) {
      //   this.paidDrawerVisible = false
      this.loadList()
      // }
    },
    // 发送按钮操作
    changeRetryPayeeFun(callback) {
      console.log('changeRetryPayeeFun')
      const tableList =
        this.paymentTitle === '办理支付'
          ? this.$refs['_payDrawerRef'].recipientList
          : this.$refs['_orderRef'].table.dataList
      if (this.changedFlag && !this.isRepay) {
        // 判断数据是否被修改过，修改过则提交保存修改
        const temp = {}
        tableList.forEach((item) => {
          temp[item.id] = item
        })
        changeRetryPayee(this.currentOrderId, temp).then((res) => {
          if (res.returnCode == '0') {
            this.changedFlag = false
          }
        })
      }
      if (this.isRepay) {
        const temp = {}
        tableList.forEach((item) => {
          temp[item.id] = item
        })
        this.currentOrderIdtemp = temp
        callback(this.currentOrderId, temp)
        // this.handleRepayTempSave(true)
      }
    },
    repay2(val) {
      if (!this.validateReceiver()) return // 收款人信息校验
      if (this.changedFlag) {
        // 判断数据是否被修改过，修改过则提交保存修改
        const temp = {}
        const tableList =
          this.paymentTitle === '办理支付'
            ? this.$refs['_payDrawerRef'].recipientList
            : this.$refs['_orderRef'].table.dataList
        tableList.forEach((item) => {
          temp[item.id] = item
        })
        if (this.bankType == '305') {
          this.pay(val)
        } else {
          changeRetryPayee(this.currentOrderId, temp).then((res) => {
            if (res.returnCode == '0') {
              this.changedFlag = false
              this.pay(val)
            }
          })
        }
      } else {
        this.pay(val)
      }
    },
    // 暂存
    tempSave2() {
      if (!this.validateReceiver()) return // 收款人信息校验
      if (this.changedFlag) {
        // 判断数据是否被修改过，修改过则提交保存修改
        const temp = {}
        const tableList =
          this.paymentTitle === '办理支付'
            ? this.$refs['_payDrawerRef'].recipientList
            : this.$refs['_orderRef'].table.dataList
        tableList.forEach((item) => {
          if (!item.parentKey) {
            temp[item.id] = item
          }
        })
        changeRetryPayee(this.currentOrderId, temp).then((res) => {
          if (res.returnCode == '0') {
            this.changedFlag = false
            this.paidDrawerVisible = false
            this.$message.success(this.$t('保存成功'))
            this.loadList()
          } else {
            this.$message.error(res.message)
          }
        })
      } else {
        this.paidDrawerVisible = false
        this.$message.success(this.$t('保存成功'))
        this.loadList()
      }
    },
    handlePay(row) {
      this.isRepay = false
      this.allowModify = false
      this.paymentTitle = this.$t('支付')
      this.callback = 'pay'
      this.currentOrderId = row.id
      this.paymentAccountVisible = true
    },
    handleRepay(row) {
      this.isRepay = true
      this.allowModify = true
      this.paymentTitle = this.$t('重新支付')
      this.callback = 'repay'
      this.tempSaveCallBack = 'tempSave'
      this.currentOrderId = row.id
      this.selectedAccountId = row.payAccountId
      this.handleAccountSelect(row.payAccountId)
      this.businessType = row.businessType
      console.log(this.isPayable, this.isRepay, 999)
      if (row.businessType == 1) {
        this.buttons2.splice(0, 1, {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: () => {
            this.paidDrawerVisible = false
          }
        })
      }
      if (row.businessType != 1) {
        this.buttons2.splice(0, 1, {
          type: '',
          text: this.$t('暂 存'),
          buttonStatus: true,
          callback: this.handleRepayTempSave
        })
      }
      // this.paymentAccountVisible = true
    },
    handleRepay2(row) {
      console.log('handleRepay2')
      this.isRepay = false
      this.allowModify = true
      this.paymentTitle = this.$t('支付')
      this.callback = 'repay2'
      this.tempSaveCallBack = 'tempSave2'
      this.currentOrderId = row.id
      this.paymentAccountVisible = true
    },
    repay(val) {
      // 重新支付
      if (!this.validateReceiver()) return
      const temp = {}
      const tableList =
        this.paymentTitle === '办理支付'
          ? this.$refs['_payDrawerRef'].recipientList
          : this.$refs['_orderRef'].table.dataList
      tableList.forEach((item) => {
        if (!item.parentKey) {
          temp[item.id] = item
        }
      })
      // ljp
      if (this.bankType === '305') {
        this.pay(val)
      } else {
        tempSaveRepay(this.currentOrderId, temp).then((res) => {
          if (res.returnCode == '0') {
            this.currentOrderId = res.data
            this.pay(val)
          } else {
            this.$message.error(res.message)
          }
        })
      }
      this.loadList()
    },
    tempSave(status) {
      if (!this.validateReceiver()) return // 收款人信息校验
      if (this.changedFlag) {
        // 判断数据是否被修改过，修改过则提交保存修改
        const temp = {}
        const tableList =
          this.paymentTitle === '办理支付'
            ? this.$refs['_payDrawerRef'].recipientList
            : this.$refs['_orderRef'].table.dataList
        tableList.forEach((item) => {
          if (!item.parentKey) {
            temp[item.id] = item
          }
        })
        this.currentOrderIdtemp = temp
        tempSaveRepay(this.currentOrderId, temp).then((res) => {
          if (res.returnCode == '0') {
            this.changedFlag = false
            this.paidDrawerVisible = false
            this.loadList()
            if (!status) {
              this.$message.success(this.$t('保存成功'))
            }
          } else {
            this.$message.error(res.message)
          }
        })
      } else {
        this.paidDrawerVisible = false
        if (!status) {
          this.$message.success(this.$t('保存成功'))
        }
        this.loadList()
      }
    },
    // 选择确认支付支付账户
    handleAccountSelect(accountId) {
      this.paymentAccountVisible = false
      this.selectedAccountId = accountId
      this.paidDrawerVisible = true
    },
    handleConfirmPay(payAccountId, callback) {
      if (payAccountId) {
        this.selectedAccountId = payAccountId
      }
      if (callback) {
        this.callback = callback
      }
      querySMS(payAccountId || this.selectedAccountId).then((res) => {
        if (res.data == '1') {
          this.smsVisible = true
        } else {
          this[this.callback]()
        }
      })
    },
    handleRepayTempSave(status = false) {
      console.log('handleRepayTempSave', status)
      this[this.tempSaveCallBack](status)
    },
    handleTerminate(row) {
      this.currentOrderId = row.id
      this.dialogVisible = true
    },
    // 办理支付
    handlePayment(row, isPay = false) {
      this.isPay = isPay
      this.paymentTitle = isPay ? '办理支付' : '发送银行'
      this.currentOrderId = row.id
      this.payVisible = true
    },
    // 确认支付结果
    confirmThePaymentResult(row) {
      this.paymentTitle = '确认支付结果'
      this.currentOrderId = row.id
      this.comfirmResVisible = true
    },
    // eslint-disable-next-line complexity
    validateReceiver() {
      let result = true
      const tableList =
        this.paymentTitle === '办理支付'
          ? this.$refs['_payDrawerRef'].recipientList
          : this.$refs['_orderRef'].table.dataList
      const data = JSON.parse(JSON.stringify(tableList))
      const message = {}
      for (const i in data) {
        const dataIndex = parseInt(i) + 1
        message[dataIndex] = []
        for (let j = 0; j < data.length; j++) {
          if (
            !data[i].parentKey &&
            j != i &&
            data[i].account &&
            data[i].account == data[j].account
          ) {
            message[dataIndex].push('收款方银行账号不能重复')
          }
        }
        if (!data[i].parentKey && !data[i].account) {
          message[dataIndex].push('收款方银行账号不能为空')
        }
        if (
          !data[i].parentKey &&
          (!data[i].accountBank || !data[i].accountBankNumber)
        ) {
          message[dataIndex].push('收款方开户银行不能为空')
        }

        data[i].key = new Date().getTime() + i
      }
      if (JSON.stringify(message) !== '{}') {
        let msg = ''
        for (const key in message) {
          if (message[key].length) {
            msg += message[key].reduce((total, curr) => {
              if (curr) {
                const temp = `第${key}条收款人信息, ${curr}<br />`
                return total + temp
              } else {
                return total
              }
            }, '')
          }
        }
        if (msg) {
          result = false
          this.$confirm(msg, {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t('确定'),
            showClose: false,
            showCancelButton: false,
            customClass: 'receiver-alert',
            type: 'warning'
          })
        } else {
          result = true
        }
      } else {
        result = true
      }
      return result
    },
    handleIgnoreRepay(row) {
      this.$confirm(this.$t('忽略后，无法再发起重新支付，是否确定忽略?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          ignoreRepay(row.id).then((res) => {
            this.$message.success(res.message)
            this.loadList()
          })
        })
        .catch(() => {})
    },
    handleExportDetail(row) {
      const url =
        process.env.VUE_APP_BASE_API +
        '/payment/manager/payOrder/exportDetail/' +
        row.id +
        '?access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.open(url)
    },
    handleConfirmTerminate() {
      // 终止支付
      if (!this.payResult) {
        return this.$message.warning(this.$t('请输入终止原因'))
      }
      this.$confirm(this.$t('终止支付后，无法再发起支付，是否确定终止?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          terminatePayOrder(this.currentOrderId, {
            payResult: this.payResult
          }).then((res) => {
            this.$message.success(res.message)
            this.dialogVisible = false
            this.payResult = ''
            this.loadList()
          })
        })
        .catch(() => {})
    },
    getAmount(val) {
      if (val === undefined) return ''
      return Number(val).toFixed(2)
    },
    changeIsPayable(val) {
      this.isPayable = val
    },
    pay(val) {
      // 支付
      const params = {
        id: val?.id ? val?.id : this.currentOrderId,
        accountId: this.selectedAccountId,
        smsId: val?.smsId || '',
        smsCode: val?.smsCode || ''
      }
      this.loadingInstance = Loading.service({
        fullscreen: true,
        text: '支付中，正在查询结果...'
      })
      pay(params)
        .then((res) => {
          if (res.data === '支付成功' || res.data === '支付失败') {
            this.$message({
              type: res.data === '支付成功' ? 'success' : 'warning',
              message: res.data
            })
            this.smsVisible = false
            this.paidDrawerVisible = false
            this.$nextTick(() => {
              this.payVisible = false
              this.loadingInstance.close()
            })
            this.loadList()
          } else {
            this.smsVisible = false
            this.paidDrawerVisible = false
            this.$nextTick(() => {
              this.payVisible = false
              this.loadingInstance.close()
            })
            this.queryResult(0, 500, val?.id ? val?.id : this.currentOrderId)
          }
        })
        .catch((error) => {
          this.smsVisible = false
          // this.$alert(error.message, '', {
          //   confirmButtonText: '确定',
          //   callback: action => {
          //   }
          // })
          this.$nextTick(() => {
            this.loadingInstance.close()
          })
          console.log(error, 'error')
        })
      // .finally(() => {
      //   this.$nextTick(() => {
      //     this.loadingInstance.close()
      //   })
      // })
    },
    queryResult(count, delay, id) {
      queryPaymentResult(id)
        .then((res) => {
          if (res.returnCode === '0') {
            if (res.data === '支付成功') {
              this.$message.success(res.data)
              this.$nextTick(() => {
                this.loadingInstance.close()
              })
              this.smsVisible = false
              this.paidDrawerVisible = false
              this.loadList()
            } else if (res.data === '部分成功') {
              this.$message.warning('支付存在部分失败，请查看具体支付明细')
              this.$nextTick(() => {
                this.loadingInstance.close()
              })
              this.smsVisible = false
              this.paidDrawerVisible = false
              this.loadList()
            } else if (res.data === '支付失败') {
              this.$message.warning(res.data)
              this.$nextTick(() => {
                this.loadingInstance.close()
              })
              this.smsVisible = false
              this.paidDrawerVisible = false
              this.loadList()
            } else if (res.data === '支付中') {
              this.$message.warning(res.message)
              this.$nextTick(() => {
                this.loadingInstance.close()
              })
              this.smsVisible = false
              this.paidDrawerVisible = false
              this.loadList()
            } else {
              if (count <= 0) {
                this.$message.warning('未能查询到支付结果，请留意支付结果')
                this.$nextTick(() => {
                  this.loadingInstance.close()
                })
                this.smsVisible = false
                this.paidDrawerVisible = false
                this.loadList()
              } else {
                const nextDelay = delay * 2
                setTimeout(() => {
                  this.queryResult(--count, nextDelay)
                }, delay)
              }
            }
          } else {
            this.$message.warning('支付结果查询失败，失败原因：' + res.message)
            this.$nextTick(() => {
              console.log(this.loadingInstance, ' this.loadingInstance')
              this.loadingInstance.close()
            })
            this.smsVisible = false
            this.paidDrawerVisible = false
            this.loadList()
          }
        })
        .catch((error) => {
          this.$nextTick(() => {
            this.loadingInstance.close()
          })

          console.log(error, 'error1')
        })
      // .finally(() => {
      //   this.loadingInstance.close()
      // })
    },
    handleDrawerClose() {
      this.currentApplication = {}
    },
    // 查看申请单
    handleViewApplicationForm(row) {
      this.loading = true
      this.applicationDrawerVisible = true
      getApplication(row.sourceId)
        .then((res) => {
          this.currentApplication = res.data[0]
          this.receiverList = res.data[1]
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-link {
  margin-left: 8px;
}
.name-detailSpan {
  cursor: pointer;
  color: #1890ff;
}
</style>
