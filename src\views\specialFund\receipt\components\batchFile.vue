<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-23 10:23:04
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-25 17:01:05
-->
<template>
  <div
    v-loading="loading"
    class="gever-form detail"
  >
    <div class="gever-title label-title">
      {{ $t('附件') }}
      <span
        style="color: red;font-size:10px;font-weight: normal;"
      >(仅支持上传gif,jpg,jpeg,png,doc,xls,txt,pdf,docx,xlsx,zip)</span>
    </div>
    <div class="form-sg">
      <gever-upload
        ref="upload"
        list-type="form-list"
        :accept="'.gif,.jpg,.jpeg,.png,.doc,.xls,.txt,.pdf,.docx,.xlsx,.zip'"
        :limit="10"
        :default-batch-id="contentForm.systemBatchId"
        :file-classification="'receipt'"
        @batch-change="handleBatchIdChange"
      />
    </div>
  </div>
</template>

<script>
import { batchFileImport } from '@/api/specialFund/receipt.js'

export default {
  props: {
    ids: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dialogVisible: false,
      contentForm: {
        systemBatchId: '',
        id: ''
      },
      loading: false
    }
  },
  methods: {
    save () {
      if (this.$refs.upload.fileList.length === 0) {
        return this.$message.warning(this.$t('请上传附件'))
      }
      const data = {
        ids: this.ids,
        systemBatchId: this.contentForm.systemBatchId
      }
      batchFileImport(data).then(res => {
        this.$emit('refreshTable')
        this.$emit('close')
        this.handleClose()
        this.$message.success(this.$t('导入成功'))
      })
    },
    handleClose () {
      this.dialogVisible = false
    },
    handleBatchIdChange (systemBatchId) {
      this.$set(this.contentForm, 'systemBatchId', systemBatchId)
    }
  }
}
</script>
<style lang="scss" scoped>
.label-title {
  color: #1890ff;
}
</style>
