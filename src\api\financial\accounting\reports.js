import request from '@/utils/request'

/**
 * @name:账表查询列表
 * @param {*} booksId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listReport(data) {
  return request({
    url: `/financial/accounting/accountingVoucher/listReport`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:资产负债表列表
 * @param {*} booksId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function accountingperiod(booksId) {
  return request({
    url: `/financial/books/basedata/accountingperiod/combotree?booksId=${booksId}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:资产负债表列表
 * @param {*} booksId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getReportList(data, reportId) {
  return request({
    url: `/financial/report/get?reportId=${reportId}&meta=false`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:资产负债表列表
 * @param {*} booksId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getMeta(data) {
  return request({
    url: `/financial/report/getMeta`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:会计报表列表导出
 * @param {*} booksId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveExcelTempFile(data) {
  return request({
    url: `/financial/report/saveExcelTempFile`,
    method: 'post',
    data: data,
    payload: true,
    withCredentials: true,
    timeout: 180000
  })
}

/**
 * @name:根据bookId和获取会计科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getAccountingItemList(booksId) {
  return request({
    url: `/financial/books/basedata/accountingItem/getAccountingItemList?booksId=${booksId}`,
    method: 'post',
    withCredentials: true
  })
}

/**
 * @name:是否
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function YesAndNo() {
  return request({
    url: `/json/financial/public/YesAndNo.json`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:级次
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getSetting(data) {
  return request({
    url: `/financial/booksSetting/getSetting`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}


export function login(data) {
  return request({
    url: `/financial/books/login`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}

//辅助核算类型
export function getAssistByAccountingItemIdList(data) {
  return request({
    url: `/financial/books/basedata/assistType/getAssistByAccountingItemIdList`,
    method: 'post',
    // params: data,
    data,
    withCredentials: true
  })
}

//暂存表会计科目

export function getAccountingItemZcList(data) {
  return request({
    url: `/financial/books/basedata/accountingItem/getAccountingItemList`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}



