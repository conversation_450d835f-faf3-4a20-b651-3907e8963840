<template>
  <div v-loading="loading" class="gever-form detail">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="170px"
      :disabled="type == 'view'"
    >
		<div class="gever-title">{{ $t('基本信息') }}</div>
		<div class="form-db">
			<el-form-item :label="$t('组织机构')" prop="fname">
				<gever-input v-model="userAreaName" disabled/>
			</el-form-item>
      <el-form-item :label="$t('收款项目编号')" prop="receiveItemCode">
				<gever-input v-model="contentForm.receiveItemCode" placeholder="手工录入，不允许重复。举例：1"/>
			</el-form-item>
		</div>
    <div class="form-db">
      <el-form-item :label="$t('收款项目')" prop="receiveItemName">
				<gever-input v-model="contentForm.receiveItemName" maxlength="100"/>
			</el-form-item>
			<el-form-item :label="$t('状态')" prop="status">
				<!-- <gever-input v-model="contentForm.payeeAccountNo" maxlength="50"/> -->
				<el-select v-model="contentForm.status" placeholder="请选择" >
					<el-option
					v-for="item in vaildOptions"
					:key="item.id"
					:label="item.text"
					:value="item.id">
					</el-option>
				</el-select>
			</el-form-item>
		</div>
    <div class="form-db">
			<el-form-item :label="$t('创建人名称')" prop="createUsername">
				<gever-input v-model="contentForm.createUsername" disabled />
			</el-form-item>
      <el-form-item :label="$t('创建时间')" prop="createDate" disabled>
				<gever-input v-model="contentForm.createDate" disabled/>
			</el-form-item>
		</div>
		<div class="form-sg">
			<el-form-item :label="$t('收款项目描述')" prop="remark">
        <el-input v-model="contentForm.remark" 
            type="textarea"
            :rows="3"
            maxlength="1000"
            show-word-limit
        />
			</el-form-item>
		</div>

    <div class="gever-title">{{ $t('附件') }}<span style="color:red;font-size:12px;">（仅支持上传gif,jpg,jpeg,png,doc,xls,txt,pdf,docx,xlsx,zip）</span></div>
    <div class="form-sg">
			<el-form-item :label="$t(' ')"  prop="qrPatmentRecItemFile" >
				<gever-upload
				ref="_uploadRef"
				list-type="form-list"
				:business-id="contentForm.id"
				:disabled="type == 'view'"
				file-classification="qrPatmentRecItemFile"
        accept=".gif,.jpg,.jpeg,.png,.doc,.xls,.txt,.pdf,.docx,.xlsx,.zip"
				@batch-change="handleBatchIdChange"
				/>
				<!-- accept=".pdf" -->
			</el-form-item>
		</div>
		
    </el-form>
  </div>
</template>

<script>
import {
  loadQrPaymentReceiveItemInfoApi,saveQrPaymentReceiveItemApi,updateQrPaymentReceiveItemApi} from '@/api/paymentQr/qrPaymentReceiveItem.js'
import { mapGetters, createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import {vaildOptions} from '../../config'

export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    userAreaCode: {
      type: String,
      default: ''
    },
    userAreaName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
	    loading: false,
      vaildOptions, 
      contentForm: {},
      batchId: '',
      rules: {
        receiveItemCode: [
          {
            required: true,
            message: '请输入收款项目编号',
            trigger: ['blur', 'change']
          }
        ],
        receiveItemName: [
          {
            required: true,
            message: '请输入收款项目',
            trigger: ['blur', 'change']
          }
        ],
        status: [
          {
            required: true,
            message: '请选择状态',
            trigger: ['blur', 'change']
          }
        ],
        remark: [
          {
            required: true,
            message: '请输入收款项目描述',
            trigger: ['blur', 'change']
          }
        ]    
      }
    }
  },
  computed: {
    // ...mapState(['areaLogin']),
    ...mapGetters([ 'name'])
    
  },
  created () {
    this.initPage()
  },
  methods: {
    initPage() {
      if (this.type === 'add') {
        // 新增页面初始化
        this.contentForm.createUsername = this.name
        const date = new Date()
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        this.$set(this.contentForm, 'createDate', `${year}-${month}-${day}`)
      } else if (this.type === 'view' || this.type === 'edit') {
        // 查看、编辑页面初始化
        this.loading = true
        loadQrPaymentReceiveItemInfoApi(this.id).then(res => {
          this.contentForm = res.data
        }).finally(() => {
          this.loading = false
        })
      }
    },
    handleBatchIdChange(systemBatchId) {
      this.contentForm.systemBatchId = systemBatchId
    },
    
    save() {
      const areaCode = this.userAreaCode
      const areaName = this.userAreaName
      this.contentForm.areaCode = areaCode
      this.contentForm.areaName = areaName

      this.$refs['_geverFormRef'].validate(valid => {
        if (!valid) {
          return this.$message.warning(this.$t('请完善表单信息'))
        }
        this.contentForm.batchId = this.batchId
        this.loading = true
        if (this.contentForm.id) {
          updateQrPaymentReceiveItemApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        } else {
          saveQrPaymentReceiveItemApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleBatchIdChange(batchId) {
      this.batchId = batchId
    },
  }
}
</script>


<style scoped>
::v-deep .btn:active {
  outline: 0;
  background-image: none;
  box-shadow: none !important; 
  cursor: auto;
}

::v-deep .btn {
  cursor: auto;
}
</style>