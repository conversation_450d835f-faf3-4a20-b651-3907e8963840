import request from '@/utils/request'
/**
 * 获取预算待办事项
 */
export function flowStatusWithOrgList (data) {
  return request({
    url: '/financial/budget/budgetFlowQuery/flowStatusWithOrgList',
    method: 'post',
    data
  })
}
/**
 * 获取列表
 * @param {orgIdList, flowStatus, projectName} data 
 * @returns 
 */
export function page (data) {
  return request({
    url: '/financial/budget/budgetFlowQuery/page',
    method: 'post',
    data
  })
}
/**
 * 获取年份数据
 * @param {orgIdList, flowStatus, projectName} data 
 * @returns 
 */
export function orgBooksYearByRegionCode (data) {
  return request({
    url: '/financial/books/orgBooksYearByRegionCode',
    method: 'post',
    data
  })
}