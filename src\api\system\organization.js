import request from '@/utils/request'

/**
 * @name:获取当前用户有权限的地区机构树(懒加载)
 * @param {String} id 本级id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function authTreeData(params) {
  return request({
    url: '/system/organization/authTreeData',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:获取所有地区机构树(懒加载)
 * @param {String} id 本级id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
// export function treeData(params) {
//   return request({
//     url: '/system/organization/treeData',
//     method: 'get',
//     params,
//     withCredentials: true
//   })
// }

export function treeData(params) {
  return request({
    url: '/system/organization/authTreeData',
    method: 'get',
    params,
    withCredentials: true
  })
}

// 根据code获取机构详情
export function loadOrg(code) {
  return request({
    url: '/system/organization/load/code/' + code,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:获取所有地区机构树(懒加载)
 * @param {String} id 本级id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function treeDataAll(params) {
  return request({
    url: '/system/organization/tree',
    method: 'get',
    params,
    withCredentials: true
  })
}