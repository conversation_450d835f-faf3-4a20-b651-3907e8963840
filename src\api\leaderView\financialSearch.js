import request from '@/utils/request'

/**
 * 获取APP使用情况
 */
export function loadReportList(data) {
  return request({
    url: '/financial/report/collection/reportList',
    method: 'post',
    timeout: 1000 * 60 * 3,
    data: data
  })
}

/**
 * 获取报表数据
 */
export function loadReport(data) {
  return request({
    url: '/financial/report/collection/get',
    method: 'post',
    timeout: 1000 * 60 * 3,
    data: data
  })
}

/**
 * 获取年份
 */
export function loadYearCombotree() {
  return request({
    url: '/financial/report/collection/booksYearCombotree',
    timeout: 1000 * 60 * 3,
    method: 'get'
  })
}

/**
 * 获取基础科目
 */
export function loadBaseItemCombotree(params) {
  return request({
    url: '/financial/report/collection/baseItemCombotree',
    timeout: 1000 * 60 * 3,
    method: 'get',
    params: params
  })
}

/**
 * 获取yes/no配置
 */
export function loadYesAndNo() {
  return request({
    url: '/json/financial/public/YesAndNo.json',
    timeout: 1000 * 60 * 3,
    method: 'get'
  })
}

/**
 * 获取地区机构树
 */
export function loadRegionTree(params) {
  return request({
    url: '/financial/report/collection/regionTree',
    timeout: 1000 * 60 * 3,
    method: 'post',
    params: params
  })
}

/**
 * 获取科目类型
 */
export function loadBalanceAccountCollectionItem() {
  return request({
    url: '/json/financial/report/balanceAccountCollectionItem.json',
    timeout: 1000 * 60 * 3,
    method: 'get'
  })
}

/**
 * 获取excel导出路径
 */
export function saveExcelTempFile(data) {
  return request({
    url: '/financial/report/collection/saveExcelTempFile',
    timeout: 1000 * 60 * 3,
    method: 'post',
    data: data,
    payload: true
  })
}

/**
 * 获取excel导出路径(特殊处理)
 * 科目余额汇总表/集体经济收入情况统计表
 */
export function saveExcelTempFileForSpec(data) {
  return request({
    url: '/financial/report/collection/exportForSpec',
    timeout: 1000 * 60 * 3,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
