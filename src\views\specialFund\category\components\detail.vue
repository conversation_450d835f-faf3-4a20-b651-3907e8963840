<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-14 10:41:56
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-18 11:08:33
-->
<template>
  <div v-loading="loading" class="gever-form detail">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="170px"
      :disabled="type == 'view'"
    >
      <div class="gever-title label-title">{{ $t('基本信息') }}</div>
      <div class="form-db">
        <el-form-item :label="$t('登记单位')" prop="orgName">
          <gever-input v-model="contentForm.areaName" disabled />
        </el-form-item>
        <el-form-item :label="$t('专项资金类别编号')" prop="categoryCode">
          <gever-input v-model="contentForm.categoryCode" disabled />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('专项资金类别名称')" prop="categoryName">
          <gever-input v-model="contentForm.categoryName" :maxlength="300" />
        </el-form-item>
        <el-form-item :label="$t('父级类别编号')" prop="creatorName">
          <gever-input v-model="contentForm.parentCode" disabled />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('父级类别名称')" prop="creatorName">
          <gever-input v-model="contentForm.parentName" disabled />
        </el-form-item>
        <el-form-item :label="$t('状态')" prop="status">
          <gever-select
            v-model="contentForm.status"
            number-key
            path="/financial/specialFund/category/status/"
          />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('创建人')" prop="parentId">
          <gever-input v-model="contentForm.creatorName" disabled />
        </el-form-item>
        <el-form-item :label="$t('创建日期')" prop="createId">
          <gever-input v-model="contentForm.createTime" disabled />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('说明')" prop="description">
          <gever-input v-model="contentForm.description" type="textarea" :maxlength="1000" />
        </el-form-item>
      </div>
      <div class="gever-title label-title">{{ $t('附件') }} <span style="color: red;font-size:10px;font-weight: normal;">(仅支持上传gif,jpg,jpeg,png,doc,xls,txt,pdf,docx,xlsx,zip)</span></div>
      <div class="form-sg">
        <el-form-item :label="$t('')" prop="attachment">
          <gever-upload
            list-type="form-list"
            :accept="'.gif,.jpg,.jpeg,.png,.doc,.xls,.txt,.pdf,.docx,.xlsx,.zip'"
            :limit="10"
            :default-batch-id="contentForm.systemBatchId"
            :business-id="contentForm.id"
            :file-classification="'category'"
            :format-name="'专项资金类别'"
            @batch-change="handleBatchIdChange"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import {
  loadCategoryInfoApi, saveCategoryApi, updateCategoryApi } from '@/api/specialFund/category.js'
export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    curTree: {
      type: Object,
      default: () => {}
    },
    area: {
      type: Object,
      default: () => {}
    },
    formData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      contentForm: {},
      rules: {
        categoryName: [
          { required: true, trigger: 'blur', message: '请输入专项资金类别名称' }
        ],
        status: [
          { required: true, trigger: 'change', message: '请选择状态' }
        ]
      }
    }
  },
  created () {
    this.initPage()
  },
  methods: {
    initPage() {
      if (this.type === 'add') {
        // 新增页面初始化
        console.log('this.curTree', this.area)
        this.contentForm.parentCode = this.curTree.parentCode
        this.contentForm.parentId = this.curTree.id
        this.contentForm.parentName = this.curTree.categoryName
        this.contentForm.creatorName = this.$store.getters.name
        this.contentForm.createTime = this.getCurrentDate()
        this.contentForm.areaName = this.area.fname
        this.contentForm.areaCode = this.area.code
      } else if (this.type === 'view' || this.type === 'edit') {
        // 查看、编辑页面初始化
        this.loading = true
        loadCategoryInfoApi(this.id).then(res => {
          this.contentForm = res.data
          this.contentForm.parentName = this.formData.parentName
          this.contentForm.parentCode = this.formData.parentCode
          this.contentForm.createTime = res.data.createTime.substring(0, 10)
        }).finally(() => {
          this.loading = false
        })
      }
    },
    handleBatchIdChange(systemBatchId) {
      this.contentForm.systemBatchId = systemBatchId
    },

    save() {
      this.$refs['_geverFormRef'].validate(valid => {
        if (!valid) {
          return this.$message.warning(this.$t('请完善表单信息'))
        }
        this.loading = true
        if (this.contentForm.id) {
          updateCategoryApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('getAreaTree')
              this.$emit('update:detailVisible', false)
              this.$emit('findAndUpdateNode', 'edit', this.contentForm)
            }
          }).finally(() => {
            this.loading = false
          })
        } else {
          saveCategoryApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('getAreaTree')
              this.$emit('update:detailVisible', false)
              this.$emit('findAndUpdateNode', 'add', res.data)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    getCurrentDate() {
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始
      const day = String(date.getDate()).padStart(2, '0')

      return `${year}-${month}-${day}`
    }
  }
}
</script>
<style lang="scss" scoped>
.label-title {
  color: #1890ff;
}
</style>
