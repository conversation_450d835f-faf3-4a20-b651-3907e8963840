<template>
  <div>
    <fold-box right-title="支付明细查询">
      <template  #right>
        <div class="right-box">
          <el-form inline :model="queryParams">
            <el-form-item :label="$t('业务类型')">
              <gever-select
                v-model="queryParams.busType"
                clearable
                class="w150"
                :options="busTypeOptions"
              />
            </el-form-item>
            <el-form-item :label="$t('申请单号')">
              <el-input
                v-model="queryParams.serialNumber"
                class="w150"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('收款方名称')">
              <el-input
                v-model="queryParams.receiverAccount"
                class="w150"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('支出类型')">
              <gever-select
                v-model="queryParams.expenditureType"
                clearable
                class="w150"
                :options="expenditureTypeOptions"
              />
            </el-form-item>
            <el-form-item :label="$t('结算方式')" prop="orgType">
              <el-select v-model="queryParams.settlementMethod" clearable class="w150">
                <el-option
                  v-for="item in settlementMethodOptins"
                  :key="item.id"
                  :label="item.settleName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('资金用途')" prop="fundsUse">
              <el-input v-model="queryParams.fundsUse" class="w150" />
            </el-form-item>
            <el-form-item :label="$t('申请详情')">
              <el-input v-model="queryParams.details" class="w150" />
            </el-form-item>
            <el-form-item :label="$t('申请时间')" prop="startCreationTime">
              <el-date-picker
                v-model="queryParams.startCreationTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
                class="w150"
                :clearable = "false"
              />
              {{ $t('至') }}
              <el-date-picker
                v-model="queryParams.endCreationTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
                class="w150"
                :clearable = "false"
              />
            </el-form-item>
            <el-form-item :label="$t('预算项目')">
              <el-input
                v-model="queryParams.budgetItemName"
                class="w150"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('会计科目编号')">
              <el-input
                v-model="queryParams.accountingItemCode"
                class="w150"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('审批状态')" prop="approvalStatusList">
              <el-select
                v-model="queryParams.approvalStatusList"
                multiple
                clearable
                class="w250"
              >
                <el-option
                  v-for="item in approvalstatusOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('支付状态')" prop="paymentStateList">
              <el-select
                v-model="queryParams.paymentStateList"
                multiple
                clearable
                class="w150"
              >
                <el-option
                  v-for="item in paymentStateOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
<!--            <el-form-item :label="$t('会计科目类型')">
              <gever-select
                v-model="queryParams.accountingItemType"
                clearable
                class="w150"
                :options="accountingItemTypeOptions"
              />
            </el-form-item>-->

<!--            <el-form-item :label="$t('结算号')">
              <el-input v-model="queryParams.settlementNo" />
            </el-form-item>
            <el-form-item :label="$t('付款账号')">
              <el-input v-model="queryParams.payAccountNumber" />
            </el-form-item>-->

<!--            <el-form-item :label="$t('乡村振兴类别')" prop="revitalizationType">
              <gever-select
                v-model="queryParams.revitalizationType"
                :options="revitalizationTypeOptions"
                clearable
                class="w150"
              />
            </el-form-item>-->

<!--            <el-form-item :label="$t('支付/终止时间')" prop="startActualPayTime">
              <el-date-picker
                v-model="queryParams.startActualPayTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
              />
              {{ $t('至') }}
              <el-date-picker
                v-model="queryParams.endActualPayTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
              />
            </el-form-item>-->
<!--            <el-form-item :label="$t('调账审批时间')" prop="startAdjustTime">
              <el-date-picker
                v-model="queryParams.startAdjustTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
              />
              {{ $t('至') }}
              <el-date-picker
                v-model="queryParams.endAdjustTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
              />
            </el-form-item>-->
            <el-form-item>
              <el-button
                round
                plain
                type="primary"
                icon="el-icon-search"
                @click="getList"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-button
                v-has-permi="'financial.payment.approvalManager.paymentDetailSearch.export'"
                type="primary"
                round
                icon="el-icon-download"
                :loading="exportLoading"
                @click="handleExport"
              >
                {{ $t('导出') }}
              </el-button>
            </el-form-item>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            v-loading="tableLoading"
            :columns="tableColumns"
            :data="tableList"
            pagination
            :total="total"
            :pagi="queryParams"
            :show-summary="true"
            :summary-method="getSum"
            :pageSizes="[20, 50, 100, 200, 300, 500]"
            @p-current-change="getList"
            @size-change="getList"
          >
            <template #expenditureType="{ row }">
              {{
                getExpenditureTypeText(
                  row.expenditureType,
                  'expenditureTypeOptions'
                )
              }}
            </template>
            <template #settlementMethod="{ row }">
              {{ converSettlementMethod(row.settlementMethod) }}
            </template>
            <template #applicationType="{ row }">
              {{ convertApplicationType(row.applicationType) }}
            </template>
            <template #revitalizationType="{ row }">
              {{ convertRevitalizationType(row.revitalizationType) }}
            </template>
            <template #approvalStatus="{ row }">
              {{ convertApprovalStatus(row.approvalStatus) }}
            </template>
            <template #payStatus="{ row }">
              {{ convertPaymentState(row.payStatus) }}
            </template>
            <template #operation="{ row }">
              <el-button v-if="row.applicationId" v-has-permi="'financial.payment.approvalManager.paymentDetailSearch.view'" type="text" @click="handleViewApplicationForm(row)">
                {{ $t('查看申请单') }}
              </el-button>
              <el-button v-if="!row.applicationId" v-has-permi="'financial.payment.approvalManager.paymentDetailSearch.view'" type="text" @click="handleViewBudgetRe(row)">
                {{ $t('查看调账') }}
              </el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <public-drawer
      ref="_appDrawerRef"
      v-loading="loading"
      :visible.sync="applicationDrawerVisible"
      :title="drawerTitle"
      :size="70"
      @close="handleDrawerClose"
    >
      <application
        v-if="applicationDrawerVisible"
        :id="currentRow.id || ''"
        ref="_appRef"
        :projectPerformance="projectPerformance"
        :view-state="viewState"
        :application="currentApplication"
        :receiver="receiverList"
        :load-type="loadType"
        :info-type="infoType"
        :show-process-btn="showProcessBtn"
        :accounting-item-id="current.id || ''"
        :visible.sync="applicationDrawerVisible"
        @loading="handleLoading"
        @Invalid="Invalid"
      />
    </public-drawer>

    <!-- 详情弹窗 -->
    <budget-adjust-detail
      v-if="budgetReVisible"
      :visible.sync="budgetReVisible"
      :dialog-type="'查看'"
      :cur-row="currentRow"
      :options-map="optionsMap"
      @success="getList"
    />

  </div>
</template>

<script>
import Application from '@/views/payment/approval/approvalApplication/applicationInfo.vue'
import BudgetAdjustDetail from '@/views/payment/approval/budgetReconciliation/components/details.vue'
import { getApplication, pagePaymentDetail, exportPaymentDetail } from '@/api/payment/approval.js'
import { paymentExpenditureTypeTree } from '@/api/payment/expenseType'
import { page as billingMethodPage } from '@/api/payment/billingMethod'
import { comboJson } from '@/api/gever/common.js'
import { getToken } from '@/utils/auth'
import moment from 'moment/moment'

export default {
  components: {
    Application,
    BudgetAdjustDetail
  },
  props: {},
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      projectPerformance: '',
      rowId: '',
      rowData: {},
      infoType: false,
      current: {}, // 当前选择的科目数据
      showProcessBtn: true,
      performanceVisible: false,
      tableLoading: false,
      exportLoading: false,
      rowIdArr: [],
      approvalstatusOptions: [],
      fundSrcOptions: [{ id: '', text: this.$t('全部') }],
      settlementMethodOptins: [{ id: '', settleName: this.$t('全部') }],
      applicationTypeOptions: [
        { id: 0, text: '常规用款' },
        { id: 1, text: '收支相抵' },
        { id: 2, text: '冲减收入' },
        { id: 3, text: '非预算项目支出' },
        { id: 4, text: '银行代扣' },
        { id: 5, text: '扣减预算' },
        { id: 6, text: '非预算收支相抵' }
      ],
      queryParams: {
        page: 1,
        rows: 20,
        orgCode: '',
        serialNumber: '',
        fundsUse: '',
        receiverAccount: '',
        approvalStatusList: ['4'],
        paymentStateList: ['2', '5'],
        startCreationTime: moment(new Date()).format('yyyy') + '-01-01',
        endCreationTime: moment(new Date()).format('yyyy-MM-DD')
      },
      performanceOptions: [
        { id: '0', text: '否' },
        { id: '1', text: '是' }
      ],
      tableList: [],
      total: 0,
      tableColumns: [
        { type: 'selection', align: 'center', width: '50', fixed: 'left' },
        { type: 'index', label: '序号', width: '80', fixed: 'left' },
        {
          label: '区名称',
          prop: 'regionName',
          minWidth: '100',
          showOverflowTooltip: true
        },
        {
          label: '镇名称',
          prop: 'townName',
          minWidth: '100',
          showOverflowTooltip: true
        },
        {
          label: '村名称',
          prop: 'villageName',
          minWidth: '100',
          showOverflowTooltip: true
        },
        {
          label: '申请单位',
          prop: 'orgName',
          minWidth: '200',
          showOverflowTooltip: true
        },
        {
          label: '业务类型',
          prop: 'busType',
          minWidth: '100',
          showOverflowTooltip: true
        },
        {
          label: '申请单号',
          prop: 'serialNumber',
          minWidth: '130',
          showOverflowTooltip: true
        },
/*        {
          label: '结算号',
          prop: 'settlementNo',
          width: '130',
          showOverflowTooltip: true
        },*/
/*        {
          label: '付款账号',
          prop: 'payAccountNumber',
          width: '200',
          showOverflowTooltip: true
        },*/
        {
          label: '收款方名称',
          prop: 'receiverAccount',
          minWidth: '200',
          showOverflowTooltip: true
        },
/*        {
          label: '收款方账号',
          prop: 'receiverBankAccount',
          minWidth: '200',
          showOverflowTooltip: true
        },
        {
          label: '收款方银行',
          prop: 'receiverOpenAccount',
          minWidth: '200',
          showOverflowTooltip: true
        },*/
        {
          label: '申请金额（元）',
          prop: 'amount',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '执行金额（元）',
          prop: 'exeAmount',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '预算项目名称',
          prop: 'budgetItemName',
          width: '150',
          showOverflowTooltip: true
        },
/*        {
          label: '会计科目编号',
          prop: 'accountingItemCode',
          width: '130',
          showOverflowTooltip: true
        },*/
        {
          label: '会计科目',
          prop: 'accountingItemName',
          width: '200',
          showOverflowTooltip: true
        },
/*        {
          label: '单据金额（元）',
          prop: 'amount',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },*/

        {
          label: '审批状态',
          prop: 'approvalStatus',
          width: '120',
          showOverflowTooltip: true
        },
        {
          label: '支付状态',
          prop: 'payStatus',
          width: '120',
          showOverflowTooltip: true
        },
        {
          label: '创建时间',
          prop: 'creationTime',
          width: '120',
          showOverflowTooltip: true
        },
        {
          label: '执行/终止时间',
          prop: 'actualPayTime',
          width: '120',
          showOverflowTooltip: true
        },
/*        {
          label: '调账审批时间',
          prop: 'adjustTime',
          width: '120',
          showOverflowTooltip: true
        },*/
/*        {
          label: '乡村振兴类别',
          prop: 'revitalizationType',
          width: '130',
          showOverflowTooltip: true
        },*/
/*        {
          label: '支付单号',
          prop: 'paymentCode',
          width: '130',
          showOverflowTooltip: true
        },*/
        {
          label: '支出类型',
          prop: 'expenditureType',
          width: '200',
          showOverflowTooltip: true
        },
        {
          label: '申请类型',
          prop: 'applicationType',
          width: '130',
          showOverflowTooltip: true
        },
        {
          label: '结算方式',
          prop: 'settlementMethod',
          width: '130',
          showOverflowTooltip: true
        },
        {
          label: '资金用途',
          prop: 'remark',
          width: '200',
          showOverflowTooltip: true
        },
        {
          label: '申请详情',
          prop: 'details',
          width: '200',
          showOverflowTooltip: true
        },
        { label: '操作', slotName: 'operation', width: '150', fixed: 'right' }
      ],
      loadType: '',
      drawerTitle: '',
      loading: false,
      currentRow: {},
      viewState: 0,
      budgetReVisible: false,
      applicationDrawerVisible: false,
      currentApplication: {},
      receiverList: [],
      sum: {
        sumAmount: 0,
        sumAppAmount: 0,
        sumExeAmount: 0
      },
      revitalizationTypeOptions: [{ id: '', text: this.$t('全部') }],
      busTypeOptions: [{ id: '', text: this.$t('全部') }, { id: '1', text: this.$t('支出申请') }, { id: '2', text: this.$t('支出退款') }, { id: '3', text: this.$t('预算调账') }],
      accountingItemTypeOptions: [{ id: '', text: this.$t('全部') }, { id: '1', text: this.$t('收入') }, { id: '2', text: this.$t('支出') }, { id: '3', text: this.$t('非损益') }],
      expenditureTypeOptions: [{ id: '', text: this.$t('全部') }],
      revitalizationTypeVisible: false,
      revitalizationTypeFrom: {},
      paymentStateOptions: [
        { id: '0', text: this.$t('未支付') },
        { id: '1', text: this.$t('支付中') },
        { id: '2', text: this.$t('支付成功') },
        { id: '5', text: this.$t('退款成功') },
        { id: '-1', text: this.$t('支付失败') },
        { id: '-9', text: this.$t('已退回') },
        { id: '-10', text: this.$t('交易终止') },
        { id: '99', text: this.$t('状态未知') }
      ]
    }
  },
  computed: {
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  watch: {
    paymentLogin: {
      /** 监听到变化后就触发一次handler 相当与created */
      handler(val) {
        if (val.type !== 'Organization') {
          if (val.id) {
            this.queryParams.areaCode = val.code
            this.queryParams.orgCode = ''
            this.getList()
          }
        } else {
          if (val.id) {
            this.queryParams.areaCode = ''
            this.queryParams.orgCode = val.code
            this.getList()
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.loadAllJson()
  },
  methods: {
    getToken,
    async getList() {
      /* if (this.paymentLogin.areaLevel < 4) {
        return this.$message.warning(
          this.$t('地区只能选择镇、村或机构进行查询，不能选择市、区')
        )
      }*/
      const startCreationTime = this.queryParams.startCreationTime
        ? new Date(this.queryParams.startCreationTime).getTime()
        : 0
      const endCreationTime = this.queryParams.endCreationTime
        ? new Date(this.queryParams.endCreationTime).getTime()
        : 0
      if (startCreationTime && endCreationTime) {
        if (startCreationTime > endCreationTime) {
          return this.$message.warning('申请时间开始日期不能大于结束日期')
        }
      }
      const startActualPayTime = this.queryParams.startActualPayTime
        ? new Date(this.queryParams.startActualPayTime).getTime()
        : 0
      const endActualPayTime = this.queryParams.endActualPayTime
        ? new Date(this.queryParams.endActualPayTime).getTime()
        : 0
      if (startActualPayTime && endActualPayTime) {
        if (startActualPayTime > endActualPayTime) {
          return this.$message.warning('支付时间开始日期不能大于结束日期')
        }
      }
      const startAdjustTime = this.queryParams.startAdjustTime
        ? new Date(this.queryParams.startAdjustTime).getTime()
        : 0
      const endAdjustTime = this.queryParams.endAdjustTime
        ? new Date(this.queryParams.endAdjustTime).getTime()
        : 0
      if (startAdjustTime && endAdjustTime) {
        if (startAdjustTime > endAdjustTime) {
          return this.$message.warning('调账审批时间开始日期不能大于结束日期')
        }
      }
      const startDay = new Date(this.queryParams.startCreationTime)
      const endDay = new Date(this.queryParams.endCreationTime)
      if (Math.floor((endDay.getTime() - startDay.getTime()) / (1000 * 60 * 60 * 24)) > 365) {
        return this.$message.warning(this.$t('申请时间范围不能大于一年'))
      }
      this.tableLoading = true
      const { data } = await pagePaymentDetail(this.queryParams)
      this.tableList = data.rows
      this.total = data.total
      this.sum.sumAmount = data.sumAmount
      this.sum.sumAppAmount = data.sumAppAmount
      this.sum.sumExeAmount = data.sumExeAmount
      this.tableLoading = false
    },
    getSum(param) {
      const { columns } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        } else if (index === 13) {
        //  sums[index] = this.comma(this.sum.sumAmount, 2)
        } else if (index === 9) {
          sums[index] = this.comma(this.sum.sumAmount, 2)
        } else if (index === 10) {
          sums[index] = this.comma(this.sum.sumExeAmount, 2)
        }
      })
      return sums
    },
    async handleExport() {
      /*      if (this.table.dataList.length <= 0) {
        return this.$message.warning(this.$t('没有需要导出的数据'))
      }*/
      if (this.paymentLogin.areaLevel < 4) {
        return this.$message.warning(
          this.$t('地区只能选择镇、村或机构进行查询，不能选择市、区')
        )
      }
      if (!this.queryParams.startCreationTime || !this.queryParams.endCreationTime) {
        return this.$message.warning(this.$t('必须选择申请时间范围'))
      }
      if (new Date(this.queryParams.startCreationTime) > new Date(this.queryParams.endCreationTime)) {
        return this.$message.warning(this.$t('申请开始时间不能晚于结束时间'))
      }
      const startDay = new Date(this.queryParams.startCreationTime)
      const endDay = new Date(this.queryParams.endCreationTime)
      if (Math.floor((endDay.getTime() - startDay.getTime()) / (1000 * 60 * 60 * 24)) > 183) {
        return this.$message.warning(this.$t('申请时间范围不能大于半年'))
      }
      this.exportLoading = true
      try {
        const { data, returnCode } = await exportPaymentDetail(this.queryParams)
        if (returnCode == '-1') {
          this.exportLoading = false
        }
        window.location.href =
          process.env.VUE_APP_BASE_API +
          '/payment/approval/approvalReceiver/downloadPaymentDetail?filePath=' +
          data +
          '&access_token=' +
          this.getToken() +
          '&tenant_id=' +
          this.$Cookies.get('X-tenant-id-header')
      } finally {
        this.exportLoading = false
      }
    },
    // 查看申请单
    handleViewApplicationForm(row) {
      this.loadType = 'view'
      this.loading = true
      this.drawerTitle = row.returnFlag ? '查看申请退款' : '查看支出申请'
      this.currentRow = row
      this.viewState = row.approvalStatus
      this.applicationDrawerVisible = true
      getApplication(row.applicationId)
        .then((res) => {
          this.currentApplication = res.data[0]
          this.receiverList = res.data[1]
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 查看申请单
    handleViewBudgetRe(row) {
      this.loadType = 'view'
      this.drawerTitle = '查看预算调账'
      this.currentRow = row
      this.budgetReVisible = true
    },
    handleLoading(flag) {
      this.loading = flag
    },
    Invalid(val) {
      this.invalidLoading = false
    },
    handleDrawerClose() {
      this.currentApplication = {}
      this.showProcessBtn = true
    },
    getExpenditureTypeText(val, options) {
      if (!val) {
        return ''
      }
      const valArr = val.split(',')
      const textArr = []
      this[options].forEach((item) => {
        if (valArr.includes(item.id)) {
          textArr.push(item.text)
        }
      })
      return textArr.join(',')
    },
    converSettlementMethod(val) {
      const settlementMethod = this.settlementMethodOptins.find(
        (item) => item.id == val
      )
      return settlementMethod ? settlementMethod.settleName : ''
    },
    convertApprovalStatus(val) {
      const status = this.approvalstatusOptions.find(
        (item) => item.id.toString() === val.toString()
      )
      return status ? status.text : ''
    },
    convertFundSrc(val) {
      const fundSrc = this.fundSrcOptions.find(
        (item) => item.id === val
      )
      return fundSrc ? fundSrc.text : ''
    },
    convertPaymentState(val) {
      const status = this.paymentStateOptions.find(
        (item) => item.id === val + ''
      )
      return status ? status.text : ''
    },
    convertApplicationType(val) {
      const applicationType = this.applicationTypeOptions.find(
        (item) => item.id == val
      )
      return applicationType ? applicationType.text : ''
    },
    formatRevitalizationType(row) {
      return row.revitalizationType ? row.revitalizationType : ''
    },
    convertRevitalizationType(val) {
      const revitalizationType = this.revitalizationTypeOptions.find(
        (item) => item.id === val + ''
      )
      return revitalizationType ? revitalizationType.text : ''
    },
    loadAllJson() {
      comboJson({ path: '/payment/approvalstatus' }).then((res) => {
        this.approvalstatusOptions.push(...res.data)
      })
      comboJson({ path: '/payment/source_of_funds/' }).then((res) => {
        this.fundSrcOptions.push(...res.data)
      })
      comboJson({ path: '/payment/revitalization_type' }).then((res) => {
        this.revitalizationTypeOptions.push(...res.data)
      })
      // 支出类型
      paymentExpenditureTypeTree({ page: 1, rows: 100 }).then((res) => {
        this.expenditureTypeOptions.push(...res.data.map((cur) => {
          if (cur.id == '0') {
            cur.id = ''
          }
          return cur
        })
        )
      })
      // 结算方式
      billingMethodPage({ page: 1, rows: 100, parentId: '0' }).then((res) => {
        this.settlementMethodOptins = res.data.rows
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
