<template>
  <div v-loading="loading" class="gever-form detail">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="170px"
      :disabled="type == 'view'"
    >
		<div class="form-sg">
      <el-form-item :label="$t('银行类型')" prop="bankType">
        <gever-select
          v-model="contentForm.bankType"
          path="/payment/pay_bank_type"
        />
      </el-form-item>
		</div>
		<div class="form-sg">
			<el-form-item :label="$t('委托类型编码')" prop="code">
				<gever-input v-model="contentForm.code" />
			</el-form-item>
		</div>
      <div class="form-sg">
        <el-form-item :label="$t('委托类型名称')" prop="name">
        <gever-input v-model="contentForm.name" />
      </el-form-item>
      </div>
		<div class="form-sg">
			<el-form-item :label="$t('是否有效')" prop="valid">
        <gever-select
          v-model="contentForm.valid"
          :options="validOptions"
        />
			</el-form-item>
		</div>
    </el-form>
  </div>
</template>

<script>
import {
  loadBankEntrustTypeInfoApi, saveBankEntrustTypeApi, updateBankEntrustTypeApi } from '@/api/payment/bankEntrustType.js'
export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    bankType: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    loading: false,
      contentForm: {
        valid: 1
      },
      rules: {
        bankType: [{ required: true, message: this.$t('请选择银行类型'), trigger: 'blur' }],
        code: [{ required: true, message: this.$t('请输入委托类型编码'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('请输入委托类型名称'), trigger: 'blur' }],
        valid: [{ required: true, message: this.$t('是否有效'), trigger: 'blur' }]
      },
      validOptions: [
        {
          id: 1,
        text: '是'
        },
        {
          id: 0,
          text: '否'
        }
      ]
    }
  },
  created () {
    this.initPage()
  },
  methods: {
    initPage() {
      if (this.type === 'add') {
        // 新增页面初始化
        if (this.bankType != '') {
          this.contentForm.bankType = this.bankType
        }
      } else if (this.type === 'view' || this.type === 'edit') {
        // 查看、编辑页面初始化
        this.loading = true
        loadBankEntrustTypeInfoApi(this.id).then(res => {
          this.contentForm = res.data
        }).finally(() => {
          this.loading = false
        })
      }
    },
    handleBatchIdChange(systemBatchId) {
      this.contentForm.systemBatchId = systemBatchId
    },
    save() {
      this.$refs['_geverFormRef'].validate(valid => {
        if (!valid) {
          return this.$message.warning(this.$t('请完善表单信息'))
        }
        this.loading = true
        if (this.contentForm.id) {
          updateBankEntrustTypeApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        } else {
          saveBankEntrustTypeApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
