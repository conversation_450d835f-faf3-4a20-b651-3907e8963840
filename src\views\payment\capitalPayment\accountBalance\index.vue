<template>
  <div>
    <fold-box
      left-title="选择"
      right-title="银行账户余额"
      :call-back="toggleSearch"
    >
      <template #left>
        <el-form v-show="showFilter" inline>
          <el-form-item :label="$t('账号：')">
            <el-input v-model="filterParams.accountNumber" class="w100" />
          </el-form-item>
          <el-form-item :label="$t('名称：')">
            <el-input v-model="filterParams.ownerName" class="w100" />
          </el-form-item>
          <el-form-item>
            <el-button
              v-hasPermi="'financial.payment.manager.balance.list'"
              type="primary"
              plain
              round
              icon="el-icon-search"
              @click="handleSearchList"
            >
              {{ $t('搜索') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="left-subNav">
          <ul class="gever-subNav">
            <li
              v-for="item in currentList"
              :key="item.id"
              :class="{ active: activeIndex.includes(item.id) }"
              @click="handleClick(item)"
            >
              <span>
                <span>{{ item.accountNumber }}</span>
                <span>{{ item.ownerName }}</span>
              </span>
            </li>
          </ul>
        </div>
      </template>
      <template #right>
        <div class="right-box">
          <el-form inline>
            <el-form-item :label="$t('账户名称')">
              <el-input v-model="queryParams.ownerName" />
            </el-form-item>
            <el-form-item :label="$t('银行账号')">
              <el-input v-model="queryParams.accountNumber" />
            </el-form-item>
            <el-form-item>
              <!-- <el-button
                type="primary"
                round
                plain
                icon="el-icon-search"
                @click="handleFilterTable"
              >
                {{ $t('搜索') }}
              </el-button> -->
              <el-button
                type="primary"
                round
                icon="el-icon-search"
                @click="handleSearchBalance"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <!-- <el-form-item style="float: right">
              <el-button
                type="primary"
                round
                icon="el-icon-search"
                @click="handleSearchBalance"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item> -->
          </el-form>
          <gever-table
            ref="_geverTableRef"
            v-loading="loading"
            :columns="tableColumns"
            :data="currentDataList"
            :pagination="false"
          />
        </div>
      </template>
    </fold-box>
  </div>
</template>

<script>
import { loadAccountList, loadAccountBalance } from '@/api/payment/capital.js'
import FoldBox from '../foldBox/index.vue'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'AccountBalance',
  components: { FoldBox },
  data() {
    return {
      loading: false,
      showFilter: false,
      filterParams: {
        // 左侧查询参数
        accountNumber: '',
        ownerName: ''
      },
      queryParams: {
        // 右侧表格筛选参数
        accountNumber: '',
        ownerName: ''
      },
      dataList: [],
      currentDataList: [],
      tableColumns: [
        { label: '账户名称', prop: 'ownerName' },
        { label: '银行账号', prop: 'accountNumber' },
        { label: '余额（元）', prop: 'balance' },
        { label: '可用余额（元）', prop: 'availBalance' },
        { label: '查询时间', prop: 'time' }
      ],
      activeIndex: [],
      accountList: [],
      currentList: []
    }
  },
  computed: {
    ...mapState(['areaLogin'])
  },
  watch: {
    areaLogin: {
      handler(val) {
        if (val) {
          this.activeIndex = []
          this.loadAccountList()
          // this.loadList()
        }
      },
      deep: true
    }
  },
  created() {
    this.loadAccountList()
  },
  methods: {
    loadAccountList() {
      loadAccountList({ authorized: 1 }).then((res) => {
        this.accountList = res.data.rows
        this.currentList = res.data.rows
        // this.accountList.push(res.data.rows[0])
        this.handleClick(res.data.rows[0])
      })
    },
    loadList() {
      this.loading = true
    },
    toggleSearch() {
      this.showFilter = !this.showFilter
    },
    handleSearchList() {
      this.currentList = this.accountList.filter((item) => {
        if (
          this.filterParams.accountNumber.length &&
          this.filterParams.ownerName.length
        ) {
          return (
            item.accountNumber.includes(this.filterParams.accountNumber) &&
            item.ownerName.includes(this.filterParams.ownerName)
          )
        } else if (this.filterParams.accountNumber.length) {
          return item.accountNumber.includes(this.filterParams.accountNumber)
        } else if (this.filterParams.ownerName.length) {
          return item.ownerName.includes(this.filterParams.ownerName)
        } else {
          return true
        }
      })
    },
    handleClick(item) {
      const index = this.activeIndex.findIndex((val) => val === item.id)
      if (index === -1) {
        this.activeIndex.push(item.id)
      } else {
        this.activeIndex.splice(index, 1)
      }
      this.handleSearchBalance()
    },
    handleSearchBalance() {
      this.dataList = []
      this.currentDataList = []
      const selected = JSON.parse(JSON.stringify(this.activeIndex))
      if (this.activeIndex.length === 0) {
        this.currentList.forEach((item) => {
          selected.push(item.id)
        })
      }
      this.loading = true
      selected.forEach((item) => {
        loadAccountBalance(item)
          .then((res) => {
            this.dataList.push(res.data)
            this.currentDataList = this.dataList
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    handleFilterTable() {
      this.currentDataList = []
      this.currentDataList = this.dataList.filter((item) => {
        if (
          this.queryParams.accountNumber.length &&
          this.queryParams.ownerName.length
        ) {
          return (
            item.accountNumber.includes(this.queryParams.accountNumber) &&
            item.ownerName.includes(this.queryParams.ownerName)
          )
        } else if (this.queryParams.accountNumber.length) {
          return item.accountNumber.includes(this.queryParams.accountNumber)
        } else if (this.queryParams.ownerName.length) {
          return item.ownerName.includes(this.queryParams.ownerName)
        } else {
          return true
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#left-container {
  .el-form {
    margin: 0 5px;
    .el-form-item {
      margin-bottom: 0 !important;
      ::v-deep input {
        border: none;
        border-bottom: 1px solid #dcdfe6;
      }
    }
  }
  .left-subNav {
    .gever-subNav li {
      display: flex;
      align-items: center;
      & > span {
        display: inline-block;
        & > span {
          display: block;
        }
      }
    }
  }
}
</style>
