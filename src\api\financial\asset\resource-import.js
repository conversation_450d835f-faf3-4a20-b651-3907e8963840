/*
 * @Author: Peng <PERSON>
 * @Date: 2021-11-24 09:38:03
 * @LastEditTime: 2021-11-30 10:07:16
 * @LastEditors: Peng Xiao
 * @Description:
 * @FilePath: \gever-zhxc-ui\src\api\financial\asset\resource-import.js
 * ^-^
 */

import request from '@/utils/request'

/**
 * @name:获取列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/asset/assetResourceImport/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取可以导入的资源
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function pageAssetResource(data) {
  return request({
    url: '/financial/asset/assetResourceImport/pageAssetResource',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/asset/assetResourceImport/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:导入资产资源
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function importAssetResource({ params, data }) {
  return request({
    url: '/financial/asset/assetResourceImport/importAssetResource',
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteWithBooks(data) {
  return request({
    url: `/financial/asset/assetResourceImport/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:关联资产资源
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function relate({ params, data }) {
  return request({
    url: `/financial/asset/assetResourceImport/relate`,
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:取消关联资产资源
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unRelate(data) {
  return request({
    url: `/financial/asset/assetResourceImport/unRelate`,
    method: 'post',
    data,
    withCredentials: true
  })
}

