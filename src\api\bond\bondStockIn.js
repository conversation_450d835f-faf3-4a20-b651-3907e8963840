/*
 * @Description: 
 * @Version: 
 * @Author: luozc
 * @Date: 2023-10-07 09:30:17
 * @LastEditors: luozc
 * @LastEditTime: 2023-10-09 19:58:38
 */
import request from '@/utils/request'

/**
 * 分页查询债券入库信息列表
 * @param {*} data 
 * @returns 
 */
 export function getBondStockList(data) {
    return request({
        url: '/bond/bondStockIn/page',
        method: 'post',
        data
    })
}

/**
 * 分页查询支付票据入库记录信息列表
 * @param {*} data 
 * @returns 
 */
 export function getBondStockRecordList(data) {
    return request({
        url: '/bond/bondStockIn/pageList',
        method: 'post',
        data
    })
}

/**
 * 获取债券入库详情信息
 * @param {*} id 
 * @returns 
 */
export function getStorkInDetail(id) {
    return request({
        url: `/bond/bondStockIn/get/${id}`,
        method: 'get'
    })
}

/**
 * 删除债券入库信息
 * @param {*} id 
 * @returns 
 */
export function removeStockIn(id) {
    return request({
        url: `/bond/bondStockIn/delete?id=${id}`,
        method: 'post'
    })
}

/**
 * 保存
 * @param {*} data 
 * @returns 
 */
export function saveBondStockIn(data){
    return request({
        url: '/bond/bondStockIn/save',
        method: 'post',
        data,
        payload: true
    })
}