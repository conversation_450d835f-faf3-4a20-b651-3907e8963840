
/**
 * 明细分类账浏览器打印功能
 * 支持独立打印和连续打印
 */

/**
 * 处理真正的连续打印 - 不同科目可以共享同一页面
 * @param {Array} subjects 科目数据
 * @param {Object} settings 打印设置
 * @returns {Object} 返回处理结果，包含页面、科目页码映射和总页数
 */
export function processTrueContinuousPrint(subjects, reportMeta, settings) {
    //console.log('===== 开始处理连续打印 =====');
    //console.log('科目数量:', subjects.length);
    //console.log('打印设置:', settings);

    const pages = [];
    let totalPages = 0;
    const subjectPageMap = {}; // 科目页码映射，用于生成目录

    // 进度更新函数 - 如果存在settings.updateProgress则调用
    const updateProgress = (text) => {
      if (settings && typeof settings.updateProgress === 'function') {
        settings.updateProgress(text);
      }
    };

    updateProgress('正在计算页面参数...');

    // 根据左右边距动态调整页面参数
    const calcPageSettings = (margins) => {
      const leftMargin = margins.marginLeft || 23;
      const rightMargin = margins.marginRight || 23;
      const averageMargin = (leftMargin + rightMargin) / 2;

      let maxItemsPerPage = 22; // 默认值
      let charsPerLine = 13;    // 默认值
      let maxTableHeight = 22.8; // 默认高度
      let multiLineHeight = 0.487; // 默认多行行高
      let singleLineHeight = 1;   // 默认单行行高
      let subjectHeaderHeight = 3; // 默认科目标题高度
      let tableHeaderHeight = 0.5; // 默认表头高度
      let footerHeight = 1.5;      // 默认页脚高度

      // 根据边距调整参数
      if (averageMargin < 20) {
        maxItemsPerPage = 30;
        charsPerLine = 14;
        maxTableHeight = 24.9;
        multiLineHeight = 0.5;
        singleLineHeight = 0.537;
      } else if (averageMargin >= 20 && averageMargin <= 23) {
        maxItemsPerPage = 23;
        charsPerLine = 13;
        maxTableHeight = 23.5;
        multiLineHeight = 0.487;
        singleLineHeight = 1;
      } else { // > 23mm
        maxItemsPerPage = 21;
        charsPerLine = 11;
        maxTableHeight = 22.8;
        multiLineHeight = 0.487;
        singleLineHeight = 1;
      }

      return {
        maxItemsPerPage,
        charsPerLine,
        maxTableHeight,
        multiLineHeight,
        singleLineHeight,
        subjectHeaderHeight,
        tableHeaderHeight,
        footerHeight
      };
    };

    // 获取页面设置
    const pageSettings = calcPageSettings(settings);
    //console.log('动态页面设置:', pageSettings);

    // 固定高度配置（单位：行高）
    const SUBJECT_HEADER_HEIGHT = pageSettings.subjectHeaderHeight; // 科目标题结构固定占用行数
    const TABLE_HEADER_HEIGHT = pageSettings.tableHeaderHeight;     // 表格表头固定占用行数
    const FOOTER_HEIGHT = pageSettings.footerHeight;               // 页脚高度固定占用行数
    const MAX_PAGE_HEIGHT = pageSettings.maxTableHeight - FOOTER_HEIGHT * 0.5; // 一页最大高度
    const MAX_ROWS_PER_PAGE = pageSettings.maxItemsPerPage;        // 每页最大行数
    const CHARS_PER_LINE = pageSettings.charsPerLine;               // 每行字符数

    //console.log('页面配置 - 科目标题高度:', SUBJECT_HEADER_HEIGHT, '表头高度:', TABLE_HEADER_HEIGHT, 
    //            '页脚高度:', FOOTER_HEIGHT, '最大页高:', MAX_PAGE_HEIGHT, '每页最大行数:', MAX_ROWS_PER_PAGE);

    // 计算摘要行高的函数
    const calcRowHeight = (str) => {
      if (!str || str.length === 0) return pageSettings.singleLineHeight;

      // 将换行符替换为空格
      const temp = str.replace(/[\r\n]+/g, ' ');

      // 计算实际字符长度（考虑半角和全角字符）
      let actualLength = 0;
      let specialCharsFound = false;
      let fullWidthDigitsFound = false;
      let specialPairsFound = false;
      let datePatternFound = false;

      // 检查是否包含日期格式
      if (/\d+\/\d+\/\d+/.test(temp) || /\d+-\d+-\d+/.test(temp) || /\d+\/\d+-\d+\/\d+/.test(temp)) {
        datePatternFound = true;
        actualLength += 1.5;
        //console.log('发现日期格式:', temp, '增加长度1.5');
      }

      for (let i = 0; i < temp.length; i++) {
        const charCode = temp.charCodeAt(i);

        // 中文字符
        if ((charCode >= 0x4E00 && charCode <= 0x9FFF) ||
            (charCode >= 0x3400 && charCode <= 0x4DBF) ||
            (charCode >= 0x20000 && charCode <= 0x2A6DF) ||
            (charCode >= 0x2A700 && charCode <= 0x2B73F) ||
            (charCode >= 0x2B740 && charCode <= 0x2B81F) ||
            (charCode >= 0x2B820 && charCode <= 0x2CEAF) ||
            (charCode >= 0xF900 && charCode <= 0xFAFF) ||
            (charCode >= 0x2F800 && charCode <= 0x2FA1F)) {
          actualLength += 0.96;
        }
        // 全角字符（包括数字、括号等）
        else if (charCode >= 0xFF00 && charCode <= 0xFFEF) {
          actualLength += 0.86;

          // 特殊处理全角数字（65296-65305）
          if (charCode >= 65296 && charCode <= 65305) {
            fullWidthDigitsFound = true;
            // 如果是全角数字，确保不会被截断
            if (i > 0 && i < temp.length - 1) {
              // 检查前后是否也是全角数字或特殊符号
              const prevCode = temp.charCodeAt(i - 1);
              const nextCode = temp.charCodeAt(i + 1);

              if ((prevCode >= 65296 && prevCode <= 65305) ||
                  prevCode === 0x3010 || prevCode === 0x3011 || // 【】
                  prevCode === 0xFF08 || prevCode === 0xFF09) { // （）
                // 增加一点宽度确保不会在不恰当的位置断行
                actualLength += 0.1;
                specialPairsFound = true;
              }

              if ((nextCode >= 65296 && nextCode <= 65305) ||
                  nextCode === 0x3010 || nextCode === 0x3011 || // 【】
                  nextCode === 0xFF08 || nextCode === 0xFF09) { // （）
                // 增加一点宽度确保不会在不恰当的位置断行
                actualLength += 0.1;
                specialPairsFound = true;
              }
            }
          }
        }
        // 特殊处理中文括号和符号
        else if (charCode === 0x3010 || charCode === 0x3011 || // 【】
                charCode === 0xFF08 || charCode === 0xFF09) {  // （）
          actualLength += 0.9; // 给特殊符号多分配一点宽度
          specialCharsFound = true;
        }
        // 特殊处理半角数字和日期相关字符
        else if ((charCode >= 0x30 && charCode <= 0x39)) { // 数字0-9
          // 半角数字计为0.46个字符
          actualLength += 0.46;

          // 检查是否是日期格式的一部分
          if (i > 0 && i < temp.length - 1) {
            const prevCode = temp.charCodeAt(i - 1);
            const nextCode = temp.charCodeAt(i + 1);

            // 检查前后是否有日期相关的字符（斜杠、连字符）
            if (prevCode === 0x2F || // /
                nextCode === 0x2F) { // /
              // 增加额外宽度，确保数字和斜杠/连字符不会分开
              actualLength += 0.4;
            }
          }
        }
        // 特殊处理斜杠和连字符（日期分隔符）
        else if (charCode === 0x2F) { // /
          // 日期分隔符计为0.6个字符
          actualLength += 0.6; // 给日期分隔符多分配宽度
          specialCharsFound = true;
        }
        // 半角字母
        else if ((charCode >= 0x41 && charCode <= 0x5A) || // 大写字母A-Z
                (charCode >= 0x61 && charCode <= 0x7A)) { // 小写字母a-z
          // 半角字母计为0.46个字符
          actualLength += 0.46;
        }
        // 其他半角字符（标点符号等）
        else if (charCode >= 0x20 && charCode <= 0x7E) {
          // 其他半角字符计为0.46个字符
          actualLength += 0.46;
        }
        // 其他特殊字符
        else {
          // 其他字符计为0.76个字符
          actualLength += 0.76;
        }
      }

      // 处理特殊案例 - 当字符串中包含数字和斜杠或连字符的组合
      if (datePatternFound) {
        // 强制增加行高，确保日期格式不会被分割
        actualLength = Math.max(actualLength, 11);
      }

      // 计算行数（使用动态行宽）
      const rowNum = Math.ceil(actualLength / CHARS_PER_LINE);

      // 返回行高（多行时每行使用multiLineHeight，单行时为singleLineHeight）
      const result = rowNum > 1 ? rowNum * pageSettings.multiLineHeight : pageSettings.singleLineHeight;

      if (rowNum > 1) {
        //console.log('摘要多行:', temp, '计算长度:', actualLength, '行数:', rowNum, '最终行高:', result);
      }

      return result;
    };

    // 初始化当前页面数据
    let currentPageItems = [];
    let currentPageHeight = 0;
    let currentPageNum = 0;
    let lastSubjectCode = '';
    // 当前页面的科目对象 - 用于解决同一科目跨页问题
    let currentSubject = null;
    // 当前页行计数
    let currentPageRowCount = 0;
    // 创建新页面的函数
    const createNewPage = () => {
      //console.log('创建新页面，当前项目数:', currentPageItems.length, '当前行数:', currentPageRowCount);

      if (currentPageItems.length > 0) {
        pages.push(createContinuousPage(currentPageItems, currentPageNum + 1, reportMeta, settings));
        totalPages++;
        currentPageNum++;

        //console.log('创建新页完成，页码:', currentPageNum, '总页数:', totalPages);

        // 重置当前页数据
        currentPageItems = [];
        currentPageHeight = 0;
        currentPageRowCount = 0;
      }
    };

    //console.log('开始处理科目数据...');

    // 处理每个科目
    subjects.forEach((subject, subjectIndex) => {
      // 更新进度信息
      const processedSubjects = subjectIndex + 1;
      updateProgress(`正在生成页面内容 (科目：${processedSubjects}/${subjects.length})...`);

      const code = subject.itemCode;
      const rows = subject.ledgerVOList || [];

      //console.log(`\n处理科目[${subjectIndex + 1}/${subjects.length}]: (${code}) ${subject.itemName}, 行数: ${rows.length}`);
      //console.log('当前页高度:', currentPageHeight, '最大高度:', MAX_PAGE_HEIGHT, '当前行数:', currentPageRowCount, '最大行数:', MAX_ROWS_PER_PAGE);

      // 检查是否是新科目
      const isNewSubject = lastSubjectCode !== code;

      // 如果是新科目，记录其起始页码并初始化科目对象
      if (isNewSubject) {
        // 计算新科目需要的初始高度（标题+表头）
        const newSubjectInitialHeight = SUBJECT_HEADER_HEIGHT + TABLE_HEADER_HEIGHT;

        // 判断是否需要创建新页 - 同时考虑高度和行数限制
        const heightOverflow = currentPageHeight + newSubjectInitialHeight > MAX_PAGE_HEIGHT;
        const rowCountOverflow = currentPageRowCount >= MAX_ROWS_PER_PAGE;
        const needNewPage = (heightOverflow || rowCountOverflow) && currentPageItems.length > 0;

        //console.log('新科目是否需要新页:', needNewPage, 
        //            '高度溢出:', heightOverflow, '行数溢出:', rowCountOverflow, 
        //            '当前高度:', currentPageHeight, 
        //            '新科目初始高度:', newSubjectInitialHeight, 
        //            '合计:', (currentPageHeight + newSubjectInitialHeight),
        //            '最大高度:', MAX_PAGE_HEIGHT,
        //            '当前行数:', currentPageRowCount,
        //            '最大行数:', MAX_ROWS_PER_PAGE);

        if (needNewPage) {
          // 当前页放不下新科目的完整结构，创建新页
          //console.log('当前页放不下新科目的完整结构或行数已达上限，创建新页');
          createNewPage();
        }

        // 不立即记录科目页码映射，而是在添加第一行数据时记录
        // 先预检查第一行数据是否会导致页面溢出
        if (rows.length > 0) {
          const firstRow = rows[0];
          const firstRowHeight = calcRowHeight(firstRow.summary);

          // 检查添加第一行后是否会导致页面溢出
          const firstRowHeightOverflow = currentPageHeight + firstRowHeight > MAX_PAGE_HEIGHT;

          if (firstRowHeightOverflow) {
            //console.log(`警告: 科目(${code})的第一行数据会导致页面溢出，需要先创建新页再记录起始页码`);
            // 如果当前页已有内容，先创建新页
            if (currentPageItems.length > 0) {
              createNewPage();
              // 重新计算科目标题和表头高度
              currentPageHeight = SUBJECT_HEADER_HEIGHT + TABLE_HEADER_HEIGHT;
            }
          }
        }

        // 现在记录科目页码映射
        subjectPageMap[code] = {
          itemName: subject.itemName,
          startPage: currentPageNum,
          order: subjectIndex
        };

        //console.log(`新科目: (${code}) ${subject.itemName}, 起始页码: ${currentPageNum} (预检查第一行后)`);

        // 更新最后处理的科目代码
        lastSubjectCode = code;

        // 添加科目标题高度
        currentPageHeight += SUBJECT_HEADER_HEIGHT;
        //console.log('添加科目标题高度后，当前页高度:', currentPageHeight);

        // 添加表格表头高度
        currentPageHeight += TABLE_HEADER_HEIGHT;
        //console.log('添加表头高度后，当前页高度:', currentPageHeight);

        // 初始化当前科目
        currentSubject = {
          type: 'subject',
          code: code,
          name: subject.itemName,
          typeName: subject.itemTypeName,
          isNewSubject: true,
          rows: []
        };
      } else {
        // 如果是同一科目的延续，只需要添加表头高度
        currentPageHeight += TABLE_HEADER_HEIGHT;
        //console.log('同一科目延续，只添加表头高度，当前页高度:', currentPageHeight);

        // 更新当前科目信息
        currentSubject = {
          type: 'subject',
          code: code,
          name: subject.itemName,
          typeName: subject.itemTypeName,
          isNewSubject: false,
          rows: []
        };
      }

      // 检查添加完科目标题和表头后是否已经超出页面高度
      if (currentPageHeight > MAX_PAGE_HEIGHT) {
        //console.log('警告: 添加科目标题和表头后高度已超出最大限制，强制创建新页');
        // 如果当前科目已经有行，先将其添加到当前页
        if (currentSubject && currentSubject.rows.length > 0) {
          currentPageItems.push({...currentSubject});
          // 增加行计数
          currentPageRowCount += currentSubject.rows.length;
        }

        // 如果是新科目且尚未添加任何行，更新起始页码
        if (isNewSubject && subjectPageMap[code]) {
          //console.log(`科目(${code})标题和表头超出页面高度，更新起始页码从${currentPageNum}到${currentPageNum + 1}`);
          subjectPageMap[code].startPage = currentPageNum + 1;
        }

        createNewPage();

        // 重新初始化当前科目
        currentSubject = {
          type: 'subject',
          code: code,
          name: subject.itemName,
          typeName: subject.itemTypeName,
          isNewSubject: true,
          rows: []
        };

        // 新页添加科目标题和表头高度
        currentPageHeight = SUBJECT_HEADER_HEIGHT + TABLE_HEADER_HEIGHT;
        //console.log('新页初始高度(标题+表头):', currentPageHeight);
      }

      // 初始化该科目的所有行
      let processedRows = 0;

      // 处理每一行数据
      for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
        const row = rows[rowIndex];
        // 计算当前行的实际高度
        const rowHeight = calcRowHeight(row.summary);

        //console.log(`行[${rowIndex + 1}], 摘要: "${row.summary?.substring(0, 20)}${row.summary?.length > 20 ? '...' : ''}", 计算行高: ${rowHeight}`);

        // 检查是否需要分页 - 同时考虑高度和行数限制
        const heightOverflow = currentPageHeight + rowHeight > MAX_PAGE_HEIGHT;
        const rowCountOverflow = currentPageRowCount + 1 > MAX_ROWS_PER_PAGE; // +1 是因为要添加当前行

        // 特殊处理：如果这是科目的第一行数据，并且需要分页，先更新科目的起始页码
        if ((heightOverflow || rowCountOverflow) && rowIndex === 0 && currentSubject && currentSubject.rows.length === 0) {
          //console.log(`科目(${code})的第一行数据需要分页，更新起始页码从${currentPageNum}到${currentPageNum + 1}`);
          // 更新科目起始页码为下一页
          if (subjectPageMap[code]) {
            subjectPageMap[code].startPage = currentPageNum + 1;
          }
        }

        if (heightOverflow || rowCountOverflow) {
          //console.log('当前页已满，需要创建新页');
          //console.log('高度溢出:', heightOverflow, '行数溢出:', rowCountOverflow,
          //           '当前高度:', currentPageHeight, '行高:', rowHeight, 
          //           '合计:', (currentPageHeight + rowHeight), '最大高度:', MAX_PAGE_HEIGHT,
          //           '当前行数:', currentPageRowCount, '行数+1:', (currentPageRowCount + 1), '最大行数:', MAX_ROWS_PER_PAGE);

          // 如果当前科目已经有行，先将其添加到当前页
          if (currentSubject && currentSubject.rows.length > 0) {
            currentPageItems.push({...currentSubject});
            //console.log(`添加科目(${currentSubject.code})到当前页，行数: ${currentSubject.rows.length}`);

            // 增加行计数
            currentPageRowCount += currentSubject.rows.length;
            processedRows += currentSubject.rows.length;
          }

          // 创建新页，即使currentPageItems为空也创建
          createNewPage();

          // 为新页准备科目结构（继承当前科目，但标记为非新科目，因为是延续的）
          currentSubject = {
            type: 'subject',
            code: code,
            name: subject.itemName,
            typeName: subject.itemTypeName,
            isNewSubject: false, // 在新页中继续显示同一科目
            rows: []
          };

          // 新页添加科目标题和表头高度
          currentPageHeight = SUBJECT_HEADER_HEIGHT + TABLE_HEADER_HEIGHT;
          //console.log('新页初始高度(标题+表头):', currentPageHeight);
        }

        // 再次检查添加行后是否会超出页面高度，这是额外的安全检查
        if (currentPageHeight + rowHeight > MAX_PAGE_HEIGHT) {
          //console.log('警告: 添加行后高度将超出最大限制，此行无法放入当前页面，强制创建新页');

          // 如果当前科目已经有行，先将其添加到当前页
          if (currentSubject && currentSubject.rows.length > 0) {
            currentPageItems.push({...currentSubject});
            // 增加行计数
            currentPageRowCount += currentSubject.rows.length;
            processedRows += currentSubject.rows.length;
          }

          createNewPage();

          // 为新页准备科目结构
          currentSubject = {
            type: 'subject',
            code: code,
            name: subject.itemName,
            typeName: subject.itemTypeName,
            isNewSubject: false,
            rows: []
          };

          // 新页添加科目标题和表头高度
          currentPageHeight = SUBJECT_HEADER_HEIGHT + TABLE_HEADER_HEIGHT;
        }

        // 添加当前行到当前科目
        currentSubject.rows.push(row);
        currentPageHeight += rowHeight;
        //console.log(`添加行后，当前页高度: ${currentPageHeight}`);
      }

      // 将当前科目添加到当前页项目中
      if (currentSubject && currentSubject.rows.length > 0) {
        //console.log(`科目(${code})添加到当前页，行数: ${currentSubject.rows.length}`);
        currentPageItems.push({...currentSubject});

        // 增加行计数
        currentPageRowCount += currentSubject.rows.length;
        //console.log('当前页行数增加到:', currentPageRowCount);

        // 重置当前科目
        currentSubject = null;
      }
    });

    // 处理最后一页
    if (currentPageItems.length > 0) {
      //console.log('处理最后一页，项目数:', currentPageItems.length, '行数:', currentPageRowCount);

      pages.push(createContinuousPage(currentPageItems, currentPageNum + 1, reportMeta, settings));
      totalPages++;

      //console.log('最后页创建完成，总页数:', totalPages);
    }

    //console.log('===== 连续打印处理完成 =====');
    //console.log('总页数:', totalPages);
    //console.log('科目页码映射:', Object.keys(subjectPageMap).length);

    // 更新所有页面的设置中的总页数
    settings.totalPages = totalPages;

    // 更新最终进度
    updateProgress(`页面内容生成完成，共生成${totalPages}页内容`);

    return { pages, subjectPageMap, totalPages };
  }

  /**
   * 创建连续打印的页面
   * @param {Array} items 当前页的项目
   * @param {number} pageNum 页码
   * @param {Object} reportMeta 报表元数据
   * @param {Object} settings 打印设置
   * @returns {Object} 返回页面对象
   */
  export function createContinuousPage(items, pageNum, reportMeta, settings) {
    const date = new Date();
    const printDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

    // 获取签名信息 - 与独立打印保持一致
    const signerList = reportMeta.expand.signerInfoList || [];
    const signerHtml = signerList.map(signer =>
      `<span class="signature">${signer}</span>`
    ).join('');

    let content = `<div class="page">`;

    // 添加内容容器开始，使用分类明细账的样式结构
    content += `<div class="content-wrapper">`;

    let lastSubjectCode = '';

    // 循环处理每个项目（科目）
    items.forEach(item => {
      const { type, code, name, typeName, isNewSubject, rows } = item;

      // 添加科目标题结构 - 无论是新科目还是延续科目都显示完整标题
      if (isNewSubject) {
        // 新科目，添加完整的科目标题结构
        content += `
          <div class="title">${typeName}分类账</div>
          <div style="text-align: center; margin: 8px 0;">会计期间：${reportMeta.periodNum || ''}</div>
          <div style="margin: 8px 0;">单位名称：${reportMeta.unitName || ''}</div>
          <div style="margin: 8px 0 12px;">会计科目：${name}</div>
        `;
      } else {
        // 延续科目，也显示完整标题结构，但在科目名称后加上"（续）"
        content += `
          <div class="title">${typeName}分类账</div>
          <div style="text-align: center; margin: 8px 0;">会计期间：${reportMeta.periodNum || ''}</div>
          <div style="margin: 8px 0;">单位名称：${reportMeta.unitName || ''}</div>
          <div style="margin: 8px 0 12px;">会计科目：${name}</div>
        `;
      }

      // 添加表格内容 - 与独立打印保持一致
      content += `
        <table>
          <tr>
            <th width="10%">日期</th>
            <th width="10%">凭证字号</th>
            <th width="29%">摘要</th>
            <th width="15%">借方</th>
            <th width="15%">贷方</th>
            <th width="6%">方向</th>
            <th width="15%">余额</th>
          </tr>
      `;

      // 添加行项目
      rows.forEach(row => {
        content += `
          <tr>
            <td>${row.periodDate || row.voucherDate || row.date || ''}</td>
            <td>${row.voucherNumber || row.voucherNo || ''}</td>
            <td class="summary">${row.summary || ''}</td>
            <td class="amount">${row.debit || row.debitAmount || ''}</td>
            <td class="amount">${row.credit || row.creditAmount || ''}</td>
            <td>${row.direction || ''}</td>
            <td class="amount">${row.balance || ''}</td>
          </tr>
        `;
      });

      // 直接关闭表格，不添加科目小计
      content += `</table>`;

      lastSubjectCode = code;
    });

    // 关闭内容容器
    content += `</div>`;

    // 添加页脚 - 与分类明细账保持一致
    content += `
        <div class="footer">
          <div style="width: 70%; display: flex; flex-wrap: wrap;">${signerHtml}</div>
          <div style="width: 30%; text-align: right;">打印日期：${settings.printingDate || printDate}</div>
        </div>
        <div class="page-number">第${pageNum}页</div>
      </div>
    `;

    return { content, pageNum };
  }

  /**
   * 执行浏览器打印功能 - 连续打印模式
   * @param {Array} reportData 报表数据
   * @param {Object} reportMeta 报表元数据
   * @param {Object} settings 打印设置
   * @param {Object} vueInstance Vue组件实例(this)，用于访问Element UI组件
   * @param {Number} printMode 打印模式 1-分批打印 2-一次性打印
   */
  export function browserContinuousPrint(reportData, reportMeta, settings, vueInstance, printMode = 1) {
    if (!reportData || reportData.length === 0) {
      return;
    }

    // 显示全屏加载
    const loading = vueInstance.$loading({
      lock: true,
      text: '正在准备打印数据...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 用于更新进度信息
    const updateMainProgress = (text) => {
      if (loading) {
        loading.text = text;
      }
    };

    try {
      // 延迟执行打印操作，让加载提示有时间显示
      setTimeout(() => {
        try {
          // 处理数据并开始打印
          processContinuousPrint(reportData, reportMeta, settings, loading, vueInstance, printMode, updateMainProgress);
        } catch (error) {
          loading.close();
          vueInstance.$message.error('打印准备过程中出错: ' + error.message);
          console.error('打印错误:', error);
        }
      }, 100);
    } catch (error) {
      loading.close();
      vueInstance.$message.error('打印过程中出错: ' + error.message);
      console.error('打印错误:', error);
    }
  }

  /**
   * 处理连续打印内容并根据模式选择打印方式
   * @param {Array} reportData 报表数据
   * @param {Object} reportMeta 报表元数据
   * @param {Object} settings 打印设置
   * @param {Object} loading 加载对象
   * @param {Object} vueInstance Vue组件实例(this)
   * @param {Number} printMode 打印模式 1-分批打印 2-一次性打印
   * @param {Function} updateProgressFunc 更新进度函数
   */
  function processContinuousPrint(reportData, reportMeta, settings, loading, vueInstance, printMode = 1, updateProgressFunc) {
    // 用于更新进度信息
    const updateMainProgress = updateProgressFunc || ((text) => {
      if (loading) {
        loading.text = text;
      }
    });

    updateMainProgress('正在处理打印数据...');

    try {
      // 确保reportData不为undefined
      if (!reportData || !Array.isArray(reportData)) {
        loading.close();
        vueInstance.$message.error('报表数据格式错误');
        return;
      }

      // 传递更新进度函数到settings
      settings.updateProgress = updateMainProgress;

      // 处理连续打印数据
      const result = processTrueContinuousPrint(reportData, reportMeta, settings);

      // 设置总页数到打印设置中，确保所有页面一致显示
      settings.totalPages = result.totalPages;
      //console.log('设置最终总页数:', settings.totalPages);

      // 根据打印模式选择打印方式
      if (printMode === 1) {
        // 分批打印模式
        updateMainProgress('使用分批打印模式...');
        batchPrintContents(result.pages, result.subjectPageMap, result.totalPages, reportMeta, settings, loading, vueInstance);
      } else {
        // 一次性打印模式
        updateMainProgress('使用一次性打印模式...');
        printAllAtOnce(result.pages, result.subjectPageMap, result.totalPages, reportMeta, settings, loading, vueInstance);
      }
    } catch (error) {
      console.error('打印准备过程中出错:', error);
      if (loading) loading.close();
      if (vueInstance) vueInstance.$message.error('打印准备过程中出错: ' + error.message);
    }
  }

  /**
   * 分批打印内容页和目录页
   * @param {Array} pageContents 所有页面内容
   * @param {Object} subjectPageMap 科目页码映射
   * @param {Number} totalPages 总页数
   * @param {Object} reportMetaData 报表元数据
   * @param {Object} loading 加载对象
   * @param {Object} vueInstance Vue组件实例
   */
  function batchPrintContents(pageContents, subjectPageMap, totalPages, reportMetaData, settings, loading, vueInstance) {
    // 更新进度显示函数
    const updateMainProgress = (text) => {
      if (loading) {
        loading.text = text;
      }
    };

    try {
      // 创建目录页
      updateMainProgress('正在生成目录页...');
      const directoryPages = createDirectoryPage(subjectPageMap, totalPages, reportMetaData);

      updateMainProgress(`目录页生成完成，共${directoryPages.length}页目录`);

      // 分批处理所有页面（每批次200页）
      const PAGES_PER_BATCH = 200;
      const batches = [];
      for (let i = 0; i < pageContents.length; i += PAGES_PER_BATCH) {
        batches.push(pageContents.slice(i, i + PAGES_PER_BATCH));
      }

      updateMainProgress(`准备分${batches.length}批次打印内容页，共${pageContents.length}页...`);

      // 先打印内容，最后打印目录 - 使用递归方式依次打印每个批次
      printContentBatches(0, batches, reportMetaData, settings, 0, pageContents.length, updateMainProgress, () => {
        // 内容打印完成后打印目录
        updateMainProgress(`内容页打印完成，准备打印目录（共${directoryPages.length}页）...`);
        printDirectoryPages(directoryPages, reportMetaData, settings, loading, vueInstance, (totalDirectoryPages) => {
          // 所有打印完成
          if (loading) loading.close();
          if (vueInstance) vueInstance.$message.success(`打印完成，共${pageContents.length}页内容和${directoryPages.length}页目录已分批打印完成`);
        });
      });
    } catch (error) {
      console.error('分批打印过程中出错:', error);
      if (loading) loading.close();
      if (vueInstance) vueInstance.$message.error('分批打印过程中出错: ' + error.message);
    }
  }

  /**
   * 递归打印内容页批次
   * @param {Number} batchIndex 当前批次索引
   * @param {Array} batches 所有批次
   * @param {Object} reportMetaData 报表元数据
   * @param {Number} printedPages 已打印页数
   * @param {Number} totalPages 总页数
   * @param {Function} updateProgress 更新进度函数
   * @param {Function} callback 打印完成后的回调
   */
  function printContentBatches(batchIndex, batches, reportMetaData, settings, printedPages, totalPages, updateProgress, callback) {
    // 检查是否打印完所有批次
    if (batchIndex >= batches.length) {
      if (callback && typeof callback === 'function') {
        callback();
      }
      return;
    }

    const currentBatch = batches[batchIndex];
    const currentBatchPages = currentBatch.length;
    const currentStart = printedPages + 1;
    const currentEnd = printedPages + currentBatchPages;

    updateProgress(`正在打印第${batchIndex + 1}批，共${batches.length}批 (${currentStart}-${currentEnd}页，共${totalPages}页)...`);

    // 创建打印窗口
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      console.error('打印窗口无法创建，可能被浏览器拦截');
      updateProgress('打印失败: 打印窗口被浏览器拦截');
      return;
    }

    // 写入打印样式和内容
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>明细分类账打印 - 第${batchIndex + 1}批</title>
        <meta charset="utf-8">
        <style>
          @page {
            size: A4 portrait;
            margin: ${settings?.marginTop || 15}mm ${settings?.marginRight || 23}mm ${settings?.marginBottom || 15}mm ${settings?.marginLeft || 23}mm;
          }

          body {
            margin: 0;
            padding: 0;
            font-family: SimSun, Arial, sans-serif;
            font-size: 12px;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }

          .page {
            page-break-after: always;
            position: relative;
            height: calc(297mm - ${settings?.marginTop || 15}mm - ${settings?.marginBottom || 15}mm);
            box-sizing: border-box;
            overflow: hidden;
          }

          .page:last-child {
            page-break-after: auto;
          }

          .title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
          }

          .subtitle {
            font-size: 14px;
            text-align: center;
            margin-bottom: 10px;
          }

          .content-wrapper {
            min-height: calc(100% - 60mm);
            position: relative;
          }

          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            table-layout: fixed;
          }

          th, td {
            border: 1px solid black;
            padding: 3px;
            font-size: 12px;
            text-align: center;
            word-break: break-word;
          }

          .summary {
            text-align: left;
          }

          .amount {
            text-align: right;
          }

          .footer {
            position: absolute;
            bottom: 10mm;
            width: 100%;
            display: flex;
            justify-content: space-between;
          }

          .signature {
            margin-right: 20px;
            white-space: nowrap;
          }

          .page-number {
            position: absolute;
            bottom: 2mm;
            width: 100%;
            text-align: center;
          }
        </style>
        <script>
          // 设置总页码
          window.onload = function() {
            const totalPages = ${totalPages};
            const pageNumberEls = document.querySelectorAll('.page-number');
            pageNumberEls.forEach(el => {
              const pageText = el.textContent;
              if (pageText && pageText.indexOf('第') === 0) {
                // 替换页码格式
                const currentPage = pageText.replace('第', '').replace('页', '').trim();
                el.textContent = '第' + currentPage + '页 / 共' + totalPages + '页';
              }
            });
          }
        </script>
      </head>
      <body>
    `);

    // 写入当前批次的内容页
    currentBatch.forEach(page => {
      if (page && page.content) {
        printWindow.document.write(page.content);
      }
    });

    printWindow.document.write('</body></html>');
    printWindow.document.close();

    // 设置打印完成后的回调
    try {
      // 打印完成后的处理
      printWindow.onafterprint = function() {
        //console.log(`第${batchIndex + 1}批次打印完成`);
        // 关闭当前打印窗口
        printWindow.close();

        // 递归调用，打印下一批
        setTimeout(() => {
          printContentBatches(
            batchIndex + 1,
            batches,
            reportMetaData,
            settings,
            printedPages + currentBatchPages,
            totalPages,
            updateProgress,
            callback
          );
        }, 500);
      };
      // 执行打印
      setTimeout(function() {
        printWindow.print();
      }, 500);
    } catch (error) {
      console.error('打印内容页错误:', error);
      updateProgress('打印失败: ' + error.message);
    }

  }

  /**
   * 打印目录页
   * @param {Array} directoryPages 目录页数组
   * @param {Object} reportMetaData 报表元数据
   * @param {Object} loading 加载对象
   * @param {Object} vueInstance Vue组件实例
   * @param {Function} callback 打印完成后的回调
   */
  function printDirectoryPages(directoryPages, reportMetaData, settings, loading, vueInstance, callback) {
    // 打开新窗口用于打印目录
    const directoryWindow = window.open('', '_blank');
    if (!directoryWindow) {
      if (vueInstance) vueInstance.$message.error('打印窗口被浏览器拦截，请允许弹出窗口后重试');
      if (loading) loading.close();
      if (callback) callback(0);
      return;
    }

    // 写入与独立打印相同的HTML结构和样式
    directoryWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>明细分类账目录</title>
        <style>
          @page {
            size: A4 portrait;
                margin: ${settings?.marginTop || 15}mm ${settings?.marginRight || 23}mm ${settings?.marginBottom || 15}mm ${settings?.marginLeft || 23}mm;
          }
          body {
            font-family: SimSun;
            font-size: 12px;
            margin: 0;
            padding: 0;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          .page {
            page-break-after: always;
            position: relative;
            height: calc(297mm - ${settings?.marginTop || 15}mm - ${settings?.marginBottom || 15}mm);
            box-sizing: border-box;
            overflow: hidden;
          }
          .page:last-child {
            page-break-after: auto;
          }
          .title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 10px 0;
          }
          .content-wrapper {
            position: relative;
            display: flex;
            flex-direction: column;
            min-height: calc(100% - 30mm);
          }
          .spacer {
            flex-grow: 1;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            table-layout: fixed;
          }
          th, td {
            border: 1px solid black;
            padding: 3px;
            text-align: center;
            word-break: break-word;
            overflow: hidden;
            height: auto;
          }
          tr {
            page-break-inside: avoid;
          }
          .directory-page-number {
            position: absolute;
            bottom: 2mm;
            width: 100%;
            text-align: center;
          }
          .t_2_td_title {
            border: solid 1px black;
            text-align: center;
            font-size: 3.5mm;
          }
          .t_2_spacn {
            width: 150px;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal;
            word-break: break-word;
            font-family: SimSun;
            line-height: 18px;
          }
        </style>
      </head>
      <body>
    `);

    // 写入目录页内容
    directoryPages.forEach(page => {
      if (page && page.content) {
        directoryWindow.document.write(page.content);
      }
    });

    directoryWindow.document.write('</body></html>');
    directoryWindow.document.close();

    // 打印目录完成后的操作
    try {


      directoryWindow.onafterprint = function() {
        //console.log('目录打印完成');
        directoryWindow.close();

        // 执行回调
        if (callback && typeof callback === 'function') {
          callback(directoryPages.length);
        }
      };

      // 执行打印
      setTimeout(function() {
        directoryWindow.print();
      }, 500);
    } catch (error) {
      console.error('打印目录页错误:', error);
      if (loading) loading.close();
      if (vueInstance) vueInstance.$message.error('打印目录页失败: ' + error.message);
      if (callback) callback(0);
    }

  }

  /**
   * 一次性打印所有内容和目录
   * @param {Array} pageContents 所有页面内容
   * @param {Object} subjectPageMap 科目页码映射
   * @param {Number} totalPages 总页数
   * @param {Object} reportMetaData 报表元数据
   * @param {Object} loading 加载对象
   * @param {Object} vueInstance Vue组件实例
   */
  function printAllAtOnce(pageContents, subjectPageMap, totalPages, reportMetaData, settings, loading, vueInstance) {
    // 更新进度显示函数
    const updateMainProgress = (text) => {
      if (loading) {
        loading.text = text;
      }
    };

    try {
      // 创建目录页
      updateMainProgress('正在生成目录页...');
      const directoryPages = createDirectoryPage(subjectPageMap, totalPages, reportMetaData);

      updateMainProgress(`目录页生成完成，准备一次性打印所有内容，共${pageContents.length}页内容和${directoryPages.length}页目录...`);

      // 创建打印窗口
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        if (vueInstance) vueInstance.$message.error('打印窗口被浏览器拦截，请允许弹出窗口后重试');
        if (loading) loading.close();
        return;
      }

      // 写入基本HTML结构和样式
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>明细分类账打印 - 全部</title>
          <style>
            @page {
              size: A4 portrait;
              margin: ${settings?.marginTop || 15}mm ${settings?.marginRight || 23}mm ${settings?.marginBottom || 15}mm ${settings?.marginLeft || 23}mm;
            }
            body {
              font-family: SimSun;
              font-size: 12px;
              margin: 0;
              padding: 0;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .page {
              page-break-after: always;
              position: relative;
              height: calc(297mm - ${settings?.marginTop || 15}mm - ${settings?.marginBottom || 15}mm);
              box-sizing: border-box;
              overflow: hidden;
            }
            .page:last-child {
              page-break-after: auto;
            }
            .title {
              text-align: center;
              font-size: 18px;
              font-weight: bold;
              margin: 10px 0;
            }
            .content-wrapper {
              min-height: calc(100% - 60mm);
              position: relative;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 10px;
              table-layout: fixed;
            }
            th, td {
              border: 1px solid black;
              padding: 3px;
              text-align: center;
              word-break: break-word;
              overflow: hidden;
            }
            td.amount {
              text-align: right;
            }
            td.summary {
              text-align: left;
            }
            .footer {
              position: absolute;
              bottom: 10mm;
              width: 100%;
              display: flex;
              justify-content: space-between;
            }
            .page-number {
              position: absolute;
              bottom: 2mm;
              width: 100%;
              text-align: center;
            }
            .signature {
              display: inline-block;
              margin-right: 20px;
            }
            .spacer {
              display: block;
              width: 100%;
            }
            .t_2_td_title {
              border: solid 1px black;
              text-align: center;
              font-size: 3.5mm;
            }
            .t_2_spacn {
              width: 150px;
              word-wrap: break-word;
              overflow-wrap: break-word;
              white-space: normal;
              word-break: break-word;
              font-family: SimSun;
              line-height: 18px;
            }
          </style>
          <script>
            // 设置总页码
            window.onload = function() {
              const totalPages = ${totalPages};
              const pageNumberEls = document.querySelectorAll('.page-number');
              pageNumberEls.forEach(el => {
                const pageText = el.textContent;
                if (pageText && pageText.indexOf('第') === 0) {
                  // 替换页码格式
                  const currentPage = pageText.replace('第', '').replace('页', '').trim();
                  el.textContent = '第' + currentPage + '页 / 共' + totalPages + '页';
                }
              });
            }
          </script>
        </head>
        <body>
      `);

      updateMainProgress('正在生成打印内容，请稍候...');

      // 写入所有内容页
      for (let i = 0; i < pageContents.length; i++) {
        if (i % 100 === 0) {
          updateMainProgress(`正在生成打印内容，处理第${i+1}/${pageContents.length}页...`);
        }

        const page = pageContents[i];
        if (page && page.content) {
          printWindow.document.write(page.content);
        }
      }

      // 写入所有目录页
      directoryPages.forEach(page => {
        if (page && page.content) {
          printWindow.document.write(page.content);
        }
      });

      printWindow.document.write('</body></html>');
      printWindow.document.close();

      // 打印前先检查内容
      setTimeout(() => {
        try {
          updateMainProgress('正在打开打印预览，请在弹出窗口中确认打印...');

          try {

            printWindow.onafterprint = function() {
              //console.log('所有内容打印完成');
              printWindow.close();

              // 所有打印任务完成
              if (loading) loading.close();
              if (vueInstance) vueInstance.$message.success(`打印完成，共${pageContents.length}页内容和${directoryPages.length}页目录已打印完成`);
            };

            // 执行打印
            setTimeout(function() {
              printWindow.print();
            }, 500);
          } catch (error) {
            console.error("打印错误:", error);
            if (loading) loading.close();
            if (vueInstance) vueInstance.$message.error('打印出错: ' + error.message);
          }

        } catch (error) {
          console.error("打印窗口处理错误:", error);
          if (loading) loading.close();
          if (vueInstance) vueInstance.$message.error('打印窗口处理错误: ' + error.message);
        }
      }, 1000);
    } catch (error) {
      console.error('一次性打印过程中出错:', error);
      if (loading) loading.close();
      if (vueInstance) vueInstance.$message.error('一次性打印过程中出错: ' + error.message);
    }
  }

  // 创建目录页面 - 使用左右结构（两列布局）
  const createDirectoryPage = (subjectPageMap, totalPages, reportMetaData = {}) => {
    // 确保subjectPageMap不为undefined
    const safeSubjectPageMap = subjectPageMap || {};
    const date = new Date();
    const printDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

    // 科目信息标准化处理
    // 收集所有科目并按顺序排列
    const subjects = Object.keys(safeSubjectPageMap).map(code => ({
      code,
      name: safeSubjectPageMap[code].itemName || '',
      page: safeSubjectPageMap[code].startPage + 1, // +1是因为从第1页开始计数
      order: safeSubjectPageMap[code].order || 0
    })).sort((a, b) => a.order - b.order);

    // 高度配置常量 (单位: mm)
    const MAX_TABLE_HEIGHT = 23; // 表体最大高度限制
    const HEADER_ROW_HEIGHT = 0.59; // 表头高度
    const BASE_ROW_HEIGHT = 0.45; // 基础行高

    // 估算科目名称产生的行高
    const estimateItemHeight = (itemName) => {
      if (!itemName) return BASE_ROW_HEIGHT;

      // 计算科目名称显示宽度
      const length = itemName.length;
      const chineseCharCount = itemName.replace(/[\u0000-\u00ff]/g, "").length;
      const otherCharCount = length - chineseCharCount;

      // 估算总宽度：中文字符宽度约为0.96，其他字符约为0.46
      const estimatedWidth = (chineseCharCount * 0.96) + (otherCharCount * 0.46);

      // 计算行数（一行大约能显示10个中文字符）
      const charPerLine = 10;
      const rowNum = Math.ceil(estimatedWidth / charPerLine);

      // 返回估算行高
      return rowNum * BASE_ROW_HEIGHT;
    };

    // 分页处理
    const pages = [];
    let currentPageItems = [];
    let currentPageHeight = HEADER_ROW_HEIGHT; // 初始高度包含表头

    // 每页最多显示20对科目（40个科目）
    const MAX_ITEMS_PER_DIRECTORY_PAGE = 20;

    // 按两列处理，确保相邻两个项目在同一行
    for (let i = 0; i < subjects.length; i += 2) {
      const leftItem = subjects[i];
      const rightItem = i + 1 < subjects.length ? subjects[i + 1] : null;

      // 计算这一行的高度（取左右两侧科目名称中较高的一个）
      const leftHeight = estimateItemHeight(leftItem.name);
      const rightHeight = rightItem ? estimateItemHeight(rightItem.name) : 0;
      const rowHeight = Math.max(leftHeight, rightHeight, BASE_ROW_HEIGHT);

      // 检查是否需要分页 - 根据高度或最大项目数限制
      if ((currentPageHeight + rowHeight > MAX_TABLE_HEIGHT || currentPageItems.length >= MAX_ITEMS_PER_DIRECTORY_PAGE) && currentPageItems.length > 0) {
        // 添加当前页到页面集合
        pages.push([...currentPageItems]);

        // 重置当前页
        currentPageItems = [];
        currentPageHeight = HEADER_ROW_HEIGHT;
      }

      // 添加当前项目到当前页
      currentPageItems.push({ leftItem, rightItem });
      currentPageHeight += rowHeight;
    }

    // 处理最后一页
    if (currentPageItems.length > 0) {
      pages.push([...currentPageItems]);
    }

    // 计算总页数
    const totalDirectoryPages = pages.length;

    // 存储所有目录页
    const directoryPages = [];

    // 分批创建目录页
    for (let pageIndex = 0; pageIndex < totalDirectoryPages; pageIndex++) {
      const currentPageRows = pages[pageIndex];

      // 计算当前目录页号码和总页数
      const currentPageNumber = pageIndex + 1; // 目录页从1开始独立计算
      const totalPagesWithDirectory = totalDirectoryPages; // 总页数只计算目录的总页数

      // 创建当前页的内容 - 确保格式完全一致
      let pageContent = `
        <div class="page">
          <div class="title">明细分类账-目录表</div>
          <div class="content-wrapper">
            <table style="width: 100%">
              <tr style="height: 5.9mm">
                <th style="width: 13%; border: solid 1px black; text-align: center; font-size: 3.5mm;">编号</th>
                <th style="width: 30%; border: solid 1px black; text-align: center; font-size: 3.5mm;">会计科目</th>
                <th style="width: 7%; border: solid 1px black; text-align: center; font-size: 3.5mm;">页码</th>
                <th style="width: 13%; border: solid 1px black; text-align: center; font-size: 3.5mm;">编号</th>
                <th style="width: 30%; border: solid 1px black; text-align: center; font-size: 3.5mm;">会计科目</th>
                <th style="width: 7%; border: solid 1px black; text-align: center; font-size: 3.5mm;">页码</th>
              </tr>
      `;

      // 添加每一行到表格
      for (let i = 0; i < currentPageRows.length; i++) {
        const { leftItem, rightItem } = currentPageRows[i];

        pageContent += `<tr>`;

        // 左侧科目
        pageContent += `
          <td style="border: solid 1px black; font-size: 3mm; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; font-family: SimSun; line-height: 18px;">${leftItem.code}</td>
          <td style="border: solid 1px black; text-align: center; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; font-family: SimSun; line-height: 18px;">${leftItem.name}</td>
          <td style="border: solid 1px black; font-size: 2.9mm; text-align: center;"><p>${leftItem.page}</p></td>
        `;

        // 右侧科目（如果存在）
        if (rightItem) {
          pageContent += `
            <td style="border: solid 1px black; font-size: 3mm; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; font-family: SimSun; line-height: 18px;">${rightItem.code}</td>
            <td style="border: solid 1px black; text-align: center; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; font-family: SimSun; line-height: 18px;">${rightItem.name}</td>
            <td style="border: solid 1px black; font-size: 2.9mm; text-align: center;"><p>${rightItem.page}</p></td>
          `;
        } else {
          // 空单元格填充 - 保持表格结构完整
          pageContent += `
            <td style="border: solid 1px black; font-size: 3mm;"></td>
            <td style="border: solid 1px black; text-align: center;"></td>
            <td style="border: solid 1px black; font-size: 2.9mm;"><p></p></td>
          `;
        }

        pageContent += `</tr>`;
      }

      // 完成页面内容 - 添加页脚和页码
      pageContent += `
            </table>
            <!-- 添加空白填充元素确保页码在底部 -->
            <div class="spacer" style="flex-grow: 1;"></div>
          </div>
          <div class="directory-page-number" style="position: absolute; bottom: 2mm; width: 100%; text-align: center; font-size: 12px;">第${currentPageNumber}页 / 共${totalPagesWithDirectory}页</div>
        </div>
      `;

      // 添加该页到目录页集合
      directoryPages.push({
        content: pageContent,
        pageNum: currentPageNumber
      });
    }

    return directoryPages;
  };