/*
 * @Descripttion:出纳账查询
 * @version:
 * @Author: 未知
 * @Date: 2021-11-08 10:56:09
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-11 13:53:53
 */
import request from '@/utils/request'

/**
 * @name:获取账套是否已经月结的标志
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getCashierFlag(params) {
  return request({
    url: '/financial/cashier/cashierSearch/getCashierFlag',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:页面
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/cashier/cashierSearch/page',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:导出当前页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function exportExcel(params) {
  return request({
    url: '/financial/cashier/cashierSearch/exportExcel',
    method: 'get',
    params,
    responseType: 'blob',
    withCredentials: true
  })
}

/**
 * @name:反月结
 * @param {String} booksId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancel(data) {
  return request({
    url: '/financial/cashier/cashierMonthlyBalance/cancel',
    method: 'post',
    data,
    withCredentials: true
  })
}

