/*
 * @Author: 未知
 * @Date: 2022-08-29 10:06:25
 * @LastEditTime: 2023-11-23 13:40:44
 * @LastEditors: Andy
 * @Description:
 * @FilePath: \b-ui\src\views\payment\approval\approvalApplication\config.js
 * ^-^
 */
export const tableColumns = [
  { type: 'selection', align: 'center', width: '50', fixed: 'left' },
  { type: 'index', label: '序号', width: '80', fixed: 'left' },
  // { label: '申请单位', prop: 'orgName', width: '150', showOverflowTooltip: true },
  { label: '审批事项名称', prop: 'matter', minWidth: '200', showOverflowTooltip: true, fixed: 'left', align: 'left' },
  { label: '申请编号', prop: 'serialNumber', width: '150', showOverflowTooltip: true, align: 'left' },
  { label: '资金用途', prop: 'fundsUse', width: '150', showOverflowTooltip: true, align: 'left' },
  { label: '申请详情', prop: 'details', width: '150', showOverflowTooltip: true, align: 'left' },
  { label: '结算方式', prop: 'settlementMethod', width: '100', showOverflowTooltip: true },
  { label: '支出类型', prop: 'expenditureTypeZh', width: '100', showOverflowTooltip: true },
  {
    label: '申请类型',
    prop: 'applicationType',
    width: '100',
    showOverflowTooltip: true,
    convert: {
      options: [
        { id: 0, text: '常规用款' },
        { id: 1, text: '收支相抵' },
        { id: 2, text: '冲减收入' },
        { id: 3, text: '非预算项目支出' },
        { id: 4, text: '银行代扣' },
        { id: 5, text: '扣减预算' },
        { id: 6, text: '非预算收支相抵' }
      ],
    }
  },
  { label: '银行账号', prop: 'payAccountNumber', width: '250', showOverflowTooltip: true },
  { label: '金额（元）', slotName: 'amount', width: '120', showOverflowTooltip: true, align: 'right' },
  // { label: '乡村振兴类别', prop: 'revitalizationType', width: '100' },
  { label: '审批状态', slotName: 'approvalStatus', width: '100' },
  { label: '当前环节', prop: 'nodeName', width: '100' },
  { label: '创建日期', slotName: 'creationTime', width: '100', showOverflowTooltip: true },
  { label: '申请人', prop: 'submitUserName', width: '100' },
  { label: '支付状态', prop: 'paymentState', width: '100' },
  { label: '操作', slotName: 'operation', width: '240', fixed: 'right' }
]

export const receiverTableColumns = [
  { type: 'selection', align: 'center', width: '50' },
  { label: '收款方银行账户名称*', minWith: '250', slotName: 'receiverAccount', prop: 'receiverAccount', showOverflowTooltip: true },
  { label: '收款方银行账号*', minWith: '250', slotName: 'receiverBankAccount', prop: 'receiverBankAccount', showOverflowTooltip: true },
  { label: '开户银行联行号', minWith: '25', slotName: 'receiverOpenAccountNumber', prop: 'receiverOpenAccountNumber', showOverflowTooltip: true },
  { label: '收款方开户银行*', minWith: '250', slotName: 'receiverOpenAccount', prop: 'receiverOpenAccount', showOverflowTooltip: true },
  { label: '金额*', minWith: '100', slotName: 'amountPayable', prop: 'amountPayable' },
  { label: '备注*', minWith: '200', slotName: 'remark', prop: 'remark', showOverflowTooltip: true }
]

export const selectReceiverTableColumns = [
  { type: 'selection', align: 'center', width: '50' },
  { label: '银行账号', prop: 'bankAccount' , width: 200},
  { label: '账户名称', prop: 'receiverName' , width: 250},
  { label: '联行号', prop: 'bankNumber' },
  { label: '银行类型', slotName: 'bankType' },
  { label: '开户网点名称', prop: 'bankName', width: 120, showOverflowTooltip: true },
  { label: '机构名', prop: 'orgName' },
  { label: '是否银行账户', prop: 'isBankAccount' , width: 100}
]

export const selectContractTableColumns = [
  { type: 'selection', align: 'center', width: '50',fixed:'left' },
  { label: '合同编号', prop: 'code', minWidth: 200,fixed:'left',align: 'left' },
  { label: '合同名称', prop: 'name', minWidth:250 ,fixed:'left',align: 'left'},
  { label: '付款期数', prop: 'payPeriod', minWidth: 120  },
  { label: '付款条件', prop: 'payCondition', minWidth: 120,align: 'left'  },
  { label: '计划支付金额', prop: 'pryMoney', minWidth: 150, filter: 'money', align: 'right' },
  { label: '剩余支付金额', prop: 'balance', minWidth: 150, filter: 'money', align: 'right' },
  { label: '本次支付金额', prop: 'nowPayMoney', minWidth: 180, filter: 'money', showOverflowTooltip: false },
  { label: '支付类型', prop:'payType', convert: { options: [{ id: '01', text: '进度款' }, { id: '02', text: '质保金额' }, { id: '03', text: '预付款' }] }, width: 120 }
]
