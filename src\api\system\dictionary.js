import request from '@/utils/request'
/**
 * @name: 数据字典下拉列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function dictionaryTree(params) {
  return request({
    url: `/system/dictionary/tree`,
    method: 'get',
    params: params
  })
}
/**
 * @name: 数据字典列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function dictionaryList(params) {
  return request({
    url: '/system/dictionary/page',
    method: 'get',
    params: params
  })
}
/**
 * 禁用无效接口
 * @param {*} data 查询参数
 */
export function setIsNotValid(data) {
  return request({
    url: '/system/dictionary/setIsNotValid',
    method: 'post',
    data: data

  })
}
/**
 * 启用有效接口
 * @param {*} data 查询参数
 */
export function setIsValid(data) {
  return request({
    url: '/system/dictionary/setIsValid',
    method: 'post',
    data: data

  })
}
/**
 * 编辑查看数据
 * @param {*} data 查询参数
 */
export function dictionarySearch(data) {
  return request({
    url: '/system/dictionary/get/' + data,
    method: 'get'
  })
}

/**
 * 保存接口
 * @param {*} data 查询参数
 */
export function dictionarySave(data) {
  return request({
    url: '/system/dictionary/save',
    method: 'post',
    data: data
  })
}
/**
 * 删除
 * @param {*} data 查询参数
 */
export function dictionaryDelete(data) {
  return request({
    url: '/system/dictionary/delete',
    method: 'post',
    data: data
  })
}

/**
 * @name:获取数据字典数据
 * @description:
 * @param {*} path 数据字典路径
 * @return {*}
 * @msg:
 */
export function combotree({ path }) {
  return request({
    url: `/system/dictionary/combotree`,
    method: 'get',
    params: { path }
  })
}
