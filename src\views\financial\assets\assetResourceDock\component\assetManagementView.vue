<!--
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-06-05 15:32:54
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-06-10 09:56:28
 * @FilePath: \rural-financial-dev-normal\src\views\financial\assets\assetResourceDock\component\assetManagementView.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <public-drawer
    :title="$t('查看资产信息')"
    :visible="assetManagementVisible"
    :buttons="[]"
    :close-on-click-modal="false"
    :size="70"
    @close="handleClose"
  >
    <component
      :is="assetManagementData.currentComponent"
      v-if="assetManagementVisible"
      :id="assetManagementData.assetId"
      :newest="'0'"
      :drawer-title="drawerTitle"
      :type="'view'"
    />
  </public-drawer>
</template>

<script>
export default {
    props: {
        assetManagementVisible: {
            type: Boolean,
            default: false
        },
        assetManagementData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            drawerTitle: '查看资产信息',
            assetId: '',
            currentComponent: ''
        }
    },
    methods: {
        handleClose() {
            this.$emit('update:assetManagementVisible', false)
        }
    }
}
</script>

<style lang="scss" scoped></style>
