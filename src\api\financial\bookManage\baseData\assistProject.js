/*
 * @Descripttion:辅助核算项目接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-27 17:48:37
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-10-29 15:38:56
 */

import request from '@/utils/request'

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/books/basedata/assistProject/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取辅助核算项目
 * @param {String} booksId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listByBooksId({ booksId = '' }) {
  return request({
    url: '/financial/books/basedata/assistProject/listByBooksId',
    method: 'post',
    data: { booksId },
    withCredentials: true
  })
}

/**
 * @name:保存辅助核算项目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/books/basedata/assistProject/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:删除辅助核算项目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteWithBooks(data) {
  return request({
    url: `/financial/books/basedata/assistProject/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

export function saveImportTemp(data) {
  return request({
    url: `/financial/books/basedata/assistProject/saveImportTemp`,
    method: 'post',
    data,
    withCredentials: true
  })
}

