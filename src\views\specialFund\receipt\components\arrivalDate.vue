<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-21 10:52:13
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-08 17:57:21
-->
<template>
  <!-- <el-dialog :title="title" width="30%" :visible.sync="dialogVisible" :before-close="handleClose"> -->
  <div class="gever-form detail">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="100px"
      :label-position="'left'"
      style="margin:20px"
    >
      <el-form-item :label="$t('金额')">
        <el-input v-model="contentForm.totalAmountStr" disabled style="width:240px" />
      </el-form-item>
      <el-form-item :label="$t('出纳凭证号')" prop="cashierVoucherNumber">
        <el-input v-model="contentForm.cashierVoucherNumber" style="width:240px" disabled>
          <el-button slot="append" style="background-color: #1890ff;color:#f5f7fa" @click="cashierClick">选择</el-button>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('入账日期')">
        <el-date-picker
          v-model="contentForm.arrivalDate"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="date"
          style="width:240px"
          disabled
        />
        <div style="color:red;font-size:10px">说明：入账日期为这笔专项资金收入的出纳日期</div>
      </el-form-item>
    </el-form>
    <public-drawer
      :visible.sync="cashierVisible"
      :title="'关联出纳账'"
      :size="65"
      :buttons="cashierButtons"
      @close="handleCloseDetail"
    >
      <cashier
        v-if="cashierVisible"
        ref="cashierRef"
        :visible.sync="cashierVisible"
        :org-id="orgId"
        :bank-account-number="bankAccountNumber"
        :total-amount="totalAmount"
        @close="handleCloseDetail"
        @cashierSave="cashierSave"
      />
    </public-drawer>
  </div>
  <!-- <span slot="footer" class="dialog-footer" style="text-align: center;">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </span>
  </el-dialog>-->
</template>

<script>
import { updateArrivalDate } from '@/api/specialFund/receipt.js'
import cashier from './cashier.vue'
export default {
  components: {
    cashier
  },
  props: {
    ids: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    totalAmount: {
      type: Number,
      default: 0.00
    },
    orgId: {
      type: String,
      default: ''
    },
    bankAccountNumber: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dialogVisible: false,
      contentForm: {
        arrivalDate: '',
        id: ''
      },
      rules: {
        arrivalDate: [{ required: true, message: '请选择达账日期', trigger: 'change' }],
        cashierVoucherNumber: [{ required: true, message: '出纳凭证号不能为空', trigger: 'change' }]
      },
      totalAmountStr: '',
      cashierVisible: false,
      cashierButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('确 定'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
    }
  },
  created () {
    this.contentForm.arrivalDate = ''
    console.log('123131', this.totalAmount.toLocaleString())

    this.contentForm.totalAmountStr = this.totalAmount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },
  methods: {
    save () {
      this.$refs['_geverFormRef'].validate(valid => {
        if (this.contentForm.cashierVoucherId == undefined || this.contentForm.cashierVoucherId == '' || this.contentForm.cashierVoucherId == null) {
          return this.$message.warning(this.$t('未关联出纳账信息'))
        }
        const data = {
          ids: this.ids,
          arrivalDate: this.contentForm.arrivalDate,
          cashierVoucherNumber: this.contentForm.cashierVoucherNumber,
          debit: this.contentForm.debit,
          cashierVoucherId: this.contentForm.cashierVoucherId,
          isHistory: 1
        }
        updateArrivalDate(data).then(res => {
          this.$message.success(this.$t('操作成功'))
          this.$emit('refreshTable')
          this.$emit('close')
          // this.handleClose()
        }).finally(() => {
        })
      })
    },
    handleClose () {
      // this.contentForm.arrivalDate = ''
      this.$refs['_geverFormRef'].resetFields()
      this.dialogVisible = false
    },
    cashierClick () {
      this.cashierVisible = true
    },
    handleCloseDetail () {
      this.cashierVisible = false
    },
    handleSave () {
      console.log('222')
      this.$refs.cashierRef.save()
    },
    cashierSave (val) {
      console.log('1111', val)
      if (val[0].unpostedAmount < this.totalAmount) {
        return this.$message.warning(this.$t('入账金额超出未入账金额'))
      }
      this.$set(this.contentForm, 'cashierVoucherNumber', val[0].typeNumber)
      this.$set(this.contentForm, 'arrivalDate', val[0].businessDate)
      this.$set(this.contentForm, 'debit', val[0].debit)
      this.$set(this.contentForm, 'cashierVoucherId', val[0].id)
      console.log('this.contentForm', this.contentForm)

      this.cashierVisible = false
    }
  }
}
</script>

<style>
