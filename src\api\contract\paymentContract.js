import request from '@/utils/request'

// 获取付款合同类型
export function getContractTypeDict() {
  return request({
    url: '/contract/contractType/getContractTypeDictPayment',
    method: 'get'
  })
}

// 付款合同列表
export function loadContractList(data) {
  return request({
    url: '/contract/paymentContract/pageX',
    method: 'post',
    data
  })
}

// 获取时间间隔
export function getDiffDate(params) {
  return request({
    url: '/contract/tools/getDiffDate',
    method: 'get',
    params
  })
}

// 获取付款计划明细
export function loadPlanDetail(data) {
  return request({
    url: '/contract/paymentContract/plan_detail',
    method: 'post',
    data,
    payload: true
  })
}

// 保存合同
export function saveContract(data) {
  return request({
    url: '/contract/paymentContract/saveX',
    method: 'post',
    data
  })
}

// 获取合同详情
export function loadContract(id) {
  return request({
    url: '/contract/paymentContract/loadX/' + id,
    method: 'get'
  })
}

// 合同跟踪前检查
export function checkTrackType(data) {
  return request({
    url: '/contract/paymentContract/checkTrackType',
    method: 'post',
    data
  })
}

// 合同提交
export function submitBatch(data) {
  return request({
    url: '/contract/workflow/submitBatch',
    method: 'post',
    data
  })
}

// 合同删除
export function removeContract(data) {
  return request({
    url: '/contract/paymentContract/deleteX',
    method: 'post',
    data
  })
}

// 撤销提交
export function unSubmitContract(data) {
  return request({
    url: '/contract/workflow/revocation',
    method: 'post',
    data
  })
}

// 获取合同付款详情
export function getPaymentDetail(id, data) {
  return request({
    url: '/contract/paymentContract/getPaymentDetail/' + id,
    method: 'post',
    data
  })
}

// 合同扣减费用前检查
export function checkDeducte(data) {
  return request({
    url: '/contract/paymentContract/checkDeducte',
    method: 'post',
    data
  })
}

// 获取扣减详情
export function getDeductionDetail(contractId, deducteId, data) {
  return request({
    url: '/contract/paymentContract/getDeductionDetail/' + contractId + '/' + deducteId,
    method: 'post',
    data
  })
}

// 保存扣减
export function saveDeduction(data) {
  return request({
    url: '/contract/paymentContract/saveDeduction',
    method: 'post',
    data
  })
}

// 获取扣减列表
export function getDeductionList(data) {
  return request({
    url: '/contract/paymentContract/getDeduction',
    method: 'post',
    data
  })
}

// 删除扣减
export function removeDeduction(id) {
  return request({
    url: '/contract/paymentContract/delDeductionDetail/' + id,
    method: 'post',
    data: { id }
  })
}

// 查看变更记录前检查
export function checkChangeRecord(id) {
  return request({
    url: '/contract/paymentContract/checkChangeRecord/' + id,
    method: 'post'
  })
}

// 合同终止前检查
export function terminationCheck(id) {
  return request({
    url: '/contract/paymentContract/terminationCheck/' + id,
    method: 'post'
  })
}

// 查看合同终止前检查
export function checkTerminationInfo(id) {
  return request({
    url: '/contract/paymentContract/checkTerminationInfo/' + id,
    method: 'post'
  })
}

// 获取合同终止信息
export function loadTerm(id, _ood) {
  return request({
    url: '/contract/paymentContract/loadTerm/' + id,
    method: 'get',
    params: { _ood }
  })
}

// 保存并提交合同终止
export function submitTerm(data) {
  return request({
    url: '/contract/paymentContract/saveAndSubmitTerm',
    method: 'post',
    data
  })
}

// 合同终止时检查
export function checkPayment(data) {
  return request({
    url: '/contract/paymentContract/checkPayment',
    method: 'post',
    data
  })
}

// 合同变更列表
export function contractChangeList(data) {
  return request({
    url: '/contract/paymentContractChange/pageX',
    method: 'post',
    data
  })
}

// 待变更的合同列表
export function getContractForChange(data) {
  return request({
    url: '/contract/paymentContractChange/getContractForChange',
    method: 'post',
    data
  })
}

// 获取合同变更数据
export function loadContractChange(id, params) {
  return request({
    url: '/contract/paymentContractChange/loadX/' + id,
    method: 'get',
    params
  })
}

// 保存合同变更
export function saveContractChange(data) {
  return request({
    url: '/contract/paymentContractChange/saveX',
    method: 'post',
    data
  })
}

// 删除合同变更
export function removeContractChange(data) {
  return request({
    url: '/contract/paymentContractChange/deleteX',
    method: 'post',
    data
  })
}

// 编辑合同变更前判断
export function checkToEdit(id) {
  return request({
    url: '/contract/paymentContractChange/checkToEdit/' + id,
    method: 'post'
  })
}

// 合同付款列表
export function contractPaymentList(data) {
  return request({
    url: '/contract/contractPayment/page',
    method: 'post',
    data
  })
}

// 获取最新合同id
export function getNewerCtId(id) {
  return request({
    url: '/contract/paymentContract/getNewerCtId/' + id,
    method: 'get'
  })
}

// 获取付款记录
export function getPayment(id) {
  return request({
    url: '/contract/contractPayment/get/' + id,
    method: 'get'
  })
}

// 删除合同付款记录
export function removeContractPayment(data) {
  return request({
    url: '/main/contract/contractPayment/deleteX',
    method: 'post',
    data
  })
}

// 获取可付款合同列表
export function paymentList(data) {
  return request({
    url: '/contract/contractPayment/paymentList',
    method: 'post',
    data
  })
}

// 保存合同付款、收回
export function saveContractPayment(data) {
  return request({
    url: '/contract/contractPayment/saveX',
    method: 'post',
    data
  })
}

// 获取付款项目列表
export function getPaymentItems(params) {
  return request({
    url: '/contract/contractPayment/combobox',
    method: 'get',
    params
  })
}

// 获取付款项目的历史付款金额
export function getHistoryMoney(params) {
  return request({
    url: '/contract/contractPayment/getHistoryMoney',
    method: 'post',
    params
  })
}

// 获取收回款项详情
export function loadRefund(id) {
  return request({
    url: '/contract/contractPayment/load/' + id,
    method: 'get'
  })
}

// 付款合同编辑检查
export function paymentContractCheckToEdit(id) {
  return request({
    url: '/contract/paymentContract/checkToEdit/' + id,
    method: 'post'
  })
}

// 合同变更记录列表
export function getChangeRecordData(data) {
  return request({
    url: '/contract/paymentContract/getChangeRecordData',
    method: 'post',
    data
  })
}
