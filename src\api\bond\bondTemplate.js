/*
 * @Description: 
 * @Version: 
 * @Author: luozc
 * @Date: 2023-10-07 09:30:17
 * @LastEditors: luozc
 * @LastEditTime: 2023-10-11 12:28:57
 */
import request from '@/utils/request'

/**
 * 分页查询债券模板列表
 * @param {*} data 
 * @returns 
 */
 export function getBondTemplateList(data) {
    return request({
        url: '/bond/bondTemplate/page',
        method: 'post',
        data
    })
}

/**
 * 查询有效债券模板列表
 * @param {*} data 
 * @returns 
 */
export function getValidBondTemplateList(params) {
    return request({
        url: '/bond/bondTemplate/validList',
        method: 'post',
        params: params
    })
}

/**
 * 修改债券模板信息
 * @param {*} data 
 * @returns 
 */
export function save(data) {
    return request({
        url: '/bond/bondTemplate/save',
        method: 'post',
        data,
        payload: true
    })
}

/**
 * 查看模板详情
 * @param {*} id 
 * @returns 
 */
export function getBondTemplate(id) {
    return request({
        url: `/bond/bondTemplate/get/${id}`,
        method: 'get'
    })
}