import request from '@/utils/request'
/**
 * @name: 用户列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function ognUser(params) {
  return request({
    url: '/system/user/page',
    method: 'get',
    params: params
  })
}
/**
 * @name: 用户地区显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function userTreeData(query) {
  return request({
    url: '/system/area/treeData',
    method: 'get',
    params: query
  })
}

/**
 * @name: 用户编辑查看数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function userLoad(data) {
  return request({
    url: `/system/user/load/${data}`,
    method: 'get'
  })
}

/**
 * @name: 角色列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadRolesList(params) {
  return request({
    url: '/system/role/page',
    method: 'get',
    params: params
  })
}
/**
 * @name: 编辑查看角色信息
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadRole(query) {
  return request({
    url: `/system/role/load/${query}`,
    method: 'get'
  })
}
/**
 * @name: 角色删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteRole(data) {
  return request({
    url: '/system/role/delete',
    method: 'post',
    data: data
  })
}
/**
 * @name: 新增角色
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveRole(query) {
  return request({
    url: '/system/role/save',
    method: 'post',
    data: query
  })
}
/**
 * @name: 角色授权
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getRoleAuthTree(query) {
  return request({
    url: '/system/roleAuth/tree',
    method: 'get',
    params: query
  })
}
export function saveRoleAuth(query) {
  return request({
    url: '/system/roleAuth/batchSave/' + query.id,
    method: 'post',
    payload: true,
    data: query.params
  })
}

export function getRoleUser(data) {
  return request({
    url: '/system/role/list_user',
    method: 'get',
    params: data
  })
}
/**
 * @name: 地区列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadAreaList(query) {
  return request({
    url: '/system/area/page',
    method: 'get',
    params: query
  })
}
/**
 * @name: 地区编辑
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadAreaInfo(query) {
  return request({
    url: '/system/area/load/' + query,
    method: 'get'
  })
}
/**
 * @name: 地区删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteArea(query) {
  return request({
    url: '/system/area/delete',
    method: 'post',
    data: query
  })
}
/**
 * @name: 新增地区保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveArea(query) {
  return request({
    url: '/system/area/save',
    method: 'post',
    data: query
  })
}
/**
 * @name: 机构列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadOrgList(query) {
  return request({
    url: '/system/organization/page',
    method: 'get',
    params: query
  })
}
/**
 * @name: 获取机构字典
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadOrgType(query) {
  return request({
    url: `/system/dictionary/combotree?path=${query.path}`,
    method: 'get',
    data: query
  })
}
/**
 * @name: 新增机构保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveOrg(query) {
  return request({
    url: '/system/organization/save',
    method: 'post',
    data: query
  })
}
/**
 * @name: 新增机构删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteOrg(query) {
  return request({
    url: '/system/organization/delete',
    method: 'post',
    data: query
  })
}
/**
 * @name: 编辑查看接口
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadOrg(query) {
  return request({
    url: '/system/organization/load/' + query,
    method: 'get'
  })
}

/**
 * 获取用户列表
 * @param {*} data 查询参数
 */
export function loadAuthorityUserList(data) {
  return request({
    url: '/system/user/page',
    method: 'get',
    params: data
  })
}
/**
 * 保存用户
 * @param {*} data 查询参数
 */
export function loadSave(data) {
  return request({
    url: '/system/user/save',
    method: 'post',
    data: data

  })
}
/**
 * 所有数据授权
 * @param {*} data 查询参数
 */
export function userOrgTree(data) {
  return request({
    url: `/system/user/dataAuth/orgTree/${data}`,
    method: 'get'

  })
}
/**
 * 数据授权选中数据
 * @param {*} data 查询参数
 */
export function getDataAuth(data) {
  return request({
    url: `/system/user/getDataAuth?userId=${data.userId}`,
    method: 'get'

  })
}
/**
 * 数据授权保存
 * @param {*} data 查询参数
 */
export function saveDataAuth(parmes, data) {
  return request({
    url: `/system/user/saveDataAuth/${parmes}`,
    method: 'post',
    data: data,
    payload: true

  })
}
/**
 * 用户删除
 * @param {*} data 查询参数
 */
export function Userdelete(data) {
  return request({
    url: '/system/user/delete',
    method: 'post',
    data: data

  })
}
/**
 * 用户修改密码保存
 * @param {*} data 查询参数
 */
export function saveUserpw(data) {
  return request({
    url: '/system/user/save_user_pw',
    method: 'post',
    data: data

  })
}
