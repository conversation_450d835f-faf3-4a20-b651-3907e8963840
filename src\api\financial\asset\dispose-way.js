/*
 * @Descripttion:会计凭证类型
 * @version:
 * @Author: 未知
 * @Date: 2021-10-29 16:40:25
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-16 10:54:00
 */

import request from '@/utils/request'

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/asset/assetDisposeWay/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// /**
//  * @name:获取出纳凭证类型类型
//  * @param {String} booksId 登陆的账套id
//  * @description:
//  * @return {*}
//  * @test:
//  * @msg:
//  */
// export function listByBooksId({ booksId = '' }) {
//   return request({
//     url: '/financial/books/basedata/cashiersettlementtype/listByBooksId',
//     method: 'post',
//     data: { booksId },
//     mock: process.env.VUE_APP_REAL,
//     withCredentials: true
//   })
// }

/**
 * @name:保存出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/asset/assetDisposeWay/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/asset/assetDisposeWay/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:删除出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/asset/assetDisposeWay/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

