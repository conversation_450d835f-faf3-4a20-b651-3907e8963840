import request from '@/utils/request'

/* 列表 */
export function page(data) {
  return request({
    url: '/bill/sundryBillStockIn/page',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 保存/编辑 */
export function save(data) {
  return request({
    url: '/bill/sundryBillStockIn/save',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 查看 */
export function load(id) {
  return request({
    url: `/bill/sundryBillStockIn/load/${id}`,
    method: 'get'
  })
}

/* 删除 */
export function deleteData(data) {
  return request({
    url: `/bill/sundryBillStockIn/delete`,
    method: 'post',
    data
  })
}

/* 发票类型列表 */
export function loadTypeList() {
  return request({
    url: `/bill/sundryBillType/loadTypeList`,
    method: 'get'
  })
}
