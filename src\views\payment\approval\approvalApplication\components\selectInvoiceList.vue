<template>
  <div class="ReceiverInvoiceList">
    <el-form :inline="true" size="small" style="margin-bottom: 12px" :disabled="loadType == 'view'">
      <el-form-item label="发票号码">
        <el-input v-model="searchForm.invoiceNo" placeholder="请输入发票号码" clearable style="width: 150px;"/>
      </el-form-item>
      <el-form-item label="销售方名称">
        <el-input v-model="searchForm.sellerName" placeholder="请输入销售方名称" clearable style="width: 150px;"/>
      </el-form-item>
      <el-form-item label="金额范围">
        <el-input v-model="searchForm.minAmount" placeholder="最小金额" style="width: 100px;" clearable />
        <span style="margin: 0 4px;">-</span>
        <el-input v-model="searchForm.maxAmount" placeholder="最大金额" style="width: 100px;" clearable />
      </el-form-item>
      <el-form-item label="开票日期">
        <el-date-picker
          v-model="searchForm.startDate"
          type="date"
          placeholder="开始日期"
          value-format="yyyy-MM-dd"
          style="width: 140px;"
          :picker-options="startDatePickerOptions"
          clearable
        />
        <span style="margin: 0 4px;">-</span>
        <el-date-picker
          v-model="searchForm.endDate"
          type="date"
          placeholder="结束日期"
          value-format="yyyy-MM-dd"
          style="width: 140px;"
          :picker-options="endDatePickerOptions"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleInvoiceConfirm">确定</el-button>
        <el-button type="primary" @click=" handleInvoiceConfirmAndClose">确定并关闭</el-button>

        <span style="color: red; margin-left: 12px; font-size: 14px; vertical-align: middle;">
          收款方金额：{{ Number(rowData.amountPayable || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}元
        </span>
      </el-form-item>
    </el-form>

    <gever-table
      ref="_geverTableRef"
      v-loading="tableLoading"
      :columns="tableColumns"
      :data="tableList"
      :pagination="true"
      :total="total"
      :height="500"
      row-key="id"
      :pagi="searchForm"
      :pageSizes="[50, 150, 300]"
      @p-current-change="getList"
      @size-change="getList"
      @selection-change="handleSelectionChange"
    >
      <template #invoiceType="{ row }">
        {{ invoiceTypeText(row.invoiceType) }}
      </template>
      <template #operation="{ row }">
        <el-button
          type="text"
          @click="handleViewInvoice(row)"
        >
          {{ $t('查看') }}
        </el-button>
      </template>
    </gever-table>
    <!-- 发票详情页 -->
    <InvoiceDetailView
      v-if="digitalDetailsVisible"
      :dialog-visible.sync="digitalDetailsVisible"
      :row-data="nowRowDatac"
      @updateList="getList"
    >
    </InvoiceDetailView>
  </div>
</template>

<script>
import { getReceiverInvoiceList } from '@/api/payment/approval.js'
import InvoiceDetailView from '@/views/financial/invoice/invoiceDetailXw/components/digitalDetails.vue'

export default {
  name: 'ReceiverInvoiceList',
  components: {
    InvoiceDetailView
  },
  props: {
    rowData: {
      type: Object,
      default: () => ({})
    },
    orgCode: {
      type: String,
      default: ''
    },
    loadType: {
      type: String,
      default: 'add'
    },
    isRefund: {
      type: Number,
      default: 0
    },
    invoiceIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableLoading: false,
      tableList: [],
      total: 0,
      tableColumns: [
        { type: 'selection', align: 'center', width: '40', fixed: 'left', selectable: this.handleSelectable },
        { type: 'index', label: '序号', width: '50', fixed: 'left' },
        { label: '发票号码', prop: 'invoiceNo', minWidth: '150', showOverflowTooltip: true },
        { label: '开票日期', prop: 'drawDate', minWidth: '120', showOverflowTooltip: true },
        { label: '价税合计金额', prop: 'totalAmount', filter: 'money', minWidth: '120', align: 'right', showOverflowTooltip: true },
        { label: '未使用金额', prop: 'unRelatedAmount', minWidth: '120', align: 'right', showOverflowTooltip: true },
        { label: '付款方名称', prop: 'payerName', minWidth: '180', showOverflowTooltip: true },
        { label: '销售方名称', prop: 'sellerName', minWidth: '180', showOverflowTooltip: true },
        { label: '摘要', prop: 'summary', minWidth: '120', showOverflowTooltip: true },
        { label: '操作', slotName: 'operation', width: '80', fixed: 'right' }
      ],
      selectArr: [],
      searchForm: {
        invoiceNo: '',
        minAmount: '',
        maxAmount: '',
        startDate: '',
        endDate: '',
        page: 1,
        rows: 50
      },
      startDatePickerOptions: {
        disabledDate: (date) => {
          if (this.searchForm.endDate) {
            // 允许等于结束日期
            return date.getTime() > new Date(this.searchForm.endDate).getTime()
          }
          return false
        }
      },
      endDatePickerOptions: {
        disabledDate: (date) => {
          if (this.searchForm.startDate) {
            // 允许等于开始日期
            return date.getTime() < new Date(this.searchForm.startDate).setHours(0, 0, 0, 0)
          }
          return false
        }
      },
      digitalDetailsVisible: false,
      nowRowDatac: {}
    }
  },
  created() {

  },
  methods: {
    async getList() {
      // 校验金额范围
      if (this.searchForm.minAmount && this.searchForm.maxAmount) {
        if (Number(this.searchForm.minAmount) > Number(this.searchForm.maxAmount)) {
          this.$message.warning('最小金额不能大于最大金额')
          return
        }
      }
      // 校验日期范围
      if (this.searchForm.startDate && this.searchForm.endDate) {
        if (this.searchForm.startDate > this.searchForm.endDate) {
          this.$message.warning('开始日期不能大于结束日期')
          return
        }
      }
      this.tableLoading = true
      try {
        this.searchForm.orgCode = this.orgCode
        /* const params = {
          orgCode: this.orgCode,
          invoiceNo: this.searchForm.invoiceNo,
          minAmount: this.searchForm.minAmount,
          maxAmount: this.searchForm.maxAmount,
          startDate: this.searchForm.startDate,
          endDate: this.searchForm.endDate,
          sellerName: this.searchForm.sellerName,
          // 添加分页参数
          page: this.searchForm.page,
          rows: this.searchForm.rows
        }*/

        const { data } = await getReceiverInvoiceList(this.searchForm)
        this.tableList = data.rows
        this.total = data.total
        this.initTable()
      } finally {
        this.tableLoading = false
      }
    },
    handleReset() {
      this.searchForm.invoiceNo = ''
      this.searchForm.totalAmount = ''
      this.getList()
    },
    invoiceTypeText(val) {
      if (val === 0 || val === '0') return '销项发票'
      if (val === 1 || val === '1') return '进项发票'
      return ''
    },
    handleSelectionChange(val) {
      this.selectArr = val
      // 触发事件，将选中项传递给父组件
      this.$emit('selection-change', val)
    },
    handleInvoiceConfirm() {
      this.$emit('handleInvoiceConfirm', this.selectArr)
    },
    handleInvoiceConfirmAndClose() {
      this.$emit('handleInvoiceConfirmAndClose', this.selectArr)
    },
    handlePayAmountInput(row) {
      // 实时校验，防止输入超限
      let val = String(row.payAmount || '')
      val = val.replace(/\.(?=.*\.)/g, '')
     // val = val.replace(/(\.\d{2}).+/, '$1')
      if (this.loadType === 'refund' || this.isRefund == 1) {
        if (Number(val) > 0) {
          this.$message.warning('退款时只能输入负数金额')
          this.$set(row, 'payAmount', '')
          return
        }
        if (row.sourceAmount && Number(val) < Number(row.sourceAmount)) {
          console.log('row.sourceAmount:', row.sourceAmount)
          this.$message.warning('退款金额不能大于原支出金额')
          this.$set(row, 'payAmount', row.sourceAmount)
          return
        }
      } else {
        if (Number(val) > row.unRelatedAmount) {
          this.$message.warning('本次关联金额不能大于未关联金额')
          this.$set(row, 'payAmount', row.unRelatedAmount.toFixed(2))
        }
      }
    },
    handlePayAmountBlur(row) {
      // 失焦时做最终修正
      let val = String(row.payAmount || '')
      if (!val) {
        this.$set(row, 'payAmount', '')
        return
      }
      this.$set(row, 'payAmount', Number(val).toFixed(2))
      if (this.$refs._geverTableRef) {
        this.$refs._geverTableRef.toggleRowSelection(row, true)
      }
    },
    handleSelectable(row, index) {
        return !this.invoiceIds.includes(row.id)
    },
    initTable() {
      this.$nextTick(() => {
        this.$refs._geverTableRef.clearSelection()
        // 已关联的数据选中并填写支出金额
         for (let i = 0; i < this.tableList.length; i++) {
          const unRelatedAmount = ((this.tableList[i].totalAmount * 100 - (this.tableList[i].relatedAmount || 0) * 100) / 100)
          this.$set(this.tableList[i], 'unRelatedAmount', unRelatedAmount.toFixed(2))
          if (this.rowData.invoiceList) {
            this.rowData.invoiceList.forEach(x => {
              if (x.invoiceId == this.tableList[i].id) {
              //  this.$refs._geverTableRef.toggleRowSelection(this.tableList[i], true)
                this.$set(this.tableList[i], 'payAmount', x.payAmount ? Number(x.payAmount).toFixed(2) : unRelatedAmount)
                if (this.loadType == 'edit') {
                  this.$set(this.tableList[i], 'unRelatedAmount', (unRelatedAmount + x.payAmount).toFixed(2))
                }
                this.$set(this.tableList[i], 'sourceAmount', Number(x.sourceAmount ? -x.sourceAmount : x.payAmount).toFixed(2))
              }
            })
          }
        }
        /* if (!this.rowData.invoiceList) {
          return
        }
        // 把已关联的数据排到前面
        const fundIds = []
        this.rowData.invoiceList.forEach(x => {
          fundIds.push(x.invoiceId)
        })
        this.tableList.sort((a, b) => {
          const aIn = fundIds.includes(a.id)
          const bIn = fundIds.includes(b.id)
          if (aIn && !bIn) return -1
          if (!aIn && bIn) return 1
          return 0
        }) */
      })
    },
    // 添加分页相关方法
    handlePageChange(page) {
      this.searchForm.page = page
      this.getList()
    },
    handleSizeChange(rows) {
      this.searchForm.rows = 1
      this.searchForm.rows = rows
      this.getList()
    },
    handleViewInvoice(row) {
      this.nowRowDatac = row
      this.digitalDetailsVisible = true
    }
  }
}
</script>
