import request from '@/utils/request'
export function getBookTypeList(data) {
    return request({
        url: '/financial/report/getBookTypeList',
        method: 'post',
        data
    })
}

export function getAuthBooksOrgTree(data) {
    return request({
        url: '/financial/report/getAuthBooksOrgTree',
        method: 'post',
        data
    })
}

export function reportList(data) {
    return request({
        url: '/financial/report/reportList',
        method: 'post',
        data
    })
}

//报表查询
export function getReportList(data, reportId) {
    return request({
        url: `/financial/report/get?reportId=${reportId}&meta=false`,
        method: 'post',
        data: data,
        withCredentials: true,
        timeout: 180000
    })
}