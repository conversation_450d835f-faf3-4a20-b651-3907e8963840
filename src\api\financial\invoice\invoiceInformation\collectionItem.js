/*
 * @Descripttion: 
 * @version: 
 * @Author: 未知
 * @Date: 2023-10-09 16:59:46
 * @LastEditors: Andy
 * @LastEditTime: 2023-10-11 11:02:27
 */
import request from '@/utils/request'

/*
export function demo(data) {
  return request({
    url: ``,
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}
*/

/*
export function demo(params) {
  return request({
    url: ``,
    method: 'get',
    params,
    // payload: true,
  })
}
*/

/* 列表 */
export function page(data) {
  return request({
    url: '/invoice/invoiceCollectionItem/page',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 保存/编辑 */
export function save(data) {
  return request({
    url: '/invoice/invoiceCollectionItem/save',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 查看 */
export function load(id) {
  return request({
    url: `/invoice/invoiceCollectionItem/load/${id}`,
    method: 'get'
  })
}

/* 删除 */
export function deleteData(data) {
  return request({
    url: `/invoice/invoiceCollectionItem/delete`,
    method: 'post',
    data
  })
}
