<template>
  <gever-dialog
    :title="$t(`发票注册订购${title}`)"
    :visible="dialogVisible"
    style="margin-top: 12vh"
    width="980px"
    @close="handleClose"
  >
    <el-form
      ref="_formRef"
      :inline="true"
      :model="form"
      :rules="rules"
      label-position="right"
      label-width="120px"
      class="gever-form"
      :disabled="statusType === 'view'"
    >
      <div class="form-sg">
        <el-form-item :label="$t('单位名称')" prop="orgName">
          <el-input v-model="form.orgName" type="text" :disabled="statusType === 'renew'" :maxlength="100" />
        </el-form-item>
        <el-form-item :label="$t('终端ID')" prop="spId">
          <el-input v-model="form.spId" type="text" disabled />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('纳税人类型')" prop="taxpayerType">
          <gever-select
            v-model="form.taxpayerType"
            path="/financial/invoice/taxpayerType/"
            :disabled="statusType === 'renew'"
          />
        </el-form-item>
        <el-form-item :label="$t('纳税人识别号')" prop="taxpayerNo">
          <el-input
            v-model="form.taxpayerNo"
            type="text"
            :maxlength="50"
            :disabled="form.registryFlag === '1'"
          />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('已注册订购')" prop="registryFlag">
          <el-switch
            v-model="form.registryFlag"
            active-value="1"
            inactive-value="0"
            active-color="#13ce66"
            disabled
          />
        </el-form-item>
        <el-form-item :label="$t('发票服务商')" prop="registryType">
          <gever-select
            v-model="form.registryType"
            path="/financial/invoice/registryType/"
            :disabled="form.registryFlag === '1'"
          />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('地址')" prop="contactAddress">
          <el-input
            v-model="form.contactAddress"
            type="text"
            :maxlength="200"
            :disabled="statusType === 'renew'"
          />
        </el-form-item>
        <el-form-item :label="$t('电话')" prop="contactPhone">
          <el-input v-model="form.contactPhone" type="text" :disabled="statusType === 'renew'" :maxlength="20" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('开户银行')" prop="accountName">
          <el-input v-model="form.accountName" type="text" :disabled="statusType === 'renew'" :maxlength="100" />
        </el-form-item>
        <el-form-item :label="$t('银行账号')" prop="accountNo">
          <el-input v-model="form.accountNo" type="text" :disabled="statusType === 'renew'" :maxlength="50" />
        </el-form-item>
      </div>
      <div v-if="!['view', 'renew'].includes(statusType)" class="form-sg">
        <el-form-item :label="$t('电子税务局账号')" prop="swjAccount">
          <el-input v-model="form.swjAccount" type="text" :disabled="statusType === 'renew'" :maxlength="100" />
        </el-form-item>
        <el-form-item :label="$t('电子税务局密码')" prop="swjPassword">
          <el-input
            v-model="form.swjPassword"
            type="password"
            show-password
            autocomplete="new-password"
            :disabled="statusType === 'renew'"
            :maxlength="50"
          />
        </el-form-item>
      </div>
      <!-- 南海专用，开票的有效期与原合同附件 $store.state.user.areaCode.startsWith('D440605') -->
      <div v-if="areaLogin.areaCode.startsWith('D440605') && form.registryFlag !== '1'" class="form-sg">
        <el-form-item label="原服务合同有效期" prop="oriDateRange">
          <el-date-picker
            v-model="oriDateRange"
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            unlink-panels
            style="width: 340px"
            @change="handleOriDateChange"
          />
        </el-form-item>
        <el-form-item label="原服务合同附件">
          <div style="display: flex; flex-direction: row; color: #1890ff">
            <span></span>
            <el-tooltip
              class="item"
              effect="dark"
              content="上传原服务商合同的服务期限截图"
              placement="top-start"
            >
              <div style="margin-left: -10px; margin-right: 10px">
                <i class="el-icon-warning"></i>
              </div>
            </el-tooltip>
            <gever-upload
              ref="_bwFilesRef"
              list-type="form-list"
              accept="jpg,jpeg,png,doc,xls,txt,pdf,docx,xlsx,zip,rar"
              :business-id="form.id"
              file-classification="bwInvoice"
              :default-batch-id="defaultBathId"
              @batch-change="(val) => (fileBatchId = val)"
            />
          </div>
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item label="订购开始日期" prop="validBeginDate">
          <el-date-picker
            v-model="form.validBeginDate"
            type="date"
            value-format="yyyy-MM-dd"
            :disabled="!['add', 'edit'].includes(statusType) || (oriDateRange || []).length > 1 || form.registryFlag === '1'"
            @change="changeValidBeginDate"
          />
        </el-form-item>
        <el-form-item label="订购结束日期" prop="validEndDate">
          <el-date-picker
            v-model="form.validEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            disabled
          />
        </el-form-item>
      </div>
      <div v-if="statusType === 'renew'" class="form-sg">
        <el-form-item label="续约开始日期" prop="serviceBeginDate">
          <el-date-picker
            v-model="form.serviceBeginDate"
            type="date"
            value-format="yyyy-MM-dd"
            @change="changeServiceBeginDate"
          />
        </el-form-item>
        <el-form-item label="续约结束日期" prop="serviceEndDate">
          <el-date-picker
            v-model="form.serviceEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            disabled
          />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('报销发票')" prop="reimburseType">
          <gever-select
            v-model="form.reimburseType"
            :options="reimburseTypeOptions"
            :disabled="statusType === 'view'"
            numberKey
          />
        </el-form-item>
        <el-form-item :label="$t('邮箱')" prop="orgEmail">
          <el-input v-model="form.orgEmail" type="text" :disabled="statusType === 'view'" :maxlength="50" autocomplete="off" />
        </el-form-item>
      </div>
    </el-form>
    <template v-if="statusType !== 'view'" #footer>
      <el-button @click="handleClose">{{ $t('取 消') }}</el-button>
      <el-button
        v-if="statusType === 'renew'"
        type="primary"
        icon="el-icon-s-operation"
        :loading="loading"
        @click="handleRenew"
      >
        {{ $t('续 约') }}
      </el-button>
      <el-button
        v-if="['add', 'edit'].includes(statusType) && form.registryFlag !== '1'"
        type="primary"
        icon="el-icon-document"
        :loading="loading"
        @click="handleSave(false)"
      >
        {{ $t('保 存') }}
      </el-button>
      <el-button
        v-if="['add', 'edit'].includes(statusType)"
        type="primary"
        icon="el-icon-document-checked"
        :loading="loading"
        @click="handleSave(true)"
      >
        {{ form.registryFlag === '1' ? $t('更新信息') : $t('保存&订购') }}
      </el-button>
    </template>
  </gever-dialog>
</template>

<script>
import {
  save,
  load,
  getSwjLoginKey,
  initNewData,
  renew
} from '@/api/financial/invoice/invoiceRegistry.js'
import { uuid } from '@/utils/gever.js'
import JSEncrypt from 'jsencrypt'

export default {
  name: 'InvoiceRegistryDetails',
  props: {
    dialogVisible: { type: Boolean, default: false },
    accountVisible: { type: Boolean, default: false },
    typeNo: { type: String, default: '' },
    formData: {
      type: Object,
      default: () => {}
    },
    statusType: { type: String, default: 'view' },
    areaLogin: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      oriDateRange: [],
      form: {
        orgId: this.areaLogin.id,
        registryFlag: '',
        registryType: '',
        swjAccount: '',
        swjPassword: '',
        validBeginDate: '',
        validEndDate: '',
        oriBeginDate: '',
        oriEndDate: '',
        serviceBeginDate: '',
        serviceEndDate: '',
        sameRegistry: false, // 是否同时注册
        reimburseType: 1, // 报销发票类型：1-数电专票；2-数电普票
        orgEmail: '' // 邮箱
      },
      rules: {
        orgName: [
          {
            required: true,
            message: '请输入单位名称',
            trigger: ['blur', 'change']
          }
        ],
        taxpayerType: [
          {
            required: true,
            message: '请选择纳税人类型',
            trigger: ['blur', 'change']
          }
        ],
        taxpayerNo: [
          {
            required: true,
            message: '请输入纳税人识别号',
            trigger: ['blur', 'change']
          }
        ],
        registryType: [
          {
            required: true,
            message: '请选择发票服务商',
            trigger: ['blur', 'change']
          }
        ],
        contactAddress: [
          {
            required: true,
            message: '请输入地址',
            trigger: ['blur', 'change']
          }
        ],
        contactPhone: [
          {
            required: true,
            message: '请输入电话',
            trigger: ['blur', 'change']
          }
        ],
        accountName: [
          {
            required: true,
            message: '请输入开户银行',
            trigger: ['blur', 'change']
          }
        ],
        accountNo: [
          {
            required: true,
            message: '请输入银行账号',
            trigger: ['blur', 'change']
          }
        ]
      },
      defaultBathId: '',
      fileBatchId: '',
      taxpayerTypeOptions: [],
      reimburseTypeOptions: [
        { id: 1, text: '数电专票'},
        { id: 2, text: '数电普票'}
      ],
      loading: false,
      title: '',
      publicKey: ''
    }
  },
  computed: {},
  watch: {
    statusType: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.defaultBathId = uuid()
        if (newVal === 'view') {
          this.getDetail()
          // this.title = '（查看）'
        } else if (newVal === 'edit') {
          this.getDetail()
          // this.title = '（编辑）'
        } else if (newVal === 'renew') {
          this.getDetail()
          this.title = '（续约）'
          if (!this.rules.serviceBeginDate) {
            this.rules.serviceBeginDate = [
              {
                required: true,
                message: '请选择续约开始日期',
                trigger: ['blur', 'change']
              }
            ]
          }
        } else {
          // this.title = '（新增）'
          this.initNewData()
        }
      }
    },
    oriDateRange: {
      deep: true,
      immediate: true,
      handler(newVal) {
        if (this.statusType === 'add' || (this.statusType === 'edit' && this.form.registryFlag !== '1')) {
          if (newVal && newVal.length === 2) {
            const oriDate1 = newVal[0]
            const oriDate2 = newVal[1]
            this.$set(this.form, 'oriBeginDate', oriDate1)
            this.$set(this.form, 'oriEndDate', oriDate2)
            // 订购开始日期=oriDate2+1天，并且为1年
            const nextDay = new Date(oriDate2)
            nextDay.setDate(nextDay.getDate() + 1)
            this.$set(this.form, 'validBeginDate', nextDay.toISOString().split('T')[0]) // 格式化为 yyyy-MM-dd
            nextDay.setFullYear(nextDay.getFullYear() + 1)
            nextDay.setDate(nextDay.getDate() - 1)
            this.$set(this.form, 'validEndDate', nextDay.toISOString().split('T')[0]) // 格式化为 yyyy-MM-dd
          } else {
            this.$set(this.form, 'oriBeginDate', '')
            this.$set(this.form, 'oriEndDate', '')
            this.$set(this.form, 'validBeginDate', '')
            this.$set(this.form, 'validEndDate', '')
          }
        }
      }
    }
  },
  mounted() {
    this.fetchPublicKey()
  },
  methods: {
    getDetail() {
      this.loading = true
      load(this.formData.id)
        .then((res) => {
          this.form = {
            ...res
          }
          if (this.form.oriBeginDate && this.form.oriEndDate) {
            this.oriDateRange = [this.form.oriBeginDate, this.form.oriEndDate]
          }
          this.form.registryFlag = this.form.registryFlag + ''
          if (this.statusType === 'renew') {
            const nowNewDate = new Date((new Date()).toISOString().split('T')[0])
            // 首先设置续约开始日期，如果试用日期已过的，默认当天开始续约，如果试用日期没有过的，默认试用结束日期加1天为续约开始日期
            let xyStartDate = new Date(this.form.validEndDate)
            if (xyStartDate >= nowNewDate) {
              xyStartDate.setDate(xyStartDate.getDate() + 1)
            } else {
              xyStartDate = nowNewDate
            }
            this.$set(this.form, 'serviceBeginDate', xyStartDate.toISOString().split('T')[0])
            // 然后再计算续约结束日期，如果存在oriEndDate（原服务合同结束日期），则判断oriEndDate是否大于当前日期，如果大于，则xuEndDate=oriEndDate
            let xuEndDate
            const validEndDate = new Date(this.form.validEndDate)
            if (validEndDate < nowNewDate) {
              nowNewDate.setDate(nowNewDate.getDate() - 1)
              xuEndDate = nowNewDate
            } else {
              xuEndDate = validEndDate
            }
            if (this.form.oriEndDate && new Date(this.form.oriEndDate) > nowNewDate) {
              xuEndDate = new Date(this.form.oriEndDate)
            }
            xuEndDate.setFullYear(xuEndDate.getFullYear() + 1)
            this.$set(this.form, 'serviceEndDate', xuEndDate.toISOString().split('T')[0])
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /* 关闭弹窗 */
    handleClose() {
      this.defaultBathId = ''
      this.fileBatchId = ''
      this.$emit('updateList')
      this.$emit('update:dialogVisible', false)
    },
    async fetchPublicKey() {
      try {
        const res = await getSwjLoginKey()
        if (res.returnCode === '0') {
          this.publicKey = res.message
        } else {
          this.$message.error('无法获取公钥:' + res.message)
        }
      } catch (error) {
        console.error('无法获取公钥:', error)
      }
    },
    encryptData(data) {
      const encryptor = new JSEncrypt()
      encryptor.setPublicKey(this.publicKey)
      return encryptor.encrypt(data)
    },
    initNewData() {
      this.loading = true
      initNewData(this.areaLogin.id)
        .then((res) => {
          const registryData = res.data
          this.$set(this.form, 'registryFlag', '0')
          this.$set(this.form, 'registryType', '')
          this.$set(this.form, 'taxpayerType', registryData.taxpayerType || '')
          this.$set(this.form, 'taxpayerNo', registryData.taxpayerNo || '')
          this.$set(this.form, 'accountName', registryData.accountName || '')
          this.$set(this.form, 'accountNo', registryData.accountNo || '')
          this.$set(
            this.form,
            'contactAddress',
            registryData.contactAddress || ''
          )
          this.$set(this.form, 'contactPhone', registryData.contactPhone || '')
          if ((registryData.orgName || '') === '') {
            this.$set(this.form, 'orgName', this.areaLogin.fname)
          } else {
            this.$set(this.form, 'orgName', registryData.orgName)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /* 确认按钮 */
    handleSave(regisFlag) {
      this.$refs['_formRef'].validate((valid) => {
        if (valid) {
          this.form.sameRegistry = !!regisFlag
          const params = {
            ...this.form,
            orgId: this.areaLogin.id
          }
          params.validBeginDate = this.form.validBeginDate || ''
          if (this.form.swjPassword && this.form.swjPassword.trim() !== '') {
            params.swjPassword = this.encryptData(this.form.swjPassword)
          }
          params.fileBatchId = this.fileBatchId || this.defaultBathId
          this.loading = true
          save(params)
            .then((res) => {
              this.$message.success(res.message)
              // 注册订购成功后，提示信息
              if (this.form.registryFlag !== '1' && !!regisFlag) {
                this.$emit('update:typeNo', this.form.registryType)
                this.$emit('update:accountVisible', true)
              }
              this.$emit('updateList')
              this.$emit('update:dialogVisible', false)
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    /* 续约 */
    handleRenew() {
      this.$refs['_formRef'].validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            orgId: this.areaLogin.id
          }
          this.loading = true
          renew(params)
            .then((res) => {
              this.$message.success(res.message)
              this.$emit('update:typeNo', this.form.registryType)
              this.$emit('update:accountVisible', true)
              this.$emit('updateList')
              this.$emit('update:dialogVisible', false)
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    handleOriDateChange(val) {
      if (val && val.length === 2) {
        const [start, end] = val
        const startDate = new Date(start)
        // 开始日期不能大于2025-06-30
        if (startDate > new Date(2025, 5, 30)) {
          this.oriDateRange = []
          return this.$message.warning('选择的原服务合同开始日期不能大于2025-06-30')
        }
        const endDate = new Date(end)
        // 计算区间天数
        const diff = (endDate - startDate) / (1000 * 60 * 60 * 24)
        if (diff > 365) {
          this.$message.warning('选择的原服务合同日期区间不能超过1年')
          this.oriDateRange = []
        }
      }
    },
    changeValidBeginDate() {
      if (this.statusType === 'add' || (this.statusType === 'edit' && this.form.registryFlag !== '1')) {
        if (this.form.validBeginDate) {
          // 加1年再减1天
          const nextEnd = new Date(this.form.validBeginDate)
          nextEnd.setFullYear(nextEnd.getFullYear() + 1)
          nextEnd.setDate(nextEnd.getDate() - 1)
          this.form.validEndDate = nextEnd.toISOString().split('T')[0] // 格式化为 yyyy-MM-dd
        } else {
          this.form.validEndDate = ''
        }
      }
    },
    changeServiceBeginDate() {
      if (this.form.serviceBeginDate) {
        // 加1年再减1天
        const nextEnd = new Date(this.form.serviceBeginDate)
        const lastEnd = new Date(this.form.validEndDate)
        if (nextEnd.getTime() <= lastEnd.getTime()) {
          this.form.serviceBeginDate = ''
          this.form.serviceEndDate = ''
          return this.$message.warning('续约开始日期不能小于已订购的结束日期')
        }
        nextEnd.setFullYear(nextEnd.getFullYear() + 1)
        nextEnd.setDate(nextEnd.getDate() - 1)
        this.form.serviceEndDate = nextEnd.toISOString().split('T')[0] // 格式化为 yyyy-MM-dd
      } else {
        this.form.serviceEndDate = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
