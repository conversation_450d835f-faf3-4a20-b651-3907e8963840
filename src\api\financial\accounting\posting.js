
import request from '@/utils/request'

/**
 * 凭证过账列表
 * @param {*} data
 * @returns
 */
export function pageVoucherShow(data) {
  return request({
    url: '/financial/accounting/accountingPosting/pageVoucherShow',
    method: 'post',
    data: data
  })
}

/**
 * 凭证过账
 * @param {*} data
 * @returns
 */
export function posting(data) {
  return request({
    url: '/financial/accounting/accountingPosting/posting',
    method: 'post',
    data: data
  })
}

/**
 * 全部凭证过账
 * @param {*} data
 * @returns
 */
export function postingAll(data) {
  return request({
    url: '/financial/accounting/accountingPosting/postingAll',
    method: 'post',
    data: data
  })
}

/**
 * 取消凭证过账
 * @param {*} data
 * @returns
 */
export function unPosting(data) {
  return request({
    url: '/financial/accounting/accountingPosting/unPosting',
    method: 'post',
    data: data
  })
}

/**
 * 取消全部凭证过账
 * @param {*} data
 * @returns
 */
export function unPostingAll(data) {
  return request({
    url: '/financial/accounting/accountingPosting/unPostingAll',
    method: 'post',
    data: data
  })
}


//凭证过账检测
export function checkBalance(data) {
  return request({
    url: `/financial/accounting/accountingPosting/checkBalance`,
    method: 'post',
    data,
  })
}
