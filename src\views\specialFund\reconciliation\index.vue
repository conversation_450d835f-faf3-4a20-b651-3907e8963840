<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-28 10:54:15
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-05 09:15:56
-->
<template>
  <div>
    <fold-box :left-title="$t('对账报表')" :right-title="$t(rightTitle)">
      <template #left>
        <div class="left-box">
          <ul>
            <li
              v-for="(item, index) in leftList"
              :key="index"
              :class="nowSelectLeft === index ? 'active' : ''"
              @click="handleLi(index,item)"
            >
              <span style="white-space: nowrap">{{ $t(item.abbr) }}</span>
            </li>
          </ul>
        </div>
      </template>
      <template #right>
        <div class="right-box">
          <component
            :is="moduleType"
            :module-id="currentModule.code"
          />
        </div>
      </template>
    </fold-box>
  </div>
</template>

<script>
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
import cashier from './components/cashier.vue'
import accounting from './components/accounting'
import accumulatedFunds from './components/accumulatedFunds'
export default {
  name: 'Receipt',
  components: {
    cashier,
    accounting,
    accumulatedFunds
  },
  data () {
    return {
      rightTitle: '专项资金出纳对账表',
      moduleType: 'cashier',
      books: [],
      currentModule: {
        code: 'cashier'
      },
      accountingItemVisible: false,
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      queryParams: {
        page: 1,
        rows: 20,
        specialType: '',
        balance: '',
        isAccountingItem: ''
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
      ],
      optionsMap: {
        status: [],
        specialSource: [],
        specialType: []
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      leftList: [
        {
          abbr: '专项资金出纳对账表',
          name: '专项资金出纳对账表',
          code: 'cashier'
        },
        {
          abbr: '专项资金会计对账表',
          name: '专项资金会计对账表',
          code: 'accounting'
        },
        {
          abbr: '专项资金沉淀统计表',
          name: '专项资金沉淀统计表',
          code: 'accumulatedFunds'
        }
      ],
      nowSelectLeft: 0
    }
  },
  computed: {
    ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler () {
        console.log('111')
        // this.getList()
      }
    }
  },
  created () {
    this.getOptions()
  },
  methods: {
    handleLi(index, item) {
      this.nowSelectLeft = index
      this.rightTitle = item.abbr
      this.moduleType = item.code
    },
    changeBalance(e) {
      console.log('changeBalance', e)
      if (e.target.tagName == 'INPUT') {
        this.queryParams.balance = this.queryParams.balance == '1' ? '' : '1'
      }
    },
    changeAccountingItem(e) {
      console.log('changeAccountingItem', e)
      if (e.target.tagName == 'INPUT') {
        this.queryParams.isAccountingItem = this.queryParams.isAccountingItem == '0' ? '' : '0'
      }
    },
    async getOptions () {
      const { data: status = [] } = await getDictionary('/financial/specialFund/receipt/status/')
      this.optionsMap.status = status
      const { data: specialSource = [] } = await getDictionary('/financial/specialFund/receipt/specialSource/')
      this.optionsMap.specialSource = specialSource
      const { data: specialType = [] } = await getDictionary('/financial/specialFund/receipt/specialType/')
      this.optionsMap.specialType = specialType
    },
    handleCategoryValue (value) {
      this.queryParams.categoryId = value
    }

  }
}
</script>
<style lang="scss" scoped>
.left-box {
  ul {
    width: 100%;
    height: 100%;
    li {
      padding-left: 20px;
      width: 100%;
      line-height: 50px;
      font-size: 14px;
      border-bottom: 1px solid #cccccc88;
    }
    .active {
      background-color: #e8f4ff;
      color: #1890ff;
    }
  }
}
</style>
