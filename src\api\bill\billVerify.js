/*
 * @Author: 未知
 * @Date: 2022-08-29 10:06:20
 * @LastEditTime: 2022-09-13 18:53:53
 * @LastEditors: Peng<PERSON>o
 * @Description:
 * @FilePath: \b-ui\src\api\bill\billVerify.js
 * ^-^
 */
import request from '@/utils/request'
// 票据回收  首页表格数据
export function getBillVerifyList(data) {
  return request({
    url: '/bill/billVerify/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 获取年份
export function getBillVerifyYearList(data) {
  return request({
    url: '/bill/billVerify/getYearList',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 提交
export function BillVerifySubmit(data) {
  return request({
    url: '/bill/billVerify/submit',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//取消提交
export function BillVerifyCancelSubmit(data) {
  return request({
    url: '/bill/billVerify/cancelSubmit',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//核销
export function BillVerify(data) {
  return request({
    url: '/bill/billVerify/verify',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//取消核销
export function BillCancelVerify(data) {
  return request({
    url: '/bill/billVerify/cancelVerify',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//详情
export function BillVerifyDetail(data) {
  return request({
    url: '/bill/billVerify/pageDetail',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
/**
 * @name:获取票据核销列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function pageBillDetail(data) {
  return request({
    url: '/bill/billVerify/pageBillDetail',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
