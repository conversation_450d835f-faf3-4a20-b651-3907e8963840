/**
 * 总账浏览器打印功能
 */

/**
 * 执行浏览器打印
 * @param {Object} printMode 打印模式（1-分批打印，2-一次性打印）
 * @param {Array} dataList 数据列表
 * @param {Object} meta 元数据
 * @param {Object} printSettings 打印设置
 * @param {Function} updateProgress 更新进度显示的函数
 * @param {Function} callback 打印完成后的回调函数
 */
export function browserPrint(printMode, dataList, meta, printSettings, updateProgress, callback) {
  // 分批打印还是一次性打印
  if (printMode === 1) {
    // 分批打印
    batchPrint(dataList, meta, printSettings, updateProgress, callback);
  } else {
    // 一次性打印
    printAllAtOnce(dataList, meta, printSettings, updateProgress, callback);
  }
}

/**
 * 分批打印
 * @param {Array} dataList 数据列表
 * @param {Object} meta 元数据
 * @param {Object} printSettings 打印设置
 * @param {Function} updateProgress 更新进度显示的函数
 * @param {Function} callback 打印完成后的回调函数
 */
function batchPrint(dataList, meta, printSettings, updateProgress, callback) {
  // 每批次打印的页数
  const PAGES_PER_BATCH = 200;
  
  // 处理数据，准备打印
  updateProgress('正在准备打印数据...');
  
  let batches = [];
  for (let i = 0; i < dataList.length; i += PAGES_PER_BATCH) {
    batches.push(dataList.slice(i, i + PAGES_PER_BATCH));
  }
  
  // 计算总页数
  const totalPages = dataList.length;
  
  updateProgress(`准备分${batches.length}批次打印，共${totalPages}页内容...`);
  
  // 先打印内容，最后打印目录
  printBatch(0, batches, meta, printSettings, 0, totalPages, updateProgress, () => {
    // 内容打印完成后打印目录
    updateProgress('内容页打印完成，准备打印目录...');
    printDirectory(dataList, meta, printSettings, updateProgress, (totalDirectoryPages) => {
      if (callback) {
        callback(`打印完成，共${totalPages}页内容和${totalDirectoryPages}页目录已分批打印完成`);
      }
    });
  });
}

/**
 * 打印目录
 * @param {Array} dataList 数据列表
 * @param {Object} meta 元数据
 * @param {Object} printSettings 打印设置
 * @param {Function} updateProgress 更新进度显示的函数
 * @param {Function} callback 打印完成后的回调函数
 */
function printDirectory(dataList, meta, printSettings, updateProgress, callback) {
  // 创建打印窗口
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    alert('浏览器阻止了弹出窗口，请允许弹出窗口后重试');
    return;
  }
  
  // 准备目录数据 - 实际页码映射
  const directoryItems = [];
  
  // 计算每个科目需要的页数并准备目录数据
  let currentPage = 1;
  for (let i = 0; i < dataList.length; i++) {
    directoryItems.push({
      itemCode: dataList[i].itemCode,
      itemName: dataList[i].itemName,
      pageNo: currentPage
    });
    
    // 每个科目占一页
    currentPage++;
  }
  
  // 目录表体最大高度限制（厘米）
  const MAX_DIRECTORY_HEIGHT = 23; // 表体最大高度23cm
  
  // 表格行高估算（厘米）
  const HEADER_ROW_HEIGHT = 0.59; // 表头高度
  const BASE_ROW_HEIGHT = 0.45; // 基础行高
  
  // 估算科目名称产生的行高
  const estimateItemHeight = (itemName) => {
    if (!itemName) return BASE_ROW_HEIGHT;
    
    // 计算科目名称显示宽度
    const length = itemName.length;
    const chineseCharCount = itemName.replace(/[\u0000-\u00ff]/g, "").length;
    const otherCharCount = length - chineseCharCount;
    
    // 估算总宽度：中文字符宽度约为0.96，其他字符约为0.46
    const estimatedWidth = (chineseCharCount * 0.96) + (otherCharCount * 0.46);
    
    // 计算行数（一行大约能显示10个中文字符）
    const charPerLine = 10;
    const rowNum = Math.ceil(estimatedWidth / charPerLine);
    
    // 返回估算行高
    return rowNum * BASE_ROW_HEIGHT;
  };
  
  // 计算目录分页
  const pages = [];
  let currentPageItems = [];
  let currentPageHeight = HEADER_ROW_HEIGHT; // 初始高度包含表头
  
  // 每页最多显示20对科目（40个科目）
  const MAX_ITEMS_PER_DIRECTORY_PAGE = 20;
  
  // 按两列处理，确保相邻两个项目在同一行
  for (let i = 0; i < directoryItems.length; i += 2) {
    const leftItem = directoryItems[i];
    const rightItem = i + 1 < directoryItems.length ? directoryItems[i + 1] : null;
    
    // 计算这一行的高度（取左右两侧科目名称中较高的一个）
    const leftHeight = estimateItemHeight(leftItem.itemName);
    const rightHeight = rightItem ? estimateItemHeight(rightItem.itemName) : 0;
    const rowHeight = Math.max(leftHeight, rightHeight, BASE_ROW_HEIGHT);
    
    // 检查是否需要分页 - 根据高度或最大项目数限制
    if ((currentPageHeight + rowHeight > MAX_DIRECTORY_HEIGHT || currentPageItems.length >= MAX_ITEMS_PER_DIRECTORY_PAGE) && currentPageItems.length > 0) {
      // 添加当前页到页面集合
      pages.push([...currentPageItems]);
      
      // 重置当前页
      currentPageItems = [];
      currentPageHeight = HEADER_ROW_HEIGHT;
    }
    
    // 添加当前项目到当前页
    currentPageItems.push({ leftItem, rightItem });
    currentPageHeight += rowHeight;
  }
  
  // 处理最后一页
  if (currentPageItems.length > 0) {
    pages.push([...currentPageItems]);
  }
  
  // 计算总页数
  const totalDirectoryPages = pages.length;
  
  // 写入基本HTML结构和样式
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>总账打印 - 目录</title>
      <style>
        @page { 
          size: A4 portrait; 
          margin: ${printSettings.marginTop || 15}mm ${printSettings.marginRight || 23}mm ${printSettings.marginBottom || 15}mm ${printSettings.marginLeft || 23}mm;
        }
        body { 
          font-family: SimSun; 
          font-size: 12px; 
          margin: 0; 
          padding: 0;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        .page { 
          page-break-after: always; 
          position: relative;
          height: calc(297mm - ${printSettings.marginTop || 15}mm - ${printSettings.marginBottom || 15}mm);
          box-sizing: border-box;
          overflow: hidden;
        }
        .page:last-child { 
          page-break-after: auto; 
        }
        .title { 
          text-align: center; 
          font-size: 18px; 
          font-weight: bold; 
          margin: 10px 0 20px; 
        }
        .content-wrapper {
          min-height: calc(100% - 20mm);
          position: relative;
        }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin-bottom: 10px; 
          table-layout: fixed;
        }
        th, td { 
          border: 1px solid black; 
          padding: 3px 5px; 
          text-align: center;
          word-break: break-word;
          overflow: hidden;
          font-size: 3mm;
        }
        th {
          font-weight: bold;
        }
        .t_2_td_title {
          border: solid 1px black;
          text-align: center;
          font-size: 3.5mm;
        }
        .t_2_spacn {
          width: 150px;
          word-wrap: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          word-break: break-word;
          font-family: SimSun;
          line-height: 18px;
        }
        .page-number { 
          position: absolute; 
          bottom: 2mm; 
          width: 100%; 
          text-align: center; 
        }
      </style>
    </head>
    <body>
  `);
  
  // 分页创建目录内容 - 两列布局
  for (let pageIndex = 0; pageIndex < totalDirectoryPages; pageIndex++) {
    const pageItems = pages[pageIndex];
    
    printWindow.document.write(`
      <div class="page">
        <div class="title">总账-目录表</div>
        <div class="content-wrapper">
          <table style="width: 100%">
            <tr style="height: 5.9mm">
              <th style="width: 13%" class="t_2_td_title">编号</th>
              <th style="width: 30%" class="t_2_td_title">会计科目</th>
              <th style="width: 7%" class="t_2_td_title">页码</th>
              <th style="width: 13%" class="t_2_td_title">编号</th>
              <th style="width: 30%" class="t_2_td_title">会计科目</th>
              <th style="width: 7%" class="t_2_td_title">页码</th>
            </tr>
    `);
    
    // 两列布局，每行显示两个科目
    for (const item of pageItems) {
      const leftItem = item.leftItem;
      const rightItem = item.rightItem;
      
      printWindow.document.write(`
        <tr>
          <td style="border:solid 1px black;font-size: 3mm;" class="t_2_spacn">${leftItem.itemCode}</td>
          <td style="border:solid 1px black;text-align:center;" class="t_2_spacn">${leftItem.itemName}</td>
          <td style="border:solid 1px black;font-size: 2.9mm;"><p class="">${leftItem.pageNo}</p></td>
      `);
      
      if (rightItem) {
        printWindow.document.write(`
          <td style="border:solid 1px black;font-size: 3mm;" class="t_2_spacn">${rightItem.itemCode}</td>
          <td style="border:solid 1px black;text-align:center;" class="t_2_spacn">${rightItem.itemName}</td>
          <td style="border:solid 1px black;font-size: 2.9mm;"><p class="">${rightItem.pageNo}</p></td>
        </tr>
        `);
      } else {
        printWindow.document.write(`
          <td style="border:solid 1px black;font-size: 3mm;" class="t_2_spacn"></td>
          <td style="border:solid 1px black;text-align:center;" class="t_2_spacn"></td>
          <td style="border:solid 1px black;font-size: 2.9mm;"><p class=""></p></td>
        </tr>
        `);
      }
    }
    
    printWindow.document.write(`
          </table>
          <!-- 添加空白填充元素确保页码在底部 -->
          <div class="spacer"></div>
        </div>
        <div class="page-number">第 ${pageIndex + 1} 页 / 共 ${totalDirectoryPages} 页</div>
      </div>
    `);
  }
  
  // 完成文档
  printWindow.document.write('</body></html>');
  printWindow.document.close();
  
  // 打印完成后关闭窗口并执行回调
  printWindow.onafterprint = function() {
    printWindow.close();
    if (callback) {
      // 将计算好的目录页数返回给回调函数
      setTimeout(() => callback(totalDirectoryPages), 500);
    }
  };
  
  // 执行打印
  setTimeout(function() {
    printWindow.print();
  }, 500);
}

/**
 * 打印单个批次
 * @param {Number} batchIndex 批次索引
 * @param {Array} batches 批次数组
 * @param {Object} meta 元数据
 * @param {Object} printSettings 打印设置
 * @param {Number} startPageIndex 开始页索引
 * @param {Number} totalPages 总页数
 * @param {Function} updateProgress 更新进度显示的函数
 * @param {Function} finishCallback 所有批次打印完成的回调
 */
function printBatch(batchIndex, batches, meta, printSettings, startPageIndex, totalPages, updateProgress, finishCallback) {
  if (batchIndex >= batches.length) {
    // 所有批次打印完成
    if (finishCallback) {
      finishCallback();
    }
    return;
  }
  
  const currentBatch = batches[batchIndex];
  const batchStartPage = startPageIndex + 1;
  const batchEndPage = startPageIndex + currentBatch.length;
  
  updateProgress(`正在打印第${batchIndex + 1}批，共${batches.length}批 (第${batchStartPage}-${batchEndPage}页)...`);
  
  // 创建打印窗口
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    alert('浏览器阻止了弹出窗口，请允许弹出窗口后重试');
    return;
  }
  
  // 写入基本HTML结构和样式
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>总账打印 - 第${batchIndex + 1}批</title>
      <style>
        @page { 
          size: A4 portrait; 
          margin: ${printSettings.marginTop || 15}mm ${printSettings.marginRight || 23}mm ${printSettings.marginBottom || 15}mm ${printSettings.marginLeft || 23}mm;
        }
        body { 
          font-family: SimSun; 
          font-size: 12px; 
          margin: 0; 
          padding: 0;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        .page { 
          page-break-after: always; 
          position: relative;
          height: calc(297mm - ${printSettings.marginTop || 15}mm - ${printSettings.marginBottom || 15}mm);
          box-sizing: border-box;
          overflow: hidden;
        }
        .page:last-child { 
          page-break-after: auto; 
        }
        .title { 
          text-align: center; 
          font-size: 18px; 
          font-weight: bold; 
          margin: 10px 0; 
        }
        .content-wrapper {
          min-height: calc(100% - 60mm);
          position: relative;
        }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin-bottom: 10px; 
          table-layout: fixed;
        }
        th, td { 
          border: 1px solid black; 
          padding: 3px; 
          text-align: center;
          word-break: break-word;
          overflow: hidden;
        }
        td.amount { 
          text-align: right; 
        }
        .footer { 
          position: absolute; 
          bottom: 15mm; 
          width: 100%; 
          display: flex; 
          justify-content: space-between; 
        }
        .page-number { 
          position: absolute; 
          bottom: 2mm; 
          width: 100%; 
          text-align: center; 
        }
        .signature { 
          display: inline-block; 
          margin-right: 20px; 
        }
      </style>
    </head>
    <body>
  `);
  
  // 添加每个科目的内容
  for (let i = 0; i < currentBatch.length; i++) {
    const item = currentBatch[i];
    const pageIndex = startPageIndex + i;
    const content = createPageContent(item, meta, pageIndex + 1, totalPages);
    printWindow.document.write(content);
  }
  
  // 完成文档
  printWindow.document.write('</body></html>');
  printWindow.document.close();
  
  // 打印完成后关闭窗口并打印下一批
  printWindow.onafterprint = function() {
    printWindow.close();
    // 延迟一点时间后打印下一批
    setTimeout(function() {
      printBatch(batchIndex + 1, batches, meta, printSettings, startPageIndex + currentBatch.length, totalPages, updateProgress, finishCallback);
    }, 500);
  };
  
  // 执行打印
  setTimeout(function() {
    printWindow.print();
  }, 500);
}

/**
 * 一次性打印所有内容
 * @param {Array} dataList 数据列表
 * @param {Object} meta 元数据
 * @param {Object} printSettings 打印设置
 * @param {Function} updateProgress 更新进度显示的函数
 * @param {Function} callback 打印完成后的回调函数
 */
function printAllAtOnce(dataList, meta, printSettings, updateProgress, callback) {
  // 创建打印窗口
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    alert('浏览器阻止了弹出窗口，请允许弹出窗口后重试');
    return;
  }
  
  // 计算总页数
  const totalPages = dataList.length;
  
  // 准备目录数据
  const directoryItems = [];
  
  updateProgress('正在准备打印数据...');
  
  // 计算每个科目实际的页码
  let currentPage = 1;
  for (let i = 0; i < dataList.length; i++) {
    directoryItems.push({
      itemCode: dataList[i].itemCode,
      itemName: dataList[i].itemName,
      pageNo: currentPage
    });
    
    // 每个科目占一页
    currentPage++;
  }
  
  // 目录表体最大高度限制（厘米）
  const MAX_DIRECTORY_HEIGHT = 23; // 表体最大高度23cm
  
  // 表格行高估算（厘米）
  const HEADER_ROW_HEIGHT = 0.59; // 表头高度
  const BASE_ROW_HEIGHT = 0.45; // 基础行高
  
  // 估算科目名称产生的行高
  const estimateItemHeight = (itemName) => {
    if (!itemName) return BASE_ROW_HEIGHT;
    
    // 计算科目名称显示宽度
    const length = itemName.length;
    const chineseCharCount = itemName.replace(/[\u0000-\u00ff]/g, "").length;
    const otherCharCount = length - chineseCharCount;
    
    // 估算总宽度：中文字符宽度约为0.96，其他字符约为0.46
    const estimatedWidth = (chineseCharCount * 0.96) + (otherCharCount * 0.46);
    
    // 计算行数（一行大约能显示10个中文字符）
    const charPerLine = 10;
    const rowNum = Math.ceil(estimatedWidth / charPerLine);
    
    // 返回估算行高
    return rowNum * BASE_ROW_HEIGHT;
  };
  
  // 计算目录分页
  const pages = [];
  let currentPageItems = [];
  let currentPageHeight = HEADER_ROW_HEIGHT; // 初始高度包含表头
  
  // 每页最多显示20对科目（40个科目）
  const MAX_ITEMS_PER_DIRECTORY_PAGE = 20;
  
  // 按两列处理，确保相邻两个项目在同一行
  for (let i = 0; i < directoryItems.length; i += 2) {
    const leftItem = directoryItems[i];
    const rightItem = i + 1 < directoryItems.length ? directoryItems[i + 1] : null;
    
    // 计算这一行的高度（取左右两侧科目名称中较高的一个）
    const leftHeight = estimateItemHeight(leftItem.itemName);
    const rightHeight = rightItem ? estimateItemHeight(rightItem.itemName) : 0;
    const rowHeight = Math.max(leftHeight, rightHeight, BASE_ROW_HEIGHT);
    
    // 检查是否需要分页 - 根据高度或最大项目数限制
    if ((currentPageHeight + rowHeight > MAX_DIRECTORY_HEIGHT || currentPageItems.length >= MAX_ITEMS_PER_DIRECTORY_PAGE) && currentPageItems.length > 0) {
      // 添加当前页到页面集合
      pages.push([...currentPageItems]);
      
      // 重置当前页
      currentPageItems = [];
      currentPageHeight = HEADER_ROW_HEIGHT;
    }
    
    // 添加当前项目到当前页
    currentPageItems.push({ leftItem, rightItem });
    currentPageHeight += rowHeight;
  }
  
  // 处理最后一页
  if (currentPageItems.length > 0) {
    pages.push([...currentPageItems]);
  }
  
  // 计算总目录页数
  const totalDirectoryPages = pages.length;
  
  updateProgress(`准备一次性打印所有内容，共${totalPages}页内容和${totalDirectoryPages}页目录...`);
  
  // 写入基本HTML结构和样式
  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>总账打印</title>
      <style>
        @page { 
          size: A4 portrait; 
          margin: ${printSettings.marginTop || 15}mm ${printSettings.marginRight || 23}mm ${printSettings.marginBottom || 15}mm ${printSettings.marginLeft || 23}mm;
        }
        body { 
          font-family: SimSun; 
          font-size: 12px; 
          margin: 0; 
          padding: 0;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        .page { 
          page-break-after: always; 
          position: relative;
          height: calc(297mm - ${printSettings.marginTop || 15}mm - ${printSettings.marginBottom || 15}mm);
          box-sizing: border-box;
          overflow: hidden;
        }
        .page:last-child { 
          page-break-after: auto; 
        }
        .title { 
          text-align: center; 
          font-size: 18px; 
          font-weight: bold; 
          margin: 10px 0; 
        }
        .content-wrapper {
          min-height: calc(100% - 60mm);
          position: relative;
        }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin-bottom: 10px; 
          table-layout: fixed;
        }
        th, td { 
          border: 1px solid black; 
          padding: 3px; 
          text-align: center;
          word-break: break-word;
          overflow: hidden;
        }
        td.amount { 
          text-align: right; 
        }
        .footer { 
          position: absolute; 
          bottom: 15mm; 
          width: 100%; 
          display: flex; 
          justify-content: space-between; 
        }
        .page-number { 
          position: absolute; 
          bottom: 2mm; 
          width: 100%; 
          text-align: center; 
        }
        .signature { 
          display: inline-block; 
          margin-right: 20px; 
        }
        .t_2_td_title {
          border: solid 1px black;
          text-align: center;
          font-size: 3.5mm;
        }
        .t_2_spacn {
          width: 150px;
          word-wrap: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          word-break: break-word;
          font-family: SimSun;
          line-height: 18px;
        }
      </style>
    </head>
    <body>
  `);
  
  updateProgress('正在生成打印预览，准备打印目录和内容...');
  
  // 首先添加目录页 - 两列布局
  for (let pageIndex = 0; pageIndex < totalDirectoryPages; pageIndex++) {
    const pageItems = pages[pageIndex];
    
    printWindow.document.write(`
      <div class="page">
        <div class="title">总账-目录表</div>
        <div class="content-wrapper">
          <table style="width: 100%">
            <tr style="height: 5.9mm">
              <th style="width: 13%" class="t_2_td_title">编号</th>
              <th style="width: 30%" class="t_2_td_title">会计科目</th>
              <th style="width: 7%" class="t_2_td_title">页码</th>
              <th style="width: 13%" class="t_2_td_title">编号</th>
              <th style="width: 30%" class="t_2_td_title">会计科目</th>
              <th style="width: 7%" class="t_2_td_title">页码</th>
            </tr>
    `);
    
    // 两列布局，每行显示两个科目
    for (const item of pageItems) {
      const leftItem = item.leftItem;
      const rightItem = item.rightItem;
      
      printWindow.document.write(`
        <tr>
          <td style="border:solid 1px black;font-size: 3mm;" class="t_2_spacn">${leftItem.itemCode}</td>
          <td style="border:solid 1px black;text-align:center;" class="t_2_spacn">${leftItem.itemName}</td>
          <td style="border:solid 1px black;font-size: 2.9mm;"><p class="">${leftItem.pageNo}</p></td>
      `);
      
      if (rightItem) {
        printWindow.document.write(`
          <td style="border:solid 1px black;font-size: 3mm;" class="t_2_spacn">${rightItem.itemCode}</td>
          <td style="border:solid 1px black;text-align:center;" class="t_2_spacn">${rightItem.itemName}</td>
          <td style="border:solid 1px black;font-size: 2.9mm;"><p class="">${rightItem.pageNo}</p></td>
        </tr>
        `);
      } else {
        printWindow.document.write(`
          <td style="border:solid 1px black;font-size: 3mm;" class="t_2_spacn"></td>
          <td style="border:solid 1px black;text-align:center;" class="t_2_spacn"></td>
          <td style="border:solid 1px black;font-size: 2.9mm;"><p class=""></p></td>
        </tr>
        `);
      }
    }
    
    printWindow.document.write(`
          </table>
          <!-- 添加空白填充元素确保页码在底部 -->
          <div class="spacer"></div>
        </div>
        <div class="page-number">第 ${pageIndex + 1} 页 / 共 ${totalDirectoryPages} 页</div>
      </div>
    `);
  }
  
  // 然后添加内容页
  for (let i = 0; i < dataList.length; i++) {
    if (i % 20 === 0) {
      updateProgress(`正在生成打印预览，处理第${i+1}/${dataList.length}页...`);
    }
    
    const item = dataList[i];
    const content = createPageContent(item, meta, i + 1, totalPages);
    printWindow.document.write(content);
  }
  
  // 完成文档
  printWindow.document.write('</body></html>');
  printWindow.document.close();
  
  updateProgress('正在打开打印预览，请在弹出窗口中确认打印...');
  
  // 添加打印完成后关闭窗口的处理器
  printWindow.onafterprint = function() {
    printWindow.close();
    if (callback) {
      callback(`打印完成，共${totalPages}页内容和${totalDirectoryPages}页目录已一次性打印完成`);
    }
  };
  
  // 执行打印
  setTimeout(function() {
    printWindow.print();
  }, 500);
}

/**
 * 创建页面内容
 * @param {Object} item 当前科目数据
 * @param {Object} meta 元数据
 * @param {Number} pageNo 当前页码
 * @param {Number} totalPages 总页数
 * @returns {String} HTML内容
 */
function createPageContent(item, meta, pageNo, totalPages) {
  const signerList = meta.expand && meta.expand.signerInfoList ? meta.expand.signerInfoList : [];
  
  // 创建签名行HTML字符串
  const signerHtml = signerList.map(signer => 
    `<span class="signature">${signer}</span>`
  ).join('');
  
  return `
    <div class="page">
      <div class="title">${meta.reportName}</div>
      <div style="text-align: center; margin: 8px 0;">会计期间：${meta.periodNum}</div>
      <div style="margin: 8px 0;">单位名称：${meta.unitName}</div>
      <div style="margin: 8px 0 12px;">会计科目：${item.itemName}</div>
      <div class="content-wrapper">
        <table>
          <tr>
            <th width="10%">期间</th>
            <th width="20%">描述</th>
            <th width="20%">借方</th>
            <th width="20%">贷方</th>
            <th width="10%">方向</th>
            <th width="20%">余额</th>
          </tr>
          ${(item.ledgerVOList || []).map(ledger => `
            <tr>
              <td>${formatString(ledger.periodName)}</td>
              <td>${formatString(ledger.summary)}</td>
              <td class="amount">${transformMoney(ledger.debit)}</td>
              <td class="amount">${transformMoney(ledger.credit)}</td>
              <td>${formatString(ledger.direction)}</td>
              <td class="amount">${transformMoney(ledger.balance)}</td>
            </tr>
          `).join('')}
        </table>
      </div>
      <div class="footer">
        <div style="width: 70%; display: flex; flex-wrap: wrap;">${signerHtml}</div>
        <div style="width: 30%; text-align: right;">打印日期：${meta.printingDate}</div>
      </div>
      <div class="page-number">第 ${pageNo} 页 / 共 ${totalPages} 页</div>
    </div>
  `;
}

/**
 * 格式化字符串
 * @param {*} value 待格式化的值
 * @returns {String} 格式化后的字符串
 */
function formatString(value) {
  if (value !== null && value !== undefined) {
    return value;
  } else {
    return '';
  }
}

/**
 * 格式化金额
 * @param {*} money 金额
 * @returns {String} 格式化后的金额字符串
 */
function transformMoney(money) {
  if (money == 0) {
    return '';
  }
  
  if (money && money != null) {
    money = String(money);
    var left = money.split('.')[0];
    var right = money.split('.')[1];
    right = right ? (right.length >= 2 ? '.' + right.substr(0, 2) : '.' + right + '0') : '.00';
    var temp = left.split('').reverse().join('').match(/(\d{1,3})/g);
    return (Number(money) < 0 ? '-' : '') + temp.join(',').split('').reverse().join('') + right;
  } else {
    return '';
  }
} 