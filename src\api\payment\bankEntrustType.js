import request from '@/utils/request'
/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadBankEntrustTypeListApi(params) {
  return request({
    url: '/payment/manager/bankEntrustType/page',
    method: 'get',
    params
  })
}

/**
 * @name: 详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadBankEntrustTypeInfoApi(id) {
  return request({
    url: `/payment/manager/bankEntrustType/get/${id}`,
    method: 'get'
  })
}

/**
 * @name: 删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteBankEntrustTypeApi(data) {
  return request({
    url: '/payment/manager/bankEntrustType/delete',
    method: 'post',
    data
  })
}

/**
 * @name: 新增保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveBankEntrustTypeApi(data) {
  return request({
    url: '/payment/manager/bankEntrustType/save',
    method: 'post',
    data
  })
}

/**
 * @name: 更新保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateBankEntrustTypeApi(data) {
  return request({
    url: '/payment/manager/bankEntrustType/update',
    method: 'post',
    data
  })
}

export function setValidBankEntrustTypeApi(data) {
  return request({
    url: '/payment/manager/bankEntrustType/setValid',
    method: 'post',
    data
  })
}

