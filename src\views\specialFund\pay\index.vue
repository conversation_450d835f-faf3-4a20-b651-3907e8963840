<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-23 18:50:28
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-14 11:43:31
-->
<template>
  <div>
    <fold-box right-title="专项资金支出查询">
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('专项资金名称')">
              <el-input v-model="queryParams.specialFundName" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金编号')">
              <el-input v-model="queryParams.specialFundCode" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金来源')">
              <gever-select
                v-model="queryParams.specialFundSource"
                class="w200"
                path="/financial/specialFund/receipt/specialSource/"
              />
            </el-form-item>
            <el-form-item :label="$t('专项资金类别')">
              <!-- :show-all-levels="false" @getValue="handleCategoryValue" -->
              <categoryTreeSelect v-model="queryParams.specialFundCategoryId"></categoryTreeSelect>
            </el-form-item>

            <el-form-item :label="$t('专项资金支出类型')">
              <!-- <el-input v-model="queryParams.paymentType" type="text" class="w200" clearable /> -->
              <gever-select
                v-model="queryParams.paymentType"
                class="w200"
                path="/payment/specialFundPayType/"
              />
            </el-form-item>
            <el-form-item :label="$t('支出申请单编号')">
              <el-input v-model="queryParams.serialNumber" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('支出审批状态')">
              <!-- <el-input v-model="queryParams.approvalStatus" type="text" class="w200" clearable /> -->
              <gever-select
                v-model="queryParams.approvalStatusAry"
                class="w200"
                number-key
                multiple
                collapse-tags
                :options="optionsMap.approvalstatus"
              />
            </el-form-item>
            <el-form-item :label="$t('支付状态')">
              <!-- <el-input v-model="queryParams.paymentState" type="text" class="w200" clearable /> -->
              <gever-select
                v-model="queryParams.paymentStateAry"
                class="w200"
                number-key
                multiple
                collapse-tags
                :options="optionsMap.payStatus"
              />
            </el-form-item>
            <el-form-item :label="$t('忽略支付失败')">
              <!-- <el-checkbox v-model="queryParams.payFail">忽略支付失败</el-checkbox> -->
              <gever-select
                v-model="queryParams.noPayFail"
                class="w200"
                number-key
                :options="optionsMap.noPayFail"
              />
            </el-form-item>
            <el-form-item :label="$t('收款方')">
              <el-input v-model="queryParams.receiverAccount" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item label="支出申请日期">
              <div style="display: flex;justify-content:space-between">
                <div>
                  <el-date-picker
                    v-model="queryParams.creationTimeStart"
                    type="date"
                    class="w150"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :picker-options="startPickerOptions"
                    style="width: 100%;"
                  ></el-date-picker>
                </div>
                <span style="text-align: center;margin-left:3px;margin-right:3px;">-</span>
                <div>
                  <el-date-picker
                    v-model="queryParams.creationTimeEnd"
                    type="date"
                    class="w150"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :picker-options="endPickerOptions"
                    style="width: 100%;"
                  ></el-date-picker>
                </div>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                plain
                icon="el-icon-search"
                type="primary"
                round
                @click="handleSearch"
              >{{ $t('搜索') }}</el-button>
              <el-button
                v-hasPermi="'payment:approval:approvalReceiver:exportSpecialFundPay'"
                icon="el-customIcon-baocun"
                type="primary"
                round
                :loading="exportloading"
                @click="handleExport"
              >{{ $t('导出') }}</el-button>
            </el-form-item>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            :loading="tableLoading"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            :row-class-name="getRowClassName"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
          >
            <template
              #specialType="{ row }"
            >{{ getTitle(row.specialType+'', optionsMap.specialType) }}</template>
            <template
              #specialSource="{ row }"
            >{{ getTitle(row.specialSource+'', optionsMap.specialSource) }}</template>
            <template #status="{ row }">{{ getTitle(row.status+'', optionsMap.status) }}</template>
            <template #specialFundCategoryCode="{ row }">
              <span
                v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
              >{{ row.specialFundCategoryCode+'-'+row.specialFundCategoryName }}</span>
            </template>
            <template
              #creationTime="{ row }"
            >{{ row.creationTime?row.creationTime.substring(0,10):"" }}</template>
            <template
              #cashierDate="{ row }"
            >{{ row.cashierDate?row.cashierDate.substring(0,10):"" }}</template>
            <template
              #actualPayTime="{ row }"
            >{{ row.actualPayTime?row.actualPayTime.substring(0,10):"" }}</template>
            <template
              #approvalTime="{ row }"
            >{{ row.approvalTime?row.approvalTime.substring(0,10):"" }}</template>
            <template #rowIndex="{row, index }">
              <span
                v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
              >{{ (queryParams.page - 1) * queryParams.rows + index + 1 }}</span>
            </template>
            <template #operation="{ row }">
              <span v-if="row.orgName != '本页合计' && row.orgName != '总合计'">
                <el-button
                  v-hasPermi="'specialFund:specialFundPay:receipt:get:id'"
                  type="text"
                  @click="handleView(row)"
                >{{ $t('查看') }}</el-button>
              </span>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <public-drawer
      :visible.sync="detailVisible"
      :title="detailTitle"
      :size="50"
      :buttons="detailButtons"
      @close="handleCloseDetail"
    >
      <detail
        v-if="detailVisible"
        ref="_detailContentRef"
        :content-form="contentForm"
        :detail-visible.sync="detailVisible"
      />
    </public-drawer>
  </div>
</template>

<script>
import { pageSpecialFundPay } from '@/api/specialFund/receipt.js'
import detail from './components/detail'
import categoryTreeSelect from '../receipt/components/categoryTreeSelect'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
import { getToken } from '@/utils/auth'
export default {
  name: 'Receipt',
  components: {
    detail,
    categoryTreeSelect
  },
  data () {
    return {
      exportloading: false,
      accountingItemVisible: false,
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      queryParams: {
        page: 1,
        rows: 20,
        specialType: '',
        balance: '',
        isAccountingItem: '',
        approvalStatusAry: [],
        paymentStateAry: [],
        noPayFail: 1
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50', selectable: this.selectEnable, fixed: 'left' },
          { label: '序号', prop: 'rowIndex', width: '50' },
          { label: '单位名称', prop: 'orgName', width: '300' },
          // { label: '编制方', prop: 'specialFundTypeText', width: '100' },
          { label: '专项资金编号', prop: 'specialFundCode', width: '150' },
          { label: '专项资金名称', prop: 'specialFundName', width: '150' },
          { label: '专项资金类别', prop: 'specialFundCategoryCode', width: '200' },
          { label: '专项资金来源', prop: 'specialFundSourceText', width: '150' },
          { label: '专项支出类型', prop: 'paymentTypeText', width: '150' },
          // { label: '收款账户名称', prop: 'bankAccountName', width: '200' },
          { label: '支出申请单编号', prop: 'serialNumber', width: '150' },
          { label: '支出申请金额（元）', prop: 'payAmount', filter: 'money', width: '150', align: 'right' },
          { label: '实际支出金额（元）', prop: 'actualPayAmount', filter: 'money', width: '150', align: 'right' },
          { label: '支出申请人', prop: 'submitUserName', width: '150' },
          { label: '支出申请日期', prop: 'creationTime', width: '150' },
          { label: '支付日期', prop: 'actualPayTime', width: '150' },
          { label: '出纳日期', prop: 'cashierDate', width: '150' },
          { label: '收款方', prop: 'receiverAccount', width: '100' },
          { label: '支出审批状态', prop: 'approvalStatusText', width: '150' },
          { label: '支付状态', prop: 'payStatusText', width: '150' },
          { label: '操作', slotName: 'operation', width: '180', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        }
      ],
      optionsMap: {
        status: [],
        specialSource: [],
        specialType: [],
        approvalstatus: [],
        payStatus: [],
        noPayFail: [
          {
            id: '1',
            text: '是'
          },
          {
            id: '0',
            text: '否'
          }
        ]
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      contentForm: {}
    }
  },
  computed: {
    ...mapState(['areaLogin']), // 地区机构数据
    startPickerOptions () {
      return {
        disabledDate: time => {
          if (this.queryParams.creationTimeEnd) {
            return (
              time.getTime() >= new Date(this.queryParams.creationTimeEnd).getTime()
            )
          }
          return false
        }
      }
    },
    endPickerOptions () {
      return {
        disabledDate: time => {
          if (this.queryParams.creationTimeStart) {
            return (
              time.getTime() <= new Date(this.queryParams.creationTimeStart).getTime() - 8.64e7
            )
          }
          return false
        }
      }
    }
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler () {
        this.getList()
      }
    }
  },
  created () {
    // this.getList()
    this.getOptions()
  },
  methods: {
    getRowClassName ({ row, rowIndex }) {
      if (row.orgName == '总合计') { // 例如，让第一行的字体加粗
        return 'row-bold'
      }
      return ''
    },
    getToken,
    handleAmountInput (field) {
      let value = this.queryParams[field]
      value = value.replace(/[^\d.]/g, '') // 只允许数字和小数点
        .replace(/^\./g, '') // 不能以小数点开头
        .replace(/\.{2,}/g, '.') // 不能有多个小数点
        .replace('.', '$#$').replace(/\./g, '').replace('$#$', '.') // 只保留第一个小数点

      // 限制小数点后最多两位
      if (value.indexOf('.') > -1) {
        const parts = value.split('.')
        if (parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2)
        }
      }
      this.queryParams[field] = value
    },
    // 失去焦点时格式化显示两位小数
    formatDecimal (field) {
      if (this.queryParams[field]) {
        const num = parseFloat(this.queryParams[field])
        if (!isNaN(num)) {
          this.queryParams[field] = num.toFixed(2)
        }
      }
    },
    validateAmountRange () {
      if (this.queryParams.payAmountStart && this.queryParams.payAmountEnd) {
        if (Number(this.queryParams.payAmountStart) > Number(this.queryParams.payAmountEnd)) {
          this.$message.warning('开始金额不能大于结束金额')
          // 可以清空其中一个值或进行其他处理
          this.queryParams.payAmountEnd = ''
        }
      }
    },
    changeBalance (e) {
      console.log('changeBalance', e)
      if (e.target.tagName == 'INPUT') {
        this.queryParams.balance = this.queryParams.balance == '1' ? '' : '1'
      }
    },
    changeAccountingItem (e) {
      console.log('changeAccountingItem', e)
      if (e.target.tagName == 'INPUT') {
        this.queryParams.isAccountingItem = this.queryParams.isAccountingItem == '0' ? '' : '0'
      }
    },
    async getOptions () {
      const { data: status = [] } = await getDictionary('/financial/specialFund/receipt/status/')
      this.optionsMap.status = status
      const { data: specialSource = [] } = await getDictionary('/financial/specialFund/receipt/specialSource/')
      this.optionsMap.specialSource = specialSource
      const { data: specialType = [] } = await getDictionary('/financial/specialFund/receipt/specialType/')
      this.optionsMap.specialType = specialType
      const { data: approvalstatus = [] } = await getDictionary('/payment/approvalstatus/')
      this.optionsMap.approvalstatus = approvalstatus
      const { data: payStatus = [] } = await getDictionary('/payment/payStatus/')
      this.optionsMap.payStatus = payStatus
    },
    handleCategoryValue (value) {
      this.queryParams.categoryId = value
    },
    getList () {
      if (this.areaLogin.type == undefined) {
        // this.$message.warning(this.$t('请选择地区/机构！'))
        return
      }
      this.tableLoading = true
      if (this.$route.query.specialFundId != undefined) {
        this.queryParams.specialFundId = this.$route.query.specialFundId
      } else {
        this.queryParams.specialFundId = ''
      }
      if (this.areaLogin.type == 'Organization') {
        this.queryParams.orgCode = this.areaLogin.code
        this.queryParams.areaCode = ''
      } else {
        this.queryParams.areaCode = this.areaLogin.code
        this.queryParams.orgCode = ''
      }
      if (this.queryParams.payAmountStart == '') {
        this.queryParams.payAmountStart = null
      }
      if (this.queryParams.payAmountEnd == '') {
        this.queryParams.payAmountEnd = null
      }
      // if (this.queryParams.payFail == true) {
      //   this.queryParams.noPayFail = 1
      // } else {
      //   this.queryParams.noPayFail = ''
      // }
      this.queryParams.approvalStatusList = this.queryParams.approvalStatusAry.join(',')
      this.queryParams.paymentStateList = this.queryParams.paymentStateAry.join(',')
      pageSpecialFundPay(this.queryParams).then(res => {
        this.table.tableList = res.data.records
        this.table.total = res.data.total
        if (res.data.total != 0) {
          this.table.tableList.push({
            orgName: '总合计',
            payAmount: res.data.totalAmount,
            actualPayAmount: res.data.totalActualPayAmount
          })
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleSearch () {
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleView (row) {
      this.detailTitle = '支出明细'
      this.type = 'view'
      this.contentForm = row
      this.detailVisible = true
    },
    handleCloseDetail () {
      this.detailVisible = false
      this.batchFileVisible = false
    },
    handleSave () {
      this.$refs._detailContentRef.save()
    },
    handleSaveDraft () {
      this.$refs._detailContentRef.saveDraft()
    },
    selectEnable (row) {
      return row.orgName != '本页合计' && row.orgName != '总合计'
    },
    handleBatchFileSave () {
      this.$refs._batchFileRef.save()
    },
    handleExport () {
      let error = false
      try {
        // 拼接参数
        const params = new URLSearchParams()
        Object.keys(this.queryParams).forEach(key => {
          const value = this.queryParams[key]
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value)
          }
        })

        // 获取 token 和 tenant_id
        const accessToken = getToken()
        const tenantId = this.$Cookies.get('X-tenant-id-header')

        // 构建完整 URL
        const api = '/payment/approval/approvalReceiver/exportSpecialFundPay'
        const url = `${process.env.VUE_APP_BASE_API}${api}?${params.toString()}&access_token=${accessToken}&tenant_id=${tenantId}`
        this.exportLoading = true
        // 创建 <a> 标签并触发下载
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', '') // 强制浏览器下载（而不是预览）
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.exportLoading = false
      } catch (err) {
        error = true // 捕获到异常，标记为错误
        this.$message.error(this.$t('导出失败')) // 显示错误消息
      } finally {
        this.exportloading = false
        if (!error) {
          // this.$message.success(this.$t('导出成功')) // 没有错误才显示成功消息
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/* 隐藏数字输入框的上下箭头 - Chrome, Safari, Edge, Opera */
::v-deep .no-spinner input::-webkit-outer-spin-button,
.no-spinner input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0 !important;
}

/* 隐藏数字输入框的上下箭头 - Firefox */
::v-deep .no-spinner input[type='number'] {
  -moz-appearance: textfield !important;
}
::v-deep .row-bold {
  font-weight: bold !important;
  background-color: #f8f8f9;
}
</style>
