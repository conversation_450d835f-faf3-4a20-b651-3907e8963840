<template>
  <public-drawer
    v-loading="isLoading"
    :visible.sync="visible"
    :size="70"
    title="批量导入收款人"
    :buttons="drawerButtons"
    @close="handleCloseDrawer"
  >
    <div v-if="visible" class="gever-form">
      <div class="module-title">
        <el-button
          v-hasPermi="
            'financial.payment.approvalManager.approvalApplication.choice'
          "
          plain
          type="primary"
          :disabled="disabledAddProject"
          @click="selectProject"
        >
          选择项目
        </el-button>
      </div>
      <div class="module-title">
        <span class="label-title">{{ $t('文件上传') }}</span>
      </div>
      <p>
        <span class="red">{{ $t('文件格式为xlsx、xls') }}</span>
      </p>
      <div class="module-content">
        <gever-upload
          list-type="picture-card"
          :accept="$attrs.accept"
          :mulpitle="mulpitle"
          :file-classification="''"
          :default-batch-id="$attrs.uuid"
          @batch-change="batchChange"
        />
      </div>
      <div class="module-title">
        <span class="label-title">{{ $t('导入说明') }}</span>
      </div>
      <div class="module-content">
        <p style="font-size: 18px">
          1.请在模板中填写收款人信息，再导入到本系统。标准模板：
          <a
            href="/excel/payment/receiverTemplate.xls"
            target="_blank"
            download
          >
            {{ $t('收款人导入模板.xlsx') }}
          </a>
        </p>
        <p style="font-size: 18px">
          2.再次导入会覆盖原有收款人信息
        </p>
      </div>
      <div class="module-title">
        <span class="label-title">{{ $t('导入结果') }}</span>
      </div>
      <div class="module-content">
        <p v-for="(item, index) in messageArr" :key="index">{{ item }}</p>
      </div>
    </div>
  </public-drawer>
</template>

<script>
import { saveImportReceiver } from '../../../api/payment/approval.js'
import { getToken } from '../../../utils/auth'
import { Loading } from 'element-ui'
export default {
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    selectForm: {
      type: Object,
      default: () => {}
    },
    drawerTitle: {
      type: String,
      require: true,
      default: ''
    },
    drawerName: {
      type: String,
      require: true,
      default: ''
    },
    isApproval: {
      type: Number,
      require: true,
      default: 0
    },
    tradingScope: {
      type: Number,
      require: true,
      default: 0
    },
    mulpitle: {
      type: Boolean,
      default: true
    },
    visible: {
      type: Boolean,
      default: false
    },
    disabledAddProject: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => ({
        orgCode: '',
        businessType: ''
      })
    },
    importProject: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      isLoading: false,
      drawerVisible: true,
      batchId: '',
      // 按钮
      drawerButtons: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDrawer
        },
        {
          type: 'primary',
          text: this.$t('导 入'),
          buttonStatus: true,
          callback: this.submitForm
        }
      ],
      messageArr: [],
      loadingInstance: null
    }
  },
  methods: {
    getToken,
    selectProject() {
      this.$emit('selectProject')
    },
    handleCloseDrawer() {
      this.$emit('update:visible', false)
    },
    batchChange(value) {
      this.batchId = value
    },
    submitForm() {
      this.loadingInstance = Loading.service({
        fullscreen: true,
        text: '导入中，请稍后'
      })
      saveImportReceiver({
        batchId: this.batchId,
        budgetItemId: this.importProject?.id || '',
        ...this.params
      })
        .then((res) => {
          this.messageArr = res.message.split('</br>')
          if (res.returnCode == '0') {
            this.$message.success('操作成功')
            this.handleCloseDrawer()
            this.$emit('reload', res.data)
            this.loadingInstance.close()
          }
        })
        .catch((res) => {
          this.messageArr = res.message.split('</br>')
          this.loadingInstance.close()
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
