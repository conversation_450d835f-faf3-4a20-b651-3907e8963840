import request from '@/utils/request'

// 查询租金收入科目
export function initAddSetting(areaCode) {
  return request({
    params: {areaCode},
    url: '/financial/budget/budgetSetting/add/init',
    method: 'get',
  })
}
// 查询租金收入科目
export function getRentalIncomeItemList(id) {
  return request({
    params: {budgetSettingId: id},
    url: '/financial/budget/budgetSetting/getRentalIncomeItemList',
    method: 'get',
  })
}

// 查询非损益类科目
export function getNonProfitAndLossItemList(id) {
  return request({
    params: {budgetSettingId: id},
    url: '/financial/budget/budgetSetting/getNonProfitAndLossItemList',
    method: 'get',
  })
}
// 预算设置是否存在判断
export function checkExist(params) {
  return request({
    params,
    url: '/financial/budget/budgetSetting/checkExist',
    method: 'get',
  })
}
// 获取表格
export function page(data) {
  return request({
    data,
    url: '/financial/budget/budgetSetting/page',
    method: 'post',
  })
}
// 获取预算设置详情
export function details(id) {
  return request({
    url: `/financial/budget/budgetSetting/get/${id}`,
    method: 'get'
  })
}
// 保存 
export function save(data) {
  return request({
    data,
    url: '/financial/budget/budgetSetting/save',
    method: 'post',
    payload: true
  })
}
// 删除 
export function deletePost(data) {
  return request({
    data,
    url: '/financial/budget/budgetSetting/delete',
    method: 'post',
  })
}
// 是否启用 
export function setIsValid(data) {
  return request({
    data,
    url: '/financial/budget/budgetSetting/setIsValid',
    method: 'post',
  })
}
// 查询科目列表
export function getAccountingItemList(params) {
  return request({
    params,
    url: '/financial/budget/budgetSetting/getAccountingItemList',
    method: 'get',
  })
}
// 预算设置-查询子表数据
export function getItemData(params) {
  return request({
    params,
    url: '/financial/budget/budgetSetting/getItemData',
    method: 'get',
  })
}
// 账套年份 
export function listYear() {
  return request({
    url: '/financial/books/listYear',
    method: 'get',
  })
}
// 账套类型
export function listByDivOrOrgId(params) {
  return request({
    params,
    url: '/financial/bookTypes/listByDivOrOrgId',
    method: 'get',
  })
}
// 预算设置-查询流程模板列表
export function getProcessDefinitionList() {
  return request({
    params: {bussinessType: "10"},
    url: '/financial/budget/budgetSetting/getProcessDefinitionList',
    method: 'get',
  })
}
// 预算设置-查询流程节点列表
export function getProcessNodeList(params) {
  return request({
    params,
    url: '/financial/budget/budgetSetting/getProcessNodeList',
    method: 'get',
  })
}
// 获取账套科目位数规则
export function getByBooksTypeId(params) {
  return request({
    params,
    url: '/financial/booktypes/basedata/itemParam/getByBooksTypeId',
    method: 'get',
  })
}
