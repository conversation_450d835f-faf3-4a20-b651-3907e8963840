/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-11-03 15:39:05
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-03 15:39:30
 */
import request from '@/utils/request'

/**
 * @name:获取结转科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/booktypes/basedata/carryoverItem/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
