/*
 * @Author: 未知
 * @Date: 2022-08-30 11:30:02
 * @LastEditTime: 2022-08-31 13:45:27
 * @LastEditors: Pengxiao
 * @Description:非合同收入类型
 * @FilePath: \b-ui\src\api\financial\income\income-type.js
 * ^-^
 */
import request from '@/utils/request'

export const baseUrl = '/financial/incomeType'

/**
 * @name:分页数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: `${baseUrl}/page`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `${baseUrl}/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `${baseUrl}/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(params) {
  return request({
    url: `${baseUrl}/delete`,
    method: 'post',
    params,
    withCredentials: true
  })
}



