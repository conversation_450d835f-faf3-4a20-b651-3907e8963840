<!-- @Description: 账号登录电子税务平台 -->
<template>
  <el-dialog
    v-if="localDialogVisible"
    title="请先登录电子税务局账号"
    width="400px"
    top="15vh"
    :visible.sync="localDialogVisible"
    :append-to-body="false"
    @close="handleCloseDialog"
  >
    <div class="loginSwjDiv">
      <el-form
        ref="_loginSwjFormRef"
        label-width="100px"
        label-position="right"
        :inline="true"
        :model="loginSwjForm"
        :rules="loginSwjRules"
      >
        <el-form-item :label="$t('账 号：')" prop="swjAccount">
          <el-input
            v-model="loginSwjForm.swjAccount"
            type="text"
            style="width: 222px"
            :maxlength="20"
            :clearable="true"
          />
        </el-form-item>
        <el-form-item :label="$t('密 码：')" prop="swjPassword">
          <el-input
            v-model="loginSwjForm.swjPassword"
            type="password"
            show-password
            autocomplete="new-password"
            style="width: 222px"
            :maxlength="20"
            :clearable="true"
          />
        </el-form-item>
        <el-form-item :label="$t('验 证 码：')" prop="swjYzm">
          <el-input
            v-model="loginSwjForm.swjYzm"
            type="text"
            style="width: 100px"
            :clearable="true"
            :maxlength="8"
          />
          <el-button
            type="primary"
            style="margin-left: 10px"
            icon="el-icon-message"
            :loading="sendYzmLoading"
            :disabled="
              !loginSwjForm.swjAccount ||
                !loginSwjForm.swjPassword ||
                yzmCountdown > 0
            "
            @click="handleSendYzm"
          >
            {{
              yzmCountdown > 0 ? `${yzmCountdown}秒后重试` : $t('发送验证码')
            }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="btnGroup">
      <el-button
        type="primary"
        round
        icon="el-icon-user"
        :disabled="
          loginSwjForm.swjAccount === '' ||
            loginSwjForm.swjPassword === '' ||
            loginSwjForm.swjYzm === ''
        "
        :loading="loginSwjLoading"
        @click="handleLoginSwj"
      >
        {{ '登 录' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  getSwjLoginKey,
  loginSwj,
  sendSwjYzm
} from '@/api/financial/invoice/invoiceRegistry.js'
import JSEncrypt from 'jsencrypt'

export default {
  name: 'LoginSwjDialog',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    zdAccount: {
      type: String,
      default: ''
    },
    reSend: {
      type: Boolean,
      default: false
    },
    registry: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      publicKey: '',
      loginSwjLoading: false,
      sendYzmLoading: false,
      loginSwjForm: {
        swjAccount: '',
        swjPassword: '',
        swjYzm: ''
      },
      loginSwjRules: {
        swjAccount: [
          {
            required: true,
            message: '请输入单位电子税务局账号',
            trigger: ['blur', 'change']
          }
        ],
        swjPassword: [
          {
            required: true,
            message: '请输入单位电子税务局密码',
            trigger: ['blur', 'change']
          }
        ],
        swjYzm: [
          {
            required: true,
            message: '请输入验证码',
            trigger: ['blur', 'change']
          }
        ]
      },
      yzmCountdown: 0, // 发送验证法倒计时
      yzmCountdownTimer: 0 // 发送验证码timer
    }
  },
  computed: {
    // 使用computed属性来处理dialogVisible，避免直接修改prop
    localDialogVisible: {
      get() {
        return this.dialogVisible
      },
      set(value) {
        this.$emit('update:dialogVisible', value)
      }
    }
  },
  watch: {},
  mounted() {
    this.fetchPublicKey()
  },
  methods: {
    async fetchPublicKey() {
      try {
        const res = await getSwjLoginKey()
        if (res.returnCode === '0') {
          this.publicKey = res.message
        } else {
          this.$message.error('无法获取公钥:' + res.message)
        }
      } catch (error) {
        console.error('无法获取公钥:', error)
      }
    },
    encryptData(data) {
      const encryptor = new JSEncrypt()
      encryptor.setPublicKey(this.publicKey)
      return encryptor.encrypt(data)
    },
    handleLoginSwj() {
      this.$refs['_loginSwjFormRef'].validate((valid) => {
        if (valid) {
          const params = {
            swjAccount: this.loginSwjForm.swjAccount,
            swjPassword: this.encryptData(this.loginSwjForm.swjPassword),
            swjYzm: this.loginSwjForm.swjYzm,
            registryType: this.registry.registryType,
            spId: this.registry.spId,
            taxNo: this.registry.taxpayerNo
          }
          this.loginSwjLoading = true
          loginSwj(params)
            .then((res) => {
              if (res.returnCode !== '0') {
                this.$message.error(res.message)
              } else {
                this.$emit('update:zdAccount', params.swjAccount)
                this.$emit('update:reSend', true)
                this.$emit('successLoginSwj')
              }
            })
            .finally(() => {
              this.loginSwjLoading = false
            })
        }
      })
    },
    handleSendYzm() {
      if (!this.loginSwjForm.swjAccount || !this.loginSwjForm.swjPassword) {
        return this.$message.warning('请先填写账号密码')
      }
      const params = {
        swjAccount: this.loginSwjForm.swjAccount,
        swjPassword: this.encryptData(this.loginSwjForm.swjPassword),
        registryType: this.registry.registryType,
        spId: this.registry.spId,
        taxNo: this.registry.taxpayerNo
      }
      this.sendYzmLoading = true
      sendSwjYzm(params)
        .then((res) => {
          if (res.returnCode !== '0') {
            this.$message.error(res.message)
          } else {
            this.$message.success(res.message)
            // 验证码有效期为3分钟
            const xm = this
            xm.yzmCountdown = 3 * 60
            // 启动定时器
            xm.yzmCountdownTimer = setInterval(() => {
              // 更新剩余时间
              xm.yzmCountdown--
              if (xm.yzmCountdown === 0) {
                xm.clearYzmCountdown()
              }
            }, 1000) // 将秒转换为毫秒
          }
        })
        .finally(() => {
          this.sendYzmLoading = false
        })
    },
    handleCloseDialog() {
      this.loginSwjForm.swjAccount = ''
      this.loginSwjForm.swjPassword = ''
      this.loginSwjForm.swjYzm = ''
      this.clearYzmCountdown()
      this.$emit('update:dialogVisible', false)
    },
    clearYzmCountdown() {
      // 清除登录验证码倒计时
      clearInterval(this.yzmCountdownTimer)
      this.yzmCountdownTimer = 0
      this.yzmCountdown = 0
      this.sendYzmLoading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.loginSwjDiv {
  margin-top: 10px;
  width: 100%;
}

.btnGroup {
  margin: 10px auto;
  width: 90%;
  text-align: center;

  ::v-deep .el-button--mini.is-round {
    padding: 10px 22px;
    font-size: 15px !important;
  }
}
</style>
