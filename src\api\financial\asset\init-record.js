/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-11-16 14:02:23
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-18 08:52:03
 */

import request from '@/utils/request'

/**
 * @name:获取数据页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/asset/assetInit/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:保存出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/asset/assetInit/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/asset/assetInit/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:获取资产类型树
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function tree({ data, params }) {
  return request({
    url: `/financial/asset/assetInit/tree`,
    method: 'post',
    params,
    data,
    withCredentials: true
  })
}

/**
 * @name:完成初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function init(data) {
  return request({
    url: `/financial/asset/assetInit/init`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:撤销初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unInit(data) {
  return request({
    url: `/financial/asset/assetInit/unInit`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:删除初始化记录
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/asset/assetInit/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取资产使用状态
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function comboList(data) {
  return request({
    url: `/financial/asset/assetUseState/comboList`,
    method: 'post',
    data,
    withCredentials: true
  })
}
