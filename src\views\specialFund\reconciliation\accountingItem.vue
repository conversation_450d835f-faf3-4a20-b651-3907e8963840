<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-23 18:50:28
 * @LastEditors: liuwf
 * @LastEditTime: 2025-09-10 14:04:11
-->
<template>
  <div>
    <fold-box right-title="会计科目设置">
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('专项资金名称')">
              <el-input v-model="queryParams.receiptName" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金类别')">
              <!-- :show-all-levels="false" @getValue="handleCategoryValue" -->
              <categoryTreeSelect v-model="queryParams.categoryId" class="w200" placeholder="请选择"></categoryTreeSelect>
            </el-form-item>
            <el-form-item :label="$t('专项资金来源')">
              <gever-select
                v-model="queryParams.specialSourceList"
                class="w200"
                multiple
                placeholder="请选择"
                path="/financial/specialFund/receipt/specialSource/"
              />
            </el-form-item>
            <el-form-item :label="$t('专项资金编号')">
              <el-input v-model="queryParams.receiptCode" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金用途')">
              <el-input v-model="queryParams.purpose" type="text" class="w200" clearable />
            </el-form-item>
            <!-- <el-form-item :label="$t('编制方')">
              <gever-select
                v-model="queryParams.specialType"
                class="w200"
                path="/financial/specialFund/receipt/specialType/"
              />
            </el-form-item>
            <el-form-item :label="$t('')">
              <el-checkbox v-model="queryParams.isBalance">显示余额为零</el-checkbox>
            </el-form-item>-->
            <el-form-item :label="$t('')">
              <el-checkbox v-model="queryParams.isAccountingItem">显示未关联科目资金</el-checkbox>
            </el-form-item>
            <el-form-item>
              <el-button
                plain
                icon="el-icon-search"
                type="primary"
                round
                @click="handleSearch"
              >{{ $t('搜索') }}</el-button>
            </el-form-item>
            <div style="float: right;">
              <el-form-item>
                <el-button
                  v-hasPermi="'specialFund:accountingItemRel:save'"
                  type="primary"
                  round
                  @click="handleUpdateAccountingItem"
                >{{ $t('关联会计科目') }}</el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'specialFund:accountingItemRel:deleteMul'"
                  type="primary"
                  round
                  @click="handleCancelAccountingItem"
                >{{ $t('取消关联') }}</el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'specialFund:accountingItemRel:copyLastYear'"
                  type="primary"
                  round
                  @click="handleCopyLastYear"
                >{{ $t('复制上年') }}</el-button>
              </el-form-item>
            </div>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            :loading="tableLoading"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
          >
            <template
              #specialType="{ row }"
            >{{ getTitle(row.specialType+'', optionsMap.specialType) }}</template>
            <template
              #specialSource="{ row }"
            >{{ getTitle(row.specialSource+'', optionsMap.specialSource) }}</template>
            <template #status="{ row }">{{ getTitle(row.status+'', optionsMap.status) }}</template>
            <template #categoryId="{ row }">
              <span
                v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
              >{{ row.categoryCode+' '+row.categoryName }}</span>
            </template>
            <template #createTime="{ row }">{{ row.createTime?row.createTime.substring(0,10):"" }}</template>

            <template #rowIndex="{row, index }">
              <span
                v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
              >{{ (queryParams.page - 1) * queryParams.rows + index + 1 }}</span>
            </template>
            <template #operation="{ row }">
              <span v-if="row.orgName != '本页合计' && row.orgName != '总合计'">
                <el-button v-hasPermi="'specialFund:accountingItem:receipt:get:id'" type="text" @click="handleView(row)">{{ $t('查看专项资金') }}</el-button>
              </span>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <public-drawer
      :visible.sync="detailVisible"
      :title="detailTitle"
      :size="50"
      :buttons="detailButtons"
      @close="handleClose"
    >
      <detail
        v-if="detailVisible"
        :id="currentId"
        ref="_detailContentRef"
        :detail-visible.sync="detailVisible"
        :area="areaLogin"
        :type="type"
        @refreshTable="getList"
        @close="handleClose"
      />
    </public-drawer>
    <book-type-tree ref="bookTypeTreeRef" @refreshTable="getList"></book-type-tree>
    <!-- <accountingItem ref="accountingItemRef" :ids="ids" @refreshTable="getList"></accountingItem> -->
  </div>
</template>

<script>
import { selectAccountingItemList, accountingItemDeleteMul, copyLastYear } from '@/api/specialFund/receipt.js'
import detail from '../receipt/components/detail'
import categoryTreeSelect from '../receipt/components/categoryTreeSelect'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('financial')
import { getDictionary } from '@/api/gever/common.js'
import { getToken } from '@/utils/auth'
import bookTypeTree from './components/bookTypeTree'

export default {
  name: 'Receipt',
  components: {
    detail,
    categoryTreeSelect,
    bookTypeTree
  },
  data () {
    return {
      accountingItemVisible: false,
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      queryParams: {
        page: 1,
        rows: 20,
        specialType: '',
        isBalance: false,
        isAccountingItem: false
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50', selectable: this.selectEnable, fixed: 'left' },
          { label: '序号', prop: 'rowIndex', width: '50' },
          { label: '单位名称', prop: 'orgName', width: '300' },
          { label: '编制方', prop: 'specialType', width: '100' },
          { label: '专项资金编号', prop: 'receiptCode', width: '150' },
          { label: '专项资金类别', prop: 'categoryId', width: '150' },
          { label: '专项资金来源', prop: 'specialSource', width: '150' },
          { label: '专项资金名称', prop: 'receiptName', width: '150' },
          { label: '会计科目编号', prop: 'accountingItemCode', width: '150' },
          { label: '会计科目名称', prop: 'accountingItemName', width: '450' },
          { label: '金额', prop: 'amountStr', width: '150' },
          { label: '支出申请金额', prop: 'expenditureApplicationAmountStr', width: '150' },
          { label: '可用金额', prop: 'availableAmountStr', width: '150' },
          { label: '实际支出金额', prop: 'actualExpenditureAmountStr', width: '150' },
          { label: '余额', prop: 'balanceStr', width: '150' },
          { label: '专项资金用途', prop: 'purpose', width: '150' },
          { label: '入账日期', prop: 'arrivalDate', width: '150' },
          { label: '期限', prop: 'term', width: '100' },
          { label: '操作', slotName: 'operation', width: '180', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
      ],
      treeData: [],
      props: {
        value: 'id', // ID字段名
        label: 'text', // 显示名称
        children: 'children', // 子级字段名
        isLeaf: 'isLeaf'
      },
      optionsMap: {
        status: [],
        specialSource: [],
        specialType: []
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      ids: '',
      areaLogin: {
        type: 'Organization'
      }
    }
  },
  computed: {
    ...mapState(['books']) // 地区机构数据
  },
  watch: {
    booksId (newValue) {
      console.log('newValue', newValue)
    },
    'books.createYear': {
      handler (newValue) {
        console.log('newValue', newValue)
        this.queryParams.year = newValue
        this.getList()
      },
      deep: true,
      immediate: true
    }
    // areaLogin: {
    //   deep: true,
    //   immediate: true,
    //   handler () {
    //     this.getList()
    //   }
    // }
  },
  created () {
    // this.getList()
    this.getOptions()
  },
  methods: {
    handleUpdateAccountingItem () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要关联的数据！'))
      }
      const data = this.tableSelection.filter(item => item.accountingItemRelId != undefined)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择未关联会计科目的数据！'))
      }
      this.ids = this.tableIdSelection.join(',')
      // const table = this.tableSelection[0]
      this.accountingItemVisible = true
      // this.$refs.accountingItemRef.dialogVisible = true
      this.$refs.bookTypeTreeRef.open(this.ids, this.queryParams.orgId, this.queryParams.year)
    },
    handleCopyLastYear () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要关联的数据！'))
      }
      const data = this.tableSelection.filter(item => item.accountingItemRelId != undefined)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择未关联会计科目的数据！'))
      }
      this.ids = this.tableIdSelection.join(',')
      this.$confirm(this.$t('确定要复制上年会计科目吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        copyLastYear({ ids: this.ids, year: this.queryParams.year, orgId: this.queryParams.orgId }).then(res => {
        this.$message.success(this.$t('已复制完成！复制逻辑：上年“专项资金编号”匹配“会计科目编号”。'))
        this.getList()
      })
      }).catch(() => { })
    },
    handleCancelAccountingItem () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要取消关联的数据！'))
      }
      const data = this.tableSelection.filter(item => item.accountingItemRelId == undefined)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择已关联会计科目的数据！'))
      }
      this.$confirm(this.$t('是否确认取消关联会计科目？'), '取消关联', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const accountingItemRelIds = this.tableSelection.map(item => item.accountingItemRelId)
        const ids = accountingItemRelIds.join(',')
        accountingItemDeleteMul({ ids }).then(res => {
          this.$message.success(this.$t('操作成功'))
          this.getList()
        })
      }).catch(() => {
      })
    },
    async getOptions () {
      const { data: status = [] } = await getDictionary('/financial/specialFund/receipt/status/')
      this.optionsMap.status = status
      const { data: specialSource = [] } = await getDictionary('/financial/specialFund/receipt/specialSource/')
      this.optionsMap.specialSource = specialSource
      const { data: specialType = [] } = await getDictionary('/financial/specialFund/receipt/specialType/')
      this.optionsMap.specialType = specialType
    },
    handleCategoryValue (value) {
      this.queryParams.categoryId = value
    },
    getList () {
      // if (this.areaLogin.type == 'Organization') {
      //   this.queryParams.orgId = this.areaLogin.id
      //   this.queryParams.areaCode = ''
      // } else {
      //   this.queryParams.areaCode = this.areaLogin.code
      //   this.queryParams.orgId = ''
      // }
      const newVal = JSON.parse(sessionStorage.getItem('financialAccount'))
      if (newVal != null) {
        this.tableLoading = true
        this.queryParams.orgId = newVal.orgId
        this.queryParams.status = '3'
        selectAccountingItemList(this.queryParams).then(res => {
          this.table.tableList = res.data.rows
          this.table.total = res.data.total
        }).finally(() => {
          this.tableLoading = false
        })
      }
    },
    handleSearch () {
      const newVal = JSON.parse(sessionStorage.getItem('financialAccount'))
      if (newVal == null) {
        this.$message.warning(this.$t('请先登录账套！'))
        return
      }
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleView (row) {
      this.detailTitle = '专项资金信息'
      this.type = 'view'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleClose () {
      console.log('关闭')
      this.detailVisible = false
      this.$set(this, 'detailVisible', false)
      // this.batchFileVisible = false
    },
    handleSave () {
      this.$refs._detailContentRef.save()
    },
    handleSaveDraft () {
      this.$refs._detailContentRef.saveDraft()
    },
    selectEnable (row) {
      return row.orgName != '本页合计' && row.orgName != '总合计'
    },
    handleBatchFileSave () {
      this.$refs._batchFileRef.save()
    },
    handleExport () {
      let _data_ = ''
      let index = 0
      const param = {
        ...this.queryParams
      }
      const keys = Object.keys(param)
      keys.forEach((d) => {
        if (param[d] != undefined) {
          _data_ += d + '=' + param[d]
          if (Object.keys(param).length - 1 !== index) {
            _data_ += '&'
          }
        }
        index++
      })
      const api =
        '/specialFund/receipt/export'
      window.open(
        `${process.env.VUE_APP_BASE_API
        }${api}?${_data_}&access_token=${getToken()}&tenant_id=${this.$Cookies.get(
          'X-tenant-id-header'
        )}`
      )
    },
    handleExpenditureDetail (row) {
      // this.$router.push({
      //   path: '/financial/specialFund/expenses',
      //   query: { id: row.id }
      // })
      if (window.__MICRO_APP_ENVIRONMENT__) {
        this.$router.push({
          path: '/financial/specialFund/expenses',
          query: { categoryId: row.id }
        })
        window.microApp.dispatch({
          type: 'openMenu',
          path: '/financial/specialFund/expenses',
          query: { categoryId: row.id }
        })
      } else {
        // 自己子应用跳转路由的逻辑
        this.$router.push({
          path: '/financial/specialFund/expenses',
          query: { categoryId: row.id }
        })
      }
    }
  }
}
</script>
