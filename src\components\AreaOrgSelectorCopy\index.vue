<!--
 * @Description:地区机构树懒加载
 * @version:
 * @Author: PengXiao
 * @Date: 2021-10-11 10:50:27
 * @LastEditors: liuwf
 * @LastEditTime: 2025-05-22 16:03:59
-->

<template>
  <div>
    <el-row
      v-if="withFilter"
      class="mb10"
      type="flex"
      justify="center"
      align="center"
    >
      <el-input
        v-model="queryString"
        :placeholder="$t('请输入搜索条件')"
        clearable
        @keydown.native.enter="handleSearchTree"
      />
      <el-button
        class="ml10"
        type="primary"
        circle
        icon="el-icon-search"
        @click="handleSearchTree"
      />
      <slot name="filterAppend" />
    </el-row>
    <slot name="tool" />
    <el-tree
      v-if="isLazy"
      ref="_treeRef"
      v-loading="loading"
      node-key="id"
      lazy
      highlight-current
      :filter-node-method="filterNodeMethod"
      :indent="indent"
      :current-node-key="currentNodeKey"
      :expand-on-click-node="expandOnClickNode"
      :load="load"
      :show-checkbox="showCheckbox"
      :props="{ label: 'text', isLeaf: 'isLeaf' }"
      :default-expanded-keys="defaultExpandedKeys"
      v-bind="$attrs"
      v-on="$listeners"
      @node-click="handleNodeClick"
    >
      <span slot-scope="{ node, data }" class="custom-tree-node">
        <i
          v-if="
            data.type === 'Organization' ||
              (data.code && data.code.startsWith('O'))
          "
          class="tree-node-org"
        />
        <i v-else-if="data.isLeaf && data.attributes.level === 3" class="tree-node-unexpanded" />
        <i v-else-if="data.isLeaf" class="tree-node-leaf" />
        <i v-else-if="node.expanded" class="tree-node-expanded" />
        <i v-else class="tree-node-unexpanded" />
        <span :title="node.label">{{ node.label }}</span>
      </span>
    </el-tree>
    <el-tree
      v-else
      ref="_treeRef"
      v-loading="loading"
      node-key="id"
      :lazy="false"
      :data="treeData"
      :show-checkbox="showCheckbox"
      highlight-current
      :filter-node-method="filterNodeMethod"
      :indent="indent"
      :current-node-key="currentNodeKey"
      :expand-on-click-node="expandOnClickNode"
      :props="{ label: 'text', isLeaf: 'isLeaf' }"
      :default-expanded-keys="defaultExpandedKeys"
      v-bind="$attrs"
      v-on="$listeners"
      @node-click="handleNodeClick"
    >
      <span slot-scope="{ node, data }" class="custom-tree-node">
        <i
          v-if="
            data.type === 'Organization' ||
              (data.code && data.code.startsWith('O'))
          "
          class="tree-node-org"
        />
        <i v-else-if="data.isLeaf" class="tree-node-leaf" />
        <i v-else-if="node.expanded" class="tree-node-expanded" />
        <i v-else class="tree-node-unexpanded" />
        <span :title="node.label">{{ node.label }}</span>
      </span>
    </el-tree>
  </div>
</template>

<script>
import {
  treeDataAll,
  authTreeData as organization,
  authTreeData as authOrganization
} from '../../api/system/organization.js'
import {
  authTreeData as area,
  authTreeData as authArea
} from '../../api/system/area.js'
import { loadAuthTreeData } from '../../api/gever/common.js'
export default {
  name: 'AreaOrgSelectorCopy', // 地区机构选择
  props: {
    isLazy: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: 'area',
      validator: (value) =>
        ['authOrg', 'org', 'authArea', 'area', 'allOrg'].includes(value)
    }, // 树的类型
    withFilter: {
      type: Boolean,
      default: false
    }, // 是否带过滤器
    expandOnClickNode: {
      type: Boolean,
      default: false
    }, // 点击节点是否默认展开
    expandFirst: {
      type: Boolean,
      default: false
    }, // 是否需要默认展开第一级
    indent: {
      type: Number,
      default: 8
    },
    showCheckbox: {
      type: Boolean,
      default: false
    },
    handleNodeClick: {
      type: Function,
      default: () => {}
    },
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      queryString: '',
      currentNodeKey: '',
      defaultExpandedKeys: [],

      treeData: []
    }
  },
  created() {
    if (!this.isLazy) {
      loadAuthTreeData(this.params).then((res) => {
        this.treeData = res.data
        const rootNode = res.data[0]
        this.currentNodeKey = rootNode?.id || ''
        // 默认高亮第一个节点
        this.$refs['_treeRef'].setCurrentKey(this.currentNodeKey)
        this.$emit('current-change', rootNode)
        if (this.expandFirst) {
          // 默认展开第一个节点
          this.defaultExpandedKeys = [this.currentNodeKey]
        }
      })
    }
  },
  methods: {
    handleSearchTree() {
      this.$refs['_treeRef'].filter(this.queryString)
    },
    filterNodeMethod(value, data) {
      if (!value) return true
      return data.text.indexOf(value) !== -1
    },
    // 重新加载节点
    reload(dataOrKey) {
      const node = this.$refs['_treeRef'].getNode(dataOrKey)
      if (node) {
        // 关闭节点
        node.expanded = false
        //  设置未进行懒加载状态
        node.loaded = false
        // 重新展开节点就会间接重新触发load达到刷新效果
        node.expand()
      }
    },
    // 获取下一级
    async getChildren(node) {
      const { level } = node
      const params = level ? { id: node?.data?.id } : {}
      const requestMap = {
        authOrg: authOrganization,
        org: organization,
        authArea: authArea,
        area: area,
        allOrg: treeDataAll
      }
      if (requestMap[this.type]) {
        const { data } = await requestMap[this.type](params)
        let isLeaf = false
        data.forEach((item) => {
          item.isLeaf = !item.state
          if (item.attributes.level > 3) {
            isLeaf = true
          }
          if (item.attributes.level >= 3) {
            item.isLeaf = true
          }
        })
        if (isLeaf) {
          return { data: [] }
        }
        return { data }
      }
      return { data: [] }
    },
    // 加载节点
    async load(node, resolve) {
      try {
        const { level } = node
        const { data = [] } = await this.getChildren(node)
        if (level === 0) {
          const [firstNode = {}] = data
          this.currentNodeKey = firstNode.id || ''
          this.$nextTick(() => {
            // 默认高亮第一个节点
            this.$refs['_treeRef'].setCurrentKey(this.currentNodeKey)
            this.$emit('current-change', firstNode)
            if (this.expandFirst) {
              // 默认展开第一个节点
              this.defaultExpandedKeys = [this.currentNodeKey]
            }
          })
        }
        resolve(data)
      } catch (error) {
        resolve([])
      }
    }
  }
}
</script>

<style lang="scss" scoped>
i[class^='tree-node-']:before {
  content: '替换';
  visibility: hidden;
}
.tree-node-unexpanded {
  background: url('../../assets/images/tree/treegridClose.png') center no-repeat;
}
.tree-node-division {
  background: url('../../assets/images/tree/level1.png') center no-repeat;
}
.tree-node-expanded {
  background: url('../../assets/images/tree/treegridOpen.png') center no-repeat;
}
.tree-node-leaf {
  background: url('../../assets/images/tree/treegridItem.png') center no-repeat;
}
.tree-node-org {
  background: url('../../assets/images/tree/level2.png') center no-repeat;
}
.el-select-dropdown .tree-node-unexpanded,
.el-select-dropdown .tree-node-expanded,
.el-select-dropdown .tree-node-leaf {
  background: url('../../assets/images/tree/level1.png') center no-repeat;
}
::v-deep .el-tree {
  .is-current > .el-tree-node__content {
    background-color: $color-primary-light;
  }
}
</style>
