<template>
  <div class="Performance">
    <el-form inline :model="queryParams" :disabled="loadType == 'view'">
      <el-form-item :label="$t('项目名称')">
        <el-input v-model="queryParams.projectName" class="w150" clearable />
      </el-form-item>
      <el-form-item :label="$t('合同编号')">
        <el-input v-model="queryParams.contractCode" class="w150" clearable />
      </el-form-item>
      <el-form-item :label="$t('履约编号')">
        <el-input
          v-model="queryParams.performanceCode"
          class="w150"
          clearable
        />
      </el-form-item>
<!--      <el-form-item :label="$t('资金来源')">
        <gever-select
          v-model="queryParams.fundingSources"
          :options="fundingSourcestOptions"
          class="w150"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button
          v-hasPermi="
            'financial.payment.approvalManager.approvalApplication.list'
          "
          round
          plain
          type="primary"
          icon="el-icon-search"
          @click="getList"
        >
          {{ $t('搜索') }}
        </el-button>
      </el-form-item>
      <gever-table
        ref="_geverTableRef"
        v-loading="tableLoading"
        :columns="tableColumns"
        :data="tableList"
        :pagination="false"
        :height="400"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <template #fundingSources="{ row }">
          {{ fundingSourcesText(row.fundingSources) }}
        </template>
        <template #payType="{ row }">
          {{ payTypeText(row.payType) }}
        </template>
      </gever-table>
    </el-form>
  </div>
</template>

<script>
import { comboJson } from '../../../api/gever/common.js'
import {
  selectRecordPerformanceToFinance,
  viewPerformance
} from '../../../api/payment/projectAssociation.js'
export default {
  name: 'Performance',
  components: {},
  props: {
    rowIdArr: {
      type: Array,
      default: () => []
    },
    selectedRows: {
      type: Array,
      default: () => []
    },
    loadType: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      queryParams: {
        page: 1,
        rows: 9999,
        projectName: '',
        contractCode: '',
        performanceCode: '',
        fundingSources: '',
        orgCode: '',
        updateList: []
      },
      tableList: [],
      tableColumns: [
        {
          type: 'selection',
          align: 'center',
          width: '50',
          fixed: 'left'
        },
        { type: 'index', label: '序号', width: '80', fixed: 'left' },
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: '200',
          showOverflowTooltip: true
        },
        {
          label: '合同编号',
          prop: 'contractCode',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '合同名称',
          prop: 'contractName',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '履约编号',
          prop: 'performanceCode',
          minWidth: '200',
          showOverflowTooltip: true
        },
/*        {
          label: '资金来源',
          prop: 'fundingSources',
          width: '150',
          showOverflowTooltip: true
        },*/
        {
          label: '付款期数',
          prop: 'payPeriod',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '付款条件',
          prop: 'payCondition',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '支付类型',
          prop: 'payType',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '计划支付金额',
          prop: 'pryMoney',
          width: '150',
          filter: 'money',
          align: 'right',
          showOverflowTooltip: true
        },
        {
          label: '实际支付金额（元）',
          prop: 'payAmount',
          width: '150',
          filter: 'money',
          align: 'right',
          showOverflowTooltip: true
        },
        {
          label: '应付未付（元）',
          prop: 'remainingAmount',
          width: '150',
          filter: 'money',
          align: 'right',
          showOverflowTooltip: true
        },
/*        {
          label: '付款方账号',
          prop: 'payerBankCode',
          width: '150',
          showOverflowTooltip: true
        },*/
        {
          label: '收款方',
          showOverflowTooltip: true,
          children: [
            {
              label: '名称',
              prop: 'pyeName',
              width: '150',
              showOverflowTooltip: true
            },
            {
              label: '开户银行',
              prop: 'pyeBankName',
              width: '200',
              showOverflowTooltip: true
            },
            {
              label: '账号',
              prop: 'pyeBankCode',
              width: '200',
              showOverflowTooltip: true
            }
          ]
        },
        {
          label: '办理时间',
          prop: 'createTime',
          width: '150',
          filter: 'date',
          showOverflowTooltip: true
        }
      ],
      fundingSourcestOptions: [],
      payTypeOptions: [],
      selectArr: []
    }
  },
  computed: {
    // ...mapState(['paymentLogin'])
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  watch: {
    paymentLogin: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val) {
          this.$set(this.queryParams, 'orgCode', val.code || '')
          this.getList()
        }
      }
    }
  },
  created() {
    this.getComboJson()
    this.getList()
  },
  methods: {
    async getList() {
      this.queryParams.updateList = this.rowIdArr
      if (['view', 'refund', 'invalid'].includes(this.loadType)) {
        const { data } = await viewPerformance({
          approvalApplicationId: this.id
        })
        this.tableList = data
      } else {
        const { data } = await selectRecordPerformanceToFinance(
          this.queryParams
        )
        this.tableList = data.records
        this.initTable()
      }
    },
    initTable() {
      this.$nextTick(() => {
        this.$refs._geverTableRef.clearSelection()
          for (let i = 0; i < this.tableList.length; i++) {
            if (this.selectedRows.includes(this.tableList[i].id)) {
              this.$refs._geverTableRef.toggleRowSelection(this.tableList[i], true)
            }
          }
      })
    },
    // 获取字典集合
    async getComboJson() {
      const payType = await comboJson({
        path: '/project/paymentContract/payType'
      })
      this.payTypeOptions = payType.data
      const fundingSources = await comboJson({
        path: '/project/paymentContract/recordPerformance/fundingSources'
      })
      this.fundingSourcestOptions = fundingSources.data
    },
    fundingSourcesText(val) {
      const obj = this.fundingSourcestOptions.find((item) => item.id == val)
      return obj?.text
    },
    payTypeText(val) {
      const obj = this.payTypeOptions.find((item) => item.id == val)
      return obj?.text
    },
    handleSelectionChange(val) {
      this.selectArr = val
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__fixed-header-wrapper {
  .el-checkbox__inner {
    display: none !important;
  }
}
::v-deep .Performance .el-table__body-wrapper {
  height: 319px !important;
  z-index: 99999999;
}
</style>
