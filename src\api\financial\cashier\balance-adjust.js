/*
 * @Author: 未知
 * @Date: 2022-10-11 13:54:21
 * @LastEditTime: 2022-10-11 17:31:18
 * @LastEditors: Pengxiao
 * @Description:余额调节表
 * @FilePath: \b-ui\src\api\financial\cashier\balance-adjust.js
 * ^-^
 */
import request from '@/utils/request'
const baseURL = '/financial/cashier/cashierBalanceAdjustment'
/**
 * @name:
 * @param {*} data
 * @description:参数
booksId:1575382102228160514   账套id
bankNumber:6228481234432112346  银行卡号
period:1     					 期间
cashierAccountingItemId:1575382171841024007  科目id
cashierAccountingItemCode： 科目code
 * @return {*}
 * @test:
 * @msg:
 */
export function selectCashierBalanceAdjustment(data) {
  return request({
    url: `${baseURL}/selectCashierBalanceAdjustment`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:
 * @param {String} bankNumber 银行账号
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBankBalance(params) {
  return request({
    url: `${baseURL}/getBankBalance`,
    method: 'get',
    params,
    withCredentials: true
  })
}
/**
 * @name:
 * @param {*} data
 * @description:参数
booksId:1575382102228160514   账套id
bankNumber:6228481234432112346  银行卡号
period:1     					 期间
cashierAccountingItemId:1575382171841024007  科目id
cashierAccountingItemCode： 科目code
 * @return {*}
 * @test:
 * @msg:
 */
export function refurbish(data) {
  return request({
    url: `${baseURL}/refurbish`,
    method: 'post',
    data,
    withCredentials: true
  })
}

export function save(data) {
  return request({
    url: `${baseURL}/save`,
    method: 'post',
    data,
    payload:true,
    withCredentials: true
  })
}

