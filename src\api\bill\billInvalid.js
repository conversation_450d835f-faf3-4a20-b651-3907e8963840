
import request from '@/utils/request'
// 获取票据作废  首页列表数据
export function getBillInvalid(data) {
  return request({
    url: '/bill/billInvalid/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 票据作废  库存数据
export function getInventory(data) {
  return request({
    url: '/bill/billStock/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//  票据作废  新增作废
export function insert(data) {
  return request({
    url: '/bill/billInvalid/insert',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//  票据作废  查看详细
export function getDetail(data) {
  return request({
    url: `/bill/billInvalid/load/${data.id}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
//  票据作废  获取详细id
export function getInvalidDetail({ billTypeId, beginNo }) {
  return request({
    url: `/bill/billDetail/getInvalidDetail?billTypeId=${billTypeId}&billNo=${beginNo}`,
    method: 'get',
    withCredentials: true
  })
}

