/*
 * @Descripttion: 
 * @version: 
 * @Author: 未知
 * @Date: 2023-10-09 16:59:30
 * @LastEditors: Andy
 * @LastEditTime: 2023-10-11 11:02:36
 */
import request from '@/utils/request'

/*
export function demo(data) {
  return request({
    url: ``,
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}
*/

/*
export function demo(params) {
  return request({
    url: ``,
    method: 'get',
    params,
    // payload: true,
  })
}
*/

/* 列表 */
export function page(data) {
  return request({
    url: '/invoice/invoiceSeller/page',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 保存/编辑 */
export function save(data) {
  return request({
    url: '/invoice/invoiceSeller/save',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 查看 */
export function load(id) {
  return request({
    url: `/invoice/invoiceSeller/load/${id}`,
    method: 'get'
  })
}

/* 根据机构ID查询收款方(进入开票页面之前需调用) */
export function getSellerByOrgId(params) {
  return request({
    url: `/invoice/invoiceSeller/getSellerByOrgId`,
    method: 'get',
    params
  })
}

/* 删除 */
export function deleteData(data) {
  return request({
    url: `/invoice/invoiceSeller/delete`,
    method: 'post',
    data
  })
}


/* 纳税人类别树形列表 */
export function tree() {
  return request({
    url: `/invoice/commonTaxKind/tree`,
    method: 'post'
  })
}
