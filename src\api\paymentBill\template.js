/*
 * @Description: 
 * @Version: 
 * @Author: luozc
 * @Date: 2023-09-20 09:29:39
 * @LastEditors: luozc
 * @LastEditTime: 2023-10-10 10:41:46
 */
import request from '@/utils/request'

/**
 * 分页查询支付票据模板列表
 * @param {*} data 
 * @returns 
 */
export function getBillTemplateList(data) {
    return request({
        url: '/paymentbill/paymentBillTemplate/page',
        method: 'post',
        data
    })
}

/**
 * 查询有效支付凭据模板列表
 * @param {*} data 
 * @returns 
 */
export function getValidBillTemplateList(params) {
    return request({
        url: '/paymentbill/paymentBillTemplate/validList',
        method: 'post',
        params: params
    })
}

/**
 * 修改凭证模板信息
 * @param {*} data 
 * @returns 
 */
export function save(data) {
    return request({
        url: '/paymentbill/paymentBillTemplate/save',
        method: 'post',
        data,
        payload: true
    })
}

/**
 * 查看模板详情
 * @param {*} id 
 * @returns 
 */
export function getBillTemplate(id) {
    return request({
        url: `/paymentbill/paymentBillTemplate/get/${id}`,
        method: 'get'
    })
}