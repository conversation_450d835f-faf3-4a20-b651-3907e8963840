<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-21 10:52:13
 * @LastEditors: liuwf
 * @LastEditTime: 2025-09-12 15:16:41
-->
<template>
  <el-dialog
    v-if="dialogVisible"
    title="导入"
    :visible.sync="dialogVisible"
    width="35%"
    :before-close="handleClose"
  >
    <div class="gever-title label-title" style="margin-top: 10px;margin-bottom: 15px;">模版下载</div>
    <div style="margin-top: 20px;padding-left:10px">
      <div style="margin-bottom:25px;color:#00000">
        <span v-if="isHistory == 1">专项资金历史数据导入模板(2025)</span>
        <span v-else>专项资金导入模板(2025)</span>
      </div>
      <el-button type="primary" @click="downloadTemplate">下载模版</el-button>
    </div>
    <div class="gever-title label-title" style="margin-top: 35px;margin-bottom: 15px;">
      上传
      <span style="color: red;font-size:10px;font-weight: normal;margin-left:5px">(仅能导入一份文件)</span>
    </div>
    <div class="form-sg" style="margin-top: 20px;margin-bottom: 10px;">
      <gever-upload
        ref="upload"
        list-type="form-list"
        :accept="'.xls,.xlsx'"
        :limit="1"
        :default-batch-id="contentForm.systemBatchId"
        :business-id="contentForm.id"
        :file-classification="'receipt'"

        @batch-change="handleBatchIdChange"
      >
      </gever-upload>
    </div>

    <span slot="footer" class="dialog-footer" style="text-align: center;margin-bottom: 15px;">
      <el-button style="margin-right:20px" @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="buttonLoading" @click="handleImport">确 定</el-button>
    </span>
    <el-dialog
      width="40%"
      title="提醒"
      :visible.sync="innerVisible"
      :before-close="handleClose"
      append-to-body
    >
      <div style="width: 100%; display: flex;  justify-content:flex-end;">
        <div>
          <el-button icon="el-icon-document-copy" type="text" @click="handleCopy">复制文本</el-button>
        </div>
      </div>
      <div
        style="height: 300px;width: 100%;border: 1px solid #000;overflow: auto;padding: 3px;"
        v-html="formatMessage(innerMessage)"
      ></div>
      <span slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button plain type="primary" @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { downloadExcelTemplate, excelBatchImport } from '@/api/specialFund/receipt.js'

export default {
  props: {
    orgId: {
      type: String,
      default: ''
    },
    specialType: {
      type: [Number, String],
      default: 1
    },
    area: {
      type: Object,
      default: () => { }
    }
  },
  data () {
    return {
      dialogVisible: false,
      contentForm: {
        systemBatchId: '',
        id: ''
      },
      innerVisible: false,
      innerMessage: '',
      buttonLoading: false,
      isHistory: ''
    }
  },
  methods: {
    formatMessage (text) {
      // 替换 "导入成功XX" 为绿色
      text = text.replace(
        '导入成功！',
        '<span style="color: #4CAF50; font-weight: bold;">导入成功！</span>'
      )
      // 替换 "导入失败XXX" 为红色
      text = text.replace(
        '导入失败！',
        '<span style="color: red; font-weight: bold;">导入失败！</span>'
      )
      return text
    },
    handleImport () {
      if (this.$refs.upload.fileList.length === 0) {
        return this.$message.warning(this.$t('请上传文件'))
      }
      const data = {
        systemBatchId: this.contentForm.systemBatchId,
        orgId: this.orgId,
        specialType: this.specialType,
        type: this.area.type == 'Organization' ? '1' : '2',
        isHistory: this.isHistory
      }
      this.buttonLoading = true
      excelBatchImport(data).then(res => {
        if (res.returnCode == 0) {
          this.buttonLoading = false
          // if (res.message.startsWith('导入失败')) {
          this.innerVisible = true
          this.innerMessage = res.message
          // } else {
          //   this.$emit('refreshTable')
          //   this.$emit('close')
          //   this.handleClose()
          //   this.$message.success(this.$t('导入成功'))
          // }
        }
      }).finally(() => {
        this.buttonLoading = false
      })
    },
    async copyTextToClipboard (text) {
      // 优先使用现代API
      if (navigator.clipboard) {
        try {
          await navigator.clipboard.writeText(text)
          return true
        } catch (err) {
          console.warn('现代API复制失败，尝试备用方法:', err)
        }
      }
    },
    handleCopy () {
      if (this.copyTextToClipboard(this.innerMessage)) {
        this.$message.success(this.$t('复制成功'))
      } else {
        this.$message.error(this.$t('复制失败，请手动复制'))
      }
    },
    handleClose () {
      this.innerVisible = false
      this.dialogVisible = false
      this.buttonLoading = false
      if (this.innerMessage.startsWith('导入成功')) {
        this.$emit('refreshTable')
        this.$emit('close')
        // this.$message.success(this.$t('导入成功'))
      }
    },
    handleBatchIdChange (systemBatchId) {
      this.$set(this.contentForm, 'systemBatchId', systemBatchId)
    },
    downloadTemplate () {
      downloadExcelTemplate({ isHistory: this.isHistory }).then(res => {
        this.downloadFile(res, this.isHistory == '1' ? '专项资金历史数据导入模板(2025).xlsx' : '专项资金导入模板(2025).xlsx')
      })
    },
    downloadFile (response, fileName) {
      // 创建Blob对象
      const blob = new Blob([response])

      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.setAttribute('download', fileName)

      // 触发下载
      document.body.appendChild(link)
      link.click()

      // 清理
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}
.label-title {
  color: #1890ff;
}
</style>
