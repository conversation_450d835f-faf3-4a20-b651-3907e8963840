import request from '@/utils/request'
/**
 * @name: 获取资源管理资源树
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function resourceTree(params) {
  return request({
    url: '/system/resource/tree',
    method: 'get',
    params: params
  })
}
/**
 * @name: 获取资源管理列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function resourceList(params) {
  return request({
    url: '/system/resource/page',
    method: 'get',
    params: params
  })
}
/**
 * @name: 获取编辑查看数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function resourceSearch(query, data) {
  return request({
    url: '/system/resource/load/' + query + '?_ood_=' + data._ood_,
    method: 'get',
    query: data
  })
}
/**
 * @name: 资源管理新增、编辑保存数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function resourceSave(data) {
  return request({
    url: '/system/resource/save',
    method: 'post',
    data: data
  })
}
/**
 * @name: 资源管理列表删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function resourceDelete(data) {
  return request({
    url: '/system/resource/delete',
    method: 'post',
    data: data
  })
}

/**
 * @name: 第三方链接调取
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function authorize(params) {
  return request({
    url: '/oauth2/authorize',
    method: 'post',
    params,
  })
}
