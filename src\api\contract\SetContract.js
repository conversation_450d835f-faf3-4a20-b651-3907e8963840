/*
 * @Descripttion:
 * @version:
 * @Author: Tanronglong
 * @Date: 2021-10-12 16:14:09
 * @LastEditors: Tanronglong
 * @LastEditTime: 2021-12-06 10:47:59
 */
import request from '@/utils/request'
// 获取合同设置
export function contractSetting(data) {
  return request({
    url: '/contract/contractSetting/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 获取机构级别
export function orgLevel(data) {
  return request({
    url: '/system/dictionary/combotree?path=/contract/contractSetting/orgLevel',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 获取机构类型
export function organizationType(data) {
  return request({
    url: '/system/dictionary/combotree?path=/system/organization_type',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 获取是否启用信息
export function isEnable(data) {
  return request({
    url: '/system/dictionary/combotree?path=/contract/contractSetting/isEnable',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 获取登记流程
export function registrations(data) {
  return request({
    url: '/contract/contractSetting/getProcessDefinitionByBussinessType/28',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 获取变更流程
export function alterations(data) {
  return request({
    url: '/contract/contractSetting/getProcessDefinitionByBussinessType/29',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 获取终止流程
export function terminations(data) {
  return request({
    url: '/contract/contractSetting/getProcessDefinitionByBussinessType/30',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 获取付款流程 获取付款变更流程 获取付款终止流程 /contract/contractSetting/getProcessDefinitionByBussinessType/31
export function paymentByBusType(data) {
  return request({
    url: `/contract/contractSetting/getProcessDefinitionByBussinessType/${data}`,
    method: 'get',
    withCredentials: true
  })
}
// 保存数据
export function contractSettingSave(data) {
  return request({
    url: '/contract/contractSetting/saveX',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 删除数据
export function contractSettingRemove(data) {
  return request({
    url: '/contract/contractSetting/delete',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 获取合同设置编辑查看接口   /contract/contractSetting/load/7228309822c7412f82777a32e13a612f
export function contractSettingLoad(data) {
  return request({
    url: `/contract/contractSetting/load/${data}`,
    method: 'get'
    // withCredentials: true
  })
}
// 获取地区机构级别关联   /contract/contractSetting/getOrgInfo?id=1391555225902387201
export function getOrgInfo(data) {
  return request({
    url: `/contract/contractSetting/getOrgInfo?id=${data.id}`,
    method: 'get',
    data: data
    // withCredentials: true
  })
}

// 获取弹出页面
export function synchronizeContractData(data) {
  return request({
    url: `/contract/contractSetting/synchronizeContractData/${data}`,
    method: 'post'
    // data: data
    // withCredentials: true
  })
}
// 获取合同类型管理列表
export function pageX(data) {
  return request({
    url: '/contract/contractType/pageX',
    method: 'post',
    data: data
  })
}
// 编辑查看 /contract/contractType/load/23
export function typeLoad(data) {
  return request({
    url: `/contract/contractType/load/${data}`,
    method: 'get'
  })
}
// 保存合同类型数据 /contract/contractType/saveX
export function saveX(data) {
  return request({
    url: '/contract/contractType/saveX',
    method: 'post',
    data: data
  })
}
// 删除合同类型数据     /contract/contractType/delete
export function typeDelete(data) {
  return request({
    url: '/contract/contractType/delete',
    method: 'post',
    data: data
  })
}
// 授权删除合同数据校验授权码     /contract/contractSetting/deleteContractCheck
export function deleteContractCheck(data) {
  return request({
    url: '/contract/contractSetting/deleteContractCheck',
    method: 'post',
    data: data
  })
}
// 授权码校验通过后，询问是否删除合同数据     /contract/contractSetting/authDeleteContract
export function authDeleteContract(dataType, authCode) {
  return request({
    url: `/contract/contractSetting/authDeleteContract/${dataType}`,
    method: 'post',
    data: authCode
  })
}
