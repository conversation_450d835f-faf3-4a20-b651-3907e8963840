<template>
  <el-tabs v-model="activeTab" @tab-click="handleClick">
    <el-tab-pane
      v-for="item in tabMap"
      :key="item.key"
      :label="item.label"
      :name="item.name"
    >
      <el-form
        :ref="`_${item.name}FormRef`"
        :model="formModel"
        :disabled="type === '查看'"
      >
        <!-- 选择合同 -->
        <el-button
          v-if="
            ['rentalIncomes', 'adjExpContracts'].includes(activeTab) &&
              type !== '查看'
          "
          plain
          type="primary"
          icon="el-icon-plus"
          style="margin-bottom: 10px"
          @click="$emit('update:contractsVisible', true)"
        >
          {{ $t('选择合同') }}
        </el-button>
        <el-button
          v-if="type !== '查看' && isPlan && ['incomeItems', 'expendItems', 'nonProfitAndLossItems', 'salaryBudgets'].includes(activeTab)"
          plain
          type="primary"
          icon="el-icon-copy-document"
          style="margin-bottom: 10px"
          @click="handleLastYearCopy"
        >
          {{ $t('复制上年') }}
        </el-button>
        <!-- 新增一行 -->
        <el-button
          v-if="type !== '查看' && activeTab === 'salaryBudgets'"
          plain
          type="primary"
          icon="el-icon-plus"
          style="margin-bottom: 10px"
          @click="handleTableAddRow"
        >
          {{ $t('新增一行') }}
        </el-button>
        <!-- 收入/支出/非损益类 -->
        <template
          v-if="
            ['incomeItems', 'expendItems', 'nonProfitAndLossItems'].includes(
              activeTab
            )
          "
        >
          <el-button
            v-if="
              activeTab === 'expendItems' &&
                type !== '查看' &&
                showBtn('salaryBudgets')
            "
            plain
            type="primary"
            icon="el-icon-reading"
            style="margin-bottom: 10px"
            @click="handleReadySalary"
          >
            {{ $t('读取工资预算明细') }}
          </el-button>
          <el-table
            v-loading="loading"
            row-key="id"
            border
            stripe
            :data="tabTable.data[item.name]"
            height="350"
          >
            <el-table-column
              v-for="(column, i) in item.columns"
              :key="`${column.prop}${i}`"
              v-bind="column"
            >
              <template
                v-if="
                  [
                    'accountingItemType',
                    'lastYearBudgetAmount',
                    'lastYearBalAmount',
                    'budgetAmount',
                    'remark',
                    'ratio',
                    'curYearAmount',
                    'totalAdjustAmountDelta',
                    'adjustAmountAfter',
                    'accountingItemType',
                    'adjustAmountDelta',
                    'operation',
                    'accountingItemTitle',
                    'accountingItemCode',
                    'projectBudget'
                  ].includes(column.prop)
                "
                #default="{ row, $index }"
              >
                <span
                  v-if="
                    [
                      'curYearAmount',
                      'totalAdjustAmountDelta',
                      'adjustAmountAfter'
                    ].includes(column.prop)
                  "
                >
                  {{ $t(formatMoney(row[column.prop])) }}
                </span>
                <span
                  v-if="column.prop === 'accountingItemCode'"
                  :style="{
                    paddingLeft: handleCountTab(row.accountingItemCode)
                  }"
                >
                  {{ row.budgetPrjType === 2 ? '' : row[column.prop] }}
                </span>
                <span v-if="column.prop === 'ratio'">
                  {{ $t(`${row.ratio} %`) }}
                </span>
                <template v-if="column.prop === 'accountingItemTitle'">
                  <span v-if="column.label === '科目名称'">
                    {{ $t(row.budgetPrjType !== 2 ? row[column.prop] : '') }}
                  </span>
                  <template v-if="column.label === '项目名称'">
                    <el-form-item
                      v-if="row.budgetPrjType === 2 && row.leafItem === 1"
                      prop="accountingItemTitle"
                      label-width="0px"
                      :rules="[
                        {
                          validator: (rule, value, callback) =>
                            ruleAccountingItemTitle(
                              rule,
                              value,
                              callback,
                              $index,
                              item.name
                            ),
                          trigger: 'blur'
                        }
                      ]"
                    >
                      <gever-input
                        v-model="row[column.prop]"
                        :disabled="row.disabled"
                      />
                    </el-form-item>
                    <span v-else>
                      {{ row.budgetPrjType === 3 ? row[column.prop] : '' }}
                    </span>
                  </template>
                </template>
                <template v-if="column.prop === 'projectBudget'">
                  <span v-if="row.budgetPrjType === 1">
                  </span>
                   <gever-select
                    v-else
                    v-model="row[column.prop]"
                    :options="[
                      {
                        id: 0,
                        text: '否'
                      },
                      {
                        id: 1,
                        text: '是'
                      }
                    ]"
                    :clearable="false"
                    :disabled="disabledProjectBudgetCell(row)"
                  />
                </template>
                <!-- <template v-if="column.prop === 'accountingItemType'">
                  <span v-if="row.leafItem !== 2">
                    {{ renderText(row.accountingItemType) }}
                  </span>
                  <gever-select
                    v-else
                    v-model="row.extExp"
                    :options="[
                      {
                        id: 0,
                        text: '预算支出'
                      },
                      {
                        id: 1,
                        text: '特大额支出'
                      }
                    ]"
                    :clearable="false"
                    @change="
                      isPlan
                        ? handleAmountCount(row, $index, activeTab)
                        : handleTotalAdjustAmountDelta(row, $index, activeTab)
                    "
                  />
                </template> -->
                <template v-if="column.prop === 'remark'">
                  <span v-if="row.budgetPrjType === 1"></span>
                  <gever-input
                    v-else
                    v-model="row[column.prop]"
                    :maxlength="250"
                  />
                </template>
                <template
                  v-if="
                    [
                      'lastYearBudgetAmount',
                      'lastYearBalAmount',
                      'budgetAmount'
                    ].includes(column.prop)
                  "
                >
                  <el-form-item
                    v-if="[2, 3].includes(row.budgetPrjType) && !isAdj"
                    prop="budgetAmount"
                    label-width="0px"
                    :rules="[
                      {
                        validator: (rule, value, callback) =>
                          ruleBudgetAmount(rule, value, callback, row),
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input-number
                      v-model="row[column.prop]"
                      :controls="false"
                      :precision="2"
                      :max="************.99"
                      @blur="
                        handleAmountCount(row, $index, activeTab, column.prop)
                      "
                    />
                  </el-form-item>
                  <span v-else>{{ formatMoney(row[column.prop]) }}</span>
                </template>
                <template v-if="column.prop === 'adjustAmountDelta' && isAdj">
                  <el-form-item
                    v-if="[2, 3].includes(row.budgetPrjType)"
                    prop="adjustAmountDelta"
                    label-width="0px"
                    :rules="[
                      {
                        validator: (rule, value, callback) =>
                          ruleAdjustAmountDelta(
                            rule,
                            value,
                            callback,
                            row,
                            $index
                          ),
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input-number
                      v-model="row[column.prop]"
                      :controls="false"
                      :precision="2"
                      :min="handleMin(row)"
                      :max="handleMax(row)"
                      @blur="
                        handleTotalAdjustAmountDelta(row, $index, activeTab)
                      "
                    />
                    <!-- :disabled="disabledCell(row)" -->
                  </el-form-item>
                  <span v-else>{{ formatMoney(row.adjustAmountDelta) }}</span>
                </template>
                <template
                  v-if="
                    column.prop === 'operation' &&
                      [
                        'expendItems',
                        'incomeItems',
                        'nonProfitAndLossItems'
                      ].includes(activeTab)
                  "
                >
                  <el-button
                    v-if="
                      row.leafItem === 1 && [1, 3].includes(row.budgetPrjType)
                    "
                    type="text"
                    size="medium"
                    icon="el-icon-circle-plus-outline"
                    :disabled="flag"
                    @click="handleAddRow(item.name, row, $index)"
                  />
                  <el-button
                    v-if="row.budgetPrjType === 2"
                    type="text"
                    size="medium"
                    icon="el-icon-remove-outline"
                    style="color: red"
                    :disabled="disabledDelRow(row)"
                    @click="
                      handleDelRow($index, row, {
                        row: row,
                        index: $index,
                        budgetType: item.name
                      })
                    "
                  />
                </template>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 租金收入明细 -->
        <el-table
          v-show="activeTab === 'rentalIncomes'"
          v-loading="loading"
          row-key="id"
          border
          stripe
          :data="tabTable.data.rentalIncomes"
          height="380"
        >
          <el-table-column
            v-for="(column, idx) in item.columns"
            :key="`${column.prop || 'rental'}${idx}`"
            v-bind="column"
          >
            <template
              v-if="
                ![
                  'contractCode',
                  'contractName',
                  'secondName',
                  'startDate',
                  'endDate'
                ].includes(column.prop)
              "
              #default="{ row, $index }"
            >
              <el-button
                v-if="column.prop === 'operation'"
                type="text"
                @click="handleDel($index, 'rentalIncomes')"
              >
                {{ $t('删除') }}
              </el-button>
              <el-input-number
                v-else
                v-model="row[column.prop]"
                :controls="false"
                :precision="2"
                :min="0"
                :max="************.99"
              />
            </template>
          </el-table-column>
        </el-table>
        <!-- 期满合同租金标准调整 -->
        <el-table
          v-show="activeTab === 'adjExpContracts'"
          v-loading="loading"
          row-key="id"
          border
          stripe
          :data="tabTable.data.adjExpContracts"
          height="380"
        >
          <el-table-column
            v-for="(column, idx) in item.columns"
            :key="`${column.prop || 'contracts'}${idx}`"
            v-bind="column"
          >
            <template
              v-if="
                [
                  'openingArea',
                  'originalPrice',
                  'openingAreaPlanPrice',
                  'planMonthRent',
                  'leaseTerm',
                  'rentIncrease',
                  'contractTotalObject',
                  'bidBond',
                  'purposeRequire',
                  'disposalMethod',
                  'remark',
                  'operation'
                ].includes(column.prop)
              "
              #default="{ row, $index }"
            >
              <el-button
                v-if="column.prop === 'operation'"
                type="text"
                @click="handleDel($index, 'adjExpContracts')"
              >
                {{ $t('删除') }}
              </el-button>
              <gever-input
                v-else-if="
                  ['purposeRequire', 'disposalMethod', 'remark'].includes(
                    column.prop
                  )
                "
                v-model="row[column.prop]"
              />
              <el-input-number
                v-else
                v-model="row[column.prop]"
                :controls="false"
                :precision="column.prop === 'leaseTerm' ? 0 : 2"
                :min="0"
                :max="************.99"
              />
            </template>
          </el-table-column>
        </el-table>
        <!-- 工资预算明细 -->
        <el-table
          v-show="activeTab === 'salaryBudgets'"
          v-loading="loading"
          row-key="id"
          border
          stripe
          :data="tabTable.data.salaryBudgets"
          height="380"
        >
          <el-table-column
            v-for="(column, idx) in item.columns"
            :key="`${column.prop || 'salary'}${idx}`"
            v-bind="column"
          >
            <template
              v-if="!['序号'].includes(column.label)"
              #default="{ row, $index }"
            >
              <span v-if="column.label === '合计'">
                {{ row.totalSalary | formatMoney }}
              </span>
              <el-button
                v-else-if="column.prop === 'operation'"
                type="text"
                @click="handleDel($index, 'salaryBudgets')"
              >
                {{ $t('删除') }}
              </el-button>
              <gever-input
                v-else-if="['level', 'postName'].includes(column.prop)"
                v-model="row[column.prop]"
              />
              <gever-input
                v-else-if="['remark'].includes(column.prop)"
                v-model="row[column.prop]"
                :maxlength="250"
              />
              <el-cascader
                v-else-if="column.prop === 'accountintItemCode'"
                v-model="row[column.prop]"
                title=""
                :show-all-levels="false"
                :append-to-body="false"
                popper-class="accountint-item-code"
                :options="accountintItemTitleOptions"
                :props="{
                  label: 'accountingItemPathTitle',
                  value: 'accountingItemCode'
                }"
                @change="(val) => handleSalarySub(val, row, $index)"
              />
              <el-input-number
                v-else
                v-model="row[column.prop]"
                :controls="false"
                :precision="2"
                :min="0"
                :max="************.99"
                @blur="salaryCount(row, $index)"
              />
            </template>
          </el-table-column>
        </el-table>
        <!-- 附件 -->
        <div>
          <slot name="file" />
        </div>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import { treeData } from '@/utils/tree.js'
import { formatMoney } from '@/utils/index.js'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('financial')
import {
  getItemList,
  getPlaitLeafItem,
  getAdjustmentLeafItem
} from '@/api/budget/budgetPlanning'
export default {
  name: 'SeniorTabSetting',
  props: {
    // 是否预算编制
    isPlan: {
      type: Boolean,
      default: false
    },
    tabMap: {
      type: Array,
      default: () => []
    },
    accountingItemType: {
      type: Array,
      default: () => []
    },
    tabTable: {
      type: Object,
      default: () => ({})
    },
    nonProfitAndLossItems: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    contractsVisible: {
      type: Boolean,
      default: false
    },
    // 会计科目
    subjectList: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ''
    },
    // 是否调整
    isAdj: {
      type: Boolean,
      default: false
    },
    // 调整类型
    adjType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      activeTab: 'incomeItems',
      formModel: {
        accountingItemTitle: '',
        budgetAmount: 0,
        adjustAmountDelta: 0
      },
      load: false,
      flag: false,
      subjectListFilter: [], // 过滤掉预算收支的下拉
      subjectListFinally: [] // 最后使用的下拉
    }
  },
  computed: {
    ...mapState(['books', 'isBooksType']),
    comName() {
      return this.$route.name
    },
    _tabMap: {
      get() {
        return this.tabMap
      }
    },
    accountintItemTitleOptions() {
      const filterData = this.tabTable.data?.expendItems.filter(
        (cur) => cur.budgetPrjType !== 2
      )

      return (
        treeData(
          filterData.map((cur) => {
            cur.accountingItemPathTitle =
              cur.accountingItemCode + '' + cur.accountingItemTitle
            return cur
          }),
          'accountingItemCode',
          'parentAccountingItemCode',
          'children',
          '0'
        ) ?? []
      )
    }
  },
  // watch: {
  //   activeTab(newVal) {
  //     if (newVal == 'nonProfitAndLossItems') {
  //       if (this.nonProfitAndLossItems.length == 0) {
  //         this.$emit('initNonProfit')
  //       } else {
  //         this.$emit('disableOne')
  //       }
  //     }
  //   }
  // },
  created() {
    console.log(this.tabMap, '-----------Map')
  },
  methods: {
    formatMoney,
    changeSub(val, index) {
      console.log(val, 7)
      const item = this[
        this.activeTab === 'nonProfitAndLossItems'
          ? 'subjectListFilter'
          : 'subjectListFinally'
      ].filter(
        (item) =>
          item[
            this.activeTab === 'nonProfitAndLossItems'
              ? 'code'
              : 'accountingItemCode'
          ] == val
      )[0]
      this.$emit('fillSubjectData', item, index)
    },
    getSubjectList(row, query) {
      console.log(row, 88888)
      this.load = true
      if (this.activeTab === 'nonProfitAndLossItems') {
        // for (let i = 0; i < this.tabTable?.data?.nonProfitAndLossItems.length; i++) {
        //   for (let j = 0; j < this.subjectListFilter.length; j++) {
        //     if (this.subjectListFilter[j].code === this.tabTable?.data?.nonProfitAndLossItems[i].accountingItemCode) {
        //       this.subjectListFilter[j].disabled = true
        //     } else {
        //       this.subjectListFilter[j].disabled = false
        //     }
        //   }
        // }
        this.$set(this, 'subjectListFinally', [])
        this.subjectListFilter.forEach((item) => {
          this.subjectListFinally.push({
            ...item,
            disabled: Boolean(
              this.tabTable.data[this.activeTab].filter(
                (element) => element.accountingItemPathTitle == item.text
              ).length
            )
          })
        })
        // this.subjectListFinally = this.subjectListFilter
        // console.log(this.subjectListFinally, 123)
        this.load = false
      } else {
        if (row.leafItem != 2) {
          if (this.comName === 'BudgetPlanning') {
            getPlaitLeafItem({
              parentItemCode: row.parentAccountingItemCode,
              booksId: this.books.id,
              accountingItemLevel: row.accountingItemLevel - 1,
              accountingItemType: this.activeTab == 'incomeItems' ? 1 : 2
            }).then((res) => {
              this.load = false
              if (res.returnCode == 0) {
                this.$set(this, 'subjectListFinally', [])
                res.data.forEach((item) => {
                  this.subjectListFinally.push({
                    ...item,
                    id: item.accountingItemCode,
                    text: item.accountingItemPathTitle,
                    code: item.accountingItemCode,
                    disabled: Boolean(
                      this.tabTable.data[this.activeTab].filter(
                        (element) =>
                          element.accountingItemPathTitle ==
                          item.accountingItemPathTitle
                      ).length
                    )
                  })
                })
              } else {
                this.load = false
                this.$set(this, 'subjectListFinally', [])
              }
            })
          } else {
            getAdjustmentLeafItem({
              parentItemCode: row.parentAccountingItemCode,
              booksId: this.books.id,
              accountingItemLevel: row.accountingItemLevel - 1,
              accountingItemType: this.activeTab == 'incomeItems' ? 1 : 2
            }).then((res) => {
              this.load = false
              if (res.returnCode == 0) {
                this.$set(this, 'subjectListFinally', [])
                res.data.forEach((item) => {
                  this.subjectListFinally.push({
                    ...item,
                    id: item.accountingItemCode,
                    text: item.accountingItemPathTitle,
                    code: item.accountingItemCode,
                    disabled: Boolean(
                      this.tabTable.data[this.activeTab].filter(
                        (element) =>
                          element.accountingItemPathTitle ==
                          item.accountingItemPathTitle
                      ).length
                    )
                  })
                })
              } else {
                this.load = false
                this.$set(this, 'subjectListFinally', [])
              }
            })
          }
        } else {
          this.load = false
          this.$set(this, 'subjectListFinally', [])
        }
      }
    },
    // 非损益类科目选择禁用
    hasDisable(row) {
      const { accountingItemCode: code, leafItem } = row
      const list = this.tabTable?.data?.nonProfitAndLossItems
      const childNode = list.filter(
        (cur) =>
          cur.accountingItemCode?.startsWith(code) &&
          cur.accountingItemCode?.length > code?.length
      )
      return leafItem === 2 || (!!childNode.length && !!code)
    },
    // 会计科目禁选
    disabledSub(row) {
      const list = this.tabTable?.data?.nonProfitAndLossItems ?? []
      return list.some(
        (cur) => cur.accountingItemCode === row.accountingItemCode
      )
    },
    // 确认读取工资明细
    sureReadySalary() {
      // 工资数据
      const salaryData = this.tabTable.data?.salaryBudgets ?? []
      // 支出数据
      const expendData = this.tabTable.data?.expendItems ?? []
      if (!salaryData.length) return
      expendData.forEach((item, index) => {
        salaryData.forEach((cur) => {
          if (
            item.accountingItemCode ===
            cur?.accountintItemCode?.[cur.accountintItemCode.length - 1]
          ) {
            this.$emit('handleReadySalary', {
              index,
              row: {
                ...item,
                budgetAmount: cur.totalSalary
              }
            })
          }
        })
      })
    },
    // 读取工资明细
    handleReadySalary() {
      this.$confirm(
        '读取工资明细后将覆盖原科目已有数据并导致年度预算支出总额增加或减少, 确定读取吗?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.sureReadySalary()
      })
    },
    // 按钮是否显示
    showBtn(linkedData) {
      const index = this._tabMap.findIndex((cur) => cur.name === linkedData)
      return index > -1
    },
    // 删除
    handleDel(index, budgetType) {
      this.$emit('handleDelFn', { index, budgetType })
    },
    disabledCell(row) {
      // console.log((this.adjType === 2 && row.budgetAmount - (row.curYearAmount ?? 0) === 0) || (row.budgetAmount < 0 && this.adjType == 2))
      return (
        (this.adjType === 2 &&
          row.budgetAmount - (row.curYearAmount ?? 0) === 0) ||
        (row.budgetAmount < 0 && this.adjType == 2)
      )
    },
    disabledProjectBudgetCell(row) {
      return (
        this.isAdj && (row.budgetPlaitItemId || row.id)
      )
    },
    disabledDelRow(row) {
      return (
        (this.adjType === 2 && (row.budgetPlaitItemId || row.id)) || Boolean(row.curYearAmount)
      )
    },
    // 处理本次调整最大输入值
    handleMax(row) {
      if (this.adjType === 2 && row.budgetAmount) {
        return ***********.99
      }
      return ***********.99 - (row.budgetAmount ?? 0)
    },
    // 处理本次调整最小输入值
    handleMin(row) {
      if (this.adjType === 2 && row.budgetAmount && row.budgetAmount > 0) {
        // const budgetAmount = ~row.budgetAmount + 1

        return -row.budgetAmount
      }
      return 0
    },
    handleSalarySub(val, row, $index) {
      const item = this.tabTable.data.expendItems[$index]
      row.accountingItemId = item.accountingItemId
    },
    // 计算工资预算明细单行合计
    salaryCount(row, index) {
      let total = 0
      for (const key in row) {
        if (
          [
            'baseSalary',
            'accruedSalary',
            'performanceAward',
            'otherSalary',
            'tranCommSubsidy',
            'holidaySubsidy',
            'overtimeDutySubsidy',
            'postSubsidy',
            'annualLeaveSubsidy',
            'priceSubsidy',
            'highTemperatureSubsidy',
            'senioritySubsidy',
            'retirementSubsidy',
            'otherSubsidy'
          ].includes(key)
        ) {
          total += row?.[key] || 0
        }
      }
      this.$set(this.tabTable.data[this.activeTab][index], 'totalSalary', total)
      this.$nextTick(() => {
        const totalSalary = this.tabTable.data[this.activeTab]?.reduce(
          (total, cur) => total + (cur?.totalSalary ?? 0),
          0
        )
        this.$emit('handleBudgetTotalSalary', totalSalary)
      })
    },
    // 本年计划数/本次调整数输入框是否显示
    showInput(row) {
      if (this.activeTab === 'nonProfitAndLossItems') {
        return true
      }
      if (['incomeItems', 'expendItems'].includes(this.activeTab)) {
        // 当前节点的下级
        const children = this.tabTable.data[this.activeTab]?.filter(
          (cur) =>
            cur?.accountingItemCode?.startsWith(row.accountingItemCode) &&
            cur?.accountingItemCode?.includes(row.accountingItemCode) &&
            cur?.accountingItemCode?.length > row?.accountingItemCode.length
        )
        if (row.leafItem !== 0) return true
        // 一级科目没有子节点或者可编辑
        if (
          row.accountingItemLevel === 1 &&
          (!children.length || children.every((cur) => cur.isTemp))
        ) {
          return true
        }
      }
      return false
    },
    // 新增一行工资预算明细 or 非损益类
    handleTableAddRow() {
      if (this.activeTab === 'salaryBudgets') {
        this.$emit('handleSalaryAddRow')
      }
      // 非损益类新增一行
      if (this.activeTab === 'nonProfitAndLossItems') {
        this.$emit(
          'handleNonProfitAndLossAddRow',
          this.comName === 'BudgetAdjustment'
        )
      }
    },
    // 表单校验是否通过
    rulePass(isPure) {
      const result = []
      console.log(this.$refs, 'this.$refs')
      for (const key in this.$refs) {
        const refs = ['_incomeItemsFormRef', '_expendItemsFormRef']
        if (this.$refs?._nonProfitAndLossItemsFormRef?.length) {
          refs.push('_nonProfitAndLossItemsFormRef')
        }
        if (this.$refs?._salaryBudgetsFormRef?.length) {
          refs.push('_salaryBudgetsFormRef')
        }
        if (refs.includes(key)) {
          result.push(
            new Promise((resolve, reject) => {
              this.$refs[key][0]?.validate((valid) => {
                console.log(valid, 999)
                if (valid) {
                  resolve()
                } else {
                  reject()
                }
              })
            })
          )
        }
      }

      Promise.all(result)
        .then(async (res) => {
          if (!isPure) {
            this.$emit('handleSubmitSave')
          }
        })
        .catch((_) => {
          console.log(
            '🚀 ~ file: seniorTabSetting.vue ~ line 438 ~ Promise.all ~ _',
            _
          )
        })
    },
    // 校验支出预算科目是否合法
    ruleAccountintItemCode(rule, value, callback, index) {
      const val = this.tabTable.data[this.activeTab][index].accountintItemCode
      if (!val) {
        callback(new Error('请选择支出预算科目'))
      }
      callback()
    },
    // 校验本次调整数是否合法
    ruleAdjustAmountDelta(rule, value, callback, row, index) {
      // 当前数据父节点index
      const parentNode = this.tabTable.data[this.activeTab].findIndex(
        (cur) => cur?.accountingItemCode === row?.parentAccountingItemCode
      )
      // 当前数据兄弟节点(含当前节点)
      const brotherNode = this.tabTable.data[this.activeTab].filter(
        (cur) =>
          cur?.accountingItemCode.includes(
            this.tabTable.data[this.activeTab][parentNode]?.accountingItemCode
          ) &&
          cur?.accountingItemCode?.length === row?.accountingItemCode.length
      )
      // 父节点额度
      const parentNodeTotal =
        this.tabTable.data[this.activeTab][parentNode]?.adjustAmountAfter ?? 0
      // 兄弟节点总额
      const brotherNodeTotal = brotherNode.reduce(
        (total, cur) => total + (cur?.adjustAmountAfter ?? 0),
        0
      )
      // 当前行
      const curRow = this.tabTable.data[this.activeTab][index]
      // 项目-叶子节点总额超出父节点额度
      if (row.leafItem === 2 && brotherNodeTotal > parentNodeTotal) {
        callback(new Error('超出预算总额'))
      }
      // 项目-当前行调整后的预算不能小于本年实际发生数
      if (
        row.leafItem &&
        curRow?.curYearAmount &&
        curRow?.adjustAmountAfter < curRow?.curYearAmount
      ) {
        callback(new Error('不能小于本年实际发生数'))
      }
      callback()
    },
    // 校验项目名称是否合法
    ruleAccountingItemTitle(rule, value, callback, index, activeTab) {
      const val =
        this.tabTable.data[activeTab]?.[index]?.accountingItemTitle ?? ''
      if (!val) {
        callback(new Error('请输入项目名称'))
      }
      callback()
    },
    // 校验科目名称是否合法
    ruleAccountingItemPathTitle(rule, value, callback, row) {
      if (!row.accountingItemCode) {
        callback(new Error('请选择科目'))
      }
      callback()
    },
    // 校验本年计划数是否合法
    ruleBudgetAmount(rule, value, callback, row) {
      if (row.accountingItemLevel === 1) return callback()
      // 当前数据父节点
      const parentNode = this.tabTable.data[this.activeTab].findIndex(
        (cur) => cur?.accountingItemCode === row?.parentAccountingItemCode
      )
      // 当前数据兄弟节点(含当前节点)
      const brotherNode = this.tabTable.data[this.activeTab].filter(
        (cur) =>
          cur?.accountingItemCode.includes(
            this.tabTable.data[this.activeTab][parentNode]?.accountingItemCode
          ) &&
          cur?.accountingItemCode?.length === row?.accountingItemCode.length
      )
      // 父节点额度
      const parentNodeTotal =
        this.tabTable.data[this.activeTab][parentNode]?.budgetAmount ?? 0
      // 兄弟节点总额
      const brotherNodeTotal = brotherNode.reduce(
        (total, cur) => total + cur?.budgetAmount ?? 0,
        0
      )
      // 叶子节点总额超出父节点额度
      if (row.leafItem === 2 && brotherNodeTotal > parentNodeTotal) {
        callback(new Error('超出预算总额'))
      }
      callback()
    },
    // 计算缩进
    handleCountTab(val) {
      const level = val?.length / 3
      return `${(level - 1) * 20}px`
    },
    // 计算本年调整数
    handleTotalAdjustAmountDelta(row, index, budgetType) {
      this.$emit('handleTotalAdjustAmountDelta', { row, index, budgetType })
      this.$nextTick(() => {
        this.rulePass(true)
      })
    },
    // 附件
    handleFileChange(batchId) {
      this.$emit('handleFileChange', batchId)
    },
    // 新增行
    handleAddRow(sourceName, row, index) {
      this.flag = true
      this.$emit('handleAddRow', { sourceName, row, index })
      setTimeout(() => {
        this.flag = false
      }, 500)
    },
    // 删除行
    handleDelRow(index, code, { row, index2, budgetType }) {
      this.$emit('handleDelRow', index, code)
    },
    // 计算本年合计
    handleAmountCount(row, index, budgetType, prop) {
      this.$emit('handleAmountCount', { row, index, budgetType, prop })
      this.$nextTick(() => {
        this.rulePass(true)
      })
    },
    // 渲染文本
    renderText(val) {
      const cur = this.accountingItemType.find((item) => item.id == val)
      return cur?.text ? `预算${cur?.text}` : ''
    },
    // 重置选中tab
    resetActiveTab() {
      this.activeTab = 'incomeItems'
    },
    async handleClick(tab, event) {
      const type = tab.$options.propsData.name
      //   非损益下拉过滤掉预算支出的
      if (
        type === 'nonProfitAndLossItems' &&
        this.subjectListFilter?.length < 1 &&
        this.books?.id
      ) {
        await getItemList({ booksId: this.books.id })
          .then((res) => {
            console.log('res is ', res)
            if (res?.returnCode == 0) {
              this.subjectListFilter = res.data
            }
          })
          .catch((err) => {
            this.$message.error(err)
          })
      }
      this.subjectListFinally =
        type === 'nonProfitAndLossItems'
          ? this.subjectListFilter
          : this.subjectList
      this.$emit('handleActiveTab', tab.name)
    }
  }
}
</script>

<style lang="scss" scoped>
// ::v-deep .el-table__row {
//   .el-form-item {
//     margin-bottom: 0 !important;
//   }
// }
::v-deep .el-input-number.is-without-controls .el-input__inner {
  text-align: right;
  padding-left: 5px;
  padding-right: 5px;
}
.el-cascader {
  width: 100%;
}
::v-deep .accountint-item-code {
  .el-radio {
    width: 100%;
    height: 100%;
    z-index: 10;
    position: absolute;
    top: 10px;
    right: 10px;
  }
  .el-radio__input {
    visibility: hidden;
  }
  .el-cascader-node__postfix {
    top: 10px;
  }
}
</style>
