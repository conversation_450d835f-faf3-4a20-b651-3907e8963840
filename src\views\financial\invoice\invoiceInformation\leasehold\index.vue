<!-- 不动产租赁 -->
<template>
  <div>
    <fold-box v-if="areaLogin.level === 5" :right-title="$t('不动产租赁')">
      <template #right>
        <div v-loading="loading" class="right-box">
          <el-form ref="_searchFormRef" :inline="true" :model="form">
            <el-form-item :label="$t('合同编号')" prop="htBh">
              <el-input v-model="form.htBh" type="text" clearable class="w126"/>
            </el-form-item>
            <el-form-item :label="$t('详细地址')" prop="bdcXxdz">
              <el-input v-model="form.bdcXxdz" type="text" clearable class="w126" />
            </el-form-item>
            <el-form-item :label="$t('产权证号')" prop="bdcCqjh">
              <el-input v-model="form.bdcCqjh" type="text" clearable class="w126" />
            </el-form-item>
            <el-form-item prop="startDate1" :label="$t('租赁开始日期')">
              <el-date-picker v-model.trim="form.startDate1" class="w126" value-format="yyyy-MM-dd" />
            </el-form-item>
            -
            <el-form-item prop="startDate2" style="margin-left: 10px">
              <el-date-picker v-model.trim="form.startDate2" class="w126" value-format="yyyy-MM-dd" />
            </el-form-item>
            <el-form-item prop="endDate1" :label="$t('租赁截止日期')">
              <el-date-picker v-model.trim="form.endDate1" class="w126" value-format="yyyy-MM-dd" />
            </el-form-item>
            -
            <el-form-item prop="endDate2" style="margin-left: 10px">
              <el-date-picker v-model.trim="form.endDate2" class="w126" value-format="yyyy-MM-dd" />
            </el-form-item>
            <div class="btn-box">
              <el-button type="primary" round @click="handleSearch">
                {{ $t('搜索') }}
              </el-button>
              <el-button type="primary" round @click="handleAdd">
                {{ $t('新增') }}
              </el-button>
            </div>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            :columns="tableColumns"
            :data="table.rows"
            highlight-current-row
            pagination
            :total="table.total"
            :pagi="form"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
          >
            <template #bdcKsbz="{ row }">
              {{ row.bdcKsbz === 1 ? '是' : '否' }}
            </template>
            <template #operation="{ row }">
              <el-button v-hasPermi="'financial.invoice.invoiceInformation.leasehold.view'" type="text"
                @click="handleView(row)">
                {{ $t('查看') }}
              </el-button>
              <el-button v-hasPermi="'financial.invoice.invoiceInformation.leasehold.save'" type="text"
                @click="handleEdit(row)">
                {{ $t('编辑') }}
              </el-button>
              <el-button v-hasPermi="'financial.invoice.invoiceInformation.leasehold.delete'" type="text"
                @click="handleDelete(row)">
                {{ $t('删除') }}
              </el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <details-dialog
      v-if="detailsVisible"
      :dialog-visible.sync="detailsVisible"
      :status-type="statusType"
      :row-data="nowRowData"
      @updateList="getList"
    ></details-dialog>
  </div>
</template>

<script>
import {
  page,
  deleteData
} from '@/api/financial/invoice/invoiceInformation/leasehold.js'
import detailsDialog from './components/details.vue'
import { createNamespacedHelpers } from 'vuex'

const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'Leasehold',
  components: { detailsDialog },
  data() {
    return {
      loading: false,
      form: {
        htBh: '',
        bdcXxdz: '',
        bdcCqjh: '',
        startDate1: '',
        endDate1: '',
        startDate2: '',
        endDate2: '',
        page: 1,
        rows: 20
      },
      tableColumns: [
        { type: 'selection', fixed: 'left', minWidth: 50 },
        { type: 'index', label: '序号', fixed: 'left', minWidth: 60 },
        { prop: 'htBh', label: '合同编号', minWidth: 150, align: 'left' },
        { prop: 'bdcSf', label: '地址', minWidth: 150, align: 'left' },
        { prop: 'bdcXxdz', label: '详细地址', minWidth: 150, align: 'left' },
        { prop: 'bdcQsrq', label: '租赁开始日期', minWidth: 110, align: 'center' },
        { prop: 'bdcJzrq', label: '租赁截止日期', minWidth: 110, align: 'center' },
        { prop: 'bdcKsbz', label: '跨市标识', minWidth: 80, align: 'center' },
        { prop: 'bdcCqjh', label: '产权证号', minWidth: 120, align: 'left' },
        { prop: 'bdcPjdw', label: '面积单位', minWidth: 80, align: 'center', convert: { path: '/financial/invoice/digital/bdcDw/' } },
        { slotName: 'operation', label: '操作', width: 120, align: 'center', fixed: 'right' }
      ],
      table: {
        rows: [],
        total: 0
      },
      nowRowData: {},
      detailsVisible: false,
      statusType: 'view'
    }
  },
  computed: {
    ...mapState(['areaLogin']), // 地区机构数据
    // table选择数据
    selectRows() {
      return this.$refs['_geverTableRef'].selection
    }
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler() {
        this.getList()
      }
    }
  },
  mounted() {
    if (this.areaLogin.level !== 5) {
      this.$message.warning('请选择机构')
    }
  },
  methods: {
    /* 获取table数据 */
    getList() {
      const params = {
        orgId: this.areaLogin.id,
        ...this.form
      }
      params.startDate1 = params.startDate1 || ''
      params.startDate2 = params.startDate2 || ''
      params.endDate1 = params.endDate1 || ''
      params.endDate2 = params.endDate2 || ''
      this.loading = true
      page(params)
        .then((res) => {
          this.table = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    /* table选择行 */
    handleSelectionChange() {
    },
    /* 重置 */
    handleReset() {
      this.$refs['_searchFormRef'].resetFields()
    },
    /* 搜索 */
    handleSearch() {
      this.getList()
    },
    /* 查看 */
    handleView(row) {
      this.statusType = 'view'
      this.nowRowData = row
      this.detailsVisible = true
    },
    /* 编辑 */
    handleEdit(row) {
      this.statusType = 'edit'
      this.nowRowData = row
      this.detailsVisible = true
    },
    /* 删除 */
    handleDelete(row) {
      this.$confirm('是否确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteData({ id: row.id }).then((res) => {
          this.$message({
            type: 'success',
            message: res.message
          })
          this.getList()
        })
      })
    },
    /* 新增 */
    handleAdd() {
      this.statusType = 'add'
      this.detailsVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-box {
  display: inline-block;
  text-align: right;
  margin-bottom: 10px;
}

::v-deep .gever-table-pagination {
  margin-top: 5px !important;
}

.w126 {
  width: 126px;
}
</style>
