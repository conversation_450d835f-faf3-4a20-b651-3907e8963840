import request from '@/utils/request'
/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentReceiveDetailListApi(data) {
  return request({
    url: '/qrpayment/detail/page',
    method: 'post',
    data
  })
}




/**
 * @name: 详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentReceiveDetailInfoApi(id) {
  return request({
    url: `/qrpayment/detail/get/${id}`,
    method: 'get'
  })
}

/**
 * @name: 删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteQrPaymentReceiveDetailApi(data) {
  return request({
    url: '/qrpayment/detail/delete',
    method: 'post',
    data
  })
}

/**
 * @name: 新增保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveQrPaymentReceiveDetailApi(data) {
  return request({
    url: '/qrpayment/detail/save',
    method: 'post',
    data
  })
}

/**
 * @name: 更新保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateQrPaymentReceiveDetailApi(data) {
  return request({
    url: '/qrpayment/detail/update',
    method: 'post',
    data
  })
}