/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-11-24 09:38:03
 * @LastEditTime: 2021-11-24 16:43:49
 * @LastEditors: Peng Xiao
 * @Description:
 * @FilePath: \gever-zhxc-ui\src\api\financial\asset\depreciation.js
 * ^-^
 */

import request from '@/utils/request'

/**
 * @name:获取资产入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/asset/assetDepreciation/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取计提详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/asset/assetDepreciation/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:保存编辑结果
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/asset/assetDepreciation/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:计提
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function depreciation(data) {
  return request({
    url: `/financial/asset/assetDepreciation/depreciaction`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:删除计提
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteWithBooks(data) {
  return request({
    url: `/financial/asset/assetDepreciation/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function posting(data) {
  return request({
    url: `/financial/asset/assetDepreciation/posting`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消资产入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unPosting(data) {
  return request({
    url: `/financial/asset/assetDepreciation/unPosting`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消凭证
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancelGenerateVoucher(data) {
  return request({
    url: `/financial/asset/assetDepreciation/cancelGenerateVoucher`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:补计提
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function supplement(data) {
  return request({
    url: `/financial/asset/assetDepreciation/supplement`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:过期计提列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function expireDeprList(data) {
  return request({
    url: `/financial/asset/assetDepreciation/expireDeprList`,
    method: 'post',
    data,
    withCredentials: true
  })
}


/**
 * 导出
 */
export function exportTemplate(params) {
  return request({
    url: `/financial/asset/assetDepreciation/exportTemp`,
    method: 'get',
    params
  })
}