/*
 * @Descripttion:出纳月结
 * @version:
 * @Author: 未知
 * @Date: 2021-11-08 10:56:09
 * @LastEditors: Peng Xiao
 * @LastEditTime: 2021-11-26 14:36:49
 */
import request from '@/utils/request'

/**
 * @name:页面
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function monthEndedList(data) {
  return request({
    url: '/financial/asset/assetMonthEnded/monthEndedList',
    method: 'post',
    data,
    withCredentials: true
  })
}

export function beforeMonthEnded(data) {
  return request({
    url: '/financial/asset/assetMonthEnded/beforeMonthEnded',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:月结
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function monthEnded(data) {
  return request({
    url: '/financial/asset/assetMonthEnded/monthEnded',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:反月结
 * @param {String} booksId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unMonthEnded(data) {
  return request({
    url: '/financial/asset/assetMonthEnded/unMonthEnded',
    method: 'post',
    data,
    withCredentials: true
  })
}

