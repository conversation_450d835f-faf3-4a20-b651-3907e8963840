<template>
  <div>
    <fold-box left-title="银行类型" right-title="银行委托类型设置">
      <template #left>
        <div class="ogn-tree">
          <area-cashier-tree
            :tree-data="treeData"
            :expanded-nodes="expandedNodes"
            @selectedNodeChange="handleNodeChange"
          />
        </div>
      </template>
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('委托类型编码')">
              <el-input v-model="queryParams.code"  clearable class="w150" />
            </el-form-item>
            <el-form-item :label="$t('委托类型名称')">
              <el-input v-model="queryParams.name" clearable class="w150" />
            </el-form-item>
            <el-form-item>
              <el-button plain icon="el-icon-search" type="primary" round @click="handleSearch">
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <div style="float: right;">
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.payment.baseSetting.bankEntrustType.add'"
                  type="primary"
                  round
                  icon="el-icon-plus"
                  @click="handleAdd"
                >
                  {{ $t('新增') }}
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.payment.baseSetting.bankEntrustType.delete'"
                  plain
                  type="primary"
                  round
                  icon="el-icon-delete"
                  @click="handleRemove"
                >
                  {{ $t('删除') }}
                </el-button>
              </el-form-item>
            </div>
          </el-form>
          <gever-table
            :loading="tableLoading"
            ref="_geverTableRef"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
          >
            <template #bankType="{ row }">
              {{ convertText(row.bankType, optionsMap.bankType) }}
            </template>
            <template #valid="{ row }">
              <el-switch
                v-has-permi="'financial.payment.baseSetting.bankEntrustType.setValid'"
                :value="row.valid"
                :active-value="1"
                :inactive-value="0"
                @change="heandleEnable(row)"
              />
            </template>
            <template #operation="{ row }">
              <el-button v-hasPermi="'financial.payment.baseSetting.bankEntrustType.view'" type="text" @click="handleView(row)">{{ $t('查看') }}</el-button>
              <el-button v-hasPermi="'financial.payment.baseSetting.bankEntrustType.edit'" type="text" @click="handleEdit(row)">{{ $t('编辑') }}</el-button>
              <el-button v-hasPermi="'financial.payment.baseSetting.bankEntrustType.delete'" type="text" @click="handleRemove(row,false)">{{ $t('删除') }}</el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <gever-dialog
      :title="detailTitle"
      append-to-body
      :visible.sync="detailVisible"
      :buttons="detailButtons"
      custom-class="photograph-dialog"
      width="50%"
      perch-height="400"
    >
      <detail
        v-if="detailVisible"
        :detail-visible.sync="detailVisible"
        ref="_detailContentRef"
        :type="type"
        :id="currentId"
        :bank-type="bankType"
        @refreshTable="getList"
      />
    </gever-dialog>
  </div>
</template>

<script>
import { loadBankEntrustTypeListApi,
  deleteBankEntrustTypeApi,
  setValidBankEntrustTypeApi } from '@/api/payment/bankEntrustType.js'
import detail from './components/detail'
import { getDictionary } from '@/api/gever/common.js'

export default {
  name: 'BankEntrustType',
  components: {
    detail
  },
  data() {
    return {
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      treeData: [],
      expandedNodes: [],
      optionsMap: {
        bankType: []
      },
      queryParams: {
        page: 1,
        rows: 20
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50' },
          { type: 'index', label: '序号', width: '50' },
          { label: '银行类型', prop: 'bankType' },
          { label: '委托类型编码', prop: 'code' },
          { label: '委托类型名称', prop: 'name' },
          { label: '是否有效', prop: 'valid' },
          { label: '操作', slotName: 'operation', width: '130', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      bankType: '',
      currentId: '',
      detailTitle: '',
      detailVisible: false,
      detailButtons: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('确 定'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
    }
  },
  created() {
    this.getDict()
    this.getList()
  },
  methods: {
    getDict() {
      getDictionary('/payment/pay_bank_type').then((res) => {
        if (res.returnCode === '0') {
          this.optionsMap.bankType = res.data
          this.treeData = [
            {
              id: '',
              name: '全部',
              text: '全部',
              state: 'closed',
              type: '',
              children: res.data
            }
          ]
          this.expandedNodes = ['']
        }
      })
    },
    getList() {
      this.tableLoading = true
      loadBankEntrustTypeListApi(this.queryParams).then(res => {
        this.table.tableList = res.data.rows
        this.table.total = res.data.total
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleSearch() {
      this.getList()
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleNodeChange(data) {
      this.selectedNode = data
      this.queryParams.bankType = data.id
      this.bankType = data.id
      this.getList()
    },
    handleAdd() {
      this.detailTitle = '新增银行委托类型设置'
      this.type = 'add'
      this.currentId = ''
      this.detailVisible = true
    },
    handleView(row) {
      this.detailTitle = '查看银行委托类型设置'
      this.type = 'view'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleEdit(row) {
      this.detailTitle = '编辑银行委托类型设置'
      this.type = 'edit'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleRemove(row, batchFlag) {
      let ids
      if (batchFlag) {
        ids = row.id
      } else {
        if (this.tableIdSelection.length == 0) {
          return this.$message.warning(this.$t('请先选择要删除的数据！'))
        }
        ids = this.tableIdSelection.join(',')
      }
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        deleteBankEntrustTypeApi({ ids }).then(res => {
          if (res) {
            this.$message.success(res.message)
            this.getList()
          }
        })
      }).catch(() => {})
    },
    handleCloseDetail() {
      this.detailVisible = false
    },
    handleSave() {
      this.$refs._detailContentRef.save()
    },
    heandleEnable(row) {
      this.$confirm(
        this.$t('确定要') +
        (row.valid == 1 ? '停用' : '启用') +
        this.$t('【') + row.fullName + this.$t('】') +
        this.$t('委托类型吗？'),
        {
          confirmButtonText: this.$t('确定'),
          cancelButtonText: this.$t('取消'),
          type: 'warning'
        }
      ).then(async () => {
        const params = {
          id: row.id,
          valid: row.valid === 1 ? 0 : 1
        }
        const { returnCode, message } = await setValidBankEntrustTypeApi(params)
        if (returnCode === '0') {
          this.$message.success(message)
          this.getList()
        } else {
          this.$message.error(message)
        }
      })
    },
    convertText(val, options) {
      const curOption = options.find(
        (item) => item.id === val + ''
      )
      return curOption ? curOption.text : ''
    }
  }
}
</script>
