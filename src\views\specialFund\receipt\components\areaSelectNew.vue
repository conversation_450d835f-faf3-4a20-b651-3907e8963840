<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-23 17:19:14
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-23 15:06:39
-->
<template>
  <el-select
    :id="dymamicId"
    ref="_treeSelectRef"
    :value="valueId"
    class="w100% tree-select"
    v-bind="$attrs"
    popper-class="bottom-start"
    :popper-append-to-body="false"
    :clearable="clearable"
    :multiple="multiple"
    :title="valueText"
    @clear="handleClear"
    v-on="$listeners"
    @visible-change="handleVisibleChange"
  >
    <el-option :value="valueId" :label="valueText" style="height: auto">
      <el-tree
        ref="_treeRef"
        lazy
        :filter-node-method="filterNodeMethod"
        :expand-on-click-node="false"
        :load="load"
        :show-checkbox="multiple"
        :props="{
          label: 'text',
          isLeaf: 'isLeaf',
          children: 'children',
          value: valueProp
        }"
        :node-key="valueProp"
        :default-expanded-keys="defaultExpandedKey"
        v-bind="$attrs"
        v-on="$listeners"
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <i
            v-if="
              data.type === 'Organization' ||
                (data.code && data.code.startsWith('O'))
            "
            class="tree-node-org"
          />
          <i v-else-if="data.isLeaf" class="tree-node-leaf" />
          <i v-else-if="node.expanded" class="tree-node-expanded" />
          <i v-else class="tree-node-unexpanded" />
          <span :title="node.label">{{ node.label }}</span>
        </span>
      </el-tree>
    </el-option>
  </el-select>
</template>

<script>
import {
  treeData as organization,
  authTreeData as authOrganization
} from '@/api/system/organization.js'
import {
  treeData as area,
  authTreeData as authArea
} from '@/api/system/area.js'
export default {
  name: 'AreaSelectNew',
  props: {
    treeNodeData: {
      type: [Object, Array],
      default: null
    }, // 默认值，包含valueProp属性(id或者code)、text属性
    valueProp: {
      type: String,
      default: 'id',
      validator: (value) => ['code', 'id', 'text'].includes(value)
    }, // 取值字段
    selectableType: {
      type: String,
      default: '',
      validator: (value) => ['org', 'area', ''].includes(value)
    }, // 可选择的节点类型，org表示只能选择机构，area表示只能选择地区，为空表示不限制
    type: {
      type: String,
      default: 'area',
      validator: (value) =>
        ['authOrg', 'org', 'authArea', 'area'].includes(value)
    }, // 树的类型
    clearable: {
      type: Boolean,
      default: false
    }, // 是否可清空
    multiple: {
      type: Boolean,
      default: false
    }, // 是否多选,多选时显示checkbox
    areaId: {
      type: String,
      default: ''
    }, // 地区id
    text: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dymamicId: Math.random() + '',
      valueId: '',
      valueText: '',
      defaultExpandedKey: [],
      expandedKeys: [],
      scrollTop: -1
    }
  },
  watch: {
    // 异步原因，初始值赋值放在watch中
    treeNodeData: {
      handler (val) {
        if (this.multiple) {
          if (this.treeNodeData.length) {
            let valid = true
            const valueArr = []
            // let text
            this.treeNodeData.forEach((item) => {
              valueArr.push(item[this.valueProp])
              if (!item[this.valueProp] || !item.text) {
                valid = false
              }
            })
            if (valid) {
              this.valueId = valueArr
            }
          } else {
            this.valueId = []
            this.valueText = ''
          }
        } else {
          if (this.treeNodeData) {
            if (this.treeNodeData[this.valueProp] && this.treeNodeData.text) {
              this.valueId = this.treeNodeData[this.valueProp]
              this.valueText = this.treeNodeData.text
              this.$emit('selectedNodeChange', this.treeNodeData) // 触发选择事件
              this.$emit('input', this.treeNodeData[this.valueProp])
            } else {
              this.valueId = ''
              this.valueText = ''
            }
          } else {
            this.valueId = ''
            this.valueText = ''
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  created () {

  },
  mounted () { },
  methods: {
    filterNodeMethod (value, data) {
      if (!value) return true
      return data.text.indexOf(value) !== -1
    },
    // 加载节点
    async load (node, resolve) {
      try {
        const { data = [] } = await this.getChildren(node)
        resolve(data)
      } catch (error) {
        resolve([])
      }
    },
    // 获取下一级
    async getChildren (node) {
      const { level } = node
      const params = level ? { id: node?.data?.id } : { id: this.areaId }
      const requestMap = {
        authOrg: authOrganization,
        org: organization,
        authArea: authArea,
        area: area
      }
      if (requestMap[this.type]) {
        const { data } = await requestMap[this.type](params)
        data.forEach((item) => {
          item.isLeaf = !item.state
        })
        return { data }
      }
      return { data: [] }
    },
    handleNodeClick (val) {
      if (this.selectableType === 'org' && val.type === 'Area') {
        return this.$message.warning('请选择机构')
      }
      if (this.selectableType === 'area' && val.type === 'Organization') {
        return this.$message.warning('请选择地区')
      }
      this.valueId = val[this.valueProp]
      this.valueText = val.text

      this.defaultExpandedKey = []
      const popper = this.$refs._treeSelectRef.popperElm
      const wrap = popper.querySelector('.el-select-dropdown__wrap')
      this.scrollTop = wrap.scrollTop
      console.log('wrap.scrollTop', wrap.scrollTop)

      this.$refs['_treeSelectRef'].blur()
      this.$emit('selectedNodeChange', val) // 触发选择事件
      this.$emit('handleChange')
      this.$emit('input', val[this.valueProp])
    },
    handleClear () {
      if (!this.clearable) return
      this.valueId = ''
      this.valueText = ''
      this.defaultExpandedKey = []
      this.$emit('selectedNodeChange', {}) // 触发选择事件
      this.$emit('input', '')
    },
    handleVisibleChange (visible) {
      if (visible) {
        this.$nextTick(() => {
          setTimeout(async () => {
            const tree = this.$refs._treeRef
            if (!tree) return
            const popper = this.$refs._treeSelectRef.popperElm
            const wrap = popper?.querySelector('.el-select-dropdown__wrap')
            if (!wrap) return
            if (this.scrollTop == -1) {
              wrap.scrollTop = 0
            } else {
              wrap.scrollTop = this.scrollTop
            }
          }, 50)
        })
      } else {
        console.log('关闭')

        const popper = this.$refs._treeSelectRef.popperElm
        const wrap = popper.querySelector('.el-select-dropdown__wrap')
        this.scrollTop = wrap.scrollTop
      }
    }
  }
}
</script>

<style lang="scss" scoped>
i[class^='tree-node-']:before {
  content: '替换';
  visibility: hidden;
}
.tree-node-unexpanded {
  background: url('../../../../assets/images/tree/treegridClose.png') center
    no-repeat;
}
.tree-node-division {
  background: url('../../../../assets/images/tree/level1.png') center no-repeat;
}
.tree-node-expanded {
  background: url('../../../../assets/images/tree/treegridOpen.png') center
    no-repeat;
}
.tree-node-leaf {
  background: url('../../../../assets/images/tree/treegridItem.png') center
    no-repeat;
}
.tree-node-org {
  background: url('../../../../assets/images/tree/level2.png') center no-repeat;
}
.el-select-dropdown .tree-node-unexpanded,
.el-select-dropdown .tree-node-expanded,
.el-select-dropdown .tree-node-leaf {
  background: url('../../../../assets/images/tree/level1.png') center no-repeat;
}
::v-deep .el-tree {
  .is-current > .el-tree-node__content {
    background-color: var(--color-primary-active);
  }
  .custom-tree-node {
    width: -webkit-fill-available;
  }
}

.tree-select {
  ::v-deep .el-select-dropdown__item {
    padding: 0;
  }
  ::v-deep .el-select-dropdown__item.selected {
    font-weight: normal !important;
  }
}
</style>
