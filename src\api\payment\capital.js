import request from '@/utils/request'

/**
 * 获取打印状态
 * @param {*} params 查询参数
 */
export function getPrintStatus(id) {
  return request({
    url: `/paymentbill/paymentBillIssue/getPrintStatus/${id}`,
    method: 'get',
    payload: true
  })
}
/**
 * 发送银行
 * @param {*} data 查询参数
 */
export function sendBank(data) {
  return request({
    url: '/paymentbill/paymentBillIssue/sendBank',
    method: 'post',
    data,
  })
}
/**
 * 更新结算信息
 * @param {*} data 查询参数
 */
export function updateSettlement(data) {
  return request({
    url: '/payment/manager/payOrder/updateSettlement',
    method: 'post',
    data,
  })
}
/**
 * 更新结算信息
 * @param {*} data 修改支付结果时间
 */
export function updateActualPayTime(data) {
  return request({
    url: '/payment/manager/payOrder/updateActualPayTime',
    method: 'post',
    data,
  })
}
/**
 * 确认支付结果保存
 * @param {*} data 查询参数
 */
export function confirmPayment(data) {
  return request({
    url: '/payment/manager/payOrder/confirmPayment',
    method: 'post',
    data,
    payload: true
  })
}
/**
 * 确认支付失败保存
 * @param {*} data 查询参数
 */
 export function confirmPaymentFail(data) {
  return request({
    url: '/payment/manager/payOrder/confirmPaymentFail',
    method: 'post',
    data,
    payload: true
  })
}
/**
 * 办理支付保存/暂存
 * @param {*} data 查询参数
 */
export function savePayment(data, url) {
  return request({
    url: `/payment/manager/payOrder/${url}`,
    method: 'post',
    data,
    payload: true
  })
}
/**
 * 获取支付单列表
 * @param {*} data 查询参数
 */
export function loadPayOrderList(data) {
  return request({
    url: '/payment/manager/payOrder/page',
    method: 'post',
    params: data
  })
}

/**
 * 获取支付单信息
 * @param {*} data 支付单id
 */
export function loadPayOrderInfo(data) {
  return request({
    url: '/payment/manager/payOrder/load/' + data,
    method: 'get'
  })
}

/**
 * 获取支付单中收款人列表
 * @param {*} data 支付单id
 */
export function loadPayOrderPayee(data) {
  return request({
    url: '/payment/manager/payOrderPayee/page',
    method: 'post',
    params: data
  })
}

/**
 * 获取可用的支付账户
 * @param {*} data 支付单id
 */
export function loadAvailableAccount(data) {
  return request({
    url: '/payment/countAvailableAccount/' + data,
    method: 'get'
  })
}

/**
 * 终止支付
 * @param {*} id 支付单id
 * @param {*} data 终止原因
 */
export function terminatePayOrder(id, data) {
  return request({
    url: '/payment/manager/payOrder/terminate/' + id,
    method: 'post',
    data: data
  })
}

/**
 * 终止支付
 * @param {*} id 支付单id
 * @param {*} data 终止原因
 */
export function loadOrderLog(data) {
  return request({
    url: '/payment/manager/orderLog/page',
    method: 'post',
    params: data
  })
}

/**
 * 银行账户列表
 * @param {*} data 查询参数
 */
export function loadBankAccountList(data) {
  return request({
    url: '/payment/manager/bankAccount/page',
    method: 'post',
    params: data
  })
}

/**
 * 支付渠道列表
 * @param {*} data 查询参数
 */
export function loadChannelList(data) {
  return request({
    url: '/payment/manager/bankAccount/listChannel',
    method: 'get',
    params: data
  })
}

/**
 * 保存银行账户
 * @param {*} data 查询参数
 */
export function saveBankAccount(data) {
  return request({
    url: '/payment/manager/bankAccount/save',
    method: 'post',
    params: data
  })
}

/**
 * 更新是否有监管协议
 * @param {*} data 查询参数
 */
export function updateRegulatoryAgreement(data) {
  return request({
    url: '/payment/manager/bankAccount/updateRegulatoryAgreement',
    method: 'post',
    params: data
  })
}

/**
 * 获取银行账户信息
 * @param {*} data 银行账户id
 */
export function loadBankAccountInfo(data) {
  return request({
    url: '/payment/manager/bankAccount/load/' + data,
    method: 'get'
  })
}

/**
 * 删除银行账户
 * @param {*} data 参数
 */
export function removeBankAccount(data) {
  return request({
    url: '/payment/manager/bankAccount/delete',
    method: 'post',
    params: data
  })
}

/**
 * 更新授权
 * @param {*} data id数组
 */
export function updateBankAccountAuthorize(data) {
  return request({
    url: '/payment/manager/bankAccount/updateAuthorize',
    method: 'post',
    data: data,
    payload: true
  })
}

/**
 * 获取银行账户列表
 * @param {*} data id数组
 */
export function loadAccountList(params) {
  return request({
    url: '/payment/manager/bankAccount/listAccount',
    method: 'get',
    params
  })
}

/**
 * 获取银行余额
 * @param {*} data id
 */
export function loadAccountBalance(data) {
  return request({
    url: '/payment/manager/bankAccount/queryBalance/' + data,
    method: 'get'
  })
}

/**
 * 获取银行明细
 * @param {*} data 查询参数
 */
export function loadAccountDetail(data) {
  return request({
    url: '/payment/manager/bankStatement/page',
    method: 'post',
    params: data
  })
}

/**
 * 获取支付用户列表
 * @param {*} data 查询参数
 */
export function loadPaymentUserList(data) {
  return request({
    url: '/payment/manager/authorize/getPaymentUser',
    method: 'post',
    params: data
  })
}

/**
 * 获取支付结果列表
 * @param {*} data 查询参数
 */
export function loadPayResultList(data) {
  return request({
    url: '/payment/manager/payOrderPayee/pageByResult',
    method: 'post',
    data: data
  })
}

/**
 * 获取支付结果详情
 * @param {*} data 支付单id
 */
export function loadPayResultInfo(data) {
  return request({
    url: '/payment/manager/payOrderPayee/loadResult/' + data,
    method: 'get'
  })
}

/**
 * 获取支付管理员列表
 */
export function loadPaymentManagerList() {
  return request({
    url: '/payment/manager/authorize/paymentManager',
    method: 'get'
  })
}

/**
 * 发送验证码
 */
export function sendSmsCode(data) {
  return request({
    url: '/payment/manager/authorize/sendSmsCode',
    method: 'post',
    params: data
  })
}

/**
 * 同步支付用户
 */
export function syncPaymentUser(data) {
  return request({
    url: '/payment/manager/authorize/syncPaymentUser',
    method: 'post',
    params: data
  })
}

/**
 * 删除支付用户
 * @param {*} data 支付用户id
 */
export function deletePaymentUser(data) {
  return request({
    url: '/payment/manager/authorize/delete/' + data,
    method: 'post'
  })
}

/**
 * 获取所有支付资源
 */
export function getAllPaymentResource(data) {
  return request({
    url: '/payment/manager/authorize/getAllPaymentResource',
    method: 'get'
  })
}

/**
 * 获取已授权支付资源
 * @param {*} data 支付用户id
 */
export function getAuthedPaymentResource(data) {
  return request({
    url: '/payment/manager/authorize/authorizedResourceId',
    method: 'get',
    params: data
  })
}

/**
 * 授权支付资源
 * @param {*} data 支参数
 */
export function authorizeResource(data) {
  return request({
    url: '/payment/manager/authorize/authorizeResource',
    data: data,
    method: 'post'
  })
}

/**
 * 根据支付单id获取可用支付账户的数量
 * @param {*} data 支付单id
 */
export function countAvailableAccount(data) {
  return request({
    url: '/payment/countAvailableAccount/' + data,
    method: 'get'
  })
}

/**
 * 根据支付单id获取可用支付账户的详情
 * @param {*} data 支付单id
 */
export function selectAvailableAccount(data) {
  return request({
    url: '/payment/selectAvailableAccount/' + data,
    method: 'get'
  })
}

/**
 * 获取支付用户预留手机号
 */
export function getPhoneNumber() {
  return request({
    url: '/payment/getPhoneNumber',
    method: 'get'
  })
}

/**
 * 发送支付验证码
 */
export function sendPaySmsCode(data) {
  return request({
    url: '/payment/sendSmsCode',
    method: 'post',
    data: data
  })
}

/**
 * 支付
 */
export function pay(data) {
  return request({
    url: '/payment/pay',
    method: 'post',
    data: data
  })
}

/**
 * 重新支付(retryType = 2)
 */
export function repay2(data) {
  return request({
    url: '/payment/repay2',
    method: 'post',
    data: data
  })
}

/**
 * 重新支付
 */
export function repay(data) {
  return request({
    url: '/payment/repay',
    method: 'post',
    data: data
  })
}

/**
 * 忽略重新支付
 */
export function ignoreRepay(data) {
  return request({
    url: '/payment/ignoreRepay/' + data,
    method: 'post'
  })
}

/**
 * 获取退回明细列表
 */
export function loadRefundedList(data) {
  return request({
    url: '/payment/manager/bankStatement/selectRefundedStatement',
    method: 'post',
    data: data
  })
}

/**
 * 退回明细匹配
 */
export function matchRefunded(data) {
  return request({
    url: '/payment/manager/bankStatement/matchingRefunded',
    method: 'post',
    data: data,
    payload: true
  })
}

/**
 * 获取支付账户信息
 */
export function loadBankStatement(id) {
  return request({
    url: '/payment/manager/bankStatement/load/' + id,
    method: 'get'
  })
}

/**
 * 获取用户是否已授权
 */
export function checkPaymentUser(id) {
  return request({
    url: '/payment/manager/authorize/checkPaymentUser?userIds=' + id,
    method: 'get'
  })
}

/**
 * 获取是否需要短信验证码
 */
export function querySMS(id) {
  return request({
    url: '/payment/manager/bankAccount/querySMS/' + id,
    method: 'get'
  })
}

/**
 * 重新支付暂存
 */
export function tempSaveRepay(id, data) {
  return request({
    url: '/payment/tempSaveRepay/' + id,
    method: 'post',
    data: data,
    payload: true
  })
}

export function changeRetryPayee(id, data) {
  return request({
    url: '/payment/changeRetryPayee/' + id,
    method: 'post',
    data: data,
    payload: true
  })
}

// 查询支付结果
export function queryPaymentResult(id) {
  return request({
    url: '/payment/queryPaymentResult/' + id,
    method: 'get'
  })
}

/**
 * 查询账号的委托业务
 * @param {*} data 银行账户id
 */
export function listEntrustType(data) {
  return request({
    url: '/payment/manager/bankAccount/listEntrustType/' + data,
    method: 'get'
  })
}

/**
 * 查询银行的委托业务
 * @param {*} data 银行类型
 */
export function listBankEntrustType(data) {
  return request({
    url: '/payment/manager/bankEntrustType/listByBankType',
    method: 'get',
    params: data
  })
}


/**
 * 保存账号的委托类型
 */
export function setEntrustType(data) {
  return request({
    url: '/payment/manager/bankAccount/setEntrustType',
    method: 'post',
    data: data
  })
}
