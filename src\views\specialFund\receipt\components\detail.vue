<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-15 16:06:46
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-25 18:03:39
-->
<template>
  <div v-loading="loading" class="gever-form detail">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="170px"
      :disabled="type == 'view'"
    >
      <div class="gever-title label-title">{{ $t('基本信息') }}</div>
      <div class="form-db">
        <el-form-item :label="$t('单位名称')" prop="orgId">
          <areaSelectNew
            v-if="area.type == 'Area' && type != 'view'"
            v-model="contentForm.orgId"
            placeholder="请选择单位名称"
            type="org"
            selectable-type="org"
            :area-id="area.id"
            :tree-node-data="treeNodeData"
            @selectedNodeChange="handleSelectedNodeChange"
          />
          <gever-input v-else v-model="contentForm.orgName" disabled />
          <!-- <gever-input v-else v-model="contentForm.orgName" /> -->
        </el-form-item>
        <el-form-item :label="$t('专项资金编号')" prop="receiptCode">
          <gever-input v-model="contentForm.receiptCode" disabled placeholder="自动生成" />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('专项资金类别')" prop="categoryId">
          <categoryTreeSelect
            v-model="contentForm.categoryId"
            :category-id="contentForm.categoryId"
            :title="contentForm.categoryCode + ' ' + contentForm.categoryName"
            :options="treeData"
            :props="props"
            :area-code="areaCode"
            :show-all-levels="false"
            :is-status="true"
            placeholder="请选择专项资金类别"
            @getValue="handleCategoryValue"
          ></categoryTreeSelect>
        </el-form-item>
        <el-form-item :label="$t('专项资金来源')" prop="specialSource">
          <gever-select
            v-model="contentForm.specialSource"
            number-key
            path="/financial/specialFund/receipt/specialSource/"
            placeholder="请选择专项资金来源"
          />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('专项资金名称')" prop="receiptName">
          <!-- <gever-input v-model="contentForm.receiptName" placeholder="请输入专项资金名称" /> -->
          <el-autocomplete
            v-model="contentForm.receiptName"
            :fetch-suggestions="querySearchAsync"
            style="width: 100%;"
            :maxlength="100"
            placeholder="请输入专项资金名称"
            show-word-limit
            @select="handleSelect"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item :label="$t('收款账户')" prop="bankAccountId">
          <gever-select
            v-model="contentForm.bankAccountId"
            :options="accountOptions"
            placeholder="请选择收款账户"
            @change="handleBankAccountChange"
          />
        </el-form-item>
      </div>
      <div class="form-db">
        <el-form-item :label="$t('金额')" prop="amount">
          <el-input-number
            v-model="contentForm.amount"
            :controls="false"
            :precision="2"
            :step="0.1"
            :min="0"
            :max="**********.99"
            placeholder="请输入金额"
          ></el-input-number>
        </el-form-item>
        <el-form-item :label="$t('期限')" prop="bankAccountName">
          <el-date-picker
            v-model="contentForm.term"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="请选择期限"
            style="width: 100%;"
          ></el-date-picker>
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('专项资金用途')" prop="purpose">
          <gever-input
            v-model="contentForm.purpose"
            type="textarea"
            :maxlength="1000"
            :rows="8"
            show-word-limit
            placeholder="请输入专项资金用途"
          />
        </el-form-item>
      </div>
      <div class="gever-title label-title">
        {{ $t('附件') }}
        <span
          style="color: red;font-size:10px;font-weight: normal;"
        >(仅支持上传gif,jpg,jpeg,png,doc,xls,txt,pdf,docx,xlsx,zip)</span>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('')" prop="attachment">
          <gever-upload
            list-type="form-list"
            :accept="'.gif,.jpg,.jpeg,.png,.doc,.xls,.txt,.pdf,.docx,.xlsx,.zip'"
            :limit="10"
            :default-batch-id="contentForm.systemBatchId"
            :business-id="contentForm.id"
            :file-classification="'receipt'"
            :disabled="type=='view'"
            @batch-change="handleBatchIdChange"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import {
  loadReceiptInfoApi, saveReceiptApi, updateReceiptApi, listAccount, selectList
} from '@/api/specialFund/receipt.js'
import { selectByParentId } from '@/api/specialFund/category.js'
import categoryTreeSelect from './categoryTreeSelect'
import areaSelectNew from './areaSelectNew'
import { getDictionary } from '@/api/gever/common.js'
export default {
  components: {
    categoryTreeSelect,
    areaSelectNew
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    specialType: {
      type: Number,
      default: 0
    },
    area: {
      type: Object,
      default: () => { }
    },
    categoryAreaCode: {
      type: String,
      default: () => { }
    }
  },
  data () {
    return {
      loading: false,
      contentForm: {
        categoryCode: '',
        categoryName: '',
        receiptName: ''
      },
      rules: {
        categoryId: [{ required: true, message: '请选择专项资金类别', trigger: 'change' }],
        specialSource: [{ required: true, message: '请选择专项资金来源', trigger: 'change' }],
        amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        purpose: [{ required: true, message: '请输入专项资金用途', trigger: 'blur' }],
        receiptName: [{ required: true, message: '请输入专项资金名称', trigger: 'blur' }],
        bankAccountId: [{ required: true, message: '请选择收款账户', trigger: 'change' }],
        orgId: [{ required: true, message: '请选择单位名称', trigger: 'change' }]

      },
      treeData: [],
      props: {
        value: 'id', // ID字段名
        label: 'text', // 显示名称
        children: 'children', // 子级字段名
        isLeaf: 'isLeaf'
      },
      accountOptions: [],
      treeNodeData: {},
      areaCode: '',
      restaurants: [],
      timeout: null,
      bankAccountType: []
    }
  },
  created () {
    this.initPage()
    this.getOptions()
  },
  methods: {
    async getOptions () {
      const { data: bankAccountType = [] } = await getDictionary('/financial/specialFund/receipt/bank_account_type/')
      // this.optionsMap.bankAccountType = bankAccountType
      this.bankAccountType = bankAccountType.map(item => item.id)
      if (this.area.type != 'Area') {
        this.listAccount(null)
      }
    },
    querySearchAsync (queryString, cb) {
      var restaurants = this.restaurants
      var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        cb(results)
      }, 500 * Math.random())
    },
    createStateFilter (queryString) {
      return (state) => {
        return (state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    handleSelect (item) {
      console.log(item)
    },
    selectList (orgId) {
      const params = {
        orgId: orgId,
        statusList: [1, 2, 3]
      }
      selectList(params).then(res => {
        this.restaurants = []
        const data = [...new Map(res.data.map(item => [item.receiptName, item])).values()]
        data.map(item => {
          this.restaurants.push({
            value: item.receiptName
          })
        })
      })
    },
    handleSelectedNodeChange (val) {
      if (val.type === 'Organization') {
        this.selectList(val.id)
        this.listAccount(val.code)
        this.$set(this.contentForm, 'bankAccountId', '')
        this.$set(this.contentForm, 'bankAccountName', '')
        this.$set(this.contentForm, 'bankAccountNumber', '')
        this.$set(this.contentForm, 'bankAccountType', '')
        this.$set(this.contentForm, 'orgId', val.id)
        this.$set(this.contentForm, 'orgName', val.text)
      }
    },
    handleCategoryValue (value, code, name) {
      this.$set(this.contentForm, 'categoryId', value || '')
      this.$set(this.contentForm, 'categoryCode', code || '')
      this.$set(this.contentForm, 'categoryName', name || '')
    },
    initPage () {
      if (this.type === 'add') {
        if (this.area.type == 'Organization') {
          this.contentForm.orgName = this.area.fname
          this.contentForm.orgId = this.area.id
        }

        // this.treeNodeData = {
        //   id: this.area.id,
        //   text: this.area.fname,
        //   type: this.area.type,
        //   code: this.area.code
        // }
        if (this.area.type === 'area') {
          this.areaCode = this.area.code
        } else {
          this.areaCode = this.area.areaCode
          this.selectList(this.area.id)
        }
        // this.selectByParentId()
        // 新增页面初始化
      } else if (this.type === 'view' || this.type === 'edit') {
        this.areaCode = this.categoryAreaCode
        // 查看、编辑页面初始化
        this.loading = true
        loadReceiptInfoApi(this.id).then(res => {
          this.contentForm = res.data
          this.$set(this.contentForm, 'categoryCode', res.data.categoryCode != undefined ? res.data.categoryCode : '')
          this.$set(this.contentForm, 'categoryName', res.data.categoryName != undefined ? res.data.categoryName : '')
          this.selectList(res.data.orgId)
          this.listAccount(res.data.orgCode)
          if (this.type === 'view') {
            this.accountOptions = [{
              text: this.contentForm.bankAccountNumber + '（' + this.contentForm.bankAccountTypeText + '）',
              bankAccountName: this.contentForm.bankAccountNumber + '（' + this.contentForm.bankAccountTypeText + '）',
              bankAccountNumber: this.contentForm.bankAccountNumber,
              bankAccountType: this.contentForm.bankAccountType,
              id: this.contentForm.bankAccountId
            }]
          }
          this.treeNodeData = {}
          if (this.type == 'edit') {
            this.treeNodeData = {
              id: res.data.orgId,
              text: res.data.orgName
            }
          }
          // this.selectByParentId()
        }).finally(() => {
          this.loading = false
        })
      }
    },
    selectByParentId () {
      const params = {
        parentId: '0',
        areaCode: this.areaCode
      }
      selectByParentId(params).then(res => {
        this.treeData = []
        res.data.map((cur) => {
          this.treeData.push({
            id: cur.id,
            state: 'closed',
            text: cur.categoryCode + ' ' + cur.categoryName,
            categoryName: cur.categoryName,
            parentCode: cur.categoryCode,
            type: 'PaymentExpenditureType',
            name: cur.categoryName,
            parentId: cur.parentId,
            isLeaf: cur.isLeaf
          })
        })
      }).finally(() => {
      })
    },
    handleBatchIdChange (systemBatchId) {
      this.$set(this.contentForm, 'systemBatchId', systemBatchId)
    },

    save () {
      this.$refs['_geverFormRef'].validate(valid => {
        if (!valid) {
          return this.$message.warning(this.$t('请完善表单信息'))
        }
        this.loading = true
        this.contentForm.status = 1
        if (this.area.type == 'Organization') {
          this.contentForm.orgId = this.area.id
          this.contentForm.orgName = this.area.fname
        }
        if (this.contentForm.id) {
          updateReceiptApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        } else {
          this.contentForm.specialType = this.specialType
          saveReceiptApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    saveDraft () {
      // this.$refs['_geverFormRef'].validate(valid => {
      //   if (!valid) {
      //     return this.$message.warning(this.$t('请完善表单信息'))
      //   }
      if (this.contentForm.orgId == null || this.contentForm.orgId == undefined) {
        return this.$message.warning(this.$t('请选择单位名称'))
      }

      this.loading = true
      this.contentForm.status = 0
      this.contentForm.specialType = this.specialType
      if (this.area.type == 'Organization') {
        this.contentForm.orgId = this.area.id
        this.contentForm.orgName = this.area.fname
      }
      this.contentForm.amount = this.contentForm.amount != undefined ? this.contentForm.amount : ''
      if (this.contentForm.id) {
        updateReceiptApi(this.contentForm).then(res => {
          if (res.returnCode === '0') {
            this.$message.success(this.$t('操作成功'))
            this.$emit('refreshTable')
            this.$emit('update:detailVisible', false)
          }
        }).finally(() => {
          this.loading = false
        })
      } else {
        this.contentForm.specialType = this.specialType
        saveReceiptApi(this.contentForm).then(res => {
          if (res.returnCode === '0') {
            this.$message.success(this.$t('操作成功'))
            this.$emit('refreshTable')
            this.$emit('update:detailVisible', false)
          }
        }).finally(() => {
          this.loading = false
        })
      }
      // })
    },
    listAccount (orgCode) {
      const params = {
        orgCode: orgCode || this.area.code,
        accountType: this.bankAccountType.join(','),
        state: 1
      }
      if (this.type != 'view') {
        listAccount(params).then(res => {
          this.accountOptions = []
          res.data.rows.filter(item => this.bankAccountType.includes(item.accountType)).map(item => (
            this.accountOptions.push({
              text: item.accountNumber + '（' + item.accountTypeName + '）',
              bankAccountName: item.accountNumber + '（' + item.accountTypeName + '）',
              bankAccountNumber: item.accountNumber,
              bankAccountType: item.accountType,
              id: item.id
            })
          ))
        })
      }
    },
    handleBankAccountChange (value) {
      // this.$set(this.contentForm, 'bankAccountName', value.text)
      const item = this.accountOptions.find(item => item.id === value)
      this.$set(this.contentForm, 'bankAccountName', item ? item.text : '')
      this.$set(this.contentForm, 'bankAccountNumber', item ? item.bankAccountNumber : '')
      this.$set(this.contentForm, 'bankAccountType', item ? item.bankAccountType : '')
    }
  }
}
</script>
<style lang="scss" scoped>
.label-title {
  color: #1890ff;
}
</style>
