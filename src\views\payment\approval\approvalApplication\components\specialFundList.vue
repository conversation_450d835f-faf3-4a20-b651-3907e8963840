<template>
  <div class="SpecialFund">
    <el-form inline :model="queryParams" :disabled="loadType == 'view'">
      <el-form-item :label="$t('专项资金名称')">
        <el-input v-model="queryParams.receiptName" class="w150" clearable />
      </el-form-item>
      <el-form-item :label="$t('专项资金编号')">
        <el-input v-model="queryParams.receiptCode" class="w150" clearable />
      </el-form-item>
      <el-form-item :label="$t('专项资金用途')">
        <el-input
          v-model="queryParams.purpose"
          class="w150"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('专项资金类别')">
        <categoryTreeSelect v-model="queryParams.categoryId" placeholder="请选择"></categoryTreeSelect>
      </el-form-item>
      <el-form-item :label="$t('专项资金来源')">
        <gever-select
          v-model="queryParams.specialSource"
          :options="specialSourceOptions"
          class="w150"
        />
      </el-form-item>
      <el-form-item :label="'达账日期'" prop="startArrivalDate">
        <el-date-picker
          v-model="queryParams.arrivalDateStart"
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="{ disabledDate: disabledDateStart }"
          :clearable="true"
        />
        至
        <el-date-picker
          v-model="queryParams.arrivalDateEnd"
          type="date"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="{ disabledDate: disabledDateEnd }"
          :clearable="true"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          round
          plain
          type="primary"
          icon="el-icon-search"
          @click="getList"
        >
          {{ $t('搜索') }}
        </el-button>
        <el-button
          v-if="relType == 3"
          type="primary"
          round
          @click="returnBalance"
        >
          {{ $t('余额退还') }}
        </el-button>
        <span style="color: red; margin-left: 12px; font-size: 14px; vertical-align: middle;">
          收款方金额：{{ Number(rowData.amountPayable || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}元
        </span>
      </el-form-item>
      <gever-table
        ref="_geverTableRef"
        v-loading="tableLoading"
        :columns="tableColumns"
        :data="tableList"
        :pagination="false"
        :height="relType == 2 ? 280 : 500"
        row-key="id"
        show-summary
        :summary-method="getTableSummary"
        @selection-change="handleSelectionChange"
      >
        <template #specialSource="{ row }">
          {{ fundingSourcesText(row.specialSource) }}
        </template>
        <template #payType="{ row }">
          {{ payTypeText(row.payType) }}
        </template>
        <template #categoryName="{ row }">
          {{ row.categoryCode + '-' + row.categoryName }}
        </template>
        <template #bankAccountNumber="{ row }">
          {{ row.bankAccountNumber + '(' + row.bankAccountTypeText + ')'}}
        </template>
        <template #payAmount="{ row }">
          <el-input
            v-model="row.payAmount"
            size="small"
            input-style="text-align: right;"
            :disabled="loadType == 'view'"
            @blur="handlePayAmountBlur(row)"
          />
        </template>
      </gever-table>
    </el-form>
    <div v-if="relType == 2" class="module-title" style="margin-top: 20px;">
      <span class="label-title">{{ $t('原支出申请') }}</span><span style="color: red">*</span>
      <el-tooltip
        class="item"
        effect="dark"
        :content="'原支出申请的本次调账金额合计必须与专项资金的本次支出金额一致'"
        placement="top-start"
      >
        <i class="el-icon-info" style="color: #1890ff;"/>
      </el-tooltip>
      <el-button
        v-if="loadType != 'view' && this.loadType != 'refund' && this.isRefund != 1"
        round
        plain
        type="primary"
        icon="el-icon-plus"
        style="float: right"
        :disabled="disableSelectButton"
        @click="handleOpenApplicationSelect"
      >
        {{ $t('选择') }}
      </el-button>
    </div>
    <gever-table
      v-if="relType == 2"
      :columns="relatedApplicationColumns"
      :data="relatedApplications"
      :pagination="false"
      :height="280"
      show-summary
      :summary-method="getApplicationSummary"
      row-key="id"
    >
      <template #payAmount="{ row }">
        <el-input
          v-model="row.payAmount"
          size="small"
          input-style="text-align: right;"
          :disabled="loadType == 'view'"
          @blur="handleApplicationPayAmountBlur(row)"
        />
      </template>
      <template #operation="{ row }">
<!--        <el-button
          type="text"
          @click="handleViewApplicationForm(row)"
        >
          {{ $t('查看') }}
        </el-button>-->
        <el-button
          v-if="loadType != 'view' && loadType != 'refund' && isRefund != 1"
          type="text"
          @click="handleDeleteRelatedApplication(row)"
        >
          {{ $t('删除') }}
        </el-button>
      </template>
      <template #expenditureType="{ row }">
        {{ getExpenditureTypeText(row.expenditureType) }}
      </template>
      <template #applicationType="{ row }">
        {{ convertApplicationType(row.applicationType) }}
      </template>
      <template #settlementMethod="{ row }">
        {{ converSettlementMethod(row.settlementMethod) }}
      </template>
    </gever-table>

    <public-drawer
      :title="$t('选择支出申请')"
      append-to-body
      :visible.sync="applicationSelectVisible"
      :size="70"
      top="5vh"
      custom-class="process-dialog"
      :buttons="[
        {
          type: '',
          text: $t('取 消'),
          buttonStatus: true,
          callback: () => {
            applicationSelectVisible = false
          }
        },
        {
          type: 'primary',
          text: $t('确 定'),
          buttonStatus: true,
          callback: handleApplicationSelectConfirm
        }
      ]"
    >
      <ApplicationSelectDialog
        v-if="applicationSelectVisible"
        ref="_applicationSelectDialogRef"
        :org-code="queryParams.orgCode"
        :receiver-bank-account="rowData.receiverBankAccount"
      />
    </public-drawer>

    <public-drawer
      v-loading="loading"
      :visible.sync="applicationDrawerVisible"
      :title="drawerTitle"
      :size="70"
      @close="handleDrawerClose"
    >
      <Application
        v-if="applicationDrawerVisible"
        :id="currentRow.id || ''"
        ref="_appRef"
        :view-state="viewState"
        :application="currentApplication"
        :receiver="receiverList"
        :load-type="'view'"
        :visible.sync="applicationDrawerVisible"
        @loading="handleLoading"
      />
    </public-drawer>
  </div>
</template>

<script>
import { comboJson } from '@/api/gever/common.js'
import {
  listSpecialFund, selectForSpecialFundAdjust
} from '@/api/payment/specialFund.js'
import { paymentExpenditureTypeTree } from '@/api/payment/expenseType'
import { page as billingMethodPage } from '@/api/payment/billingMethod'
import ApplicationSelectDialog from './specialApplicationSelect.vue'
import categoryTreeSelect from '@/views/specialFund/receipt/components/categoryTreeSelect.vue'
import { formatMoney } from '@/filter'
import Application from '@/views/payment/approval/approvalApplication/applicationInfo.vue'
import { getApplication } from '@/api/payment/approval'

export default {
  name: 'SpecialFund',
  components: {
    Application,
    ApplicationSelectDialog,
    categoryTreeSelect
  },
  filters: {
    money: formatMoney
  },
  props: {
    rowData: {
      type: Object,
      default: () => ({})
    },
    loadType: {
      type: String,
      default: 'add'
    },
    orgId: {
      type: String,
      default: ''
    },
    orgCode: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    relType: {
      type: Number,
      default: 1
    },
    bankAccountId: {
      type: String,
      default: ''
    },
    isRefund: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      tableLoading: false,
      queryParams: {
        page: 1,
        rows: 9999,
        orgId: '',
        receiptName: '',
        receiptCode: '',
        purpose: '',
        categoryId: '',
        specialSource: '',
        arrivalDateStart: '',
        arrivalDateEnd: ''
      },
      tableList: [],
      tableColumns: [
        {
          type: 'selection',
          align: 'center',
          width: '40',
          fixed: 'left'
        },
        { type: 'index', label: '序号', width: '50', fixed: 'left' },
        {
          label: '专项资金编号',
          prop: 'receiptCode',
          minWidth: '180',
          showOverflowTooltip: true
        },
        {
          label: '可用金额',
          prop: 'availableAmount',
          width: '150',
          filter: 'money',
          align: 'right',
          showOverflowTooltip: true
        },
        {
          label: '本次支出金额 *',
          prop: 'payAmount',
          width: '150',
          align: 'right',
          showOverflowTooltip: true,
          renderHeader(h) {
            return h('span', [
              '本次支出金额 ',
              h('span', { style: 'color: red;' }, '*')
            ])
          }
        },
        {
          label: '专项资金类别',
          prop: 'categoryName',
          width: '250',
          showOverflowTooltip: true
        },
        {
          label: '专项资金名称',
          prop: 'receiptName',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '专项资金银行账户',
          prop: 'bankAccountNumber',
          width: '250',
          showOverflowTooltip: true
        },
        {
          label: '达账日期',
          prop: 'arrivalDate',
          minWidth: '130',
          showOverflowTooltip: true
        },
        {
          label: '限期',
          prop: 'term',
          width: '130',
          showOverflowTooltip: true
        },
        {
          label: '专项资金来源',
          prop: 'specialSource',
          width: '100',
          showOverflowTooltip: true
        },
        {
          label: '专项资金用途',
          prop: 'purpose',
          width: '150',
          showOverflowTooltip: true
        }
      ],
      categoryOptions: [],
      specialSourceOptions: [],
      selectArr: [],
      selectIds: [],
      applicationSelectVisible: false,
      relatedApplications: [],
      targetReceiverIds: [],
      targetReceiverPayAccountNumber: '',
      relatedApplicationColumns: [
        { type: 'index', label: '序号', width: '80', fixed: 'left' },
        {
          label: '申请编号',
          prop: 'serialNumber',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '支出类型',
          prop: 'expenditureType',
          width: '150',
          showOverflowTooltip: true,
          slotName: 'expenditureType'
        },
        {
          label: '支付金额（元）',
          prop: 'amountPayable',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '已调账金额（元）',
          prop: 'adjustedAmount',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '未调账金额（元）',
          prop: 'unAdjustedAmount',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '本次调账金额（元） *',
          prop: 'payAmount',
          width: '150',
          align: 'right',
          showOverflowTooltip: true,
          renderHeader(h) {
            return h('span', [
              '本次调账金额（元） ',
              h('span', { style: 'color: red;' }, '*')
            ])
          }
        },
        {
          label: '申请类型',
          prop: 'applicationType',
          width: '150',
          showOverflowTooltip: true,
          slotName: 'applicationType'
        },
        {
          label: '结算方式',
          prop: 'settlementMethod',
          width: '150',
          showOverflowTooltip: true,
          slotName: 'settlementMethod'
        },
        {
          label: '申请日期',
          prop: 'creationTime',
          width: '150',
          filter: 'date',
          showOverflowTooltip: true
        },
        {
          label: '资金用途',
          prop: 'fundsUse',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '申请详情',
          prop: 'details',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '付款账号',
          prop: 'payAccountNumber',
          width: '180',
          showOverflowTooltip: true
        },
        {
          label: '收款方',
          prop: 'receiverAccount',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '备注',
          prop: 'remark',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '操作',
          slotName: 'operation',
          width: '130',
          fixed: 'right'
        }
      ],
      expendTypeComboOptions: [],
      applicationTypeOptions: [
        { id: 0, text: '常规用款' },
        { id: 1, text: '收支相抵' },
        { id: 2, text: '冲减收入' },
        { id: 3, text: '非预算项目支出' },
        { id: 4, text: '银行代扣' },
        { id: 5, text: '扣减预算' },
        { id: 6, text: '非预算收支相抵' }
      ],
      settlementMethodOptins: [{ id: '', settleName: this.$t('全部') }],
      loading: false,
      applicationDrawerVisible: false,
      drawerTitle: '',
      currentRow: {},
      currentApplication: {},
      receiverList: [],
      viewState: 0,
      isAutoClear: false
    }
  },
  computed: {
    // ...mapState(['paymentLogin'])
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    },
    disableSelectButton() {
      return false
    //  return this.relatedApplications.length > 0
    },
    totalPayAmount() {
      const sum = (this.tableList.reduce((sum, item) => {
        const amount = parseFloat(item.payAmount) || 0
        return sum + amount
      }, 0) || 0).toFixed(2)
      return sum
    }
  },
  watch: {
    paymentLogin: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val) {
          if (this.loadType == 'add') {
            this.$set(this.queryParams, 'orgId', val.id || '')
            this.$set(this.queryParams, 'orgCode', val.code || '')
          }
          // this.$set(this.queryParams, 'orgCode', val.code || '')
          this.getList()
        }
      }
    }
  },
  created() {
    this.getComboJson()
    this.getList()
    if (this.rowData && this.rowData.relatedApplications) {
      this.relatedApplications = this.rowData.relatedApplications
    }
  },
  methods: {
    async getList() {
      if (this.loadType != 'add') {
        this.queryParams.orgCode = this.orgCode
      }
      if (!this.queryParams.orgCode && !this.queryParams.orgId) {
        this.$message.warning('获取当前机构异常')
        return
      }
      this.queryParams.bankAccountId = this.bankAccountId
      if (this.loadType == 'view' || this.loadType == 'refund' || this.isRefund == 1) {
        const fundIds = []
        this.rowData.specialFundList.forEach(x => {
          fundIds.push(x.specialFundId)
        })
        this.queryParams.ids = fundIds
      }
        const { data } = await listSpecialFund(
          this.queryParams
        )
        this.tableList = data
        if (this.loadType != 'view' && this.loadType != 'refund' && this.isRefund != 1) {
          // 排除可用金额0 的数据
          this.tableList = this.tableList.filter(x => Number(x.availableAmount) > 0)
        }
        this.initTable()
      if (this.relType == 2) {
        this.rowData.specialFundList.forEach(x => {
          x.adjustmentList.forEach(y => {
            this.targetReceiverIds.push(y.targetReceiverId)
          })
        })
        if (this.targetReceiverIds && this.targetReceiverIds.length > 0) {
          this.getRelReceiver()
        }
      }
    },
    async getRelReceiver() {
      try {
        const { data } = await selectForSpecialFundAdjust({ ids: this.targetReceiverIds })
        this.relatedApplications = data.records
        let index = 0
        this.relatedApplications.forEach(application => {
          this.rowData.specialFundList.forEach(x => {
            x.adjustmentList.forEach(y => {
              if (y.targetReceiverId == application.id) {
                // 修改:确保payAmount是字符串格式且保留两位小数，使输入框可以编辑
               // application.payAmount = this.sub(y.payAmount, 0)
                this.$set(this.relatedApplications[index], 'payAmount', Number(y.payAmount).toFixed(2))
                if (this.loadType == 'edit' || (this.loadType == 'refund' && this.isRefund == 1)) {
                  application.adjustedAmount = this.sub(application.adjustedAmount, y.payAmount)
                  application.unAdjustedAmount = this.add(application.unAdjustedAmount, y.payAmount)
                }
                if (this.loadType == 'refund' || this.isRefund == 1) {
                  application.sourceAmount = y.sourceAmount ? -y.sourceAmount : y.payAmount
                }
              }
            })
          })
          index++
        })
      } finally {
        this.tableLoading = false
      }
    },
    add(a, b) {
      // 将输入值固定为两位小数
      a = a ? a : 0
      b = b ? b : 0
      const precision = 100 // 因为是两位小数
      return (Math.round(a * precision) + Math.round(b * precision)) / precision
    },
    sub(a, b) {
      // 将输入值固定为两位小数
      a = a ? a : 0
      b = b ? b : 0
      const precision = 100 // 因为是两位小数
      return (Math.round(a * precision) - Math.round(b * precision)) / precision
    },
    initTable() {
      this.$nextTick(() => {
        this.$refs._geverTableRef.clearSelection()
        if (!this.rowData.specialFundList) {
          return
        }
        // 把已关联的数据排到前面
        const fundIds = []
        this.rowData.specialFundList.forEach(x => {
          fundIds.push(x.specialFundId)
        })
        this.tableList.sort((a, b) => {
          const aIn = fundIds.includes(a.id)
          const bIn = fundIds.includes(b.id)
          if (aIn && !bIn) return -1
          if (!aIn && bIn) return 1
          return 0
        })
        // 已关联的数据选中并填写支出金额
        for (let i = 0; i < this.tableList.length; i++) {
          if (this.rowData.specialFundList) {
            this.rowData.specialFundList.forEach(x => {
              if (x.specialFundId == this.tableList[i].id) {
                this.$refs._geverTableRef.toggleRowSelection(this.tableList[i], true)
                this.$set(this.tableList[i], 'payAmount', Number(x.payAmount).toFixed(2))
                this.$set(this.tableList[i], 'sourceAmount', Number(x.sourceAmount ? -x.sourceAmount : x.payAmount).toFixed(2))
              }
            })
          }
        }
      })
    },
    // 获取字典集合
    async getComboJson() {
      const specialSource = await comboJson({
        path: '/financial/specialFund/receipt/specialSource'
      })
      this.specialSourceOptions = specialSource.data

      paymentExpenditureTypeTree({ page: 1, rows: 100 }).then((res) => {
        if (res.returnCode === '0') {
          this.expendTypeComboOptions.push(...res.data)
        }
      })
      billingMethodPage({ page: 1, rows: 100, parentId: '0' }).then((res) => {
        this.settlementMethodOptins = res.data.rows
      })
    },
    fundingSourcesText(val) {
      const obj = this.specialSourceOptions.find((item) => item.id == val)
      return obj?.text
    },
    payTypeText(val) {
      const obj = this.payTypeOptions.find((item) => item.id == val)
      return obj?.text
    },
    getExpenditureTypeText(val) {
      const valArr = val ? val.split(',') : []
      const textArr = []
      this.expendTypeComboOptions.forEach((item) => {
        if (valArr.includes(item.id)) {
          textArr.push(item.text)
        }
      })
      return textArr.join(',')
    },
    converSettlementMethod(val) {
      const settlementMethod = this.settlementMethodOptins.find(
        (item) => item.id == val
      )
      return settlementMethod ? settlementMethod.settleName : ''
    },
    convertApplicationType(val) {
      const applicationType = this.applicationTypeOptions.find(
        (item) => item.id == val
      )
      return applicationType ? applicationType.text : ''
    },
    handleSelectionChange(val) {
      if (this.relType == 2 && val.length > 1) {
        this.isAutoClear = true
        this.$refs['_geverTableRef'].clearSelection()
        this.$refs['_geverTableRef'].toggleRowSelection(val[1])
        this.selectArr = []
        this.selectArr.push(val[1])
      } else {
        this.selectArr = val
      }
      this.selectIds = []
      this.selectArr.forEach(x => {
        this.selectIds.push(x.id)
      })
      console.log(this.isAutoClear)
      console.log(this.selectArr)
      // 清空取消选中行的支出金额
      this.tableList.forEach(row => {
        if (this.relType == 2) {
          // 调账特殊处理
          if (!this.selectIds.includes(row.id) && this.selectIds > 0) {
            this.$set(row, 'payAmount', '')
            this.isAutoClear = false
          }
          if (!this.selectIds.includes(row.id) && !this.isAutoClear) {
            this.$set(row, 'payAmount', '')
          }
        } else {
          if (!this.selectIds.includes(row.id)) {
            this.$set(row, 'payAmount', '')
          }
        }
      })
    },
    handlePayAmountBlur(row) {
      if (!row) return
      let val = String(row.payAmount || '')
      val = val.replace(/\.(?=.*\.)/g, '')
      val = val.replace(/(\.\d{2}).+/, '$1')
      if (!val || val === '') {
        return
      }
      if (this.loadType === 'refund' || this.isRefund == 1) {
        if (Number(val) > 0) {
          this.$message.warning('退款时只能输入负数金额')
          this.$set(row, 'payAmount', '')
          return
        }
        if (row.sourceAmount && Number(val) < Number(row.sourceAmount)) {
          this.$message.warning('退款金额不能大于原支出金额')
          this.$set(row, 'payAmount', row.sourceAmount)
          return
        }
      } else {
        if (Number(val) <= 0) {
          this.$message.warning('请输入大于0且最多两位小数的金额')
          this.$set(row, 'payAmount', '')
          return
        }
        if (row.availableAmount && Number(val) > Number(row.availableAmount)) {
          this.$message.warning('本次支出金额不能大于可用金额')
          this.$set(row, 'payAmount', row.availableAmount)
          return
        }
      }

      // 自动格式化为两位小数
      this.$set(row, 'payAmount', Number(val).toFixed(2))
      // 自动勾选当前行
      if (!this.selectArr.includes(row)) {
        this.$refs._geverTableRef.toggleRowSelection(row, true)
      }
    },
    handleApplicationPayAmountBlur(row) {
      if (!row) return
      let val = String(row.payAmount || '')
      val = val.replace(/\.(?=.*\.)/g, '')
      val = val.replace(/(\.\d{2}).+/, '$1')
      if (!val || val === '') {
        return
      }
      if (this.loadType === 'refund' || this.isRefund == 1) {
        if (Number(val) > 0) {
          this.$message.warning('退款时只能输入负数金额')
          this.$set(row, 'payAmount', '')
          return
        }
        if (row.sourceAmount && Number(val) < Number(row.sourceAmount)) {
          this.$message.warning('退款金额不能大于原调账金额')
          this.$set(row, 'payAmount', row.sourceAmount)
          return
        }
      } else {
        if (Number(val) <= 0) {
          this.$message.warning('请输入大于0且最多两位小数的金额')
          this.$set(row, 'payAmount', '')
          return
        }
        if (row.unAdjustedAmount && Number(val) > Number(row.unAdjustedAmount)) {
          this.$message.warning('本次调账金额不能大于未调账金额')
          this.$set(row, 'payAmount', row.amountPayable)
          return
        }
      }
      // 自动格式化为两位小数
      this.$set(row, 'payAmount', Number(val).toFixed(2))
    },
    disabledDateStart(time) {
      // 如果endAcceptanceDate已选择，则禁用所有大于该日期的时间
      if (this.queryParams.arrivalDateEnd) {
        // 使用new Date()构造函数将日期字符串转换为Date对象，并忽略时间部分
        const endDate = new Date(this.queryParams.arrivalDateEnd)
        return time.getTime() > endDate.getTime()
      } else {
        return false
      }
    },
    disabledDateEnd(time) {
      // 如果startAcceptanceDate已选择，则禁用所有小于该日期的时间
      if (this.queryParams.startArrivalDate) {
        const startDate = new Date(this.queryParams.startArrivalDate)
        return time.getTime() < startDate.getTime()
      } else {
        return false
      }
    },
    handleOpenApplicationSelect() {
      this.applicationSelectVisible = true
    },
    handleApplicationSelectConfirm() {
      const selected = this.$refs._applicationSelectDialogRef?.selectedApplications || []

      if (selected.length === 0) {
        this.$message.warning('请至少选择一条支出申请数据')
        return
      }
/*      if (selected.length > 1) {
        this.$message.warning('只能选择一条支出申请数据')
        return
      }*/
/*
      if (this.relatedApplications.length > 0) {
        this.$message.warning('关联支出申请列表已有数据，无法再次选择')
        return
      }*/

      const newApplications = selected.filter(item => !this.relatedApplications.some(existingItem => existingItem.id === item.id))

      this.relatedApplications.push(...newApplications)
      this.targetReceiverId = newApplications[0]?.id || ''
      this.targetReceiverPayAccountNumber = newApplications[0]?.payAccountNumber || ''
      this.applicationSelectVisible = false
    },
    handleDeleteRelatedApplication(row) {
      this.$confirm('确定删除该关联支出申请吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.relatedApplications = this.relatedApplications.filter(item => item.id !== row.id)
        this.targetReceiverId = ''
        this.targetReceiverPayAccountNumber = ''
        this.$message.success('删除成功')
      }).catch(() => {})
    },
    handleViewApplicationForm(row) {
      this.loading = true
      this.drawerTitle = row.returnFlag ? '查看申请退款' : '查看支出申请'
      this.currentRow = row
      this.viewState = row.approvalStatus
      this.applicationDrawerVisible = true
      getApplication(row.approvalApplicationId)
        .then((res) => {
          this.currentApplication = res.data[0]
          this.receiverList = res.data[1]
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleDrawerClose() {
      this.currentApplication = {}
    },
    handleLoading(flag) {
      this.loading = flag
    },
    returnBalance() {
      if (this.selectArr.length === 0) {
        this.$message.warning('请先勾选需要余额退还的数据')
        return
      }
      this.selectArr.forEach(row => {
        // 设置本次支出金额等于可用金额，用于余额退还
        if (!row.payAmount) {
          const availableAmount = Number(row.availableAmount || 0).toFixed(2)
          this.$set(row, 'payAmount', availableAmount)
        }
      })
      this.$message.success('已自动填充余额退还金额')
    },
    getTableSummary({ columns, data }) {
      const sums = []
      columns.forEach((column, index) => {
        if (index === 2) {
          sums[index] = '合计'
          return
        }
        if (column.property === 'payAmount') {
          const total = data.reduce((sum, row) => {
            const value = this.selectIds.includes(row.id) ? parseFloat(row.payAmount) : 0
            return sum + (isNaN(value) ? 0 : value)
          }, 0)
          sums[index] = this.$options.filters.money
            ? this.$options.filters.money(total)
            : total.toFixed(2)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    getApplicationSummary({ columns, data }) {
      const sums = []
      columns.forEach((column, index) => {
        if (index === 2) {
          sums[index] = '合计'
          return
        }
        if (column.property === 'payAmount') {
          const total = data.reduce((sum, row) => {
            const value = parseFloat(row.payAmount)
            return sum + (isNaN(value) ? 0 : value)
          }, 0)
          sums[index] = this.$options.filters.money
            ? this.$options.filters.money(total)
            : total.toFixed(2)
        } else if (column.property === 'amountPayable') {
          const total = data.reduce((sum, row) => {
            const value = parseFloat(row.amountPayable)
            return sum + (isNaN(value) ? 0 : value)
          }, 0)
          sums[index] = this.$options.filters.money
            ? this.$options.filters.money(total)
            : total.toFixed(2)
        } else if (column.property === 'adjustedAmount') {
          const total = data.reduce((sum, row) => {
            const value = parseFloat(row.adjustedAmount)
            return sum + (isNaN(value) ? 0 : value)
          }, 0)
          sums[index] = this.$options.filters.money
            ? this.$options.filters.money(total)
            : total.toFixed(2)
        } else if (column.property === 'unAdjustedAmount') {
          const total = data.reduce((sum, row) => {
            const value = parseFloat(row.unAdjustedAmount)
            return sum + (isNaN(value) ? 0 : value)
          }, 0)
          sums[index] = this.$options.filters.money
            ? this.$options.filters.money(total)
            : total.toFixed(2)
        } else {
          sums[index] = ''
        }
      })
      return sums
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__fixed-header-wrapper {
  .el-checkbox__inner {
    display: none !important;
  }
}
::v-deep .SpecialFund .el-table__body-wrapper {
  height: 319px !important;
  z-index: 99999999;
}
</style>
