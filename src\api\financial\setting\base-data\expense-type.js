/*
 * @Descripttion:支出类型接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-29 16:40:25
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-10-29 17:02:39
 */

import request from '@/utils/request'

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/books/basedata/cashierexpensetype/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取支出类型类型
 * @param {String} booksId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listByBooksId({ booksId = '' }) {
  return request({
    url: '/financial/books/basedata/cashierexpensetype/listByBooksId',
    method: 'post',
    data: { booksId },
    withCredentials: true
  })
}

/**
 * @name:保存支出类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/books/basedata/cashierexpensetype/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:删除支出类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/books/basedata/cashierexpensetype/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}

