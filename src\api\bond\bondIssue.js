import request from '@/utils/request'

/**
 * 查询债券开出列表
 * @param {*} data 
 * @returns 
 */
 export function getBondIssueList(data){
    return request({
        url: '/bond/bondIssue/page',
        method: 'post',
        data
    })
}

/**
 * 查询债券开出详情
 * @param {*} id 
 * @returns 
 */
export function getIssueDetail(id){
    return request({
        url: `/bond/bondIssue/get/${id}`,
        method: 'get'
    })
}

/**
 * 作废
 * @param {*} params 
 * @returns 
 */
export function cancel(params){
    return request({
        url: '/bond/bondIssue/cancel',
        method: 'post',
        params
    })
}

/**
 * 补打
 */
export function rePrint(id){
    return request({
        url: `/bond/bondIssue/rePrint?issueId=${id}`,
        method: 'post'
    })
}

/**
 * 查询债券凭证列表
 * @param {*} data 
 * @returns 
 */
 export function getBondVoucherList(data) {
    return request({
        url: '/bond/bondIssue/pageList',
        method: 'post',
        data
    })
}

/**
 * 模拟功能（开出）接口
 * @param {*} data 
 * @returns 
 */
export function invoice(data) {
    return request({
        url: '/bond/bondIssue/invoice',
        method: 'post',
        data
    })
}

/**
 * 模拟功能（打印）接口
 * @param {*} id 
 * @returns 
 */
 export function print(id) {
    return request({
        url: `/bond/bondIssue/print?issueId=${id}`,
        method: 'post'
    })
}