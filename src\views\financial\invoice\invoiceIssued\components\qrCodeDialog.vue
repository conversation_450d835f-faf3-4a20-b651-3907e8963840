<!-- @Description: 电子税务平台扫脸认证 -->
<template>
  <el-dialog
    v-if="dialogVisible"
    title="认证已过期，请重新认证"
    width="380px"
    top="15vh"
    class="scanDialogTitle"
    :append-to-body="false"
    :visible.sync="dialogVisible"
    @close="handleCloseDialog"
  >
    <div v-loading="qrCodeLoading" style="text-align: center; height: 360px">
      <div ref="_scanTop" class="scanTop">
        <div
          v-for="(item, index) in ['税务APP', '个税APP']"
          :key="index"
          :class="['scanTitle', { scanSelected: smlx === String(index) }]"
          @click="scanTitleClick(String(index))"
        >
          <span>{{ item }}</span>
        </div>
      </div>
      <div id="swjQrCode" ref="_swjQrCode">
        <img
          ref="_refreshImg"
          src="../../../../../assets/images/refresh_logo.png"
        />
      </div>
      <div style="width: 100%; height: 22px; overflow: hidden; margin: 0 auto">
        <span ref="_secondText" style="line-height: 22px"></span>
      </div>
      <el-button type="primary" round @click="handleRefreshQrCode">
        {{ $t('刷新二维码') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  getSwjLoginQrCode,
  getSwjLoginResult
} from '@/api/financial/invoice/invoiceRegistry.js'

export default {
  name: 'QrCodeDialog',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    zdAccount: {
      type: String,
      default: ''
    },
    registry: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      qrCodeLoading: false, // 二维码加载中
      smlx: '0', // 税务app，1-个税app
      rzid: '', // 扫脸二维码认证id
      timerId: 0, // 定时器id
      scanResultInvoking: false // 扫码认证调用中
    }
  },
  computed: {},
  watch: {},
  mounted() {
    this.handleRefreshQrCode()
  },
  methods: {
    // 刷新电子税务局认证二维码
    handleRefreshQrCode() {
      this.qrCodeLoading = true
      // smlx:扫脸通道 0 税务app 1个税app
      getSwjLoginQrCode(
        this.registry.registryType,
        this.smlx,
        this.registry.spId,
        this.registry.taxpayerNo,
        this.zdAccount
      )
        .then((qrcode) => {
          if (qrcode.returnCode !== '0') {
            return this.$message.error(qrcode.message)
          }
          this.rzid = qrcode.rzid || ''
          // 先隐藏刷新的图片
          this.$refs._refreshImg.style.display = 'none'
          const qrDom = this.$refs['_swjQrCode']
          // 创建img元素
          const img = document.createElement('img')
          img.style.width = '226px'
          img.style.height = '226px'
          img.style.objectFit = 'contain'
          // 拼接base64前缀
          img.src = `data:image/jpeg;base64,${qrcode.img}`
          // 错误处理
          img.onerror = (e) => {
            return this.$message.error('二维码加载失败，请重试')
          }
          // 判断qrDom的child是不是2个或更多，如果是就删除
          while (qrDom.childElementCount >= 2) {
            qrDom.removeChild(qrDom.lastChild)
          }
          qrDom.appendChild(img)
          this.handleCountDown(Number(qrcode.time))
        })
        .finally(() => {
          this.qrCodeLoading = false
        })
    },
    // 倒计时刷新扫码状态
    handleCountDown(seconds) {
      const vm = this // 保留组件实例引用
      vm.clearFaceCountdown() // 停止定时器
      // 定义总时间和间隔时间
      const totalTime = Math.floor(seconds) // 总时间（秒）
      let remainingTime = totalTime // 剩余时间（秒）
      const callInterval = 5 // 每 5 秒调用一次 listStatus 方法
      // 获取扫码认证结果
      // 使用箭头函数保持this指向
      const getScanResult = (registry, smlx, spid, taxpayerNo, rzid, zdAccount) => {
        if (!vm.scanResultInvoking) {
          vm.scanResultInvoking = true
          getSwjLoginResult(registry, smlx, spid, taxpayerNo, rzid, zdAccount)
            .then((res) => {
              if (res.returnCode === '0' && res.message === '2') {
                vm.$message.success('认证成功，请重新操作')
                vm.clearFaceCountdown()
                vm.$emit('successScanCode')
                vm.smlx = '0'
              }
            })
            .finally(() => {
              vm.scanResultInvoking = false
            })
        }
      }
      vm.$refs._secondText.innerHTML = '二维码有效时间：' + remainingTime + 's'
      // 启动定时器
      vm.timerId = setInterval(() => {
        // 更新剩余时间
        remainingTime -= 1
        vm.$refs._secondText.innerHTML =
          '二维码有效时间：' + remainingTime + 's'
        // 每 5 秒调用一次 listStatus 方法
        if (
          (totalTime - remainingTime) % callInterval === 0 &&
          remainingTime > 0
        ) {
          getScanResult(
            vm.registry.registryType,
            vm.smlx,
            vm.registry.spId,
            vm.registry.taxpayerNo,
            vm.rzid,
            vm.zdAccount
          )
        }
        // 如果剩余时间小于等于 0，则停止定时器
        if (remainingTime <= 0) {
          vm.clearFaceCountdown() // 停止定时器
          vm.$refs._secondText.innerHTML = ''
          const qrDom = vm.$refs['_swjQrCode']
          // qrDom只保留一个默认图片
          while (qrDom.childElementCount >= 2) {
            qrDom.removeChild(qrDom.lastChild)
          }
          vm.$refs._refreshImg.style.display = 'block'
        }
      }, 1000) // 将秒转换为毫秒
    },
    // 选择扫码方式
    scanTitleClick(topIndex) {
      this.smlx = topIndex
      this.handleRefreshQrCode()
    },
    handleCloseDialog() {
      this.smlx = '0'
      this.clearFaceCountdown()
      this.$emit('update:dialogVisible', false)
    },
    clearFaceCountdown() {
      // 清除扫脸二维码倒计时
      clearInterval(this.timerId)
      this.timerId = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.scanDialogTitle {
  ::v-deep .el-dialog__title {
    color: red;
  }
}

#swjQrCode {
  width: 226px;
  height: 226px;
  margin: 15px auto 5px;
  padding: 0 0;

  img {
    width: 226px;
    height: 226px;
    margin: 0 0;
  }
}

.scanTop {
  width: 282px;
  height: 42px;
  margin: 0 auto;
  padding: 0 0;
  border-radius: 5px;
  border: 1px solid #409eff;
}

.scanTitle {
  width: 140px;
  height: 100%;
  float: left;
  text-align: center;
  cursor: pointer;
  span {
    font-size: 16px;
    line-height: 40px;
  }
}

.scanSelected {
  background-color: #409eff;
  color: #ffffff;
}

.scanUnSelected {
  background-color: #ffffff;
  color: #333333;
}
</style>
