/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-11-16 09:10:03
 * @LastEditors: Peng Xiao
 * @LastEditTime: 2021-11-23 14:54:58
 */

import request from '@/utils/request'

/**
 * @name:获取资产分类
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/asset/assetClass/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:保存出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/asset/assetClass/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/asset/assetClass/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:获取资产类型树
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function tree({ data, params }) {
  return request({
    url: `/financial/asset/assetClass/tree`,
    method: 'post',
    params,
    data,
    withCredentials: true
  })
}

/**
 * @name:获取资产类型树
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listTree(params) {
  return request({
    url: `/financial/asset/assetClass/listTree`,
    method: 'post',
    params,
    withCredentials: true
  })
}

/**
 * @name:删除出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/asset/assetClass/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

