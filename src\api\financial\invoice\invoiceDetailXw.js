/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-10-09 16:59:46
 * @LastEditors: Andy
 * @LastEditTime: 2023-10-12 09:06:56
 */
import request from '@/utils/request'


/* 列表 */
export function page(data) {
  return request({
    url: '/invoice/invoiceDetailXw/page',
    method: 'post',
    data: data
  })
}

/* 查看 */
export function getInvoiceDetailXwItem(params) {
  return request({
    url: `/invoice/invoiceDetailXw/getInvoiceDetailXwItem`,
    method: 'get',
    params
  })
}

/* 获取外面发票数据 */
export function getApiInvoiceDetail(params) {
  return request({
    url: `/invoice/invoiceDetailXw/getApiInvoiceDetail`,
    method: 'post',
    params
  })
}


/* 获取外面发票数据 */
export function getInvoiceType(params) {
  return request({
    url: `/invoice/invoiceDetailXw/getInvoiceType`,
    method: 'post',
    params
  })
}

