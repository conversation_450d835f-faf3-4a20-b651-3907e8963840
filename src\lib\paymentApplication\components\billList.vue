<!--
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-07-11 09:32:28
 * @LastEditors: Andy
 * @LastEditTime: 2023-12-11 17:04:41
-->
<template>
    <div v-loading="loading" class="dialog-box">
      <gever-table
        ref="_geverBillTableRef"
        :columns="tableColumns"
        :data="table.rows"
        highlight-current-row
        height="550"
        :pagination="false"
        :total="table.total"
        @pagination-change="getList"
        @selection-change="handleSelectionChange"
      ></gever-table>
    </div>
</template>

<script>
import {
  getDeductInvoice,
  getDeductBillList,
  getDeductSundryBillList
} from '../../../api/payment/offsetNote.js'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')

export default {
  name: 'Details',
  props: {
    loadType: { type: String, default: 'add' },
    billVisible: { type: Boolean, default: false },
    rowData: { type: Object, default: () => {} },
    offsetNoteType: { type: String, default: '1' },
    creationTime: { type: String, default: new Date().getFullYear() + '-01-01' },
    selectBillIds: {
      type: Array,
      default: () => []
    },
    relBillIds: {
      type: Array,
      default: () => []
    },
    otherSelectBillIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      nowSelectData: [],
      loading: false,
      title: '',
      tableColumns: [],
      tableColumnsBills: [
        { type: 'selection', fixed: 'left' },
        { type: 'index', fixed: 'center', width: 50 },
        {
          prop: 'billNature',
          label: '票据种类',
          minWidth: 120,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '收款收据' },
              { id: 2, text: '定额票据' },
              { id: 3, text: '付款凭据' }
            ]
          }
        },
        {
          prop: 'billName',
          label: '票据类型',
          minWidth: 120,
          align: 'left'
        },
        {
          prop: 'billNo',
          label: '票据号码',
          minWidth: 120,
          align: 'left'
        },
        {
          prop: 'drawDate',
          label: '开票日期',
          minWidth: 100,
          align: 'left'
        },
        {
          prop: 'totalAmount',
          label: '开票金额',
          minWidth: 200,
          align: 'right',
          filter: 'money'
        },
        {
          prop: 'otherSide',
          label: '缴款人/单位',
          minWidth: 120,
          align: 'left'
        },
        { prop: 'summary', label: '开票摘要', minWidth: 100, align: 'left' },
        {
          prop: 'drawer',
          label: '开票人',
          minWidth: 100,
          align: 'left'
        },
        {
          prop: 'invoicingStage',
          label: '开票方式',
          minWidth: 100,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '先收后开' },
              { id: 2, text: '先开后收' },
              { id: 3, text: '补开' }
            ]
          }
        },
        {
          prop: 'incomeType',
          label: '收入类型',
          minWidth: 120,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '合同收入' },
              { id: 2, text: '非合同收入' }
            ]
          }
        },
        {
          prop: 'drawState',
          label: '票据状态',
          minWidth: 120,
          align: 'center',
          convert: {
            options: [
              { id: 0, text: '未开' },
              { id: 1, text: '已开' }
            ]
          }
        }
      ],
      tableSundryColumnsBills: [
        { type: 'selection', fixed: 'left' },
        { type: 'index', fixed: 'center', width: 50 },
        {
          prop: 'billName',
          label: '票据类型',
          minWidth: 120,
          align: 'left'
        },
        {
          prop: 'billNo',
          label: '票据号码',
          minWidth: 120,
          align: 'left'
        },
        {
          prop: 'drawDate',
          label: '开票日期',
          minWidth: 100,
          align: 'left'
        },
        {
          prop: 'totalAmount',
          label: '开票金额',
          minWidth: 200,
          align: 'right',
          filter: 'money'
        },
        {
          prop: 'otherSide',
          label: '缴款人/单位',
          minWidth: 120,
          align: 'left'
        },
        { prop: 'summary', label: '开票摘要', minWidth: 100, align: 'left' },
        {
          prop: 'drawer',
          label: '开票人',
          minWidth: 100,
          align: 'left'
        },
        {
          prop: 'invoicingStage',
          label: '开票方式',
          minWidth: 100,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '先收后开' },
              { id: 2, text: '先开后收' },
              { id: 3, text: '补开' }
            ]
          }
        },
        {
          prop: 'incomeType',
          label: '收入类型',
          minWidth: 120,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '合同收入' },
              { id: 2, text: '非合同收入' }
            ]
          }
        },
        {
          prop: 'drawState',
          label: '票据状态',
          minWidth: 120,
          align: 'center',
          convert: {
            options: [
              { id: 0, text: '未开' },
              { id: 1, text: '已开' }
            ]
          }
        }
      ],
      tableColumnsInvoice: [
        { type: 'selection', fixed: 'left' },
        { type: 'index', fixed: 'center', width: 50 },
        {
          prop: 'invoiceAbbr',
          label: '发票类型',
          minWidth: 120,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '收款收据' },
              { id: 2, text: '定额票据' },
              { id: 3, text: '付款凭据' }
            ]
          }
        },
        {
          prop: 'invoiceCode',
          label: '发票代码',
          minWidth: 120,
          align: 'left'
        },
        {
          prop: 'invoiceNo',
          label: '发票号码',
          minWidth: 120,
          align: 'left'
        },
        {
          prop: 'drawDate',
          label: '开票日期',
          minWidth: 100,
          align: 'left'
        },
        {
          prop: 'totalAmount',
          label: '开票金额',
          minWidth: 200,
          align: 'right',
          filter: 'money'
        },
        {
          prop: 'payerName',
          label: '缴款人/单位',
          minWidth: 120,
          align: 'left'
        },
        { prop: 'summary', label: '开票摘要', minWidth: 100, align: 'left' },
        {
          prop: 'drawer',
          label: '开票人',
          minWidth: 100,
          align: 'left'
        },
        {
          prop: 'invoicingStage',
          label: '开票方式',
          minWidth: 100,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '先收后开' },
              { id: 2, text: '先开后收' },
              { id: 3, text: '补开' }
            ]
          }
        },
        {
          prop: 'incomeType',
          label: '收入类型',
          minWidth: 120,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '合同收入' },
              { id: 2, text: '非合同收入' }
            ]
          }
        },
        {
          prop: 'invoiceState',
          label: '发票状态',
          minWidth: 120,
          align: 'center',
          convert: {
            options: [
              { id: 1, text: '开出' },
              { id: 2, text: '作废' },
              { id: 3, text: '遗失' }
            ]
          }
        }
      ],
      table: {
        row: [],
        total: 0
      }
    }
  },
  computed: {
    // 地区机构数据
    ...mapState(['areaLogin']),
    // 转浮点数
    comma() {
      return (val, keep_dec = 2) => {
        if (val) {
          if (keep_dec) {
            val = Number(val).toFixed(keep_dec)
          }
          val = String(val)
          if (val.indexOf('.') == -1) {
            return val.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,')
          }
          return (
            val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
            '.' +
            val.split('.')[1]
          )
        } else {
          return '0.00'
        }
      }
    }
  },
  watch: {
    offsetNoteType: {
      deep: true,
      immediate: true,
      handler(newVal) {
        if (newVal == '1') {
          this.tableColumns = this.tableColumnsBills
          this.getList()
          this.title = '票据'
        } else if (newVal === '2') {
          this.tableColumns = this.tableColumnsInvoice
          this.getList()
          this.title = '发票'
        } else if (newVal === '3') {
          this.tableColumns = this.tableSundryColumnsBills
          this.getList()
          this.title = '非电子票据'
        }
      }
    }
  },
  created() {
    // this.getOptions()
  },
  mounted() {
    // this.initTable()
  },
  methods: {
    getList() {
      console.log(this.offsetNoteType)
      const year = this.creationTime.split('-')[0]
      const params = {
        orgId: this.areaLogin.id,
        beginDate: `${year}-01-01`,
        endDate: `${year}-12-31`,
        drawee: this.rowData.receiverAccount,
        amount: this.rowData.amountPayable,
        ids: this.loadType == 'view' && this.selectBillIds ? this.selectBillIds.join(',') : '',
        relIds: this.relBillIds.join(',')
        // amount: this.rowData.originalData.paymentApprovalApplicationVO.amount
      }
      this.loading = true
      if (this.offsetNoteType === '1') {
        getDeductBillList(params)
          .then((res) => {
            this.table.rows = res.data
            if (this.otherSelectBillIds && this.otherSelectBillIds.length > 0) {
              this.table.rows = this.table.rows.filter(i => !this.otherSelectBillIds.includes(i.id))
            }
            this.table.rows = this.table.rows.filter(cur => this.rowData.amountPayable >= cur.totalAmount && this.rowData.receiverAccount == cur.otherSide)
            this.initTable()
          })
          .finally(() => {
            this.loading = false
          })
      } else if (this.offsetNoteType === '2') {
        getDeductInvoice(params)
          .then((res) => {
            this.table.rows = res.data
            if (this.otherSelectBillIds && this.otherSelectBillIds.length > 0) {
              this.table.rows = this.table.rows.filter(i => !this.otherSelectBillIds.includes(i.id))
            }
            this.table.rows = this.table.rows.filter(cur => this.rowData.amountPayable >= cur.totalAmount && this.rowData.receiverAccount == cur.payerName)
            this.initTable()
          })
          .finally(() => {
            this.loading = false
          })
      } else {
        getDeductSundryBillList(params)
          .then((res) => {
            this.table.rows = res.data
            if (this.otherSelectBillIds && this.otherSelectBillIds.length > 0) {
              this.table.rows = this.table.rows.filter(i => !this.otherSelectBillIds.includes(i.id))
            }
            this.table.rows = this.table.rows.filter(cur => this.rowData.amountPayable >= cur.totalAmount && this.rowData.receiverAccount == cur.otherSide)
            this.initTable()
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    /* table 选择复选框回调 */
    handleSelectionChange(data) {
      this.nowSelectData = data
    },
    /* 关闭弹窗 */
    handleClose() {
      this.$emit('updatelist')
      this.$emit('update:billVisible', false)
    },
    initTable() {
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs._geverBillTableRef) {
            console.log(this.selectBillIds)
            this.$refs._geverBillTableRef.clearSelection()
            for (let i = 0; i < this.table.rows.length; i++) {
              if (this.selectBillIds.includes(this.table.rows[i].id)) {
                this.$refs._geverBillTableRef.toggleRowSelection(this.table.rows[i], true)
              }
            }
          }
        })
      })
    }
    /* 确认按钮 */
/*    handleSelect() {
      if (this.nowSelectData.length === 0) {
        return this.$message.warning('请至少选择一条数据')
      }
      // if (this.nowSelectData.length > 1) {
      //   return this.$message.warning('只能选择一条数据')
      // }
      const deductBusinessId = this.nowSelectData.map((item) => item.id)
      const deductBusinessNo = this.nowSelectData.map((item) =>
        this.offsetNoteType === '1'
          ? item.billNo
          : item.invoiceCode + item.invoiceNo
      )
      const deductBusinessAmount = this.nowSelectData.map(
        (item) => item.totalAmount
      )
      if (
        this.rowData.amountPayable !=
        deductBusinessAmount.reduce((acc, curr) => acc + curr, 0).toFixed(2)
      ) {
        return this.$message.warning(
          '冲抵收据的金额合计与收款方信息的金额必须一致'
        )
      }
      const params = {
        // id: this.rowData.originalData.id,
        // deductBusinessId: this.nowSelectData[0].id,
        // deductBusinessNo:
        //   this.statusType === '1'
        //     ? this.nowSelectData[0].billNo
        //     : this.nowSelectData[0].invoiceCode +
        //       this.nowSelectData[0].invoiceNo,
        // deductState: 1
        id: this.rowData.originalData.id,
        deductBusinessId: deductBusinessId,
        deductBusinessNo: deductBusinessNo,
        deductBusinessAmount: deductBusinessAmount
      }
      this.loading = true
      offsetNote(params)
        .then((res) => {
          this.$message.success(res.message)
          this.$emit('updatelist')
          this.$emit('update:billVisible', false)
        })
        .finally(() => {
          this.loading = false
        })
    }*/
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog .el-dialog__body {
  height: 80%;
  overflow: hidden;
}
.btn-box {
  width: 100%;
  margin-bottom: 20px;
}
</style>
