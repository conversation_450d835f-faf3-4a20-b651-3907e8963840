
import request from '@/utils/request'

/**
 * 获取凭证入账业务类型
 */
export function getModule() {
  return request({
    url: '/financial/accounting/accountingBusinessRelation/getModule',
    method: 'get'
  })
}

/**
 * 获取银行流水收款列表
 */
export function loadAccountingPaymentBankStatement(data) {
  return request({
    url: '/financial/accounting/business/accountingPaymentBankStatement/noPage',
    method: 'post',
    data: data
  })
}

/**
 * 获取票据收款列表
 */
export function loadAccountingBill(data) {
  return request({
    url: '/financial/accounting/business/accountingBill/noPage',
    method: 'post',
    data: data
  })
}

/**
 * 获取合同收款列表
 */
export function loadAccountingContractCollection(data) {
  return request({
    url: '/financial/accounting/business/accountingContractCollection/noPage',
    method: 'post',
    data: data
  })
}

/**
 * 获取合同收款列表
 */
export function loadAccountingContractPayment(data) {
  return request({
    url: '/financial/accounting/business/accountingContractPayment/noPage',
    method: 'post',
    data: data
  })
}

/**
 * 获取票据收款的收款明细
 */
export function loadBillItemList(id) {
  return request({
    url: '/bill/billDetail/getItemList/' + id,
    method: 'get'
  })
}

/**
 * 获取票据收款的详细信息
 */
export function loadBillDetail(id) {
  return request({
    // url: '/bill/billDetail/load/' + id,
    url: `/bill/billDetail/loadDetail?detailId=${id}`,
    method: 'get'
  })
}

/**
 * 生成凭证前检查(票据)
 */
export function checkBeforeBillGenerate(data) {
  return request({
    url: '/financial/accounting/business/accountingBill/checkBeforeGenerate',
    method: 'post',
    data: data
  })
}

/**
 * 生成凭证前检查(合同)
 */
export function checkBeforeContractGenerate(data) {
  return request({
    url: '/financial/accounting/business/accountingContractCollection/checkBeforeGenerate',
    method: 'post',
    data: data
  })
}

/**
 * 生成凭证前检查(银行流水)
 */
export function checkBeforeBankGenerate(data) {
  return request({
    url: '/financial/accounting/business/accountingPaymentBankStatement/checkBeforeGenerate',
    method: 'post',
    data: data
  })
}

/**
 * 获取常用摘要
 */
export function getModelSummaryList(data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/getModelSummaryList',
    method: 'post',
    data: data
  })
}

/**
 * 获取会计科目
 */
export function getAccountingItemList(data) {
  return request({
    url: '/financial/books/basedata/accountingItem/getAccountingItemList',
    method: 'post',
    data: data
  })
}

/**
 * 获取银行账户列表
 */
export function getBankAccountList(params) {
  return request({
    url: '/financial/accounting/business/accountingPaymentBankStatement/listBankAccount',
    method: 'get',
    params: params
  })
}

/**
 * 获取会结算方式
 */
export function getSettlementTypeList(data) {
  return request({
    url: '/financial/books/basedata/cashiersettlementtype/getModelList',
    method: 'post',
    data: data
  })
}

/**
 * 票据一对一生成凭证数据
 */
export function genWithOneToOneWithBill(data) {
  return request({
    url: '/financial/accounting/business/accountingBill/genWithOneToOne',
    method: 'post',
    data: data
  })
}

/**
 * 银行流水一对一生成凭证数据
 */
export function genWithOneToOneWithPaymentBankStatement(data) {
  return request({
    url: '/financial/accounting/business/accountingPaymentBankStatement/genWithOneToOne',
    method: 'post',
    data: data
  })
}

/**
 * 合同一对一生成凭证数据
 */
export function genWithOneToOneWithContract(data) {
  return request({
    url: '/financial/accounting/business/accountingContractCollection/genWithOneToOne',
    method: 'post',
    data: data
  })
}

/**
 * 票据多对一生成凭证数据
 */
export function genWithManyToOneWithBill(data) {
  return request({
    url: '/financial/accounting/business/accountingBill/genWithManyToOne',
    method: 'post',
    data: data
  })
}

/**
 * 银行流水一对一生成凭证数据
 */
export function genWithManyToOneWithPaymentBankStatement(data) {
  return request({
    url: '/financial/accounting/business/accountingPaymentBankStatement/genWithManyToOne',
    method: 'post',
    data: data
  })
}

/**
 * 合同多对一生成凭证数据
 */
export function genWithManyToOneWithContract(data) {
  return request({
    url: '/financial/accounting/business/accountingContractCollection/genWithManyToOne',
    method: 'post',
    data: data
  })
}

/**
 * 获取辅助核算类型
 */
export function getAssistForVoucher(params) {
  return request({
    url: '/financial/books/basedata/assistType/getAssistForVoucher',
    method: 'get',
    params: params
  })
}

/**
 * 生成凭证(票据)
 */
export function saveBillVoucher(params, data) {
  return request({
    url: '/financial/accounting/business/accountingBill/saveVoucher',
    method: 'post',
    params: params,
    data: data,
    payload: true
  })
}

/**
 * 生成凭证(合同)
 */
export function saveContractVoucher(params, data) {
  return request({
    url: '/financial/accounting/business/accountingContractCollection/saveVoucher',
    method: 'post',
    params: params,
    data: data,
    payload: true
  })
}

/**
 * 生成凭证(银行流水)
 */
export function saveBankVoucher(params, data) {
  return request({
    url: '/financial/accounting/business/accountingPaymentBankStatement/saveVoucher',
    method: 'post',
    params: params,
    data: data,
    payload: true
  })
}

/**
 * 取消生成、关联凭证(票据)
 */
export function cancelGenerateBillVoucher(data) {
  return request({
    url: '/financial/accounting/business/accountingBill/cancelsaveVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 取消生成、关联凭证(合同)
 */
export function cancelGenerateContractVoucher(data) {
  return request({
    url: '/financial/accounting/business/accountingContractCollection/cancelGenerateVoucher',
    method: 'post',
    data: data
  })
}

export function cancelGenerate(data) {
  return request({
    url: '/financial/accounting/business/accountingBill/cancelGenerateVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 取消生成、关联凭证(银行流水)
 */
export function cancelGenerateBankVoucher(data) {
  return request({
    url: '/financial/accounting/business/accountingPaymentBankStatement/cancelGenerateVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 获取会计凭证分录(票据)
 */
export function loadBillPageEntry(data) {
  return request({
    url: '/financial/accounting/business/accountingBill/pageEntry',
    method: 'post',
    data: data
  })
}

/**
 * 获取会计凭证分录(合同)
 */
export function loadContractPageEntry(data) {
  return request({
    url: '/financial/accounting/business/accountingContractCollection/pageEntry',
    method: 'post',
    data: data
  })
}

/**
 * 获取会计凭证分录(银行流水)
 */
export function loaBankdPageEntry(data) {
  return request({
    url: '/financial/accounting/business/accountingPaymentBankStatement/pageEntry',
    method: 'post',
    data: data
  })
}

/**
 * 凭证关联(票据)
 */
export function relateBillEntry(data) {
  return request({
    url: '/financial/accounting/business/accountingBill/relateEntry',
    method: 'post',
    data: data
  })
}

/**
 * 凭证关联(合同)
 */
export function relateContractEntry(data) {
  return request({
    url: '/financial/accounting/business/accountingContractCollection/relateEntry',
    method: 'post',
    data: data
  })
}

/**
 * 凭证关联(银行流水)
 */
export function relateBankEntry(data) {
  return request({
    url: '/financial/accounting/business/accountingPaymentBankStatement/relateEntry',
    method: 'post',
    data: data
  })
}
