<template>
  <div v-if="attachmentInfo" class="gever-form">
    <div class="module-title">
      <span class="label-title">
        {{ attachmentInfo.fileTitle }}
        <span class="red">
          ({{ $t('仅支持上传gif,jpg,jpeg,png,doc,xls,txt,pdf,docx,xlsx,zip') }})
        </span>
        <el-button
          v-if="isApprovalTask && fileList.length"
          type="primary"
          round
          plain
          @click="handleDownload"
        >
          批量下载
        </el-button>
      </span>
      <el-popover v-if="!isView" placement="left" width="400" trigger="click">
        <div v-if="!scanSuccess" v-loading="loading" class="code-container">
          <h2>{{ $t('扫一扫便捷上传附件') }}</h2>
          <p>{{ $t('请使用浏览器或微信扫码进行附件上传') }}</p>
          <div :id="'qrcode_' + uuid" ref="qrCodeUrl" />
        </div>
        <div v-else class="success-container">
          <img :src="require('@/assets/images/qr_success.png')" alt="" />
          <div>
            <span>{{ $t('扫码成功，') }}</span>
            <br />
            <span>{{ $t('请在手机完成附件上传') }}</span>
          </div>
        </div>
        <el-button
          slot="reference"
          type="primary"
          plain
          round
          icon="el-customIcon-scanning"
          @click="handleScanningUpload"
        >
          {{ $t('扫码上传') }}
        </el-button>
      </el-popover>
    </div>
    <el-upload
      ref="_uploadRef"
      v-loading="loading"
      :class="{ 'upload-none': isView == true }"
      :action="VUE_APP_BASE_API + uploadConfig.serverUrl"
      :headers="currentHeaders"
      :data="paramsData"
      :limit="20"
      name="files"
      multiple
      list-type="picture-card"
      :max-size="50"
      :on-change="handleChange"
      :on-success="handleSuccess"
      :file-list="fileList"
      :disabled="isView"
      :before-upload="handleBeforeUpload"
    >
      <i slot="default" class="el-icon-plus" />

      <div
        slot="file"
        slot-scope="{ file }"
        :title="file.name || '请上传附件'"
        style=""
      >
        <img class="el-upload-list__item-thumbnail" :src="getSrc(file)" />
        <span class="el-upload-list__item-actions">
          <span
            v-if="imageSuffix.includes(getFileType(file))"
            class="el-upload-list__item-preview"
            @click="handlePictureCardPreview(file)"
          >
            <i class="el-icon-zoom-in" />
          </span>
          <span
            class="el-upload-list__item-delete"
            @click="handlePictureCardDownload(file)"
          >
            <i class="el-icon-download" />
          </span>
          <span
            v-if="!isView"
            class="el-upload-list__item-delete"
            @click="handlePictureCardRemove(file)"
          >
            <i class="el-icon-delete" />
          </span>
        </span>
        <div class="ellipsis">{{ file.name }}</div>
      </div>
    </el-upload>
    <el-image-viewer
      v-if="showPreview"
      :url-list="urlList"
      :initial-index="imageIndex"
      :z-index="9999"
      :on-close="closeViewer"
    />

    <!-- <pdf-preview v-if="previewVisible" :preview-visible.sync="previewVisible" :pdf-src="pdfSrc" /> -->
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import {
  loadUploadUrl,
  loadFileList,
  getBatchList,
  deleteFile
} from '../../../api/gever/common.js'
import { uuid } from '../../../utils/gever.js'
import QRCode from 'qrcodejs2'
import Cookies from 'js-cookie'
import Base64 from '../../../utils/base64.js'
export default {
  components: {
    'el-image-viewer': () =>
      import('element-ui/packages/image/src/image-viewer')
  },
  props: {
    attachment: { type: Object, default: () => {} },
    businessId: { type: String, default: '' },
    batchId: { type: String, default: '' },
    isView: { type: Boolean, default: true }
  },
  data() {
    return {
      previewVisible: false,
      pdfSrc: '',
      showPreview: false,
      urlList: [],
      fileList: [],
      imageIndex: 0,
      uploadConfig: {},
      uuid: '',
      currentHeaders: {},
      imageSuffix: ['gif', 'jpg', 'jpeg', 'png', 'pdf'],
      fileType: [
        'gif',
        'jpg',
        'jpeg',
        'png',
        'doc',
        'pdf',
        'xls',
        'txt',
        'docx',
        'xlsx',
        'zip'
      ],
      qrSocket: null,
      scanSuccess: false,
      loading: false,
      VUE_APP_BASE_API: process.env.VUE_APP_BASE_API,
      urlConfig: {
        model: '0',
        serverUrl: '/filesystem/fileInfo/upload',
        fileUrl: '/filesystem/fileInfo/preview?path=',
        downloadUrl: '/filesystem/fileInfo/download?path='
      }
    }
  },
  computed: {
    isApprovalTask() {
      return this?.$route?.name === 'ApprovalTask'
    },
    attachmentInfo() {
      return this.attachment
    },
    paramsData() {
      return {
        batchId: this.batchId || this.uuid,
        fileClassification: this.attachment.indexx,
        id: 'WU_FILE_' + this.attachment.indexx
      }
    }
  },
  watch: {
    batchId: {
      handler(val) {
        if (val) {
          this.uuid = val
        } else {
          this.uuid = uuid()
        }
      },
      immediate: true,
      deep: true
    },
    attachment: {
      handler: function (val) {
        if (val?.fileList) {
          this.fileList = val.fileList.map((item) => {
            item.url =
              '/' +
              process.env['VUE_APP_REAL'] +
              '/filesystem/fileInfo/preview?path=' +
              item.path +
              '&access_token=' +
              this.getToken()
            return item
          })
        }
      },
      immediate: true,
      deep: true
    },
    businessId: {
      handler: function (val) {
        if (!val) return
        // 回填附件信息
        const params = {
          businessId: this.businessId,
          fileClassification: this.attachment.indexx
        }
        loadFileList(params).then((res) => {
          res.data.forEach((item) => {
            item.url =
              '/' +
              process.env['VUE_APP_REAL'] +
              '/filesystem/fileInfo/preview?path=' +
              item.path +
              '&access_token=' +
              this.getToken()
            this.fileList.push(item)
          })
        })
      },
      immediate: true
    },
    fileList: {
      handler: function (val) {
        this.urlList = []
        val.forEach((item) => {
          this.urlList.push(item.url)
        })
      },
      immediate: true
    },
    loading(newVal) {
      this.$emit('loading', newVal)
    }
  },
  created() {
    loadUploadUrl().then((res) => {
      this.uploadConfig = res.data
    })
    this.getHeader()
  },
  methods: {
    getToken,
    handleDownload() {
      const serviceUrl =
        process.env.NODE_ENV === 'development'
          ? 'http://192.168.5.153'
          : window.location.origin
      const filedIds = this.fileList.map((cur) => cur.id)
      window.location.href = `${serviceUrl}/${
        process.env.VUE_APP_REAL
      }/payment/approval/approvalApplication/compressAndAppend?filedIds=${filedIds.join()}&access_token=${getToken()}&tenant_id=${this.$Cookies.get(
        'X-tenant-id-header'
      )}`
    },
    getHeader() {
      let areaCode = Cookies.get('X-tenant-id-header') || ''
      if (areaCode.length === 12) {
        if (areaCode.substring(6) === '000000') {
          areaCode = areaCode.substring(0, 6)
        } else if (areaCode.substring(9) === '000') {
          areaCode = areaCode.substring(0, 9)
        }
        areaCode = 'D' + areaCode
      }
      this.currentHeaders = {
        'X-user-token-header': this.getToken(),
        'X-tenant-id-header': areaCode || '',
        'X-app-id': Cookies.get('X-app-id') || ''
      }
    },
    handleBeforeUpload(file) {
      this.loading = true
      let isFileType = true // 文件类型
      let isFileSize = true // 文件大小
      if (!this.fileType.includes(this.getFileType(file))) {
        this.$message.warning(this.$t('当前文件类型不支持'))
        isFileType = false
      }
      if (file.size > 1024 * 1024 * 50) {
        this.$message.warning(this.$t('文件大小超出允许大小'))
        isFileSize = false
      }
      if (!(isFileType && isFileSize)) {
        // 检测不通过时取消loading
        this.loading = false
      }
      return isFileType && isFileSize
    },
    handleScanningUpload() {
      this.scanSuccess = false
      this.createQrWebSocket()
    },
    getURL(serverUrl, path, file) {
      return `/${
        process.env.VUE_APP_REAL
      }${serverUrl}${path}&access_token=${getToken()}&tenant_id=${this.$Cookies.get(
        'X-tenant-id-header'
      )}`
    },
    handlePictureCardPreview(file) {
      const { fileUrl: previewURL } = this.urlConfig
      const preview = file.preview ? file.preview : file.path
      this.previewUrl = this.getURL(previewURL, preview)
      console.log(file.type)
      if (file.type && file.type.startsWith('image')) {
        this.showPreview = true
        const currentUrl = file.url
        this.imageIndex = this.urlList.indexOf(currentUrl)
      } else {
        const previewServiceUrl =
          process.env.NODE_ENV === 'development'
            ? 'http://192.168.5.153'
            : window.location.origin
        this.previewUrl =
          previewServiceUrl +
          process.env.VUE_APP_BASE_API +
          this.urlConfig.downloadUrl +
          (file.preview || file.path) +
          `&access_token=${getToken()}&tenant_id=${this.$Cookies.get(
            'X-tenant-id-header'
          )}`
        const base = new Base64()
        console.log(this.previewUrl)
        const result = base.encode(this.previewUrl)
        window.open(
          previewServiceUrl +
            process.env.VUE_APP_BASE_API +
            '/kkfileview/onlinePreview?url=' +
            encodeURIComponent(result)
        )
      }
      // 执行el-image的点击事件开启预览
      // this.$refs['_imageRef'].clickHandler()
    },
    // handlePictureCardPreview(file) {
    //   console.log(file)
    //   if (file.suffix === 'pdf' || file.response?.data[0]?.suffix === 'pdf') {
    //     const path = file.path || file.response?.data[0]?.path
    //     this.pdfSrc =
    //       window.location.origin +
    //       '/' +
    //       process.env['VUE_APP_REAL'] +
    //       '/filesystem/fileInfo/preview?path=' +
    //       path +
    //       '&access_token=' +
    //       this.getToken()
    //     this.previewVisible = true
    //   } else {
    //     const currentUrl = file.url
    //     this.imageIndex = this.urlList.indexOf(currentUrl)
    //     this.showPreview = true
    //   }
    // },
    handlePictureCardDownload(file) {
      console.log(file)
      // const a = document.createElement('a')
      // const event = new MouseEvent('click')
      // a.download = file.name
      // a.href = file.url
      // a.dispatchEvent(event)
      this.getBlob(file.url).then((blob) => {
        this.saveAs(blob, file.name)
      })
    },
    saveAs(blob, filename) {
      var link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
    },
    getBlob(url) {
      return new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob'
        xhr.onload = () => {
          if (xhr.status === 200) {
            resolve(xhr.response)
          }
        }
        xhr.send()
      })
    },
    handlePictureCardRemove(file) {
      this.loading = true
      let id = ''
      let path = ''
      if (file.response) {
        id = file.response.data[0].id
        path = file.response.data[0].path
      } else {
        id = file.id
        path = file.url
      }
      deleteFile({
        batchId: this.batchId || this.uuid,
        fileId: id,
        path
      })
        .then((res) => {
          if (res.returnCode == '0') {
            this.$message.success(res.message)
            this.$refs['_uploadRef'].handleRemove(file)
          } else {
            this.$message.error(res.message)
          }
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    closeViewer() {
      this.showPreview = false
    },
    handleChange(file, fileList) {
      const imageList = fileList.filter((item) => {
        const index = item.name.lastIndexOf('.')
        if (index === -1) return
        const fileSuffix = item.name.substr(index + 1)
        return this.imageSuffix.includes(fileSuffix)
      })
      const urlList = []
      imageList.forEach((item) => {
        urlList.push(item.url)
      })
      this.urlList = urlList
    },
    handleSuccess(response, file, fileList) {
      this.loading = false
      // console.log(response, file, fileList)
      // this.fileList.push(file)
      // const temp = response.data[0]
      // temp.url = '/' + process.env['VUE_APP_REAL'] + '/filesystem/fileInfo/preview?path=' + temp.path + '&access_token=' + this.getToken()
      // this.fileList.push(temp)
    },
    getFileType(file) {
      let currentFileSuffix = ''
      const currentIndex = file.name.lastIndexOf('.')
      if (currentIndex === -1) {
        currentFileSuffix = ''
      } else {
        currentFileSuffix = file.name.substr(currentIndex + 1)
        if (currentFileSuffix.match(/^[0-9]{3}$/)) {
          // 判断是否zip分卷压缩包，后缀时 xxxx.zip.001
          const realFileName = file.name.substr(0, currentIndex)
          if (realFileName.length > 4 && realFileName.substr(realFileName.length - 4).toLowerCase() == '.zip') {
            return 'zip'
          }
        }
      }
      return currentFileSuffix.toLowerCase()
    },
    getSrc(file) {
      let url = ''
      switch (this.getFileType(file)) {
        case 'doc':
        case 'docx':
          url = require('@/assets/images/fileThumbnail/image-doc.png')
          break
        case 'xls':
        case 'xlsx':
          url = require('@/assets/images/fileThumbnail/image-xls.png')
          break
        case 'txt':
          url = require('@/assets/images/fileThumbnail/image-txt.png')
          break
        case 'pdf':
          url = require('@/assets/images/fileThumbnail/image-pdf.png')
          break
        case 'zip':
          url = require('@/assets/images/fileThumbnail/image-zip.png')
          break
        default:
          url = file.url
          break
      }
      return url
    },
    createCode() {
      // 创建二维码，并保存信息
      this.loading = true
      const url =
        window.location.origin +
        '/toPhone?batchId=' +
        this.paramsData.batchId +
        '&id=' +
        this.paramsData.id +
        '&fileClassification=' +
        this.paramsData.fileClassification +
        '&token=' +
        this.getToken() +
        '&userId=' +
        localStorage.getItem('userId') +
        '&serverUrl=' +
        this.uploadConfig.serverUrl
      setTimeout(() => {
        document.getElementById('qrcode_' + this.uuid).innerHTML = ''
        const qrcode = new QRCode(this.$refs['qrCodeUrl'], {
          width: 180,
          height: 180,
          correctLevel: 3
        })
        qrcode.makeCode(url)
        this.loading = false
      }, 100)
    },
    createQrWebSocket() {
      // 创建二维码webScoket
      let webSocketUrl =
        window.location.origin +
        process.env.VUE_APP_BASE_API +
        '/websocket/' +
        this.getToken()
      webSocketUrl = webSocketUrl.replace('http', 'ws').replace('https', 'wss')
      this.qrSocket = this.buildWebSocket({
        socketUrl: webSocketUrl,
        onMessage: (data) => {
          if (data.type == 'FS_SCAN') {
            this.scanSuccess = true
            console.log('开始扫描')
          } else if (data.type == 'FS_UPLOAD') {
            this.updateFile()
            console.log('上传')
          } else if (data.type == 'connectSuccess') {
            this.createCode()
            console.log('二维码webSocket连接成功')
          } else if (data.type == 'heartbeat') {
            console.log('pong!')
          } else if (data.type == 'WORKFLOW_NEW_MESSAGE') {
            // 工作流通知
          } else if (data.type == 'NOTIC_NEW_MESSAGE') {
            // 公告模块通知
          } else {
            console.error('二维码webSocket连接失败')
          }
        },
        onOpen: function () {
          // 2020.12.01 创建成功时，将setting参数上传至服务器保存
          // qrSocket.sendMessage(JSON.stringify(setting))
        }
      })
      this.qrSocket.init()
    },
    buildWebSocket(options) {
      return {
        socket: null,
        socketUrl: (options && options.socketUrl) || null,
        lockReconnect: false,
        onOpen: (options && options.onOpen) || null,
        onMessage: (options && options.onMessage) || null,
        onClose: (options && options.onClose) || null,
        onError: (options && options.onError) || null,
        init: function () {
          if (typeof WebSocket == 'undefined') {
            console.log('您的浏览器不支持WebSocket')
          } else {
            this.heartCheck.parent = this
            console.log('您的浏览器支持WebSocket')
            if (this.socket != null) {
              this.socket.close()
              this.socket = null
            }
            if (!this.socketUrl) {
              throw new Error('WebSocket地址未指定！')
            }
            this.socketUrl = this.socketUrl
              .replace('https', 'ws')
              .replace('http', 'ws')
            const self = this
            try {
              this.socket = new WebSocket(this.socketUrl)
            } catch (e) {
              this.reconnect()
              return
            }
            this.socket.onopen = function () {
              console.log('websocket已打开')
              self.heartCheck.reset().start()
              if (self.onOpen) {
                self.onOpen()
              }
            }
            this.socket.onmessage = function (msg) {
              self.heartCheck.reset().start()
              if (msg.data != 'pong' && msg.data != '连接成功') {
                if (self.onMessage) {
                  self.onMessage(JSON.parse(msg.data))
                }
              } else {
                console.log(msg.data + '!')
              }
            }
            this.socket.onclose =
              this.onClose ||
              function () {
                console.log('websocket已关闭')
                self.socket = null
                self.reconnect()
              }
            this.socket.onerror =
              this.onError ||
              function () {
                console.log('websocket发生了错误')
                self.socket = null
                self.reconnect()
              }
          }
        },
        reconnect: function () {
          if (this.socketUrl == null) return
          if (this.lockReconnect) return
          this.lockReconnect = true
          const self = this
          setTimeout(function () {
            self.init()
            self.lockReconnect = false
          }, 2000)
        },
        sendMessage: function (msg) {
          if (this.socket) {
            this.socket.send(msg)
          } else {
            throw new Error('WebSocket未初始化！')
          }
        },
        close: function () {
          if (this.socket) {
            this.socket.close()
            this.socket = null
          } else {
            console.log('WebSocket未初始化！')
          }
        },
        heartCheck: {
          parent: null,
          timeout: 3000,
          timeoutObj: null,
          serverTimeoutObj: null,
          reset: function () {
            clearTimeout(this.timeoutObj)
            clearTimeout(this.serverTimeoutObj)
            return this
          },
          start: function () {
            const self = this
            this.timeoutObj = setTimeout(function () {
              try {
                self.parent.sendMessage(
                  JSON.stringify({
                    type: 'heartbeat',
                    content: 'PING'
                  })
                )
              } catch (e) {
                self.parent.init()
                self.parent.sendMessage(
                  JSON.stringify({
                    type: 'heartbeat',
                    content: 'PING'
                  })
                )
              }
              console.log('ping!')
              self.serverTimeoutObj = setTimeout(function () {
                self.parent.close()
              }, self.timeout)
            }, this.timeout)
          }
        }
      }
    },
    updateFile() {
      getBatchList({
        batchId: this.batchId || this.uuid,
        fileClassification: this.attachment.indexx
      }).then((res) => {
        this.fileList = this.fileList.filter((item) => !item.batchId)
        res.data.forEach((item) => {
          item.url =
            '/' +
            process.env['VUE_APP_REAL'] +
            '/filesystem/fileInfo/preview?path=' +
            item.path +
            '&access_token=' +
            this.getToken()
          this.fileList.push(item)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  div {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .el-upload-list__item-thumbnail {
      height: auto;
      width: auto;
      max-width: 100%;
    }
  }
}
.code-container {
  width: 375px;
  height: 295px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 10px;
}
.ellipsis {
  height: unset !important;
  width: 100%;
  z-index: 2;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block !important;
  font-size: 12px;
  position: absolute;
  bottom: 0;
  text-align: center;
}
.success-container {
  width: 375px;
  height: 295px;
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .el-upload-list__item-thumbnail {
  width: 76%;
  padding-top: 3px !important;
  max-height: calc(100% - 45px);
  max-width: calc(100% - 40px);
}
</style>
