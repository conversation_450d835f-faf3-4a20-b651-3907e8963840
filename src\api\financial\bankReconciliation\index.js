import request from '@/utils/request'

/**
 * @name:银行对账列表数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listReconciliation(data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/listReconciliation',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
/**
 * @name:获取银行账号
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBankAccount(booksId) {
  return request({
    url: '/financial/books/basedata/bankaccount/getBankAccount',
    method: 'get',
    params: {booksId},
    withCredentials: true
  })
}