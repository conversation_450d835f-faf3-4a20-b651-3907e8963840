
<template>
  <div>
    <fold-box
      left-title="银行账户"
      right-title="账户明细对账"
      :call-back="toggleSearch"
    >
      <template #left>
        <el-form v-show="showFilter" inline>
          <el-form-item :label="$t('账号：')">
            <el-input v-model="filterParams.accountNumber" class="w100" />
          </el-form-item>
          <el-form-item :label="$t('名称：')">
            <el-input v-model="filterParams.ownerName" class="w100" />
          </el-form-item>
          <el-form-item>
            <el-button
              v-hasPermi="'financial.payment.manager.bankStatement.list'"
              type="primary"
              plain
              round
              icon="el-icon-search"
              @click="handleSearchList"
            >
              {{ $t('搜索') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="left-subNav" :loading="accountLoading">
          <ul class="gever-subNav">
            <li
              v-for="item in currentList"
              :key="item.id"
              :class="{ active: activeIndex === item.id }"
              @click="handleClick(item)"
            >
              <span>
                <span>{{ item.bankAccountNo }}</span>
                <span>{{ item.accountName }}</span>
              </span>
            </li>
          </ul>
        </div>
      </template>
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <!-- 更新表单字段 -->
            <el-form-item :label="$t('对账状态')" prop="qrPaymentReconciliation">
              <el-select v-model="queryParams.qrPaymentReconciliation" clearable placeholder="请选择">
                <el-option
                  v-for="item in reconciliationStatusOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('收入金额')" prop="incomeAmountMin">
              <gever-input 
                v-model="queryParams.inAmtMin" 
                style="width:45%" 
                @input="handleAmountInput('inAmtMin', $event)" 
              /> -
              <gever-input 
                v-model="queryParams.inAmtMax" 
                style="width:45%" 
                @input="handleAmountInput('inAmtMax', $event)" 
              />
            </el-form-item>
            <el-form-item :label="$t('账户明细时间')" prop="accountDetailTimeStart">
              <el-date-picker
                v-model="queryParams.startDay"
                class="w45%"
                type="date"
                value-format="yyyy-MM-dd"
              /> -
              <el-date-picker
                v-model="queryParams.endDay"
                class="w45%"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            <el-form-item :label="$t('对方账号')" prop="otherAccountNumber">
              <gever-input 
                v-model="queryParams.otherAccountNumber" 
              />
            </el-form-item>
            <el-form-item :label="$t('附言')" prop="postScript">
              <gever-input v-model="queryParams.postScript" />
            </el-form-item>
            <el-form-item>
              <el-button plain icon="el-icon-search" type="primary" round @click="handleSearch">
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <div style="float: right;">
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.qrPayment.reconciliation.accountReconciliation.handleReconciliation'"
                  type="primary"
                  round
                  @click="handleReconciliation"
                >
                  {{ $t('对账') }}
                </el-button>
                <el-button
                  v-hasPermi="'financial.qrPayment.reconciliation.accountReconciliation.cancelReconciliation'"
                  type="primary"
                  round
                  @click="cancelReconciliation"
                >
                  {{ $t('取消对账') }}
                </el-button>
              </el-form-item>
            </div>
          </el-form>

          <gever-table
            :loading="tableLoading"
            ref="_geverTableRef"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
            style="clear: both;"
          >

            <template #operation="{ row }">
              <el-button type="text" @click="handleView(row)">查看</el-button>
              <el-button type="text" @click="editReconciliation(row)" :disabled="row.qrPaymentReconciliation != '0' && row.qrPaymentReconciliation != '-1'">编辑</el-button>
            </template>

            <template #qrPaymentReconciliation="{ row }"> 
              <!-- 当对账状态为-1时显示红色字体 -->
              <span :style="row.qrPaymentReconciliation == '-1' ? { color: 'red' } : {}">
                {{ getTitle('' + row.qrPaymentReconciliation, reconciliationStatusOptions) }}
              </span>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <public-drawer 
      :visible.sync="detailDialogVisible" 
      title="对账"
      :size="80" 
      :buttons="type == 'view' ? viewButtons : detailButtons" 
      @close="detailDialogVisible = false"
    >
    <detail
        ref="_detailContentRef"
        v-if="detailDialogVisible"
        :type="type"
        :id="detailId"
        :activeAccountName="activeAccountName"
        :activeBankAccountNo="activeBankAccountNo"
        :activeMerchantNo="activeMerchantNo"
        :detailVisible="detailDialogVisible"
        :account-detail="currentRow"
        @update:detailVisible="detailDialogVisible = $event"
        @refreshTable="getList"
      />
    </public-drawer>
  </div>
</template>

<script>
import { loadQrPaymentAccountListApi } from '@/api/paymentQr/qrPaymentAccount.js'
import { loadQrPaymentReceiveListApi, loadAppQrPaymentReceiveListApi, deleteQrPaymentReceiveApi } from '@/api/paymentQr/qrPaymentReceive.js'
import { loadQrPaymentReceiveItemOptionsApi } from '@/api/paymentQr/qrPaymentReceiveItem.js'
import { loadAccountList, loadAccountDetail } from '@/api/payment/capital.js'
import detail from './components/detail'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import FoldBox from '../foldBox/index.vue'
// 引入获取字典的方法
import { getDictionary } from '@/api/gever/common.js' 
import { cancelReconcile } from '@/api/paymentQr/qrPaymentSettle.js' 

export default {
  name: 'QrPaymentReceive',
  components: { FoldBox, detail},
  data() {
    return {
      reconciliationStatusOptions: [], // 初始化为空数组
      accountLoading: false, 
      currentRow: {},
      type: 'view',
      queryParams: {
        qrPaymentReconciliation: null,
        inAmtMin: null,
        inAmtMax: null,
        startDay: this.getYesterday(),
        endDay: this.getToday(), 
        accountId: null,
        page: 1,
        rows: 20
      },
      detailButtons: [
        {
          type: '',
          text: this.$t('取消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          text: this.$t('确定'),
          type: 'primary',
          buttonStatus: true,
          callback: this.handleConfirm
        }
      ],
      viewButtons: [
        {
          type: '',
          text: this.$t('关闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        }
      ],
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50' },
          { label: '序号', type: 'index', index: this.indexMethod, width: '80' },
          { label: '账户明细时间', prop: 'tradeTime', minWidth: '180', resizable: true },
          { label: '收入金额（元）', prop: 'inAmt', minWidth: '150', resizable: true },
          { label: '账户余额（元）', prop: 'balance', minWidth: '150', resizable: true },
          { label: '上笔余额（元）', prop: 'preBalance', minWidth: '150', resizable: true },
          { label: '对方账号', prop: 'otherAccountNumber', minWidth: '250', resizable: true },
          { label: '对方户名', prop: 'otherAccountOwner', minWidth: '300', resizable: true },
          { label: '对方行名', prop: 'bankName', minWidth: '400', resizable: true },
          { label: '交易摘要', prop: 'absInfo', minWidth: '200', resizable: true },
          { label: '附言', prop: 'postScript', minWidth: '200', resizable: true },
          { label: '对账状态', prop: 'qrPaymentReconciliation', width: '100', fixed: 'right' },
          { label: '操作', slotName: 'operation', width: '100', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      showFilter: false,
      filterParams: {
        accountNumber: '',
        ownerName: ''
      },
      accountsQueryParams: {
        page: 1,
        rows: 99999,
        status: 1
      },
      accountList: [],
      currentList: [],
      activeIndex: null,
      activeAccountName: null,
      activeBankAccountNo: null,
      activeMerchantNo: null,
      tableLoading: false,
      // 新增：对账弹窗和选中行
      selectedRows: [],
      detailDialogVisible: false,
      detailId: null
    }
  },
  computed: {
    ...mapState(['areaLogin'])
  },
  watch: {
    activeIndex(newVal) {
      // this.queryParams.accountId = newVal
      this.queryParams.accountId = this.activeAccountId
      this.getList()
    },
    areaLogin: {
      handler: function (val) {
        // this.loadAccountList()
        this.loadAccounts()
      },
      immediate: true,
      deep: true
    }
  },
  async created() {
    await this.getReconciliationStatusOptions()
    // this.loadAccountList()
    window.addEventListener('resize', () => {
      this.$nextTick(() => {
        this.$refs._geverTableRef.setHeight()
      })
    })
  },
  methods: {
    toggleSearch() {
      this.showFilter = !this.showFilter
    },
    loadAccountList() {
      loadAccountList().then((res) => {
        this.accountList = res.data.rows
        this.currentList = res.data.rows
        if (this.currentList.length > 0) {
          this.activeIndex = this.currentList[0].id
        }
      })
    },
    // 新增方法，用于获取对账状态字典数据
    async getReconciliationStatusOptions () {
      const { data: reconciliationStatus = [] } = await getDictionary('/financial/qrPayment/reconciliationStatus/')
      this.reconciliationStatusOptions = reconciliationStatus
    },
    loadAccounts(){
      const areaCode = this.areaLogin.areaCode || this.areaLogin.code
      const orgCode = this.areaLogin.code
      if(this.areaLogin.type == 'Organization'){
        this.accountsQueryParams.orgCode = orgCode
        this.accountsQueryParams.areaCode = ''
      }else{
        this.accountsQueryParams.areaCode = areaCode
        this.accountsQueryParams.orgCode = ''
      }
      if(!areaCode){
        return
      }

      this.accountLoading = true
      
      loadQrPaymentAccountListApi(this.accountsQueryParams).then(res => {
        res.data.rows.forEach(v=>{
          if(v.status){
            v.status = true
          }else{
            v.status = false
          }
        })

        const seenIds = new Set();
        this.accountList = res.data.rows.filter(item => {
          if (!seenIds.has(item.bankAccountId)) {
            seenIds.add(item.bankAccountId);
            return true;
          }
          return false;
        });
        this.currentList = this.accountList
      })
      .catch(err => {
          this.table.tableList = []
          this.table.total = 0
        })
      .finally(() => {
        this.accountLoading = false
      })
    },
    handleSearchList() {
      this.currentList = this.accountList.filter((item) => {
        if (
          this.filterParams.accountNumber.length &&
          this.filterParams.ownerName.length
        ) {
          return (
            item.bankAccountNo.includes(this.filterParams.accountNumber) &&
            item.accountName.includes(this.filterParams.ownerName)
          )
        } else if (this.filterParams.accountNumber.length) {
          return item.bankAccountNo.includes(this.filterParams.accountNumber)
        } else if (this.filterParams.ownerName.length) {
          return item.accountName.includes(this.filterParams.ownerName)
        } else {
          return true
        }
      })
    },
    handleClick(item) {
      this.activeIndex = item.id
      this.activeAccountId = item.bankAccountId
      this.activeAccountName = item.accountName
      this.activeBankAccountNo = item.bankAccountNo
      this.activeMerchantNo = item.merchantNo
    },
    getList() {
      this.tableLoading = true
      if(!this.queryParams.accountId){
        this.table.tableList = []
        this.table.total = 0
        this.tableLoading = false
        return
      }
      if(this.queryParams.inAmtMin == ''){
        this.queryParams.inAmtMin = null
      }
      if(this.queryParams.inAmtMax == ''){
        this.queryParams.inAmtMax = null
      }
      const params = JSON.parse(JSON.stringify(this.queryParams))
      // params.direction = params.direction.trim()
      params.absInfo = '汇总转入'
      params.direction = 'C'
      console.log(params)
//       postScript  摘要
// qrPaymentReconciliation   0未对账   1已对账  -1对账不一致
// startDay   时间范围开始
// endDay    时间范围结束
// inAmtMin  收入金额范围开始
// inAmtMax  收入金额范围结束
      loadAccountDetail(params)
        .then((res) => {
          this.table.tableList = res.data.rows
          this.table.total = res.data.total
        })
        .catch(err => {
          this.table.tableList = []
          this.table.total = 0
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handleSearch() {
      this.getList()
    },
    handleReconciliation() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择且只选择一条记录进行对账')
        return
      }
      if (this.selectedRows[0].qrPaymentReconciliation != '0' && this.selectedRows[0].qrPaymentReconciliation != '-1') {
        this.$message.warning('请选择“未对账”或“对账不一致”的记录进行对账')
        return
      }
      this.detailId = this.selectedRows[0].id
      this.currentRow = this.selectedRows[0]
      this.detailDialogVisible = true
      this.type = 'edit'
    },
    editReconciliation(row){
      this.detailId = row.id
      this.detailDialogVisible = true
      this.currentRow = row
      this.type = 'edit'
    },
    async cancelReconciliation() {
      if (this.selectedRows.length === 0) {
        this.$message.warning(this.$t('请选择需要取消对账的记录'))
        return
      }

      if (this.selectedRows.some(row => row.qrPaymentReconciliation == '0')) {
        this.$message.warning('只能选择对账状态为“对账不一致”或“对账一致”的记录进行取消对账')
        return
      }
      
      try {
        await this.$confirm(
          '取消对账关系后，交易账单与交易流水的关联关系将取消，且操作不可逆！是否确认当前操作？',
          '提示',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const accountDetailIds = this.selectedRows.map(row => row.id)
        const params = { accountDetailIds }

        const res = await cancelReconcile(params);
        if (res.returnCode === '0') {
          this.$message.success(this.$t('取消对账成功'))
          this.getList()
        } else {
          this.$message.error(this.$t('取消对账失败') + ': ' + res.message);
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消对账失败', error)
          this.$message.error(this.$t('取消对账失败') + (error.message || ''))
        }
      }
    },
    getYesterday() {
      const date = new Date();
      date.setDate(date.getDate() - 1);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    getToday() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    handleView(row) {
      this.detailId = row.id
      this.type = 'view'
      this.currentRow = row
      this.detailDialogVisible = true
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    indexMethod(index) {
      return (this.queryParams.page - 1) * this.queryParams.rows + index + 1
    },
    handleCloseDetail() {
      this.detailDialogVisible = false
    },
    handleConfirm() {
      this.$refs._detailContentRef.confirm()
    },
    handleAccountInput(value) {
      // 只保留英文字母和数字（过滤所有非字母数字字符）
      const filteredValue = value.replace(/[^a-zA-Z0-9]/g, '');
      // 更新绑定值
      this.queryParams.otherAccountNumber = filteredValue;
    },
    // 收入金额输入处理（限制两位小数）
    handleAmountInput(field, value) {
      // 1. 移除非数字和非小数点字符
      let sanitized = value.replace(/[^0-9.]/g, '');
      
      // 2. 限制只能有一个小数点（移除第一个小数点后的所有其他小数点）
      const firstDotIndex = sanitized.indexOf('.');
      if (firstDotIndex !== -1) {
        // 保留第一个小数点前的部分 + 第一个小数点 + 后续部分移除所有小数点
        sanitized = sanitized.slice(0, firstDotIndex + 1) + 
                    sanitized.slice(firstDotIndex + 1).replace(/\./g, '');
      }
      
      // 3. 限制小数点后最多两位
      sanitized = sanitized.replace(/\.(\d{2})\d*/g, '.$1');
      
      // 4. 移除开头的小数点（如：".12" 转为 "0.12"）
      if (sanitized.startsWith('.')) {
        sanitized = '0' + sanitized;
      }
      
      // 更新绑定值
      this.queryParams[field] = sanitized;
    },
  }
}
</script>

<style scoped>

</style>

<style lang="scss" scoped>
#left-container {
  .el-form {
    margin: 0 5px;
    .el-form-item {
      margin-bottom: 0 !important;
      ::v-deep input {
        border: none;
        border-bottom: 1px solid #dcdfe6;
      }
    }
  }
  .left-subNav {
    .gever-subNav li {
      display: flex;
      align-items: center;
      & > span {
        display: inline-block;
        & > span {
          display: block;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.advance-search {
  * {
    font-size: 12px;
  }
  h3 {
    font-size: 14px;
    font-weight: bold;
    margin: 0 0 5px 0;
  }
}
.flex-w {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>