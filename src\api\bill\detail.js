/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-11-15 14:11:21
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-15 14:16:52
 */

import request from '@/utils/request'
/**
 * 根据报表id获取报表数据
 * @param {String} reportId 报表id
 * @param {String} meta 是否
 * @returns 文件信息
 */
export function getItemList({ id }) {
  return request({
    url: `/bill/billDetail/getItemList/${id}`,
    method: 'get'
  })
}

/**
 * @name:票据详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/bill/billDetail/load/${id}`,
    method: 'get'
  })
}

export function getDetail({ id }) {
  return request({
    url: `/bill/billDetail/loadDetail?detailId=${id}`,
    method: 'get'
  })
}