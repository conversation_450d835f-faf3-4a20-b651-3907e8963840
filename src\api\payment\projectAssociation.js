import request from '@/utils/request'

//查询履约数据
export function selectRecordPerformanceToFinance (data) {
  return request({
    url: '/project/projectRecordPerformance/selectRecordPerformanceToFinance',
    data,
    method: 'post'
  })
}

// 工程履约关联-列表
export function pagePerformance (data) {
  return request({
    url: '/payment/manager/payOrderPayee/pagePerformance',
    data,
    method: 'post'
  })
}

// 工程履约关联-关联履约
export function relationPerformance (data) {
  return request({
    url: '/payment/manager/payOrderPayee/relationPerformance',
    data,
    method: 'post'
  })
}

// 工程履约关联-取消关联
export function cancelRelationPerformance (data) {
  return request({
    url: '/payment/manager/payOrderPayee/cancelRelationPerformance',
    data,
    method: 'post'
  })
}

export function viewPerformance (data) {
  return request({
    url: '/payment/manager/payOrderPayee/viewPerformance',
    data,
    method: 'post'
  })
}

export function viewPerformanceByReceiverId (data) {
  return request({
    url: '/payment/manager/payOrderPayee/viewPerformanceByReceiverId',
    data,
    method: 'post'
  })
}
