<template>
  <div>
    <public-drawer
      :title="`${dialogType}预算项目调账`"
      :visible.sync="drawerVis"
      width="1350px"
      top="5vh"
      append-to-body
      custom-class="voucher"
      :close-on-click-modal="false"
      :show-close="false"
      @close="handleClose"
    >
      <el-form
        ref="_formRef"
        :model="form"
        label-width="100px"
        :rules="formRules"
        :disabled="dialogType === '查看'"
      >
        <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
          <el-col :span="12">
            <el-form-item prop="adjustDate" label="调账日期">
              <el-date-picker
                v-model="form.adjustDate"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                @change="handleAdjustDate"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="updateName" label="调账人">
              <gever-input v-model="form.updateName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="srcItemName" label="调账项目">
              <gever-input
                v-model="form.srcItemName"
                :clearable="false"
                disabled
              >
                <el-button slot="append" @click="handleProject(0)">
                  选择
                </el-button>
              </gever-input>
              <div v-if="selectProject.show" class="font12">
                已申请金额：{{
                  selectProject.srcItem.applyAmount || 0 | formatMoney
                }}，已执行金额：
                {{ selectProject.srcItem.executeAmount || 0 | formatMoney }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="tarItemName" label="目标项目">
              <gever-input
                v-model="form.tarItemName"
                :clearable="false"
                disabled
              >
                <el-button slot="append" @click="handleProject(1)">
                  选择
                </el-button>
              </gever-input>
              <div v-if="selectProject.show" class="font12">
                已申请金额：{{
                  selectProject.tarItem.applyAmount || 0 | formatMoney
                }}，已执行金额：
                {{ selectProject.tarItem.executeAmount || 0 | formatMoney }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="srcItemNameText" label="调账科目">
              <gever-input
                v-model="form.srcItemNameText"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="tarItemNameText" label="目标科目">
              <gever-input
                v-model="form.tarItemNameText"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="adjustAmount" label="调账金额">
              <gever-input-number
                v-model="form.adjustAmount"
                :controls="false"
                :min="0"
                :max="selectProject.srcItem.executeAmount || 9999999999.99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="adjustRemark" label="调账说明">
              <gever-input
                v-model="form.adjustRemark"
                type="textarea"
                :rows="3"
                :maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="footer-btn">
          <el-button @click="handleClose">{{ $t('关闭') }}</el-button>
          <el-button
            v-if="dialogType !== '查看'"
            v-hasPermi="
              'financial.cashier.bankReconciliation.cashierReconciliation.save'
            "
            type="primary"
            :loading="btnLoading"
            @click="handleSubmit"
          >
            {{ $t('保存') }}
          </el-button>
        </div>
      </template>
    </public-drawer>
    <chooseBudgetProject
      v-if="budgetVisible"
      :visible.sync="budgetVisible"
      :cur-row="form"
      :is-target="isTarget"
      :select-project="selectProject"
      @saveProject="saveProject"
    />
  </div>
</template>

<script>
import { save, load } from '@/api/payment/budgetReconciliation'
import chooseBudgetProject from './chooseBudgetProject.vue'
export default {
  components: { chooseBudgetProject },
  props: {
    dialogType: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    curRow: {
      type: Object,
      default: () => ({})
    },
    optionsMap: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      drawerVis: this.visible,
      formRules: {
        adjustDate: [
          {
            required: true,
            message: '请选择调账日期',
            trigger: ['blur', 'change']
          }
        ],
        srcItemName: [
          {
            required: true,
            message: '请选择调账项目',
            trigger: ['blur', 'change']
          }
        ],
        tarItemName: [
          {
            required: true,
            message: '请选择目标项目',
            trigger: ['blur', 'change']
          }
        ],
        adjustAmount: [
          {
            required: true,
            message: '请输入调账金额',
            trigger: ['blur', 'change']
          },
          { validator: this.validateNumber, trigger: ['blur', 'change'] }
        ]
      },
      form: {
        id: ''
      },
      loading: false,
      budgetVisible: false,
      btnLoading: false,
      isTarget: 0,
      settlementTypeOptions: [],
      initData: {},
      selectProject: {
        srcItem: {},
        tarItem: {},
        show: false
      }
    }
  },
  computed: {
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  mounted() {
    if (this.dialogType === '新增') {
      this.selectProject.show = true
    }
    if (this.dialogType !== '新增') {
      this.getLoad()
      return
    }
    this.form = {
      ...this.curRow,
      orgId: this.paymentLogin.id,
      updateName: this.$store.getters.name
    }
  },
  methods: {
    validateNumber(rule, value, callback) {
      if (parseFloat(value) <= 0) {
        callback(new Error('调账金额必须大于0'))
      }
      callback()
    },
    handleAdjustDate() {
      this.$set(this.form, 'tarItemName', '')
      this.$set(this.form, 'tarItemId', '')
      this.$set(this.form, 'srcItemName', '')
      this.$set(this.form, 'srcItemId', '')
      this.selectProject = {
        srcItem: {},
        tarItem: {}
      }
    },
    saveProject(item) {
      this.selectProject.show = true
      if (this.isTarget) {
        this.selectProject.tarItem = item
        this.$set(this.form, 'tarItemName', item.projectName)
        this.$set(this.form, 'tarItemId', item.id)
        this.form.tarAccountingItemId = item.accountingItemId
        this.form.tarAccountingItemCode = item.accountingItemCode
        this.form.tarAccountingItemType = item.accountingItemType
        this.form.tarItemNameText = item.accountingItemName
        return
      }
      this.selectProject.srcItem = item
      this.$set(this.form, 'srcItemName', item.projectName)
      this.$set(this.form, 'srcItemId', item.id)
      this.form.srcAccountingItemId = item.accountingItemId
      this.form.srcAccountingItemCode = item.accountingItemCode
      this.form.srcAccountingItemType = item.accountingItemType
      this.form.srcItemNameText = item.accountingItemName
    },
    handleProject(isTarget) {
      if (!this.form.adjustDate) {
        return this.$message.warning('请先设置调账日期')
      }
      this.isTarget = isTarget
      this.budgetVisible = true
    },
    getLoad() {
      this.loading = true
      load(this.curRow.id)
        .then((res) => {
          this.form = {
            ...res
            /*,
            srcItemName: res.srcAccountingItemCode + res.srcItemName,
            tarItemName: res.tarAccountingItemCode + res.tarItemName*/
          }
          this.selectProject = {
            srcItem: {
              id: res.srcItemId
            },
            tarItem: {
              id: res.tarItemId
            }
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleClose() {
      this.form = {
        id: ''
      }
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs['_formRef'].validate((valid) => {
        if (!valid) return
        this.btnLoading = true
        save(this.form)
          .then((res) => {
            if (res.returnCode !== '0') return
            this.$message.success(res.message)
            this.handleClose()
            this.$emit('success')
          })
          .finally(() => {
            this.btnLoading = false
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-input-number,
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}
.footer-btn {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}
</style>
