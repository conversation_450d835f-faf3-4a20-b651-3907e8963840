<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-06-03 09:57:34
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-03 14:25:24
-->
<template>
  <div>
    <!-- 外部 Tooltip 手动控制 -->
    <el-tooltip
      :content="valueTitle"
      placement="top"
      :disabled="valueTitle.length == 1"
      :manual="true"
      :value="showExternalTooltip"
      @mouseenter.native="handleExternalTooltipEnter"
      @mouseleave.native="handleExternalTooltipLeave"
    >
      <el-select
        v-model="valueTitle"
        :clearable="clearable"
        popper-class="tree-select-popper"
        :placeholder="placeholder"
        @clear="clearHandle"
        @visible-change="handleSelectVisibleChange"
      >
        <el-option :value="valueTitle" :label="valueTitle">
          <div class="tree-scroll-container">
            <div
              class="tree-inner-container"
              @mouseenter="handleTreeAreaEnter"
              @mouseleave="handleTreeAreaLeave"
            >
              <el-tree
                id="tree-option"
                ref="selectTree"
                :accordion="accordion"
                :data="options"
                :props="props"
                :node-key="props.value"
                :default-expanded-keys="defaultExpandedKey"
                @node-click="handleNodeClick"
              >
                <template slot-scope="{ node }">
                  <span v-if="node.isLeaf" class="el-icon-document">{{ node.label }}</span>
                  <span v-else class="el-icon-folder">{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-option>
      </el-select>
    </el-tooltip>
  </div>
</template>

<script>
import { getAccountingItemList } from '@/api/specialFund/receipt.js'
export default {
  name: 'BookTypeTreeSelect',
  props: {
    /* 配置项 */
    props: {
      type: Object,
      default: () => {
        return {
          value: 'id', // ID字段名
          label: 'text', // 显示名称
          children: 'children', // 子级字段名
          isLeaf: 'isLeaf'
        }
      }
    },
    /* 可清空选项 */
    clearable: {
      type: Boolean,
      default: () => { return true }
    },
    /* 自动收起 */
    accordion: {
      type: Boolean,
      default: () => { return false }
    },
    placeholder: {
      type: String,
      default: () => { return '' }
    },
    orgId: {
      type: String,
      default: () => { return '' }
    },
    year: {
      type: String,
      default: () => { return '' }
    }
  },
  data () {
    return {
      valueId: '', // 初始值
      valueTitle: '',
      defaultExpandedKey: [],
      showOuterTooltip: false,
      code: this.areaCode,
      options: [],
      showExternalTooltip: false, // 控制外部 tooltip 显示状态
      isMouseInTreeArea: false // 是否鼠标在树区域内
    }
  },
  watch: {
  },
  mounted () {
    this.valueTitle = ''
    console.log('valueTitle', this.valueTitle)

    this.getBaseItems()
    this.initHandle()
  },
  methods: {
    // 鼠标离开树区域
    handleTreeAreaLeave () {
      this.isMouseInTreeArea = false
      this.showExternalTooltip = !!this.valueTitle
    },

    // 鼠标进入 select 输入框区域
    handleExternalTooltipEnter () {
      if (!this.isMouseInTreeArea) {
        this.showExternalTooltip = !!this.valueTitle
      }
    },

    // 鼠标离开 select 输入框区域
    handleExternalTooltipLeave () {
      this.showExternalTooltip = false
    },
    // 鼠标进入树区域
    handleTreeAreaEnter () {
      this.isMouseInTreeArea = true
      this.showExternalTooltip = false // 进入树区域不显示外部 tooltip
    },
    // 下拉菜单打开/关闭时触发
    handleSelectVisibleChange (open) {
      if (!open) {
        // 下拉关闭时判断是否要显示外部 tooltip
        this.showExternalTooltip = this.isMouseInTreeArea
      }
    },
    // 初始化值
    initHandle () {
      // if (this.valueId) {
      //   this.valueTitle = this.$refs.selectTree.getNode(this.valueId).data[this.props.label] // 初始化显示
      //   this.$refs.selectTree.setCurrentKey(this.valueId) // 设置默认选中
      //   this.defaultExpandedKey = [this.valueId] // 设置默认展开
      // }
      this.$nextTick(() => {
        const scrollWrap = document.querySelectorAll('.el-scrollbar .el-select-dropdown__wrap')[0]
        const scrollBar = document.querySelectorAll('.el-scrollbar .el-scrollbar__bar')
        scrollWrap.style.cssText = 'margin: 0px; max-height: 400px; overflow: auto;'
        scrollBar.forEach(ele => ele.style.width = 0)
      })
    },
    // 切换选项
    handleNodeClick (node) {
      if (node.isLeaf) {
        console.log('11111', node)
        this.valueTitle = node[this.props.label]
        this.valueId = node[this.props.value]
        console.log('22222', this.valueId, this.valueTitle)
        this.$emit('getValue', this.valueId, node.itemCode, node.itemTitle)
        this.$emit('input', this.valueId)
        this.defaultExpandedKey = []
      }
    },
    // 清除选中
    clearHandle () {
      console.log('22222', this.valueId, this.valueTitle)
      this.valueTitle = ''
      this.valueId = null
      this.defaultExpandedKey = []
      this.clearSelected()
      this.$emit('getValue', null)
      this.$emit('input', null)
    },
    /* 清空选中样式 */
    clearSelected () {
      const allNode = document.querySelectorAll('#tree-option .el-tree-node')
      allNode.forEach((element) => element.classList.remove('is-current'))
    },
    getBaseItems () {
      const params = {
        orgId: this.orgId,
        year: this.year
      }
      getAccountingItemList(params).then(res => {
        if (res.returnCode == 0) {
          this.itemTypes = [
            ...new Map(
              res.data.map(item => [item.accountingItemTypeId, item])
            ).values()
          ]
          this.itemTypeId = this.itemTypes[0].accountingItemTypeId
          this.data = this.convertToJsonTree(res.data, '0')
          // 保存tree数据
          // const dataTree = this.data.filter(item => item.accountingItemTypeId == this.itemTypeId)
          this.options = this.data
          // this.options = [
          //   {
          //     id: 0,
          //     itemName: '会计科目',
          //     children: dataTree
          //   }
          // ]
          this.initHandle()
        }
      })
    },
    // 处理会计科目数据
    convertToJsonTree (list, parent) {
      const tree = []
      list.forEach(item => {
        if (item.parentId == parent) {
          item.text = item.itemCode + '-' + item.itemTitle
          const children = this.convertToJsonTree(list, item.id) // 递归查找子节点
          if (children.length) {
            item.children = children
            item.isLeaf = false
          } else {
            item.isLeaf = true
          }
          tree.push(item)
        }
      })
      return tree
    }
  }
}
</script>

<style lang="scss" scoped>
/* 控制选择器宽度 */
::v-deep .el-select {
  width: 100%;
}

/* 下拉菜单宽度控制 */
::v-deep .tree-select-popper {
  width: var(--el-select-width) !important;
}

/* 选项容器样式 */
::v-deep .el-select-dropdown__item {
  padding: 0 !important;
  height: auto !important;
}

/* 滚动容器样式 */
.tree-scroll-container {
  max-height: 300px;
  width: var(--el-select-width);
  overflow-x: auto;
  overflow-y: auto;
}

/* 内部滚动区域 */
.tree-inner-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: auto;
  //max-width: 500px;
}

/* 树组件样式 */
::v-deep .el-tree {
  min-width: 100%;
  width: max-content !important;
}

/* 树节点样式 */
::v-deep .el-tree-node__content {
  white-space: nowrap;
  font-weight: normal !important;
}

/* 移除默认滚动行为 */
::v-deep .el-select-dropdown__wrap {
  max-height: none !important;
}

::v-deep .el-scrollbar__wrap {
  overflow: hidden !important;
}

/* 确保树节点文本使用正常字重 */
::v-deep .el-tree-node__label {
  font-weight: normal;
}

/* 选中状态下保持正常字重 */
::v-deep .el-tree-node.is-current > .el-tree-node__content {
  font-weight: normal !important;
}

.el-tooltip__popper {
  z-index: 9999 !important;
}
/* 如果需要进一步调整 el-tree 的样式 */
::v-deep .el-tree-node__content {
  padding-right: 10px; /* 增加一些内边距避免内容过于贴近滚动条 */
}

/* 确保 tree 的节点文本不换行，并支持横向滚动 */
::v-deep .tree-select-popper .el-tree-node__content {
  white-space: nowrap;
}

/* 设置树的基础样式 */
::v-deep .el-tree {
  width: fit-content;
  min-width: 100%;
}

</style>
