<template>
  <div class="ReceiverInvoiceList">
    <el-form :inline="true" size="small" style="margin-bottom: 12px" :disabled="loadType == 'view'">
      <el-form-item label="发票号码">
        <el-input v-model="searchForm.invoiceNo" placeholder="请输入发票号码" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button type="primary" @click="openSelectInvoice">添加</el-button>
        <el-button @click="deleteSelected">删除</el-button>
<!--        <el-button @click="handleReset">重置</el-button>-->
        <span style="color: red; margin-left: 12px; font-size: 14px; vertical-align: middle;">
          收款方金额：{{ Number(rowData.amountPayable || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}元
        </span>
      </el-form-item>
    </el-form>

    <gever-table
      ref="_geverTableRef"
      v-loading="tableLoading"
      :columns="tableColumns"
      :data="filteredTableList"
      :pagination="false"
      :height="500"
      row-key="id"
      show-summary
      :summary-method="getTableSummary"
      @selection-change="handleSelectionChange"
    >
      <template #invoiceType="{ row }">
        {{ invoiceTypeText(row.invoiceType) }}
      </template>
      <template #payAmount="{ row }">
        <el-input
          v-model="row.payAmount"
          size="small"
          input-style="text-align: right;"
          :disabled="loadType == 'view'"
          @input="handlePayAmountInput(row)"
          @blur="handlePayAmountBlur(row)"
        />
      </template>
      <template #operation="{ row }">
        <el-button
                  type="text"
                  @click="handleViewInvoice(row)"
                >
                  {{ $t('查看') }}
        </el-button>
      </template>
    </gever-table>
    <!-- 添加选择发票弹窗 -->
    <el-dialog
      title="选择发票"
      :visible.sync="selectInvoiceVisible"
      width="70%"
      :before-close="handleSelectInvoiceClose"
    >
      <select-invoice-list
        ref="selectInvoiceRef"
        :row-data="rowData"
        :org-code="orgCode"
        :load-type="loadType"
        :is-refund="isRefund"
        :invoice-ids="invoiceIds"
        @confirm="handleSelectInvoiceConfirm"
        @handleInvoiceConfirm="handleInvoiceConfirm"
        @handleInvoiceConfirmAndClose="handleInvoiceConfirmAndClose"
      />
<!--      <span slot="footer" class="dialog-footer">
        <el-button @click="handleSelectInvoiceClose">取 消</el-button>
        <el-button type="primary" @click="handleSelectInvoiceConfirm">确 定</el-button>
      </span>-->
    </el-dialog>

    <!-- 发票详情页 -->
    <InvoiceDetailView
      v-if="digitalDetailsVisible"
      :dialog-visible.sync="digitalDetailsVisible"
      :row-data="nowRowDatac"
      @updateList="getList"
    >
    </InvoiceDetailView>
  </div>
</template>

<script>
import { getReceiverInvoiceList } from '@/api/payment/approval.js'
import SelectInvoiceList from './selectInvoiceList.vue'
import InvoiceDetailView from '@/views/financial/invoice/invoiceDetailXw/components/digitalDetails.vue'

export default {
  name: 'ReceiverInvoiceList',
  components: {
    SelectInvoiceList,
    InvoiceDetailView
  },
  props: {
    rowData: {
      type: Object,
      default: () => ({})
    },
    orgCode: {
      type: String,
      default: ''
    },
    loadType: {
      type: String,
      default: 'add'
    },
    isRefund: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      tableLoading: false,
      tableList: [],
      filteredTableList: [],
      tableColumns: [
        { type: 'selection', align: 'center', width: '40', fixed: 'left' },
        { type: 'index', label: '序号', width: '50', fixed: 'left' },
        { label: '发票号码', prop: 'invoiceNo', minWidth: '150', showOverflowTooltip: true },
        { label: '开票日期', prop: 'drawDate', minWidth: '120', showOverflowTooltip: true },
        { label: '价税合计金额', prop: 'totalAmount', filter: 'money', minWidth: '120', align: 'right', showOverflowTooltip: true },
        { label: '未使用金额', prop: 'unRelatedAmount', minWidth: '120', align: 'right', showOverflowTooltip: true },
        { label: '本次使用金额*', prop: 'payAmount', minWidth: '150', align: 'right', showOverflowTooltip: true, slotName: 'payAmount',
          renderHeader(h) {
            return h('span', [
              '本次使用金额 ',
              h('span', { style: 'color: red;' }, '*')
            ])
          } },
        { label: '销售方名称', prop: 'sellerName', minWidth: '200', showOverflowTooltip: true },
        { label: '摘要', prop: 'summary', minWidth: '120', showOverflowTooltip: true },
        { label: '操作', slotName: 'operation', width: '80', fixed: 'right' }
      ],
      selectArr: [],
      invoiceIds: [],
      searchForm: {
        invoiceNo: '',
        minAmount: '',
        maxAmount: '',
        startDate: '',
        endDate: ''
      },
      startDatePickerOptions: {
        disabledDate: (date) => {
          if (this.searchForm.endDate) {
            // 允许等于结束日期
            return date.getTime() > new Date(this.searchForm.endDate).getTime()
          }
          return false
        }
      },
      endDatePickerOptions: {
        disabledDate: (date) => {
          if (this.searchForm.startDate) {
            // 允许等于开始日期
            return date.getTime() < new Date(this.searchForm.startDate).setHours(0,0,0,0)
          }
          return false
        }
      },
      selectInvoiceVisible: false, // 添加选择发票弹窗控制
      digitalDetailsVisible: false,
      nowRowDatac: {}
    }
  },
  created() {
    // 修改：初始化时不自动查询数据
    this.getList()
  },
  methods: {
    async getList() {
      if (!this.rowData.invoiceList
        || this.rowData.invoiceList.length === 0) {
        return
      }
      this.tableLoading = true
      try {
        const params = {
          orgCode: this.orgCode,
          invoiceNo: this.searchForm.invoiceNo
        }
        if (this.rowData.invoiceList) {
          const ids = []
          this.rowData.invoiceList.forEach(row => {
            ids.push(row.invoiceId)
          })
          params.ids = ids
        }
        params.view = 1
        const { data } = await getReceiverInvoiceList(params)
        this.tableList = data.rows
        this.filteredTableList = this.tableList
        this.initTable()
      } finally {
        this.tableLoading = false
      }
    },
    search() {
      if (!this.searchForm.invoiceNo) {
        this.filteredTableList = this.tableList
        return
      }
      // 前端根据条件过滤列表数据，不调后台
      this.filteredTableList = this.tableList.filter(item => item.invoiceNo.includes(this.searchForm.invoiceNo))
    },
    handleReset() {
      this.searchForm.invoiceNo = ''
      this.searchForm.totalAmount = ''
      this.getList()
    },
    invoiceTypeText(val) {
      if (val === 0 || val === '0') return '销项发票'
      if (val === 1 || val === '1') return '进项发票'
      return ''
    },
    handleSelectionChange(val) {
      // 自动补全本次使用金额
/*      val.forEach(row => {
        if (!row.payAmount || Number(row.payAmount) <= 0) {
          this.$set(row, 'payAmount', row.unRelatedAmount ? row.unRelatedAmount : '')
        }
      })*/
      // 清空未勾选行的本次使用金额
     /* this.tableList.forEach(row => {
        if (!val.includes(row)) {
          this.$set(row, 'payAmount', '')
        }
      })*/
      this.selectArr = val
    },
    handlePayAmountInput(row) {
      // 实时校验，防止输入超限
      let val = String(row.payAmount || '')
      val = val.replace(/\.(?=.*\.)/g, '')
     // val = val.replace(/(\.\d{2}).+/, '$1')
      if (this.loadType === 'refund' || this.isRefund == 1) {
        if (Number(val) > 0) {
          this.$message.warning('退款时只能输入负数金额')
          this.$set(row, 'payAmount', '')
          return
        }
        if (row.sourceAmount && Number(val) < Number(row.sourceAmount)) {
          console.log('row.sourceAmount:', row.sourceAmount)
          this.$message.warning('退款金额不能大于原支出金额')
          this.$set(row, 'payAmount', row.sourceAmount)
          return
        }
      } else {
        if (Number(val) > row.unRelatedAmount) {
          this.$message.warning('本次使用金额不能大于未使用金额')
          this.$set(row, 'payAmount', Number(row.unRelatedAmount).toFixed(2))
        }
      }
    },
    handlePayAmountBlur(row) {
      // 失焦时做最终修正
      let val = String(row.payAmount || '')
      if (!val) {
        this.$set(row, 'payAmount', '')
        return
      }
      this.$set(row, 'payAmount', Number(val).toFixed(2))
/*      if (this.$refs._geverTableRef) {
        this.$refs._geverTableRef.toggleRowSelection(row, true)
      }*/
    },
    getTableSummary({ columns, data }) {
      const sums = []
      columns.forEach((column, index) => {
        if (index === 2) {
          sums[index] = '合计'
          return
        }
        if (column.property === 'payAmount') {
          if (!data || data.length == 0) {
            sums[index] = 0
          } else {
            const total = data.reduce((sum, row) => {
              const value = parseFloat(row.payAmount)
              return sum + (isNaN(value) ? 0 : value)
            }, 0)
            sums[index] = total.toFixed(2)
          }
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    initTable() {
      this.$nextTick(() => {
        this.$refs._geverTableRef.clearSelection()
        // 已关联的数据选中并填写支出金额
        for (let i = 0; i < this.tableList.length; i++) {
          const unRelatedAmount = ((this.tableList[i].totalAmount * 100 - (this.tableList[i].relatedAmount || 0) * 100) / 100)
          this.$set(this.tableList[i], 'unRelatedAmount', unRelatedAmount.toFixed(2))
          if (this.rowData.invoiceList) {
            this.rowData.invoiceList.forEach(x => {
              if (x.invoiceId == this.tableList[i].id) {
              //  this.$refs._geverTableRef.toggleRowSelection(this.tableList[i], true)
                this.$set(this.tableList[i], 'payAmount', Number(x.payAmount).toFixed(2))
                if (this.loadType == 'edit') {
                  this.$set(this.tableList[i], 'unRelatedAmount', (unRelatedAmount + Number(x.payAmount)).toFixed(2))
                }
                this.$set(this.tableList[i], 'sourceAmount', Number(x.sourceAmount ? -x.sourceAmount : x.payAmount).toFixed(2))
              }
            })
          }
        }
/*        if (!this.rowData.invoiceList) {
          return
        }
        // 把已关联的数据排到前面
        const fundIds = []
        this.rowData.invoiceList.forEach(x => {
          fundIds.push(x.invoiceId)
        })
        this.tableList.sort((a, b) => {
          const aIn = fundIds.includes(a.id)
          const bIn = fundIds.includes(b.id)
          if (aIn && !bIn) return -1
          if (!aIn && bIn) return 1
          return 0
        })*/
      })
    },
    // 打开选择发票弹窗
    openSelectInvoice() {
      // 获取已选择的发票
      this.invoiceIds = this.tableList && this.tableList.length > 0
        ? this.tableList.map(x => x.id) : []
      this.selectInvoiceVisible = true
      // 在打开弹窗时刷新数据
      this.$nextTick(() => {
        if (this.$refs.selectInvoiceRef) {
          this.$refs.selectInvoiceRef.getList()
        }
      })
    },
    // 关闭选择发票弹窗
    handleSelectInvoiceClose() {
      // this.handleSelectInvoiceConfirm()
      this.selectInvoiceVisible = false
    },
    // 确认选择发票
    handleSelectInvoiceConfirm() {
      const selectInvoiceRef = this.$refs.selectInvoiceRef
      if (selectInvoiceRef && selectInvoiceRef.selectArr && selectInvoiceRef.selectArr.length > 0) {
        // 将选中的发票添加到当前列表中
        const newInvoices = selectInvoiceRef.selectArr.map(invoice => {
          // 检查是否已经存在
          const exists = this.tableList.some(item => item.id === invoice.id)
          if (!exists) {
            return {
              ...invoice,
              payAmount: invoice.payAmount || invoice.unRelatedAmount
            }
          }
          return null
        }).filter(item => item !== null)
        // 添加新发票到列表
        this.tableList.push(...newInvoices)
      }
    },
    // 处理子组件的handleInvoiceConfirm事件(确认但不关闭)
    handleInvoiceConfirm(selectArr) {
      if (selectArr && selectArr.length > 0) {
        // 将选中的发票添加到当前列表中
        const newInvoices = selectArr.map(invoice => {
          // 检查是否已经存在
          const exists = this.tableList.some(item => item.id === invoice.id)
          if (!exists) {
            return {
              ...invoice,
              payAmount: invoice.payAmount || invoice.unRelatedAmount
            }
          }
          return null
        }).filter(item => item !== null)
        // 添加新发票到列表
        this.tableList.push(...newInvoices)
        this.searchForm.invoiceNo = ''
        this.filteredTableList = this.tableList
      }
    },

    // 处理子组件的handleInvoiceConfirmAndClose事件(确认并关闭)
    handleInvoiceConfirmAndClose(selectArr) {
      this.handleInvoiceConfirm(selectArr)
      this.selectInvoiceVisible = false
    },
    // 删除选中项
    deleteSelected() {
      if (this.selectArr && this.selectArr.length > 0) {
        this.$confirm('确定要删除选中的发票吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const idsToDelete = this.selectArr.map(item => item.id)
          this.tableList = this.tableList.filter(item => !idsToDelete.includes(item.id))
          this.selectArr = []
          this.filteredTableList = this.tableList.filter(item => !idsToDelete.includes(item.id))
          this.$message.success('删除成功')
        })
      } else {
        this.$message.warning('请先选择要删除的发票')
      }
    },
    handleViewInvoice(row) {
      this.nowRowDatac = row
      this.digitalDetailsVisible = true
    }
  }
}
</script>
