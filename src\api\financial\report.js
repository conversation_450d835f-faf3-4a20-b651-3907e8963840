/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-11-12 11:37:35
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-02-19 14:25:07
 */

import request from '@/utils/request'
import { method } from 'lodash'

/**
 * 根据模块获取模块报表
 * @param {String} reportId 报表id
 * @param {String} meta 是否
 * @returns 文件信息
 */
export function reportList(data) {
  return request({
    url: '/financial/report/reportList',
    method: 'post',
    data
  })
}
/**
 * 根据报表id获取报表数据
 * @param {String} reportId 报表id
 * @param {String} meta 是否
 * @returns 文件信息
 */
export function report({ params, data }) {
  return request({
    url: '/financial/report/get',
    method: 'post',
    params,
    data
  })
}

/**
 * 创建、编辑账套时获取初始化信息
 * @param {*} data { booksId: '', noPager:  }
 * @returns
 */
export function getMeta(data) {
  return request({
    url: '/financial/report/getMeta',
    method: 'post',
    params: data
  })
}

/**
 * 获取导出的下载地址
 * @param {*} data { booksId: '', noPager:  }
 * @returns
 */
export function saveExcelTempFile(data) {
  return request({
    url: '/financial/report/saveExcelTempFile',
    method: 'post',
    data,
    payload: true,
    timeout: 180000
  })
}

/**
 * 获取导出的下载地址（使用easyExcel导出）
 * @param {*} data { booksId: '', noPager:  }
 * @returns
 */
 export function saveExcelTempFileWithEasyExcel(data) {
  return request({
    url: '/financialExcel/saveExcelTempFile',
    method: 'post',
    data,
    payload: true,
    timeout: 180000
  })
}

/**
 * 保存打印设置
 */
export function savePrintSettings(data) {
  return request({
    url: '/financial/report/printSetting/save',
    method: 'post',
    data
  })
}

/**
 * 加载打印设置
 * @param {*} params 
 * @returns 
 */
export function loadPrintSetting(params){
  return request({
    url: '/financial/report/printSetting/loadByBooksId',
    method: 'get',
    params
  })
}

/**
 * 导出目录数据为Excel
 * @param {*} data { booksId: '', directoryData: [] }
 * @returns
 */
export function saveDirectoryExcel(data) {
  return request({
    url: '/financialExcel/saveDirectoryExcel',
    method: 'post',
    data,
    payload: true,
    timeout: 180000
  })
}