/*
 * @Descripttion:出纳月结
 * @version:
 * @Author: 未知
 * @Date: 2021-11-08 10:56:09
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-03-19 17:47:37
 */
import request from '@/utils/request'

export const baseUrl = '/financial/cashier/cashierVoucher'
/**
 * @name:获取账套是否已经月结的标志
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getCashierFlag(params) {
  return request({
    url: '/financial/cashier/cashierVoucher/getCashierFlag',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:页面
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/page',
    method: 'post',
    data,
    withCredentials: true
  })
}

export function deleteVoucher({ params, data }) {
  return request({
    url: '/financial/cashier/cashierVoucher/deleteVoucher',
    method: 'post',
    params,
    data,
    withCredentials: true
  })
}

/**
 * @name:获取新增的额外参数
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function init(params, voucherType) {
  return request({
    url: `/financial/cashier/cashierVoucher/add/init/${voucherType}`,
    method: 'get',
    params,
    withCredentials: true
  })
}
/**
 * @name:获取编辑的额外参数
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function initEdit(params, voucherType, id) {
  return request({
    url: `/financial/cashier/cashierVoucher/edit/init/${voucherType}/${id}`,
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:获取查看的额外参数
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function initView(params, voucherType, id) {
  return request({
    url: `/financial/cashier/cashierVoucher/view/init/${voucherType}/${id}`,
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:加载详情
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/cashier/cashierVoucher/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:检测余额是否正确
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function checkBalance(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/checkBalance',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存表单数据
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveCashierVoucher({ params, data }) {
  return request({
    url: '/financial/cashier/cashierVoucher/saveCashierVoucher',
    method: 'post',
    params,
    data,
    withCredentials: true
  })
}

/**
 * @name:获取搜索建议
 * @param {String} booksId 登陆的账套id
 * @param {String} type 类型
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function combotree(params) {
  return request({
    url: '/financial/cashier/cashierCommonInfo/combotree',
    method: 'post',
    params,
    isRepeat: true
  })
}

/**
 * @name:删除搜索建议
 * @param {String} infoId info的id
 * @param {String} type 类型
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteInfo(data) {
  return request({
    url: '/financial/cashier/cashierCommonInfo/deleteInfo',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存新的搜索建议
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveInfo(data) {
  return request({
    url: '/financial/cashier/cashierCommonInfo/saveInfo',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:检测出纳记账是否产生断号
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function check({ data, params }) {
  return request({
    url: '/financial/cashier/cashierVoucher/check',
    method: 'post',
    data,
    params,
    withCredentials: true
  })
}

/**
 * @name:获取手动调整断号的初始数据
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function manualCheck(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/manualCheck',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取手动分析整理断号的结果数据
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function manualAnalysis({ data, params }) {
  return request({
    url: '/financial/cashier/cashierVoucher/manualAnalysis',
    method: 'post',
    data,
    params,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:获取自动分析整理断号的结果数据
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function analysis({ data, params }) {
  return request({
    url: '/financial/cashier/cashierVoucher/analysis',
    method: 'post',
    data,
    params,
    withCredentials: true
  })
}

/**
 * @name:保存整理结果
 * @param {String} booksId 账套的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function reorganize({ data, params }) {
  return request({
    url: '/financial/cashier/cashierVoucher/reorganize',
    method: 'post',
    data,
    params,
    payload: true,
    withCredentials: true
  })
}
/**
 * @name:获取票据库
 * @param {*} data
 * @param {*} params
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getProofWhitBill(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/getProofWhitBill',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:查询银行流水数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBankStatement(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/getBankStatement',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name: 查询支付申请单数据
 * @param {*} orgId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getPaymentApprovalApplication(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/getPaymentApprovalApplication',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:原始凭证数据保存
 * @param {*} data
 * @description:
          具体参数为     booksId  账套id
            period 期间
            batchId 批次id
            businessId  业务id（凭证主表id）
 * @return {*}
 * @test:
 * @msg:
 */
export function saveOriginalVoucher(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/saveOriginalVoucher',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:撤销凭证
 * @param {*} data
 * @description:参数 paymentId 支付单业务id
 * @return {*}
 * @test:
 * @msg:
 */
export function revocationVoucher(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/revocationVoucher',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:撤销凭证
 * @param {String} booksId
 * @param {String} accountingItemCode
 * @description:参数 paymentId 支付单业务id
 * @return {*}
 * @test:
 * @msg:
 */
export function getBudgetAmount(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/getBudgetAmount',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:查询银行对账中出纳单数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function bankReconciliationVoucher(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/bankReconciliationVoucher',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:查询银行对账中银行流水数据
 * @param {*} data
 * @description:参数是  机构id orgId
              银行卡号 accountNumber
           是否对账 relationFlag   空值为全部   0 是未对账  1是已对账
           银行流水id    bankStatementId     多个使用逗号分隔
 * @return {*}
 * @test:
 * @msg:
 */
export function bankReconciliationBankStatement(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/bankReconciliationBankStatement',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:手动对账
 * @param {*} data
 * @description:  参数： 凭证id voucherId
              银行流水id bankSerialNumber    多个使用逗号分隔
 * @return {*}
 * @test:
 * @msg:
 */
export function bankReconciliation(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/bankReconciliation',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:取消对账接口
 * @param {*} data
 * @description:    post请求 / financial / cashier / cashierVoucher /
  凭证id voucherId
              银行流水id bankSerialNumber
 * @return {*}
 * @test:
 * @msg:
 */
export function clearBankReconciliation(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/clearBankReconciliation',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:根据流水id
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function bankStatementByIds(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/bankStatementByIds',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:查看凭证
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getCashierVoucherList(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/getCashierVoucherList',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:查看票据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBillList(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/getBillList',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectProjectContractList(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/selectProjectContractList',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:查询是否符合审批设置
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectPaymentSettingsInfo(params) {
  return request({
    url: '/financial/cashier/cashierVoucher/selectPaymentSettingsInfo',
    method: 'get',
    params,
    withCredentials: true
  })
}
/**
 * @name:查询支付申请单数据
 * @param {*} orgId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getPaymentOrderList(data) {
  return request({
    url: '/financial/cashier/cashierVoucher/getPaymentOrderList',
    method: 'post',
    data,
    withCredentials: true
  })
}