import request from '@/utils/request'

/**
 * @name:列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/cashier/cashierBankBalanceAdjustment/refresh',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveX(params, data) {
  return request({
    url: '/financial/cashier/cashierBankBalanceAdjustment/saveX',
    method: 'post',
    data,
    params,
    payload: true
  })
}
/**
 * @name:生成
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function generate(data) {
  return request({
    url: '/financial/cashier/cashierBankBalanceAdjustment/generate',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteAdjustment(data) {
  return request({
    url: '/financial/cashier/cashierBankBalanceAdjustment/deleteAdjustment',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name: 审核
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function approve(data) {
  return request({
    url: '/financial/cashier/cashierBankBalanceAdjustment/approve',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:销审
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unApprove(data) {
  return request({
    url: '/financial/cashier/cashierBankBalanceAdjustment/unApprove',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:获取前置条件控制标识
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBankStatementFlag(params) {
  return request({
    url: '/financial/cashier/cashierBankBalanceAdjustment/getBalanceAdjustmentFlag',
    method: 'get',
    params,
  })
}