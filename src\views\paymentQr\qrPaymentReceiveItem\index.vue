<template>
  <div>
    <fold-box right-title="收款项目管理">
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('收款项目编号')" prop="channelName">
              <gever-input v-model="queryParams.receiveItemCode"  />
            </el-form-item>
            <el-form-item :label="$t('收款项目')" prop="receiveItemName">
              <gever-input v-model="queryParams.receiveItemName"  />
            </el-form-item>
            <el-form-item :label="$t('状态')" prop="status">
              <el-select v-model="queryParams.status" clearable  placeholder="请选择">
                <el-option
                  v-for="item in vaildOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button plain icon="el-icon-search" type="primary" round @click="handleSearch">
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <div style="float: right;">
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.qrPayment.receiveItem.save'"
                  type="primary"
                  round
                  icon="el-icon-plus"
                  @click="handleAdd"
                >
                  {{ $t('新增') }}
                </el-button>
              </el-form-item>
            </div>
          </el-form>
          <gever-table
		    :loading="tableLoading"
            ref="_geverTableRef"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
            style="clear: both;"
          >
            <template #status="{ row }">
              <el-switch
              v-hasPermi="'financial.qrPayment.receiveItem.save'"
              v-model="row.status"
              @change="handleVoidChange(row)"
              :disabled="userAreaLevel != 3"
              >
              </el-switch>
            </template>
            <template #operation="{ row }">
              <el-button v-hasPermi="'financial.qrPayment.receiveItem.view'" type="text" @click="handleView(row)">{{ $t('查看') }}</el-button>
              <el-button v-hasPermi="'financial.qrPayment.receiveItem.save'" type="text" :disabled="userAreaLevel != 3" @click="handleEdit(row)">{{ $t('编辑') }}</el-button>
              <el-button v-hasPermi="'financial.qrPayment.receiveItem.delete'" type="text" :disabled="userAreaLevel != 3" @click="handleRemove(row,true)">{{ $t('删除') }}</el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <public-drawer 
        :visible.sync="detailVisible" 
        :title="detailTitle" 
        :size="70" 
        :buttons="type == 'view' ? viewButtons : detailButtons" 
        @close="handleCloseDetail"
    >
      <detail
        v-if="detailVisible"
        :detail-visible.sync="detailVisible"
        ref="_detailContentRef"
        :type="type"
        :id="currentId"
        :userAreaCode="userAreaCode"
        :userAreaName="userAreaName"
        @refreshTable="getList"
      />
    </public-drawer>
  </div>
</template>

<script>
import { loadQrPaymentReceiveItemListApi, deleteQrPaymentReceiveItemApi, setVoidApi } from '@/api/paymentQr/qrPaymentReceiveItem.js'
import { getAreaApi } from '@/api/paymentQr/qrPaymentAccount.js'
import detail from './components/detail'
import {vaildOptions} from '../config'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')

export default {
  name: 'QrPaymentReceiveItem',
  components: {
    detail
  },
  data() {
    return {
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      userAreaName: '',
      userAreaCode: '',
      userAreaLevel: null,
      vaildOptions, 
      queryParams: {
        page: 1,
        rows: 20,
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50' },
          { type: 'index', label: '序号', width: '50' },
          { label: '地区', prop: 'areaName' , minWidth: '200' ,resizable: true},
          { label: '收款项目编号', prop: 'receiveItemCode' , minWidth: '150' ,resizable: true},
          { label: '收款项目', prop: 'receiveItemName' , minWidth: '200' ,resizable: true},
          { label: '状态', prop: 'status' , minWidth: '100' ,resizable: true},
          { label: '创建人', prop: 'createUsername' , minWidth: '150' ,resizable: true},
          { label: '创建日期', prop: 'createDate' , minWidth: '200' ,resizable: true},
          { label: '操作', slotName: 'operation', width: '130', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
	  currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ],
      viewButtons: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        }
      ]
    }
  },
  computed: {
    // ...mapState(['areaLogin']),
  },
  watch: {
    // areaLogin: {
    //   handler: function (val) {
    //     console.log("初始化", val);
    //     this.getList()
    //   },
    //   immediate: true,
    //   deep: true
    // }
  },
  created() {
    this.getList()
    this.getUserArea()
  },
  methods: {
    getList() {
      // console.log(this.areaLogin)
      // const areaCode = this.areaLogin.code
      // if (this.areaLogin.type == 'Area' && this.areaLogin.areaLevel <= 3 ) {
        this.tableLoading = true
        // this.queryParams.areaCode = areaCode
        loadQrPaymentReceiveItemListApi(this.queryParams).then(res => {
          res.data.rows.forEach(v=>{
            if(v.status){
              v.status = true
            }else{
              v.status = false
            }
          })
          this.table.tableList = res.data.rows
          this.table.total = res.data.total
        })
        .catch(err => {
          this.table.tableList = []
          this.table.total = 0
        })
        .finally(() => {
          this.tableLoading = false
        })
      // }else{
      //   // return this.$message.warning('请选择区级地区')
      // }
    },
    async getUserArea(){
      const { data: userArea = {} } = await getAreaApi()
      this.userAreaName = userArea.fname
      this.userAreaLevel = userArea.areaLevel
      this.userAreaCode = userArea.code
      console.log(this.userAreaLevel + " " + this.userAreaName)
    },
    handleSearch() {
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleAdd() {
      // if (this.areaLogin.type == 'Area' && this.areaLogin.areaLevel == 3){
      //   this.detailTitle = '新增收款项目'
      //   this.type = 'add'
      //   this.currentId = ''
      //   this.detailVisible = true
      // }else{
      //   return this.$message.warning('请选择区级地区')
      // }

      if (this.userAreaLevel == 3){
        this.detailTitle = '新增收款项目'
        this.type = 'add'
        this.currentId = ''
        this.detailVisible = true
      }else{
        return this.$message.warning('区级用户才能新增收款项目')
      }

      disabled=""
    },
    handleView(row) {
      this.detailTitle = '查看收款项目'
      this.type = 'view'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleEdit(row) {
      // if (this.areaLogin.type == 'Area' && this.areaLogin.areaLevel == 3){
      //   this.detailTitle = '编辑收款项目'
      //   this.type = 'edit'
      //   this.currentId = row.id
      //   this.detailVisible = true
      // }else{
      //   return this.$message.warning('请选择区级地区')
      // }
      this.detailTitle = '编辑收款项目'
      this.type = 'edit'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleRemove(row, batchFlag) {
      // if (!(this.areaLogin.type == 'Area' && this.areaLogin.areaLevel == 3)){
      //   return this.$message.warning('请选择区级地区')
      // }
      let ids
      if (batchFlag) {
        ids = [row.id]
      } else {
        if (this.tableIdSelection.length == 0) {
          return this.$message.warning(this.$t('请先选择要删除的数据！'))
        }
        ids = this.tableIdSelection
      }
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        deleteQrPaymentReceiveItemApi(ids).then(res => {
          if (res) {
            this.$message.success(res.message)
            this.getList()
          }
        })
      }).catch(() => {})
    },
    handleCloseDetail() {
      this.detailVisible = false
    },
    handleSave() {
      this.$refs._detailContentRef.save()
    },
    handleVoidChange(row) {
      let voidName = row.status ? "生效" : "失效"
      setVoidApi( row.id ).then(res => {
          if (res) {
            this.$message.success("设置" + voidName + "成功！")
            // this.getList()
          }else{
            this.getList()
          }
        })
    }
  }
}
</script>