import request from '@/utils/request'


export function copyFileByBusinessId(params) {
  return request({
    url: '/filesystem/fileInfo/entry/copyFileByBusinessId',
    params,
    method: 'get'
  })
}
export function saveImportReceiver(data) {
  return request({
    url: '/payment/approval/approvalReceiver/saveImportReceiver',
    data,
    method: 'post'
  })
}
export function getAccountingItem(data) {
  return request({
    url: '/payment/approval/approvalApplication/getAccountingItem',
    data,
    method: 'post'
  })
}
export function infoOpinionDelete(data) {
  return request({
    url: '/payment/paymentCommonInfoOpinion/delete',
    data,
    method: 'post'
  })
}
export function infoOpinionSave(data) {
  return request({
    url: '/payment/paymentCommonInfoOpinion/save',
    data,
    method: 'post'
  })
}
export function infoOpinionPage(data) {
  return request({
    url: '/payment/paymentCommonInfoOpinion/noPage',
    data,
    method: 'post'
  })
}
export function infoDetailsDelete(data) {
  return request({
    url: '/payment/paymentCommonInfoDetails/delete',
    data,
    method: 'post'
  })
}
export function infoDetailsSave(data) {
  return request({
    url: '/payment/paymentCommonInfoDetails/save',
    data,
    method: 'post'
  })
}
export function infoDetailsPage(data) {
  return request({
    url: '/payment/paymentCommonInfoDetails/noPage',
    data,
    method: 'post'
  })
}
export function infoFundsUseDelete(data) {
  return request({
    url: '/payment/paymentCommonInfoFundsUse/delete',
    data,
    method: 'post'
  })
}
export function infoFundsUseSave(data) {
  return request({
    url: '/payment/paymentCommonInfoFundsUse/save',
    data,
    method: 'post'
  })
}
export function infoFundsUsePage(data) {
  return request({
    url: '/payment/paymentCommonInfoFundsUse/noPage',
    data,
    method: 'post'
  })
}
export function infoStaffDelete(data) {
  return request({
    url: '/payment/paymentCommonInfoStaff/delete',
    data,
    method: 'post'
  })
}
export function infoStaffSave(data) {
  return request({
    url: '/payment/paymentCommonInfoStaff/save',
    data,
    method: 'post'
  })
}
export function infoStaffPage(data) {
  return request({
    url: '/payment/paymentCommonInfoStaff/noPage',
    data,
    method: 'post'
  })
}
export function loadProcessList() {
  return request({
    url: '/payment/approval/approvalSettings/processList?type=17',
    method: 'get'
  })
}
export function getBudgetSettingOneBy(data) {
  return request({
    url: '/payment/approval/approvalApplication/getBudgetSettingOneBy',
    data,
    method: 'post'
  })
}

export function loadApprocalSetting(data) {
  return request({
    url: '/payment/approval/approvalSettings/load/' + data,
    method: 'get'
  })
}

export function loadNode(data) {
  return request({
    url: '/payment/approval/approvalSettings/load_node',
    method: 'post',
    data: data
  })
}

export function selectReturnFlagApplication(data) {
  return request({
    url: '/payment/approval/approvalApplication/selectReturnFlagApplication',
    method: 'post',
    data: data
  })
}
export function selectFundsUse(data) {
  return request({
    url: '/payment/approval/approvalApplication/selectFundsUse',
    method: 'post',
    data: data
  })
}
export function loadUserList(data) {
  return request({
    url: '/payment/approval/approvalSettings/userlist',
    method: 'post',
    data: data
  })
}

export function loadReceiverList(data) {
  return request({
    url: '/payment/approval/receiverBankInfo/page',
    method: 'post',
    data: data
  })
}

export function loadContractList(data) {
  return request({
    url: '/payment/approval/approvalApplication/selectProjectContractList',
    method: 'post',
    data: data
  })
}

export function searchBankNumber(data) {
  return request({
    url: '/payment/bankNumber',
    method: 'get',
    params: data
  })
}

export function saveReceiver(data) {
  return request({
    url: '/payment/approval/receiverBankInfo/save',
    method: 'post',
    data: data
  })
}

export function deleteReceiver(data) {
  return request({
    url: '/payment/approval/receiverBankInfo/delete',
    method: 'post',
    data: data
  })
}

export function batchAddReceiver(orgId, data) {
  return request({
    url: '/payment/approval/receiverBankInfo/batchAdd/' + orgId,
    method: 'post',
    data: data,
    payload: true
  })
}

export function loadApprovalApplicationList(data) {
  return request({
    url: '/payment/approval/approvalApplication/page',
    method: 'post',
    data: data
  })
}

export function tovoid(data) {
  return request({
    url: '/payment/approval/approvalApplication/tovoid',
    method: 'post',
    data: data
  })
}
export function loadApprovalSettingList(data) {
  return request({
    url: '/payment/approval/approvalSettings/page',
    method: 'post',
    data: data
  })
}

export function loadApprovalSetting(data) {
  return request({
    url: '/payment/approval/approvalSettings/load_settings_pay',
    method: 'post',
    data: data
  })
}

export function saveApprovalSetting(data) {
  return request({
    url: '/payment/approval/approvalSettings/save',
    method: 'post',
    data: data
  })
}

export function removeApprovalSetting(data) {
  return request({
    url: '/payment/approval/approvalSettings/delete',
    method: 'post',
    data: data
  })
}

export function loadRoleList(data) {
  return request({
    url: '/payment/approval/approvalSettings/rolelist',
    method: 'post',
    data: data
  })
}

export function loadAttachmentList(data) {
  return request({
    url: '/payment/approval/approvalApplication/getWF',
    method: 'post',
    params: data
  })
}

export function saveApprovalApplication(data) {
  return request({
    url: '/payment/approval/approvalApplication/save',
    method: 'post',
    data: data,
    payload: true
  })
}

export function getApplication(data) {
  return request({
    url: '/payment/approval/approvalApplication/get/' + data,
    method: 'get'
  })
}
export function approvalApplicationSearch(data) {
  return request({
    url: '/payment/approval/approvalApplicationSearch/get/' + data,
    method: 'get'
  })
}

export function removeApprovalApplication(data) {
  return request({
    url: '/payment/approval/approvalApplication/deleteX',
    method: 'post',
    data: data
  })
}

export function submitApprovalApplication(data) {
  return request({
    url: '/payment/approval/workflow/submit',
    method: 'post',
    data: data
  })
}

export function tovoidApprovalApplication(data) {
  return request({
    url: '/payment/approval/approvalApplication/tovoid',
    method: 'post',
    data: data
  })
}

export function revocationApprovalApplication(data) {
  return request({
    url: '/payment/approval/workflow/revocation',
    method: 'post',
    data: data
  })
}

export function getTodoList(data) {
  return request({
    url: '/payment/approval/approvalTask/getTodoList?path=/standard/payment&subArea=true',
    method: 'post',
    params: data
  })
}

export function dealApproval(data) {
  return request({
    url: '/payment/approval/workflow/verify',
    method: 'post',
    params: data,
    timeout: 1000 * 60 * 15
  })
}

export function getDoneList(data) {
  return request({
    url: '/payment/approval/approvalTask/getHasDoneTaskList?path=/standard/payment&subArea=true',
    method: 'post',
    params: data
  })
}

export function loadApprovalReportList(data) {
  return request({
    url: '/payment/approval/approvalReport/page',
    method: 'post',
    params: data
  })
}

export function loadApprovalSearchList(data) {
  return request({
    url: '/payment/approval/approvalApplicationSearch/page',
    method: 'post',
    params: data
  })
}

/**
 * 获取六步工作法附件
 */
export function loadZip(id) {
  return request({
    url: '/payment/approval/approvalApplication/export_zip',
    method: 'get'
  })
}

/**
 * 获取机构、地区详细信息
 */
export function getOrgInfo(params) {
  return request({
    url: `/payment/approval/approvalSettings/getOrgInfo`,
    method: 'get',
    params: params
  })
}

/**
 * 获取地区树
 */
export function areaTreeDataAll(code) {
  return request({
    url: `/authority/area/authTreeDataAll`,
    method: 'post'
  })
}


/**
 * 获取账套的预算项目列表
 */
export function budgetItemList(data) {
  return request({
    url: `/payment/approval/approvalApplication/choice`,
    data,
    method: 'post'
  })
}

/**
 * 获取授权机构
 */
   export function authOrgDataAll() {
    return request({
      url: `/payment/approval/approvalTask/authOrgDataAll`,
      method: 'get'
    })
  }

/**
 * 设置乡村振兴类型
 */
export function setRevitalizationType(data) {
  return request({
    url: `/payment/approval/approvalApplication/setRevitalizationType`,
    data,
    method: 'post'
  })
}

/**
 * 设置乡村振兴金额
 */
export function setRevitalizationAmount(data) {
  return request({
    url: `/payment/approval/approvalApplication/setRevitalizationAmount`,
    data,
    method: 'post'
  })
}

/**
 * 设置乡村振兴所属机构
 */
export function setRevitalizationOrg(data) {
  return request({
    url: `/payment/approval/approvalApplication/setRevitalizationOrg`,
    data,
    method: 'post'
  })
}

// 乡村振兴设置-列表
export function pageRevitalization (data) {
  return request({
    url: '/payment/manager/payOrderPayee/pageRevitalization',
    data,
    method: 'post'
  })
}

/**
 * 锁定乡村振兴
 */
export function lockRevitalization(data) {
  return request({
    url: `/payment/approval/approvalApplication/lockRevitalization`,
    data,
    method: 'post'
  })
}

/**
 * 解锁乡村振兴
 */
export function unlockRevitalization(data) {
  return request({
    url: `/payment/approval/approvalApplication/unlockRevitalization`,
    data,
    method: 'post'
  })
}

/**
 * 锁定全部乡村振兴
 */
export function lockAllRevitalization(data) {
  return request({
    url: `/payment/approval/approvalApplication/lockAllRevitalization`,
    data,
    method: 'post'
  })
}

/**
 * 解锁全部乡村振兴
 */
export function unlockAllRevitalization(data) {
  return request({
    url: `/payment/approval/approvalApplication/unlockAllRevitalization`,
    data,
    method: 'post'
  })
}
export function getAuthTown() {
  return request({
    url: '/payment/approval/approvalApplication/getAuthTown',
    method: 'get'
  })
}
/**
 * 获取根据地区编码获取机构
 */
export function getOrgByAreaCode(areaCode) {
  return request({
    url: '/payment/approval/approvalApplication/getOrgByAreaCode?areaCode=' + areaCode,
    method: 'get'
  })
}


// 支付明细查询-列表
export function pagePaymentDetail (data) {
  return request({
    url: '/payment/approval/approvalReceiver/pagePaymentDetail',
    data,
    method: 'post'
  })
}

export function selectRevitalizationProgress ( data) {
  return request({
    url: '/payment/approval/approvalReceiver/selectRevitalizationProgress',
    data,
    method: 'post'
  })
}

/**
 * 检查是否需要关联进项发票
 * @param {string} orgId 机构ID
 * @returns {Promise}
 */
export function checkHasInvoiceRegistry(orgCode) {
  return request({
    url: '/payment/approval/receiverInvoice/checkHasInvoiceRegistry',
    method: 'get',
    params: { orgCode }
  })
}

export function getReceiverInvoiceList ( data) {
  return request({
    url: '/payment/approval/receiverInvoice/list',
    data,
    method: 'post'
  })
}

export function exportPaymentDetail ( data) {
  return request({
    url: '/payment/approval/approvalReceiver/exportPaymentDetail',
    params: data,
    method: 'get'
  })
}

export function getSystemConfig ( data) {
  return request({
    url: '/system/config/getByCode',
    params: data,
    method: 'get'
  })
}
