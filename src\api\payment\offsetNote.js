/*
 * @Descripttion:渠道商户
 * @version:
 * @Author: 未知
 * @Date: 2021-01-12 13:46:21
 * @LastEditors: Andy
 * @LastEditTime: 2023-11-24 10:49:30
 */

import request from '@/utils/request'


// /**
//  * @name:启用
//  * @param {*} data
//  * @description:
//  * @return {*}
//  * @test:
//  * @msg:
//  */
// export function enable(params) {
//   return request({
//     url: '/payment/manager/maintain/enabled',
//     method: 'get',
//     params
//   })
// }


/**
 * @name:获取冲销账列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectApplicationReceiver(data) {
  return request({
    url: '/payment/approval/approvalApplication/selectApplicationReceiver',
    method: 'post',
    data,
    // payload: true,
    // withCredentials: true
  })
}

/**
 * @name:冲销
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function offsetNote(data) {
  return request({
    url: '/payment/approval/approvalApplication/offsetNote',
    method: 'post',
    data,
    // payload: true,
    // withCredentials: true
  })
}

/**
 * @name:撤销冲销
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function offsetNoteCancel(data) {
  return request({
    url: '/payment/approval/approvalApplication/offsetNoteCancel',
    method: 'post',
    data,
    // payload: true,
    // withCredentials: true
  })
}

/**
 * @name:票据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getDeductBillList(data) {
  return request({
    url: '/bill/billDetail/getDeductBillList',
    method: 'post',
    data,
    // payload: true,
    // withCredentials: true
  })
}


/**
 * @name:发票
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getDeductInvoice(data) {
  return request({
    url: '/invoice/invoiceDetail/getDeductInvoice',
    method: 'post',
    data,
    // payload: true,
    // withCredentials: true
  })
}

/**
 * @name:非电子发票
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getDeductSundryBillList(data) {
  return request({
    url: '/bill/sundryBillDetail/getDeductBillList',
    method: 'post',
    data,
    // payload: true,
    // withCredentials: true
  })
}

/**
 * @name:查看收款人关联票据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getReceiverDeductList(params) {
  return request({
    url: '/payment/approval/approvalApplication/getReceiverDeductList',
    method: 'get',
    params,
  })
}

/**
 * @name:确认冲销
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function offsetNoteConfirm(data) {
  return request({
    url: '/payment/approval/approvalApplication/offsetNoteConfirm',
    method: 'post',
    data,
  })
}
