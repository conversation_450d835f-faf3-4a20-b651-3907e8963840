import request from '@/utils/request'
/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadReceiptListApi(params) {
  return request({
    url: '/specialFund/receipt/page',
    method: 'get',
    params
  })
}

/**
 * @name: 详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadReceiptInfoApi(id) {
  return request({
    url: `/specialFund/receipt/get/${id}`,
    method: 'get'
  })
}

/**
 * @name: 删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteReceiptApi(data) {
  return request({
    url: '/specialFund/receipt/deleteMul',
    method: 'post',
    data
  })
}

/**
 * @name: 新增保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveReceiptApi(data) {
  return request({
    url: '/specialFund/receipt/save',
    method: 'post',
    data
  })
}

/**
 * @name: 更新保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateReceiptApi(data) {
  return request({
    url: '/specialFund/receipt/update',
    method: 'post',
    data
  })
}

/**
 * @name: 状态审核
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateStatus(data) {
  return request({
    url: '/specialFund/receipt/updateStatus',
    method: 'post',
    data
  })
}
/**
 * @name: 模版下载
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function downloadExcelTemplate(data) {
  return request({
    url: '/specialFund/receipt/downloadFile',
    method: 'GET',
    params:data,
    responseType: 'blob', // 必须指定为blob类型
  })
}

/**
 * @name: 银行账户
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listAccount(params) {
  return request({
    url: '/payment/manager/bankAccount/listAccount',
    method: 'get',
    params
  })
}

/**
 * @name: 批量导入
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function excelBatchImport(data) {
  return request({
    url: '/specialFund/receipt/excelBatchImport',
    method: 'post',
    data
  })
}

/**
 * @name: 批量上传文件
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function batchFileImport(data) {
  return request({
    url: '/specialFund/receipt/batchFileImport',
    method: 'post',
    data
  })
}


/**
 * @name: 修改入账日期
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateArrivalDate(data) {
  return request({
    url: '/specialFund/receipt/updateArrivalDate',
    method: 'post',
    data
  })
}

/**
 * @name: 关联会计科
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateAccountingItem(data) {
  return request({
    url: '/specialFund/accountingItemRel/save',
    method: 'post',
    data
  })
}

/**
 * @name: 取消关联
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function accountingItemDeleteMul(data) {
  return request({
    url: '/specialFund/accountingItemRel/deleteMul',
    method: 'post',
    data
  })
}


/**
 * @name: 获取会计科目类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getAccountingItemList(data) {
  return request({
    url: `/specialFund/receipt/getAccountingItemList`,
    method: 'get',
    params: data
  })
}


/**
 * @name: 获取会计科目类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listByBooksId(data) {
  return request({
    url: `/financial/books/basedata/accountingItemType/listByBooksId`,
    method: 'post',
    data
  })
}


/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectAccountPage(params) {
  return request({
    url: '/specialFund/receipt/selectAccountPage',
    method: 'get',
    params
  })
}

/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectList(params) {
  return request({
    url: '/specialFund/receipt/selectList',
    method: 'get',
    params
  })
}

/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectAccountingItemList(params) {
  return request({
    url: '/specialFund/receipt/selectAccountingItemList',
    method: 'get',
    params
  })
}


/**
 * @name: 会计科目复制上年
 * @param {*} data
 * @description:
  * @return {*}
  * @test:
 * @msg:
 */
export function copyLastYear(data) {
  return request({
    url: `/specialFund/accountingItemRel/copyLastYear`,
    method: 'post',
    data
  })
}

/**
 * @name: 出纳对账表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectCashierReport(params) {
  return request({
    url: '/specialFund/reconciliation/report/selectCashierReport',
    method: 'get',
    params
  })
}

/**
 * @name: 会计对账表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectAccountingReport(params) {
  return request({
    url: '/specialFund/reconciliation/report/selectAccountingReport',
    method: 'get',
    params
  })
}

/**
 * @name: 支出列表
 * @param {*} data
 * @description:
  * @return {*}
  * @test:
 * @msg:
 */
export function pageSpecialFundPay(data) {
  return request({
    url: `/payment/approval/approvalReceiver/pageSpecialFundPay`,
    method: 'post',
    data
  })
}


/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cashierVoucherListByBankAccount(params) {
  return request({
    url: '/specialFund/receipt/cashierVoucherListByBankAccount',
    method: 'get',
    params
  })
}


/**
 * @name: 专项资金沉淀统计表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function accumulatedFundsReport(params) {
  return request({
    url: '/specialFund/reconciliation/report/accumulatedFundsReport',
    method: 'get',
    params
  })
}

/**
 * @name: 取消入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function batchCancelAccounting(data) {
  return request({
    url: '/specialFund/receipt/batchCancelAccounting',
    method: 'post',
    data,
		payload: 'json'
  })
}