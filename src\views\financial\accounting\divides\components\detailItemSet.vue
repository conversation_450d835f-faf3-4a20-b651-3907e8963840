<!--
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-04-25 17:26:48
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-08-29 15:12:44
 * @FilePath: \rural-financial-dev-normal\src\views\financial\accounting\divides\components\detailItemSet.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="detailItemSet">
    <el-dialog
      :title="$t('设置明细项目')"
      :visible.sync="setDetailItemDialogVisible"
      width="70%"
      append-to-body
      :before-close="handleClose"
    >
      <div>
        <el-form inline>
          <el-form-item>
            <el-button
              round
              type="primary"
              icon="el-icon-plus"
              @click="handleAdd"
            >
                            &nbsp;{{ $t('新增') }}
            </el-button>
            <el-button
              round
              icon="el-icon-delete"
              @click="handleDelete"
            >
              &nbsp;{{ $t('删除') }}
            </el-button>
            <el-button
              v-hasPermi="
                'financial.accounting.accountingDivides.importSpecialFund'
              "
              round
              icon="el-icon-import"
              @click="handleImportSpecialFund"
            >
              &nbsp;{{ $t('导入专项资金') }}
            </el-button>
            <span class="red" style="margin-left: 20px;">出纳单金额：{{ formatMoney(cashierVoucherAmount) }}</span>
          </el-form-item>
        </el-form>
      </div>
      <gever-table
        ref="_tableRef"
        class="wrap-table"
        style="width: 100%"
        show-summary
        :loading="loading"
        :columns="tableColumns"
        :data="tableData"
        :pagination="false"
        :height="400"
        :summary-method="getSummaries"
        cell-class-name="cell-wrap"
      >
        <template #operation="{ row }">
          <gever-button-group
            :buttons="[
              { text: '编辑', click: 'edit'},
              { text: '删除', click: 'delete'},
            ]"
            @edit="handleEdit(row)"
            @delete="handleDelete(row)"
          />
        </template>
      </gever-table>
      <span slot="footer" class="dialog-footer">
        <el-button round @click="handleClose">{{ $t('关 闭') }}</el-button>
        <el-button round type="primary" @click="handleSaveToDatabase">{{ $t('保 存') }}</el-button>
      </span>
    </el-dialog>
    <!-- 新增 -->
    <el-dialog
      :title="$t('新增明细项目')"
      :visible.sync="addDialogVisible"
      width="40%"
      append-to-body
      @close="handleAddDialogClose"
    >
      <el-form
        ref="_formRef"
        label-width="120px"
        :model="form"
        :rules="rules"
        :disabled="addFormDisabled"
      >
        <el-form-item prop="detailItemName" label="明细项目">
          <gever-input
            v-model="form.detailItemName"
            type="textarea"
            :rows="4"
            :maxlength="300"
            show-word-limit
          />
        </el-form-item>
        <el-form-item prop="accountingItemId" label="会计科目">
          <div class="flex">
            <el-autocomplete
              v-model="form.accountingItemName"
              popper-class="my-autocomplete"
              style="width: 80%"
              clearable
              :fetch-suggestions="querySearch"
              @select="handleSelectAccountingItemFromAutocomplete"
              @clear="handleClearAccountingItem"
            >
              <template slot-scope="{ item }">
                <div v-if="item.leafItem == 0" class="red">
                  {{ item.itemTitle }}
                </div>
                <div v-else-if="item.assistFlag > 0" class="blue">
                  {{ item.itemTitle }}
                </div>
                <div v-else>
                  {{ item.itemTitle }}
                </div>
              </template>
            </el-autocomplete>
            <el-button
              type="text"
              @click="handleSelectAccountingItem"
            >
              {{ $t('选择') }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item prop="amount" label="金额">
          <el-input-number
            style="width:50%"
            v-model="form.amount"
            :min="1"
            :max="***************"
            :step="1"
            :precision="2"
            :controls="true"
          />
        </el-form-item>
      </el-form>
      <template v-if="!addFormDisabled" #footer>
        <el-button @click="handleAddDialogClose">{{ $t("关闭") }}</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ $t("添加到列表") }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 会计科目选择 -->
    <el-dialog
      :title="$t('会计科目选择')"
      :visible.sync="accountingItemDialogVisible"
      width="800px"
      class="subject-dialog"
      append-to-body
      @close="handleAccountingItemDialogClose"
    >
      <subjectComponent
        v-if="accountingItemDialogVisible"
        style="height: 500px"
        :table-show="false"
        @handleSubjectData="handleSubjectData"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="accountingItemDialogVisible = false">{{ $t('关 闭') }}</el-button>
        <el-button type="primary" @click="handleConfirmSelectAccountingItem">
          {{ $t('确 定') }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { formatMoney } from '@/utils/index.js'
import { getAccountingItemList } from '@/api/financial/bookManage/baseData/item'
import { getDetailItems, saveDetailItems, importSpecialFundSubject } from '@/api/financial/accounting/divides'
import subjectComponent from '@/views/financial/setting/base-data/accounting-item/components/subject-component.vue'
export default {
    name: 'DetailItemSet',
    components: {
        subjectComponent
    },
    props: {
        setDetailItemDialogVisible: {
            type: Boolean,
            default: false
        },
        booksId: {
            type: String,
            default: ''
        },
        currentForm: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            dialogVisible: false, // 本地状态，用于控制对话框显示
            loading: false,
            addDialogVisible: false,
            addFormDisabled: false,
            accountingItemDialogVisible: false, // 会计科目选择对话框
            accountingItemNameOptions: [], // 会计科目选项列表
            subjectData: {}, // 当前选中的会计科目
            detailItemAmountSum: 0.00,
            deletedDbItemIds: [], // 存储被删除的数据库数据的ID
            // 表单初始值
            formInitialValues: {
                detailItemName: '',
                accountingItemId: '',
                accountingItemName: '',
                amount: 0.00
            },
            // 表单当前值
            form: {
                detailItemName: '',
                accountingItemId: '',
                accountingItemName: '',
                amount: 0.00
            },
            rules: {
                detailItemName: [
                    { required: true, message: '明细项目必填', trigger: 'blur' }
                ],
                accountingItemId: [
                    { required: true, message: '会计科目必填', trigger: 'blur' }
                ],
                amount: [
                    { required: true, message: '金额必填', trigger: 'blur' }
                ]
            },
            queryParams: {
                page: 1,
                rows: 20,
                editDisable: 0,
                source: 0
            },
            tableData: [],
            tableColumns: [
                { type: 'selection' },
                {
                    label: '明细项目',
                    prop: 'detailItemName',
                    className: 'wrap-cell' // 添加自定义类名
                },
                {
                    label: '会计科目',
                    prop: 'accountingItemName',
                    className: 'wrap-cell' // 添加自定义类名
                },
                {
                    label: '金额',
                    prop: 'amount',
                    filter: 'money'
                },
                {
                    label: '操作',
                    slotName: 'operation',
                    width: 150
                }
            ]
        }
    },
    computed: {
        selection() {
            return this.$refs['_tableRef'].selection
        },
        selectedIds() {
            return this.selection.map(({ id }) => id).join(',')
        },
        cashierVoucherAmount() {
            // 如果currentForm中的debit有值，则使用debit，否则使用credit
            return this.currentForm?.debit || this.currentForm?.credit || 0.00
        }
    },
    watch: {},
    created() {
        // 获取会计科目列表
        this.getAccountingItemList()
        // 获取明细项目列表
        this.getList()
    },
    mounted() {},
    methods: {
        formatMoney,
        handleClose() {
            // 检查列表中是否存在新增的数据（isNew为true的数据）
            const hasNewData = this.tableData.some(item => item.isNew === true)
            // 检查是否有删除的数据库数据
            const hasDeletedDbData = this.deletedDbItemIds.length > 0

            if (hasNewData || hasDeletedDbData) {
                // 构建提示消息
                let message = ''
                if (hasNewData && hasDeletedDbData) {
                    message = this.$t('列表中存在未保存到数据库的新增数据和删除的数据库数据，需要保存才能生效，确认关闭吗？')
                } else if (hasNewData) {
                    message = this.$t('列表中存在未保存到数据库的新增数据，确认关闭吗？')
                } else {
                    message = this.$t('列表中存在删除的数据库数据，需要保存才能生效，确认关闭吗？')
                }

                // 弹出确认对话框
                this.$confirm(message, this.$t('提示'), {
                    confirmButtonText: this.$t('确认'),
                    cancelButtonText: this.$t('取消'),
                    type: 'warning'
                }).then(() => {
                    // 用户点击确认，关闭窗口
                    this.$emit('setDetailItemClose')
                }).catch(() => {
                    // 用户点击取消，不做任何操作
                })
            } else {
                // 如果不存在新增数据和删除的数据库数据，直接关闭窗口
                this.$emit('setDetailItemClose')
            }
        },
        handleAdd() {
            // 重置表单为初始值
            this.form = JSON.parse(JSON.stringify(this.formInitialValues))
            console.log('this.currentForm：', this.currentForm)
            this.form.detailItemName = this.currentForm.remark
            this.addFormDisabled = false
            this.addDialogVisible = true
            // 确保DOM更新后再重置表单验证状态
            this.$nextTick(() => {
                if (this.$refs['_formRef']) {
                    this.$refs['_formRef'].clearValidate()
                }
            })
        },
        handleAddDialogClose() {
            // 重置表单为初始值
            this.form = JSON.parse(JSON.stringify(this.formInitialValues))
            // 清除验证状态
            if (this.$refs['_formRef']) {
                this.$refs['_formRef'].clearValidate()
            }
            this.addDialogVisible = false
        },
        handleEdit(row) {
            this.addFormDisabled = false
            // 深拷贝行数据到表单
            this.form = JSON.parse(JSON.stringify(row))
            this.addDialogVisible = true
            // 确保DOM更新后再重置表单验证状态
            this.$nextTick(() => {
                if (this.$refs['_formRef']) {
                    this.$refs['_formRef'].clearValidate()
                }
            })
        },
        handleSubmit() {
            this.$refs['_formRef'].validate(async(valid) => {
                if (valid) {
                    try {
                        if (this.currentForm.cashierAccountingItemId == this.form.accountingItemId) {
                            this.$message.error('选择的科目不允许与出纳科目相同，请重选选择')
                            return
                        }
                        // 创建一个新的明细项目对象
                        const newItem = {
                            id: this.form.id || Date.now().toString(), // 如果是编辑，保留原ID，否则生成临时ID
                            detailItemName: this.form.detailItemName,
                            accountingItemId: this.form.accountingItemId,
                            accountingItemName: this.form.accountingItemName,
                            amount: this.form.amount,
                            isNew: !this.form.id // 标记是否为新添加的项目
                        }

                        // 如果是编辑现有项目
                        if (this.form.id) {
                            const index = this.tableData.findIndex(item => item.id === this.form.id)
                            if (index !== -1) {
                                // 更新现有项目
                                this.tableData.splice(index, 1, newItem)
                            }
                        } else {
                            // 添加新项目到列表
                            this.tableData.push(newItem)
                        }

                        this.$message.success('已添加到列表')
                        // 重置表单为初始值
                        this.form = JSON.parse(JSON.stringify(this.formInitialValues))
                        this.addDialogVisible = false
                    } catch (error) {
                        console.error(error)
                        this.$message.error('添加失败')
                    }
                }
            })
        },
        handleDelete(row) {
            const ids = row?.id ? [row.id] : this.selectedIds.split(',').filter(id => id)

            if (ids.length > 0) {
                this.$confirm(this.$t('确认从列表中删除选中项目吗?'), '', {
                    confirmButtonText: this.$t('确认'),
                    cancelButtonText: this.$t('取消'),
                    type: 'warning'
                })
                .then(async (action) => {
                    if (action === 'confirm') {
                        // 从列表中删除选中的项目
                        ids.forEach(id => {
                            const index = this.tableData.findIndex(item => item.id === id)
                            if (index !== -1) {
                                // 如果删除的是非新增的数据（数据库中已存在的数据），则将其ID添加到deletedDbItemIds数组中
                                if (!this.tableData[index].isNew) {
                                    this.deletedDbItemIds.push(id)
                                }
                                this.tableData.splice(index, 1)
                            }
                        })
                        this.$message.success('已从列表中删除')
                    }
                })
                .catch(() => {})
            } else {
                this.$message.warning('至少选择一项数据')
            }
        },
        getSummaries(param) {
            const { columns } = param
            const sums = []
            columns.forEach((_, index) => {
                if (index === 2) {
                    sums[index] = '合计'
                    return
                }
                if (index === 3) {
                    const amounts = this.tableData.map((item) => Number(item.amount || 0))
                    const countAmount = amounts.reduce((prev, curr) => {
                        return (
                            (Math.round(prev * 100) / 100).toFixed(2) * 1 +
                            (curr || 0)
                        )
                    }, 0)
                    sums[index] = `${formatMoney(countAmount)}元`
                    return
                }
            })
            return sums
        },
        getList() {
            this.loading = true
            const params = {
                booksId: this.booksId,
                cashierVoucherId: this.currentForm.id
            }
            getDetailItems(params).then(res => {
                this.tableData = res.data.map(item => ({
                    ...item,
                    detailItemName: item.summary,
                    amount: item?.debit && item?.debit !== 0 ? item?.debit : item?.credit,
                    isNew: false // 标记为非新项目
                }))
                this.loading = false
            }).catch(error => {
                console.error('获取明细项目失败', error)
                this.loading = false
            })
        },
        // 会计科目选择相关方法
        handleSelectAccountingItem() {
            this.accountingItemDialogVisible = true
        },
        handleAccountingItemDialogClose() {
            this.subjectData = {}
            this.accountingItemDialogVisible = false
        },
        // 处理科目组件选择的数据
        handleSubjectData(data) {
            this.subjectData = data
        },
        // 确认选择会计科目
        handleConfirmSelectAccountingItem() {
            if (this.subjectData?.attributes?.seal) {
                this.$message.warning(this.$t('不能选择已封存的科目'))
                return
            }
            if (this.subjectData.state && this.subjectData.state === 'closed') {
                this.$message.warning(this.$t('请选择明细科目！'))
                return
            }

            this.form.accountingItemId = this.subjectData.id
            this.form.accountingItemName = this.subjectData.text || this.subjectData.name

            this.accountingItemDialogVisible = false
        },

        // 获取会计科目列表
        getAccountingItemList() {
            getAccountingItemList({
                booksId: this.booksId,
                choicesType: 0 // 根据实际情况调整
            }).then(res => {
                this.accountingItemNameOptions = res.data.map(item => {
                    // 确保每个项目都有itemTitle属性
                    if (!item.itemTitle && item.text) {
                        item.itemTitle = item.text
                    }
                    return item
                })
            }).catch(error => {
                console.error('获取会计科目列表失败', error)
            })
        },

        // 自动完成搜索方法
        querySearch(queryString, cb) {
            const restaurants = this.accountingItemNameOptions
            const results = queryString
                ? restaurants.filter(this.createStateFilter(queryString))
                : restaurants
            cb(results)
        },

        // 过滤方法
        createStateFilter(queryString) {
            return (state) => {
                return (
                    (state.itemPathTitle && state.itemPathTitle.includes(queryString)) ||
                    (state.itemTitle && state.itemTitle.includes(queryString))
                )
            }
        },

        // 从自动完成中选择会计科目
        handleSelectAccountingItemFromAutocomplete(item) {
            if (item.leafItem == 0) {
                this.$message.warning(this.$t('请选择明细科目！'))
                return
            }

            this.form.accountingItemId = item.id
            this.form.accountingItemName = item.itemTitle || item.text
        },

        // 清除会计科目
        handleClearAccountingItem() {
            this.form.accountingItemId = ''
            this.form.accountingItemName = ''
        },

        // 保存到数据库
        handleSaveToDatabase() {
            // 计算明细项目金额合计
            const detailItemTotalAmount = this.tableData.reduce((sum, item) => {
                return sum + Number(item.amount || 0)
            }, 0)

            // 获取出纳单金额
            const cashierAmount = Number(this.cashierVoucherAmount || 0)

            // 比较两个金额是否相等（考虑浮点数精度问题，使用toFixed(2)进行比较）
            if (this.tableData.length > 0 && detailItemTotalAmount.toFixed(2) !== cashierAmount.toFixed(2)) {
                this.$message.error(`明细项目金额合计(${detailItemTotalAmount.toFixed(2)})必须等于出纳单金额(${cashierAmount.toFixed(2)})`)
                return
            }
            const loadingInstance = this.$loading({
                    lock: true,
                    text: '保存中...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)' })
            try {
                // 设置明细项目
                // eslint-disable-next-line vue/no-mutating-props
                this.currentForm.accountingCashierDetailVOList = this.tableData.map(item => ({
                        id: item.id && !item.isNew ? item.id : '', // 如果是新项目，传空id
                        summary: item.detailItemName,
                        accountingItemId: item.accountingItemId,
                        debit: this.currentForm?.debit && this.currentForm?.debit !== 0 ? item.amount : 0.00,
                        credit: this.currentForm?.credit && this.currentForm?.credit !== 0 ? item.amount : 0.00
                    }))
                saveDetailItems(this.booksId, JSON.stringify(this.currentForm)).then((res) => {
                    if (res.returnCode === '0') {
                        loadingInstance.close()
                        this.$message.success('保存成功')
                        // 清除所有项目的isNew标记，表示已保存到数据库
                        this.tableData.forEach(item => {
                            item.isNew = false
                        })
                        // 清空已删除数据的ID数组
                        this.deletedDbItemIds = []
                        this.$emit('setDetailItemClose')
                    } else {
                        this.$message.error(res.message)
                    }
                })
            } catch (error) {
                loadingInstance.close()
                console.error(error)
                this.$message.error('保存失败')
            } finally {
                loadingInstance.close()
            }
        },
        async handleImportSpecialFund() {
            const loadingInstance = this.$loading({
                    lock: true,
                    text: '导入中...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)' })
            try {
                const params = {
                    booksId: this.booksId,
                    cashierVoucherId: this.currentForm.id,
                    voucherType: this.currentForm.voucherType
                }
                const res = await importSpecialFundSubject(params)
                if (res.returnCode !== '0') {
                    loadingInstance.close()
                    return this.$message.error(res.message)
                }
                if (!res.data || res.data.length <= 0) {
                    loadingInstance.close()
                    return this.$message.warning('没有可导入的专项资金')
                }
                // 检查是否有专项资金未关联会计科目
                const unassociatedFunds = Object.values(
                    res.data
                        .filter(item => !item.subjectId)
                        .reduce((map, item) => {
                            // 以 specialFundNumber 为 key，避免重复
                            if (!map[item.specialFundNumber]) {
                                map[item.specialFundNumber] = item
                            }
                            return map
                        }, {})
                )
                if (unassociatedFunds.length > 0) {
                    const fundList = unassociatedFunds.map(item => `${item.specialFundNumber}-${item.specialFundName}`).join('、')
                    const message = `存在以下专项资金未关联当前账套的会计科目\n${fundList}`
                    loadingInstance.close()
                    return this.$alert(message, '导入失败', {
                        confirmButtonText: '确定',
                        type: 'error'
                    })
                }
                const newItems = res.data.map(item => ({
                    id: item.id,
                    detailItemName: item.name,
                    accountingItemId: item.subjectId,
                    accountingItemName: item.subjectName,
                    amount: item.amount,
                    isNew: true
                })).filter(newItem => {
                    // 检查是否已存在于当前表格数据中
                    return !this.tableData.some(existingItem => existingItem.id === newItem.id)
                })
                if (newItems.length > 0) {
                    this.tableData = [...this.tableData, ...newItems]
                    this.$message.success(`成功导入${newItems.length}条专项资金数据`)
                } else {
                    this.$message.warning('没有新的专项资金可导入')
                }
                loadingInstance.close()
            } catch (error) {
                loadingInstance.close()
                console.error(error)
                this.$message.error('导入失败')
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
    align-items: center;
}
.red {
    color: red;
}
.blue {
    color: blue;
}
.my-autocomplete {
    li {
        line-height: normal;
        padding: 7px;

        .name {
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
}
.subject-dialog {
    .el-dialog__body {
        padding: 10px 20px;
        max-height: 500px;
        overflow-y: auto;
    }
}

/* 表格单元格自动换行样式 */
::v-deep .wrap-cell {
    white-space: pre-wrap !important; /* 保留空白符并允许自动换行 */
    word-wrap: break-word !important; /* 允许长单词换行 */
    word-break: break-all !important; /* 允许在任意字符间换行 */
    max-width: 300px; /* 设置最大宽度 */
    overflow: hidden; /* 隐藏溢出内容 */
}

/* 全局表格单元格样式 */
::v-deep .el-table .cell {
    white-space: pre-wrap !important;
    word-break: break-all !important;
}

/* 为 cell-wrap 类添加样式 */
::v-deep .cell-wrap {
    white-space: pre-wrap !important;
    word-break: break-all !important;
}

/* 为整个表格添加自动换行样式 */
.wrap-table {
    ::v-deep .el-table__body td .cell {
        white-space: pre-wrap !important;
        word-break: break-all !important;
        line-height: 1.5;
        padding: 8px;
    }
}
</style>
