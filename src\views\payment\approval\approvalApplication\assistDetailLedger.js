import { getLodop } from '@base/src/print/LodopFuncs.js'
import strBodyStyle from './public.js'
import { Message } from 'element-ui'
import {
  getApplication,
} from '@/api/payment/approval.js'
import { curry } from 'lodash';

function printinit_01(printData) {
  var printmsg = '';
  printmsg +=
    '<table width="100%" class="t_bab">'
    +
    '<thead>'
    +
    '<tr>'
    +
    '<td  height="10.5mm"  colspan="12">'
    +
    '<p class="title">支&nbsp;&nbsp;出&nbsp;&nbsp;证&nbsp;&nbsp;明&nbsp;&nbsp;单</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="0">'
    +
    '<p class="t_p">'
    +
    '<span class="t_span"></span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="12">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + printData.areaName + '镇(街道)村级财务</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="4">'
    +
    '<p class="t_p">单位名称:'
    +
    '<span class="t_span">' + printData.orgName + '</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="8">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + (new Date(printData.creationTime).getFullYear() || '') + '年' + ((new Date(printData.creationTime).getMonth() + 1) || '') + '月' + (new Date(printData.creationTime).getDate() || '') + '日' + '</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="13%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">费用来源(打√)</span>'
    +
    '</td>'
    +
    '<td  width="13%" height="30" class="t_2_td_title" rowspan="1">'
    +
    '<span class="t_2_spacn">上级拨款(&nbsp;&nbsp;)</span>'
    +
    '</td>'
    +
    '<td  width="11%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">上级拨款项目名称</span>'
    +
    '</td>'
    +
    '<td  width="36%" class="t_2_td_title" rowspan="2"  colspan="2">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '</td>'
    +
    '<td  width="12%" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">单据数量</span>'
    +
    '</td>'
    +
    '<td  width="10%" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">' + printData.docNumber + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="20%" height="30" class="t_2_td_title" rowspan="1">'
    +
    '<span class="t_2_spacn">集体资金(&nbsp;&nbsp;)</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</thead>'
    +
    '<tbody>';

  // var printDataRows = printData.agricultureReportDataList;
  // for (var i = 0; i < printDataRows.length; i++) {

  // }

  const str = printData.amount.toString()
  let arr, arr2 = []
  if (str.includes('.')) {
    arr = str.split('.')[0].split('')
    arr2 = str.split('.')[1].split('')
  } else {
    arr = str.split('')
  }
  const toBigger = (index) => {
    const arr = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '佰', '仟']
    return arr[index]
  }
  printmsg +=
    '<tr>'
    +
    '<td  width="13%" height="30" class="t_2_td_title" rowspan="5">'
    +
    '<span class="t_2_spacn">支付方式(打√)</span>'
    +
    '</td>'
    +
    '<td  width="13%" height="30" class="t_2_td_title" rowspan="1">'
    +
    '<span class="t_2_spacn">现金(&nbsp;&nbsp;)</span>'
    +
    '</td>'
    +
    '<td  width="11%" height="30" class="t_2_td_title" rowspan="2" colspan="5">'
    +
    '<span class="t_2_spacn">/</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="30" class="t_2_td_title" rowspan="3">'
    +
    '<span class="t_2_spacn">转账(&nbsp;&nbsp;)</span>'
    +
    '</td>'
    // '<td width="15%" height="30" class="t_2_td_title" rowspan="2">'
    // +
    // '<span class="t_2_spacn">收款人</span>'
    // +
    // '</td>'
    // +
    // '<td width="15%" height="30" class="t_2_td_title" rowspan="2">'
    // +
    // '<span class="t_2_spacn">' + (printData.receiverAccount || '') + '</span>'
    // +
    // '</td>'
    +
    '<td height="30" class="t_2_td_title" rowspan="1">'
    +
    '<span class="t_2_spacn">收款人</span>'
    +
    '</td>'
    +
    '<td height="30" class="t_2_td_title" rowspan="1" colspan="4">'
    +
    '<span class="t_2_spacn">' + (printData.receiverAccount || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="30" class="t_2_td_title" rowspan="1">'
    +
    '<span class="t_2_spacn">银行账号</span>'
    +
    '</td>'
    +
    '<td height="30" class="t_2_td_title" rowspan="1" colspan="4">'
    +
    '<span class="t_2_spacn">' + (printData.receiverBankAccount.replace(/,/g,'，') || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="30" class="t_2_td_title" rowspan="1">'
    +
    '<span class="t_2_spacn">开户银行</span>'
    +
    '</td>'
    +
    '<td height="30" class="t_2_td_title" rowspan="1" colspan="4">'
    +
    '<span class="t_2_spacn">' + (printData.receiverOpenAccount || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="12%" height="120" class="t_2_td_title" rowspan="2" colspan="1">'
    +
    '<span class="t_2_spacn">费用支出事由</span>'
    +
    '</td>'
    +
    '<td  width="88%" height="120" class="t_2_td_title" rowspan="2" colspan="6">'
    +
    '<p style="text-align:left;margin: 5px">' + (printData.fundsUse || '') + '</p>'
    +
    '<span class="t_2_spacn" style="display:inline-block;margin-top: 100px;">经办人：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td width="12%" height="30" class="t_2_td_title">'
    +
    '<span>金&nbsp;&nbsp;额(人民币)</span>'
    +
    '</td>'
    +
    '<td  width="88%" height="30" class="t_2_td_title" colspan="6">'
    +
    '<p class="title2" style="text-align: left;"><span class="t_2_spacn">大写：<span class="underline">' + (toBigger(arr[arr.length - 8]) || '&nbsp;&nbsp;') + '</span>仟<span class="underline">' + (toBigger(arr[arr.length - 7]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 6]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 5]) || '&nbsp;&nbsp;') + '</span>万<span class="underline">' + (toBigger(arr[arr.length - 4]) || '&nbsp;&nbsp;') + '</span>仟<span class="underline">' + (toBigger(arr[arr.length - 3]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 2]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 1]) || '&nbsp;&nbsp;') + '</span>元<span class="underline">' + (toBigger(arr2[0] || 0)) + '</span>角<span class="underline">' + (toBigger(arr2[1] || 0)) + '</span>分&nbsp;&nbsp;&nbsp;&nbsp;（小写：¥&nbsp;&nbsp;<span class="underline">' + (Number(Math.abs(printData.amount)).toFixed(2) || '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;') + '</span>&nbsp;&nbsp;元）</span></p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="12%" height="120" class="t_2_td_title" rowspan="2" colspan="1">'
    +
    '<span class="t_2_spacn">证明人意见</span>'
    +
    '</td>'
    +
    '<td  width="88%" height="120" class="t_2_td_title" rowspan="2" colspan="6">'
    +
    '<span class="t_2_spacn" style="display:inline-block;margin-top: 100px;">签名：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="12%" height="120" class="t_2_td_title" rowspan="2" colspan="1">'
    +
    '<span class="t_2_spacn">分管财经工作领导意见</span>'
    +
    '</td>'
    +
    '<td  width="88%" height="120" class="t_2_td_title" rowspan="2" colspan="6">'
    +
    '<span class="t_2_spacn" style="display:inline-block;margin-top: 100px;">签名：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="12%" height="120" class="t_2_td_title" rowspan="2" colspan="1">'
    +
    '<span class="t_2_spacn">主管“两委”或理事意见</span>'
    +
    '</td>'
    +
    '<td  width="88%" height="120" class="t_2_td_title" rowspan="2" colspan="6">'
    +
    '<span class="t_2_spacn" style="display:inline-block;margin-top: 100px;">签名：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="12%" height="120" class="t_2_td_title" rowspan="2" colspan="1">'
    +
    '<span class="t_2_spacn">主要领导审核意见</span>'
    +
    '</td>'
    +
    '<td  width="88%" height="120" class="t_2_td_title" rowspan="2" colspan="6">'
    +
    '<span class="t_2_spacn" style="display:inline-block;margin-top: 100px;">签名：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</tbody>'
    +
    '</table>'
  ;

  var createDiv = document.createElement("div");
  createDiv.id = "printmsg";
  document.body.appendChild(createDiv);
  //  $('#printmsg').append(printmsg);
  document.getElementById('printmsg').innerHTML = printmsg
}
function printinit_02(printData) {
  var printmsg = '';
  printmsg +=
    '<table width="100%" class="t_bab">'
    +
    '<thead>'
    +
    '<tr>'
    +
    '<td  height="10.5mm"  colspan="12">'
    +
    '<p class="title">费&nbsp;&nbsp;用&nbsp;&nbsp;报&nbsp;&nbsp;销&nbsp;&nbsp;单</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="0">'
    +
    '<p class="t_p">'
    +
    '<span class="t_span"></span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="12">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + printData.areaName + '镇(街道)村级财务</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="9">'
    +
    '<p class="t_p">单位名称:'
    +
    '<span class="t_span">' + printData.orgName + '</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="3">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + (new Date(printData.creationTime).getFullYear() || '') + '年' + ((new Date(printData.creationTime).getMonth() + 1) || '') + '月' + (new Date(printData.creationTime).getDate() || '') + '日' + '&nbsp;&nbsp;附单据及附件共&nbsp;' + (printData.docNumber + printData.approvalFileSettingInsts.length) + '&nbsp;张</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</thead>'
    +
    '<tbody>';

  ;

  const str = printData.amount.toString()
  let arr, arr2 = []
  if (str.includes('.')) {
    arr = str.split('.')[0].split('')
    arr2 = str.split('.')[1].split('')
  } else {
    arr = str.split('')
  }
  const toBigger = (index) => {
    const arr = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '佰', '仟']
    return arr[index]
  }
  printmsg +=
    '<tr>'
    +
    '<td  width="100" class="t_2_td_title" rowspan="1" colspan="2">'
    +
    '<span class="t_2_spacn">项&nbsp;&nbsp;&nbsp;&nbsp;目</span>'
    +
    '</td>'
    +
    '<td width="860" height="30" class="t_2_td_title" rowspan="1" colspan="10">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="100" height="90" class="t_2_td_title" rowspan="2" colspan="2">'
    +
    '<span class="t_2_spacn">用&nbsp;&nbsp;&nbsp;&nbsp;途</span>'
    +
    '</td>'
    +
    '<td  width="860" height="120" style="text-align:left; vertical-align: top; padding-left: 5px; padding-right: 5px " class="t_2_td_title" rowspan="2" colspan="10">'
    +
    '<span class="t_2_spacn" style="display: inline-block; word-break: break-word; ">' + (printData.fundsUse || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="100" height="30" class="t_2_td_title" colspan="2">'
    +
    '<span class="t_2_spacn">金&nbsp;&nbsp;&nbsp;&nbsp;额</span>'
    +
    '</td>'
    +
    '<td  width="860" height="30" class="t_2_td_title" colspan="10">'
    +
    '<p class="title2"><span class="t_2_spacn" style="text-align: center;">大写：<span class="underline">' + (toBigger(arr[arr.length - 8]) || '&nbsp;&nbsp;') + '</span>仟<span class="underline">' + (toBigger(arr[arr.length - 7]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 6]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 5]) || '&nbsp;&nbsp;') + '</span>万<span class="underline">' + (toBigger(arr[arr.length - 4]) || '&nbsp;&nbsp;') + '</span>仟<span class="underline">' + (toBigger(arr[arr.length - 3]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 2]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 1]) || '&nbsp;&nbsp;') + '</span>元<span class="underline">' + (toBigger(arr2[0] || 0)) + '</span>角<span class="underline">' + (toBigger(arr2[1] || 0)) + '</span>分&nbsp;&nbsp;&nbsp;&nbsp;（小写：¥&nbsp;&nbsp;' + (Number(Math.abs(printData.amount)).toFixed(2) || '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;') + '&nbsp;&nbsp;元）</span></p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="80" height="120" class="t_2_td_title" rowspan="4" colspan="1">'
    +
    '<span class="t_2_spacn">说明</span>'
    +
    '</td>'
    +
    '<td  width="280" height="30" style="text-align:left" class="t_2_td_title" colspan="5">'
    +
    '<span>原借款¥&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
    +
    '</td>'
    +
    '<td style="text-align:left"  width="400" height="30" class="t_2_td_title" colspan="4">'
    +
    '<span>应退(补)差额¥&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
    +
    '</td>'
    +
    '<td  style="text-align:left;border-top: none;border-left: none;"  width="200" height="30" class="t_2_td_title" colspan="2">'
    +
    '<span>报销方式：口现金&nbsp;&nbsp;口转账</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    // '<td  width="80" height="30" class="t_2_td_title" rowspan="2" colspan="1">'
    // +
    // '<span class="t_2_spacn">收款人</span>'
    // +
    // '</td>'
    // +
    // '<td  width="240" height="30" class="t_2_td_title" rowspan="2" colspan="4">'
    // +
    // '<span class="t_2_spacn">' + (printData.receiverAccount || '') + '</span>'
    // +
    // '</td>'
    '<td  width="100"  height="30" class="t_2_td_title" rowspan="1" colspan="1">'
    +
    '<span class="t_2_spacn">收款人</span>'
    +
    '</td>'
    +
    '<td  width="240" height="30" class="t_2_td_title" rowspan="1" colspan="10">'
    +
    '<span class="t_2_spacn">' + (printData.receiverAccount || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="100" height="30" class="t_2_td_title" rowspan="1" colspan="1">'
    +
    '<span class="t_2_spacn">银行账号</span>'
    +
    '</td>'
    +
    '<td  width="240" height="30" class="t_2_td_title" rowspan="1" colspan="10">'
    +
    '<span class="t_2_spacn">' + (printData.receiverBankAccount || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="100" height="30" class="t_2_td_title" rowspan="1" colspan="1">'
    +
    '<span class="t_2_spacn">开户银行</span>'
    +
    '</td>'
    +
    '<td  width="240" height="30" class="t_2_td_title" rowspan="1" colspan="10">'
    +
    '<span class="t_2_spacn">' + (printData.receiverOpenAccount || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="90" class="t_2_td_title" rowspan="3" colspan="1">'
    +
    '<span class="t_2_spacn">主要领导审核意见</span>'
    +
    '</td>'
    +
    '<td height="90" class="t_2_td_title" rowspan="3" colspan="11">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="12" style="text-align:left;">'
    +
    '<p class="title2">主管“两委”或理事：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;分管财经工作领导：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;复核人：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;经手人：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;出纳：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</tbody>'
    +
    '</table>'
  ;

  var createDiv = document.createElement("div");
  createDiv.id = "printmsg";
  document.body.appendChild(createDiv);
  //  $('#printmsg').append(printmsg);
  document.getElementById('printmsg').innerHTML = printmsg
}
function printinit_03(printData) {
  var printmsg = '';
  printmsg +=
    '<table width="100%" class="t_bab">'
    +
    '<thead>'
    +
    '<tr>'
    +
    '<td  height="10.5mm"  colspan="12">'
    +
    '<p class="title">资&nbsp;&nbsp;金&nbsp;&nbsp;使&nbsp;&nbsp;用&nbsp;&nbsp;审&nbsp;&nbsp;批&nbsp;&nbsp;单</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="0">'
    +
    '<p class="t_p">'
    +
    '<span class="t_span"></span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="12">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + printData.areaName + '镇(街道)村级财务</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="8">'
    +
    '<p class="t_p">单位名称:'
    +
    '<span class="t_span">' + printData.orgName + '</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="4">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + (new Date(printData.creationTime).getFullYear() || '') + '年' + ((new Date(printData.creationTime).getMonth() + 1) || '') + '月' + (new Date(printData.creationTime).getDate() || '') + '日</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</thead>'
    +
    '<tbody>';

  const str = printData.amount.toString()
  let arr, arr2 = []
  if (str.includes('.')) {
    arr = str.split('.')[0].split('')
    arr2 = str.split('.')[1].split('')
  } else {
    arr = str.split('')
  }
  const toBigger = (index) => {
    const arr = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '佰', '仟']
    return arr[index]
  }
  printmsg +=
    '<tr>'
    +
    '<td  width="300" height="60" class="t_2_td_title" rowspan="2" colspan="4">'
    +
    '<span class="t_2_spacn">收款单位(人)</span>'
    +
    '</td>'
    +
    '<td  width="400" height="60" class="t_2_td_title" rowspan="2" colspan="5">'
    +
    '<span class="t_2_spacn">' + (printData.receiverAccount || '') + '</span>'
    +
    '</td>'
    +
    '<td  width="80" height="30" class="t_2_td_title" rowspan="1" colspan="1">'
    +
    '<span class="t_2_spacn">银行账号</span>'
    +
    '</td>'
    +
    '<td  width="160" height="30" class="t_2_td_title" rowspan="1" colspan="2">'
    +
    '<span class="t_2_spacn">' + (printData.receiverBankAccount || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="80" height="30" class="t_2_td_title" rowspan="1" colspan="1">'
    +
    '<span class="t_2_spacn">开户银行</span>'
    +
    '</td>'
    +
    '<td  width="160" height="30" class="t_2_td_title" rowspan="1" colspan="2">'
    +
    '<span class="t_2_spacn">' + (printData.receiverOpenAccount || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="220" height="150" class="t_2_td_title" rowspan="1" colspan="3">'
    +
    '<span class="t_2_spacn">资金使用情况说明</span>'
    +
    '</td>'
    +
    '<td  width="720" height="150" class="t_2_td_title"  style="text-align:left" rowspan="1" colspan="10">'
    +
    '<span class="t_2_spacn"> 资金用途：'  + (printData.fundsUse || '') + '</span>'
    +
    '<span class="t_2_spacn"> </br></br>申请详细：'  + (printData.details || '') + '</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td width="220" height="30" class="t_2_td_title" colspan="3">'
    +
    '<span class="t_2_spacn" align="left">付款金额(人民币)</span>'
    +
    '</td>'
    +
    '<td  width="560" height="30" class="t_2_td_title" colspan="7">'
    +
    '<p class="title2" style="text-align:left"><span class="t_2_spacn" style="text-align: center;">大写：<span class="underline">' + (toBigger(arr[arr.length - 8]) || '&nbsp;&nbsp;') + '</span>仟<span class="underline">' + (toBigger(arr[arr.length - 7]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 6]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 5]) || '&nbsp;&nbsp;') + '</span>万<span class="underline">' + (toBigger(arr[arr.length - 4]) || '&nbsp;&nbsp;') + '</span>仟<span class="underline">' + (toBigger(arr[arr.length - 3]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 2]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 1]) || '&nbsp;&nbsp;') + '</span>元<span class="underline">' + (toBigger(arr2[0] || 0)) + '</span>角<span class="underline">' + (toBigger(arr2[1] || 0)) + '</span>分</span></p>'
    +
    '</td>'
    +
    '<td style="text-align:left;" width="160" height="30" class="t_2_td_title" colspan="2">'
    +
    '<span class="t_2_spacn" style="text-align:left;">¥&nbsp;&nbsp;' + (Number(Math.abs(printData.amount)).toFixed(2) || '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;') + '&nbsp;&nbsp;(元)</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="140" height="60" class="t_2_td_title" rowspan="2" colspan="2">'
    +
    '<span class="t_2_spacn">费用来源(打√)</span>'
    +
    '</td>'
    +
    '<td  width="160" height="30" class="t_2_td_title" rowspan="1" colspan="2">'
    +
    '<span class="t_2_spacn">上级拨款(&nbsp;&nbsp;&nbsp;&nbsp;)</span>'
    +
    '</td>'
    +
    '<td  width="160" height="30" class="t_2_td_title" rowspan="2" colspan="2">'
    +
    '<span class="t_2_spacn">上级拨款项目名称</span>'
    +
    '</td>'
    +
    '<td  width="240" height="60" class="t_2_td_title" rowspan="2" colspan="3">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '<td  width="80" height="60" class="t_2_td_title" rowspan="2" colspan="1">'
    +
    '<span class="t_2_spacn">单据数量</span>'
    +
    '</td>'
    +
    '<td  width="160" height="60" class="t_2_td_title" rowspan="2" colspan="2">'
    +
    '<span class="t_2_spacn">' + printData.docNumber + '&nbsp;张</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="160" height="30" class="t_2_td_title" rowspan="1" colspan="2">'
    +
    '<span class="t_2_spacn">集体资金(&nbsp;&nbsp;&nbsp;&nbsp;)</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="80" height="240" class="t_2_td_title" rowspan="2" colspan="1" style="writing-mode: vertical-rl;">'
    +
    '<span class="t_2_spacn">审批意见</span>'
    +
    '</td>'
    +
    '<td  width="160" height="120" class="t_2_td_title" rowspan="1" colspan="2">'
    +
    '<span class="t_2_spacn">主管“两委”或理事意见</span>'
    +
    '</td>'
    +
    '<td  width="700" height="120" class="t_2_td_title" rowspan="1" colspan="9">'
    +
    '<span class="t_2_spacn" style="display:inline-block;margin-top: 120px;">签名：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="160" height="120" class="t_2_td_title" rowspan="1" colspan="2">'
    +
    '<span class="t_2_spacn">主要领导审核意见</span>'
    +
    '</td>'
    +
    '<td  width="700" height="120" class="t_2_td_title" rowspan="1" colspan="9">'
    +
    '<span class="t_2_spacn" style="display:inline-block;margin-top: 120px;">签名：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="12" style="text-align:left;">'
    +
    '<p class="title2">分管财经工作领导：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;复核：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;经办人：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;出纳：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</tbody>'
    +
    '</table>'
  ;

  var createDiv = document.createElement("div");
  createDiv.id = "printmsg";
  document.body.appendChild(createDiv);
  //  $('#printmsg').append(printmsg);
  document.getElementById('printmsg').innerHTML = printmsg
}
function printinit_04(printData) {
  var printmsg = '';
  printmsg +=
    '<table width="100%" class="t_bab">'
    +
    '<thead>'
    +
    '<tr>'
    +
    '<td  height="10.5mm"  colspan="12">'
    +
    '<p class="title">(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)&nbsp;&nbsp;&nbsp;&nbsp;开&nbsp;&nbsp;&nbsp;&nbsp;支&nbsp;&nbsp;&nbsp;&nbsp;单</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="8">'
    +
    '<p class="t_p">单位名称:'
    +
    '<span class="t_span">' + printData.orgName + '</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="4">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + printData.areaName + '镇(街道)村级财务</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="5">'
    +
    '<p class="t_p">支付事项:'
    +
    '<span class="t_span"></span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="7">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + (new Date(printData.creationTime).getFullYear() || '') + '年' + ((new Date(printData.creationTime).getMonth() + 1) || '') + '月' + (new Date(printData.creationTime).getDate() || '') + '日' + '</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="3%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">序号</span>'
    +
    '</td>'
    +
    '<td  width="7%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">姓名</span>'
    +
    '</td>'
    +
    '<td  width="14%" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">身份证号码</span>'
    +
    '</td>'
    +
    '<td  width="10%" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">联系电话</span>'
    +
    '</td>'
    +
    '<td  width="8%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">标准(元/天或元/人)</span>'
    +
    '</td>'
    +
    '<td  width="8%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">数量(天数或人数)</span>'
    +
    '</td>'
    +
    '<td  width="8%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">总金额(元)</span>'
    +
    '</td>'
    +
    '<td  width="36%" height="8" class="t_2_td_title" colspan="3">'
    +
    '<span class="t_2_spacn">支付方式:口现金口转账</span>'
    +
    '</td>'
    +
    '<td  width="11%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">备注</span>'
    +
    '</td>'
    +
    '<td width="0.1%" style="border-width:0px;" rowspan="2"></td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="11%" height="24" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">收款人(现金由收款人亲笔签名)</span>'
    +
    '</td>'
    +
    '<td  width="11%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">开户账号</span>'
    +
    '</td>'
    +
    '<td  width="11%" height="30" class="t_2_td_title" rowspan="2">'
    +
    '<span class="t_2_spacn">开户银行</span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</thead>'
    +
    '<tbody>';

  ;

  var printDataRows = printData.agricultureReportDataList.filter(cur => !cur.parentKey)
  for (var i = 0; i < 10; i++) {

    printmsg +=
      '<tr>'
      +
      '<td height="5.9mm" style="border:solid 1px black;text-align:center;">'
      +
      '<span class="t_2_spacn">' + (i + 1) + '</span>'
      +
      '</td>'
      +
      '<td height="5.9mm" style="border:solid 1px black;">'
      +
      '<span class="t_2_spacn">' + (printDataRows[i]?.fullName || '') + '</span>'
      +
      '</td>'
      +
      '<td style="border:solid 1px black;text-align:center;">'
      +
      '<span class="t_2_spacn">' + (printDataRows[i]?.identityCard || '') + '</span>'
      +
      '</td>'
      +
      '<td style="border:solid 1px black;text-align:center;">'
      +
      '<span class="t_2_spacn">' + '' + '</span>'
      +
      '</td>'
      +
      '<td style="border:solid 1px black;text-align:center;">'
      +
      '<span class="t_2_spacn">' + '' + '</span>'
      +
      '</td>'
      +
      '<td style="border:solid 1px black;text-align:center;">'
      +
      '<span class="t_2_spacn">' + '' + '</span>'
      +
      '</td>'
      +
      '<td style="border:solid 1px black;text-align:center;">'
      +
      '<span class="t_2_spacn">' + (Math.abs(printDataRows[i]?.amountPayable ?? 0) || '') + '</span>'
      +
      '</td>'
      +
      '<td style="border:solid 1px black;text-align:center;">'
      +
      '<span class="t_2_spacn">' + '' + '</span>'
      +
      '</td>'
      +
      '<td style="border:solid 1px black;text-align:center;">'
      +
      '<span class="t_2_spacn">' + (printDataRows[i]?.receiverBankAccount || '') + '</span>'
      +
      '</td>'
      +
      '<td style="border:solid 1px black;text-align:center;">'
      +
      '<span class="t_2_spacn">' + (printDataRows[i]?.receiverOpenAccount || '') + '</span>'
      +
      '</td>'
      +
      '<td style="border:solid 1px black;text-align:left;">'
      +
      '<span class="t_2_spacn">' + '' + '</span>'
      +
      '</td>'
      +
      '</td>';
    if (i == printDataRows.length - 1) {
      printmsg += '<td rowspan="2" style="border-width:0px;"></td>';
    } else {
      printmsg += '<td style="border-width:0px;"></td>';
    }
    printmsg +=
      '</tr>';
  }
  const str = printData.amount.toString()
  let arr, arr2 = []
  if (str.includes('.')) {
    arr = str.split('.')[0].split('')
    arr2 = str.split('.')[1].split('')
  } else {
    arr = str.split('')
  }
  const toBigger = (index) => {
    const arr = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '佰', '仟']
    return arr[index]
  }
  printmsg +=
    '<tr>'
    +
    '<td height="5.9mm" colspan="11" style="border:solid 1px black;text-align:left;">'
    +
    '<p class="title2" style="text-align:left"><span class="t_2_spacn" style="text-align: center;">总金额人民币(大写)：<span class="underline">' + (toBigger(arr[arr.length - 8]) || '&nbsp;&nbsp;') + '</span>仟<span class="underline">'+ (toBigger(arr[arr.length - 7]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 6]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 5]) || '&nbsp;&nbsp;') + '</span>万<span class="underline">' + (toBigger(arr[arr.length - 4]) || '&nbsp;&nbsp;') + '</span>仟<span class="underline">' + (toBigger(arr[arr.length - 3]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 2]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 1]) || '&nbsp;&nbsp;') + '</span>元<span class="underline">' + (toBigger(arr2[0] || 0)) + '</span>角<span class="underline">' + (toBigger(arr2[1] || 0)) + '</span>分&nbsp;&nbsp;&nbsp;&nbsp;（小写：¥&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + Number(Math.abs(printData.amount))?.toFixed(2) + '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;元）</span></p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="12" style="text-align:center;">'
    +
    '<p class="title2">主要领导：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;主管“两委”或理事：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;分管财经工作领导：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;复核：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;经办人：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;出纳：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</tbody>'
    +
    '</table>'
  ;

  var createDiv = document.createElement("div");
  createDiv.id = "printmsg";
  document.body.appendChild(createDiv);
  //  $('#printmsg').append(printmsg);
  document.getElementById('printmsg').innerHTML = printmsg
}
function printinit_05(printData) {
  var printmsg = '';
  printmsg +=
    '<table width="100%" class="t_bab">'
    +
    '<thead>'
    +
    '<tr>'
    +
    '<td  height="10.5mm"  colspan="12">'
    +
    '<p class="title underline">借&nbsp;&nbsp;&nbsp;&nbsp;支&nbsp;&nbsp;&nbsp;&nbsp;单</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="0">'
    +
    '<p class="t_p">'
    +
    '<span class="t_span"></span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="12">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + printData.areaName + '镇(街道)村级财务</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="8">'
    +
    '<p class="t_p">单位名称:'
    +
    '<span class="t_span">' + printData.orgName + '</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '<td colspan="4">'
    +
    '<p class="t_p_right">'
    +
    '<span class="t_span">' + (new Date(printData.creationTime).getFullYear() || '') + '年' + ((new Date(printData.creationTime).getMonth() + 1) || '') + '月' + (new Date(printData.creationTime).getDate() || '') + '日' + '</span>'
    +
    '</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</thead>'
    +
    '<tbody>';

  ;

  const str = printData.amount.toString()
  let arr, arr2 = []
  if (str.includes('.')) {
    arr = str.split('.')[0].split('')
    arr2 = str.split('.')[1].split('')
  } else {
    arr = str.split('')
  }
  const toBigger = (index) => {
    const arr = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '佰', '仟']
    return arr[index]
  }
  printmsg +=
    '<tr>'
    +
    '<td  width="160" height="30" class="t_2_td_title" colspan="2">'
    +
    '<span class="t_2_spacn">借支人姓名</span>'
    +
    '</td>'
    +
    '<td  width="160" height="30" class="t_2_td_title" colspan="2">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '<td  width="80" height="30" class="t_2_td_title" colspan="1">'
    +
    '<span class="t_2_spacn">单位</span>'
    +
    '</td>'
    +
    '<td  width="560" height="30" class="t_2_td_title" colspan="7">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="160" height="90" class="t_2_td_title" colspan="2">'
    +
    '<span class="t_2_spacn">借支事由</span>'
    +
    '</td>'
    +
    '<td  width="800" height="90" class="t_2_td_title" colspan="10">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="80" height="30" class="t_2_td_title" colspan="2">'
    +
    '<span class="t_2_spacn">预算归还日期</span>'
    +
    '</td>'
    +
    '<td  width="800" height="30" class="t_2_td_title" colspan="10">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="160" height="30" class="t_2_td_title" colspan="2">'
    +
    '<span class="t_2_spacn">金&nbsp;&nbsp;&nbsp;&nbsp;额</span>'
    +
    '</td>'
    +
    '<td  width="800" height="30" class="t_2_td_title" colspan="10" style="text-align: left;">'
    +
    '<p class="title2" style="text-align: left;"><span class="t_2_spacn" ><span class="underline">'
    + (toBigger(arr[arr.length - 7]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 6]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 5]) || '&nbsp;&nbsp;') + '</span>万<span class="underline">' + (toBigger(arr[arr.length - 4]) || '&nbsp;&nbsp;') + '</span>仟<span class="underline">' + (toBigger(arr[arr.length - 3]) || '&nbsp;&nbsp;') + '</span>佰<span class="underline">' + (toBigger(arr[arr.length - 2]) || '&nbsp;&nbsp;') + '</span>拾<span class="underline">' + (toBigger(arr[arr.length - 1]) || '&nbsp;&nbsp;') + '</span>元<span class="underline">' + (toBigger(arr2[0] || 0)) + '</span>角<span class="underline">' + (toBigger(arr2[1] || 0)) + '</span>分&nbsp;&nbsp;&nbsp;&nbsp;（小写：¥&nbsp;&nbsp;' + (Number(Math.abs(printData.amount)).toFixed(2) || '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;') + '&nbsp;&nbsp;元）</span></p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td  width="160" height="60" class="t_2_td_title" colspan="2">'
    +
    '<span class="t_2_spacn">备&nbsp;&nbsp;&nbsp;&nbsp;注</span>'
    +
    '</td>'
    +
    '<td  width="480" height="60" class="t_2_td_title" colspan="6">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '<td  width="80" height="60" class="t_2_td_title" colspan="1">'
    +
    '<span class="t_2_spacn">出&nbsp;&nbsp;&nbsp;&nbsp;纳</span>'
    +
    '</td>'
    +
    '<td  width="240" height="60" class="t_2_td_title" colspan="3">'
    +
    '<span class="t_2_spacn"></span>'
    +
    '</td>'
    +
    '</tr>'
    +
    '<tr>'
    +
    '<td height="5.9mm" colspan="12" style="text-align:center;">'
    +
    '<p class="title2">主要领导：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;主管“两委”或理事：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;分管财经工作领导：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;复核人：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;借支人：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>'
    +
    '</td>'
    +
    '</tr>'
    +
    '</tbody>'
    +
    '</table>'
  ;

  var createDiv = document.createElement("div");
  createDiv.id = "printmsg";
  document.body.appendChild(createDiv);
  //  $('#printmsg').append(printmsg);
  document.getElementById('printmsg').innerHTML = printmsg
}
export function printReport(row) {
  // console.log(row, 1111);
  // if (row.approvalStatus == 1) {
  //   Message({
  //     message: '未提交审批的事项不能打印',
  //     type: 'warning'
  //   })
  //   return
  // }
  getApplication(row.id)
    .then((res) => {
      const queryparams = { ...row, ...res.data[0] }
      queryparams.agricultureReportDataList = res.data[1]
      let receiverAccount = ''
      let receiverOpenAccount = ''
      let receiverBankAccount = ''
      queryparams.agricultureReportDataList.map(item => {
        if (item.receiverAccount) {
          receiverAccount += receiverAccount ? ('，' + item.receiverAccount) : item.receiverAccount
        }
        if (item.receiverOpenAccount) {
          receiverOpenAccount += receiverOpenAccount ? ('，' + item.receiverOpenAccount) : item.receiverOpenAccount
        }
        if (item.receiverBankAccount) {
          receiverBankAccount += receiverBankAccount ? ('，' + item.receiverBankAccount) : item.receiverBankAccount
        }
      })
      queryparams.receiverAccount = receiverAccount
      queryparams.receiverBankAccount = receiverBankAccount
      queryparams.receiverOpenAccount = receiverOpenAccount
      LODOP = getLodop();
      // printinit_03(queryparams);
      console.log(queryparams);
      switch (queryparams.requisitionExpenditure) {
        case '1':
          // 支出证明单
          printinit_01(queryparams);
          LODOP.SET_PRINT_PAGESIZE(1, 0, 0, "A4");
          break;
        case '2':
          // 费用报销单
          printinit_02(queryparams);
          // 设置默认纸张
          let pageSize = queryparams.agricultureReportDataList.filter(item=> item.receiverAccount).length> 12 ? "A4" : "A5"
          //LODOP.SET_PRINT_PAGESIZE(pageSize == "A4" ? 1 : 2, 0, 0, pageSize);
          break;
        case '3':
          // 资金使用审批单
          printinit_03(queryparams);
          LODOP.SET_PRINT_PAGESIZE(1, 0, 0, "A4");
          break;
        case '4':
          // 开支单
          printinit_04(queryparams);
          LODOP.SET_PRINT_PAGESIZE(2, 0, 0, "A4");
          break;
        case '5':
          // 借支单
          printinit_05(queryparams);
          LODOP.SET_PRINT_PAGESIZE(2, 0, 0, "A5");
          break;
        default:
          Message({
            message: '该条数据没有要打印的支出申请单!',
            type: 'warning'
          })
          return
      }
      //addWaterMark(LODOP,waterMarkContext);
      LODOP.SET_PRINT_STYLE("FontSize", 9);
      // LODOP.SET_PRINT_PAGESIZE(2, 0, 0, "A4");
      LODOP.SET_SHOW_MODE("LANDSCAPE_DEFROTATED", 1);
      // console.log('打印内容');
      // console.log(document.getElementById('printmsg').innerHTML);
      // console.log(strBodyStyle);
      LODOP.ADD_PRINT_TABLE("0%", "3%", "95%", "80%", strBodyStyle + document.getElementById("printmsg")?.innerHTML);
      //   $("#printmsg").remove();
      document.getElementById('printmsg').remove()
      LODOP.PREVIEW();
    })
    .catch((error) => {
      console.log(error)
    })
}

function changeFontSize(context) {
  var i = context + "";
  if (i.length > 12) {
    return 10;
  } else {
    return '';
  }
}
function transformMoney(money) {
  if (money && money != null) {
    money = String(money);
    var left = money.split('.')[0], right = money.split('.')[1];
    right = right ? (right.length >= 2 ? '.' + right.substr(0, 2) : '.' + right + '0') : '.00';
    var temp = left.split('').reverse().join('').match(/(\d{1,3})/g);
    return (Number(money) < 0 ? "-" : "") + temp.join(',').split('').reverse().join('') + right;
  } else if (money === 0) {   //注意===在这里的使用，如果传入的money为0,if中会将其判定为boolean类型，故而要另外做===判断
    return '';
  } else {
    return "";
  }
};
