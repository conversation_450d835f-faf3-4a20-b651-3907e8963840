<template>
  <div>
    <fold-box right-title="支付查询">
      <template #right>
        <div class="right-box">
          <el-form ref="_formRef" inline :model="queryParams">
            <el-form-item :label="$t('支付单号')" prop="paymentCode">
              <el-input v-model="queryParams.paymentCode" class="w150" />
            </el-form-item>
            <el-form-item
              :label="$t('申请单编号')"
              prop="approvalApplicationSerialNumber"
            >
              <el-input
                v-model="queryParams.approvalApplicationSerialNumber"
                class="w150"
              />
            </el-form-item>
            <el-form-item :label="$t('资金用途')" prop="fundsUsed">
              <el-input v-model="queryParams.fundsUsed" class="w150" />
            </el-form-item>
            <el-form-item :label="$t('金额(元)')">
              <el-input-number
                v-model="queryParams.startAmount"
                :precision="2"
                :controls="false"
                class="w100"
              />
              {{ $t('至') }}
              <el-input-number
                v-model="queryParams.endAmount"
                :precision="2"
                :controls="false"
                class="w100"
              />
            </el-form-item>
            <el-form-item :label="$t('支付状态')" prop="payStatus">
              <el-select v-model="queryParams.payStatus" clearable class="w150">
                <el-option
                  v-for="item in paymentStateOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('机构类型')" prop="orgType">
              <el-select v-model="queryParams.orgType" clearable class="w100">
                <el-option
                  v-for="item in orgTypeOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('银行账户类型')" prop="accountType">
              <el-select
                v-model="queryParams.accountType"
                clearable
                class="w100"
              >
                <el-option
                  v-for="item in accountTypeOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('申请开始日期')" prop="startCreateTime">
              <el-date-picker
                v-model="queryParams.startCreateTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
              />
            </el-form-item>
            <el-form-item :label="$t('申请结束日期')" prop="endCreateTime">
              <el-date-picker
                v-model="queryParams.endCreateTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
              />
            </el-form-item>

            <el-form-item :label="$t('支出开始日期')" prop="startActualPayTime">
              <el-date-picker
                v-model="queryParams.startActualPayTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
              />
            </el-form-item>
            <el-form-item :label="$t('支出结束日期')" prop="endActualPayTime">
              <el-date-picker
                v-model="queryParams.endActualPayTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
              />
            </el-form-item>
            <el-form-item :label="$t('结算方式')" prop="orgType">
              <el-select v-model="queryParams.settlementMethod" clearable class="w150">
                <el-option
                  v-for="item in mapOptins.settlementMethod"
                  :key="item.id"
                  :label="item.settleName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('结算号')">
              <el-input v-model="queryParams.settlementNo" />
            </el-form-item>
            <el-form-item :label="$t('付款账号')">
              <el-input v-model="queryParams.payAccountNumber" />
            </el-form-item>
            <el-form-item :label="$t('付款账户所属银行')" prop="bankType">
              <gever-select
                v-model="queryParams.bankType"
                path="/payment/pay_bank_type"
                clearable
                class="w150"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                v-hasPermi="'financial.payment.manager.payQuery.list'"
                type="primary"
                round
                icon="el-icon-search"
                @click="loadList"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <el-form-item style="float: right">
              <el-button
                v-hasPermi="'financial.payment.manager.payQuery.payLog'"
                type="primary"
                round
                @click="handleViewLog"
              >
                {{ $t('日志') }}
              </el-button>
              <el-button
                v-hasPermi="'financial.payment.manager.payQuery.updateTime'"
                round
                @click="handleEditPayTime">
                {{ $t('修改支付结果时间') }}
              </el-button>
            </el-form-item>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            v-loading="loading"
            :columns="tableColumns"
            :data="table.dataList"
            :total="table.total"
            :pagi="queryParams"
            @p-current-change="loadList"
            @size-change="loadList"
          >
            <template #countOrName="{ row }">
              <span v-if="orderType(row)">{{ row.countOrName }}</span>
              <el-link
                v-else
                :underline="false"
                type="primary"
                @click="handleView(row)"
              >
                {{ row.countOrName + $t('个收款方') }}
              </el-link>
            </template>
            <template #returnFlag="{ row }">
              <span>{{ row.returnFlag ? '是' : '否' }}</span>
            </template>
            <template #businessType="{ row }">
              <span>{{ getText(row.businessType, 'businessType') }}</span>
            </template>
            <template #approvalApplicationSerialNumber="{ row }">
              <span class="name-detailSpan" @click="handleViewApplicationForm(row)">{{ row.approvalApplicationSerialNumber }}</span>
            </template>
            <template #operation="{ row }">
              <el-button
                v-for="(l, i) in getButtons(row)['buttons1']"
                :key="i"
                v-hasPermi="l.hasPermi"
                type="text"
                @click="l.callback(row)"
              >
                {{ $t(l.text) }}
              </el-button>
              <el-dropdown
                v-if="getButtons(row)['buttons2'].length"
                trigger="click"
                style="margin-left: 5px"
              >
                <span class="el-dropdown-link">
                  <i class="el-icon-more" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="(l, i) in getButtons(row)['buttons2']"
                    :key="i"
                    v-hasPermi="l.hasPermi"
                    @click.native="l.callback(row)"
                  >
                    {{ $t(l.text) }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <template #createTime="{ row }">
              {{ settingDate(row.createTime) }}
            </template>
            <template #orgType="{ row }">
              {{ getOrgTypePayment(row.orgType) }}
            </template>
            <template #accountType="{ row }">
              {{ getAccountType(row.accountType) }}
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <el-dialog :title="$t('查看支付申请单日志')" :visible.sync="logVisible">
      <order-log v-if="logVisible" :order-id="currentOrderId" />
    </el-dialog>

    <public-drawer
      :visible.sync="drawerVisible"
      :title="$t(drawerTitle)"
      :size="70"
    >
      <order-info
        v-if="drawerVisible"
        :business-type="businessType"
        :order-id="currentOrderId"
        :map-optins="mapOptins"
      />
    </public-drawer>

    <el-dialog
      :title="$t('终止支付')"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <el-form>
        <el-form-item :label="$t('终止原因')" required>
          <el-input
            v-model="payResult"
            type="textarea"
            resize="none"
            :maxlength="250"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('关 闭') }}</el-button>
        <el-button type="primary" @click="handleConfirmTerminate">
          {{ $t('保 存') }}
        </el-button>
      </span>
    </el-dialog>

    <PaymentAccount
      v-if="paymentAccountVisible"
      :visible="paymentAccountVisible"
      :order-id="currentOrderId"
      @handleAccountSelect="handleAccountSelect"
      @close="paymentAccountVisible = false"
    />

    <sms
      v-if="smsVisible"
      ref="_smsRef"
      :payment-title="paymentTitle"
      :sms-visible="smsVisible"
      :callback="callback"
      :order-id="currentOrderId"
      :account-id="selectedAccountId"
      :bank-type="bankType"
      @closeSms="closeSms"
      @pay="pay"
      @repay="repay"
      @repay2="repay2"
      @changeRetryPayeeFun="changeRetryPayeeFun"
    />
    <!-- @directPay="directPay" -->

    <public-drawer
      :visible.sync="paidDrawerVisible"
      :title="paymentTitle"
      :size="70"
      :buttons="isPayable ? (isRepay ? buttons2 : buttons1) : []"
    >
      <order-to-paid
        v-if="paidDrawerVisible"
        ref="_orderRef"
        :payment-title="paymentTitle"
        :allow-modify="allowModify"
        :order-id="currentOrderId"
        :business-type="businessType"
        :selected-account-id="selectedAccountId"
        :bank-type="handleBankType"
        @changeIsPayable="changeIsPayable"
        @getOrder="getOrder"
        @flagChange="changedFlag = true"
      />
    </public-drawer>
    <!-- 办理支付 -->
    <PayDrawer
      v-if="payVisible"
      ref="_payDrawerRef"
      :is-pay="isPay"
      :current-order-id="currentOrderId"
      :map-optins="mapOptins"
      :prop-visible.sync="payVisible"
      @successFn="loadList"
      @handleConfirmPay="handleConfirmPay"
    />
    <!-- 确认支付结果 -->
    <ComfirmResDrawer
      v-if="comfirmResVisible"
      :current-order-id="currentOrderId"
      :map-optins="mapOptins"
      :prop-visible.sync="comfirmResVisible"
      @successFn="loadList"
    />
    <!-- 确认支付失败 -->
    <ComfirmResDrawerFail
      v-if="comfirmResVisibleFail"
      :current-order-id="currentOrderId"
      :map-optins="mapOptins"
      :prop-visible.sync="comfirmResVisibleFail"
      @successFn="loadList"
    />
    <!-- 修改支付结果时间 -->
    <el-dialog
      title="修改支付结果时间"
      :visible.sync="updatePayTime"
      width="30%"
      :before-close="handleClosePayTime"
    >
      <el-form ref="_payTimeForm" :model="payTimeForm" label-width="130px">
        <el-form-item
          label="支付结果时间"
          prop="actualPayDate"
          :rules="[
            {
              required: true,
              message: '请选择支付结果时间',
              trigger: 'blur'
            }
          ]"
        >
          <el-date-picker
            v-model="payTimeForm.actualPayDate"
            placeholder="请选择时间"
            style="width: 100%"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClosePayTime">取 消</el-button>
        <el-button
          type="primary"
          :loading="payTimeLoading"
          @click="savePayTime"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
    <!--    支付申请查看-->
    <public-drawer
      ref="_appDrawerRef"
      v-loading="loading"
      :visible.sync="applicationDrawerVisible"
      :title="'支出申请查看'"
      :size="70"
      :is-before-close="false"
      @close="handleDrawerClose"
    >
      <application
        v-if="applicationDrawerVisible"
        :id="currentApplication.id || ''"
        ref="_appRef"
        :application="currentApplication"
        :receiver="receiverList"
        :load-type="'view'"
        :show-process-btn="true"
        :visible.sync="applicationDrawerVisible"
      />
    </public-drawer>
  </div>
</template>

<script>
import {
  loadPayOrderList,
  terminatePayOrder,
  pay,
  repay2,
  repay,
  ignoreRepay,
  querySMS,
  tempSaveRepay,
  changeRetryPayee,
  queryPaymentResult,
  updateActualPayTime
} from '@/api/payment/capital.js'
import { getApplication } from '@/api/payment/approval.js'
import OrderInfo from '../order/index.vue'
import OrderToPaid from '../order/index1.vue'
import OrderLog from '../orderLog/index.vue'
import PaymentAccount from '../orderToBePaid/choosePaymentAccount.vue'
import Sms from '../orderToBePaid/sms.vue'
import { getToken } from '@/utils/auth'
import { comboJson } from '@/api/gever/common.js'
import { Loading } from 'element-ui'
import PayDrawer from '../orderToBePaid/components/payDrawer.vue'
import ComfirmResDrawer from '../orderToBePaid/components/comfirmResDrawer.vue'
import ComfirmResDrawerFail from '../orderToBePaid/components/comfirmResDrawerFail.vue'
import { page as billingMethodPage } from '@/api/payment/billingMethod'
import Application from '@/views/payment/approval/approvalApplication/applicationInfo.vue'

// import { createNamespacedHelpers } from 'vuex'
// const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'OrderSearch',
  components: {
    OrderInfo,
    OrderToPaid,
    OrderLog,
    PaymentAccount,
    Sms,
    PayDrawer,
    ComfirmResDrawer,
    ComfirmResDrawerFail,
    Application
  },
  data() {
    return {
      payTimeForm: {
        actualPayDate: ''
      },
      payTimeLoading: false,
      updatePayTime: false,
      bankType: '',
      businessType: 1,
      paymentTitle: this.$t('支付'),
      order: {},
      payVisible: false,
      comfirmResVisible: false,
      comfirmResVisibleFail: false,
      loading: false,
      mapOptins: {
        businessType: [],
        settlementMethod: [{ id: '', settleName: this.$t('全部') }],
        interOrOutOptions: [
          { id: 1, text: '同行' },
          { id: 2, text: '跨行' }
        ]
      },
      queryParams: {
        toBePaid: '',
        paymentCode: '',
        fundsUsed: '',
        orgType: ' ',
        accountType: '',
        startAmount: undefined,
        endAmount: undefined,
        payStatus: '',
        approvalApplicationSerialNumber: '',
        startCreateTime: '',
        endCreateTime: '',
        startActualPayTime: '',
        endActualPayTime: '',
        page: 1,
        rows: 20,
        region: '',
        settlementMethod: ''
      },
      table: {
        total: 0,
        dataList: []
      },
      orgTypeOptions: [{ id: ' ', text: this.$t('全部') }],
      accountTypeOptions: [{ id: '', text: this.$t('全部') }],
      paymentStateOptions: [
        { id: '', text: this.$t('全部') },
        { id: '0', text: this.$t('未支付') },
        { id: '1', text: this.$t('支付中') },
        { id: '2', text: this.$t('支付成功') },
        { id: '3', text: this.$t('部分成功') },
        { id: '5', text: this.$t('退款成功') },
        { id: '-1', text: this.$t('支付失败') },
        { id: '-9', text: this.$t('已退回') },
        { id: '-10', text: this.$t('交易终止') },
        { id: '99', text: this.$t('状态未知') }
      ],
      tableColumns: [
        { type: 'selection', width: '50', fixed: 'left'},
        { label: '序号', type: 'index', index: this.indexMethod, width: '80', fixed: 'left' },
        { label: '支付单号', prop: 'paymentCode', minWidth: '120' },
        {
          label: '申请单编号',
          prop: 'approvalApplicationSerialNumber',
          minWidth: '120'
        },
        { label: '申请单位', prop: 'orgName', minWidth: '230' },
        // { label: '机构类型', slotName: 'orgType', width: '100' },
        // { label: '银行账户类型', slotName: 'accountType', width: '100' },
        {
          label: '申请类型',
          prop: 'applicationType',
          width: '100',
          convert: {
            options: [
              { id: 0, text: '常规用款' },
              { id: 1, text: '收支相抵' },
              { id: 2, text: '冲减收入' },
              { id: 3, text: '非预算项目支出' },
              { id: 4, text: '银行代扣' },
              { id: 5, text: '扣减预算' },
              { id: 6, text: '非预算收支相抵' }
            ]
          }
        },
        { label: '资金用途', prop: 'fundsUsed', minWidth: '200' },
        { label: '金额（元）', prop: 'payAmount', minWidth: '120' },
        { label: '实际支付时间/终止时间', prop: 'actualPayTime', width: '200' },
        { label: '结算号', prop: 'settlementNoName', minWidth: '120' },
        { label: '支付申请收款方账户', slotName: 'countOrName', width: '200' },
        { label: '申请人', prop: 'applyUserName', width: '120' },
        { label: '申请时间', prop: 'createTime', width: '200' },
        { label: '支付发起人/终止人', prop: 'payUserName', width: '200' },
        { label: '支付状态', prop: 'payStatus', width: '120' },
        { label: '支付结果说明', prop: 'payResult', minWidth: '150' },
        { label: '申请单来源', prop: 'sourceText', width: '120' },
        { label: '结算方式', prop: 'settlementMethodName', width: '120' },
        { label: '业务类型', prop: 'businessType', width: '120' },
        { label: '是否退款', prop: 'returnFlag', width: '120' },
        { label: '操作', slotName: 'operation', width: '250', fixed: 'right' }
      ],
      currentOrderId: '',
      drawerVisible: false,
      drawerTitle: '',
      dialogVisible: false,
      payResult: '',
      logVisible: false,

      paidDrawerVisible: false,
      paymentAccountVisible: false,
      selectedAccountId: null,

      allowModify: false, // 重新支付时允许修改收款人账号、收款人开户行
      isPay: true,
      isPayable: false,
      isRepay: false,
      buttons1: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: () => {
            this.paidDrawerVisible = false
          }
        },
        {
          type: 'primary',
          permission: 'financial.payment.manager.payOrder.pay',
          text: this.$t('支 付'),
          buttonStatus: true,
          callback: this.handleConfirmPay
        }
      ],
      buttons2: [
        {
          permission: 'financial.payment.manager.payQuery.tempSaveRepay',
          text: this.$t('暂 存'),
          buttonStatus: true,
          callback: this.handleRepayTempSave
        },
        {
          type: 'primary',
          permission: 'financial.payment.manager.payOrder.pay',
          text: this.$t('支 付'),
          buttonStatus: true,
          callback: this.handleConfirmPay
        }
      ],
      applicationDrawerVisible: false,
      currentApplication: {},
      receiverList: [],
      smsVisible: false,
      callback: '',
      smsConfig: '',
      changedFlag: false, // repay2中的收款人信息是否修改
      loadingInstance: null
    }
  },
  computed: {
    orderType() {
      return function (row) {
        if (row.orderType === '单笔支付' || isNaN(Number(row.countOrName))) {
          return true
        } else {
          return false
        }
      }
    },
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
    // ...mapState(['paymentLogin'])
  },
  watch: {
    paymentLogin: {
      /** 监听到变化后就触发一次handler 相当与created */
      immediate: true,
      handler(val) {
        if (val) {
          this.queryParams.region = val.code || ''
          this.loadList()
        }
      },
      deep: true
    }
  },
  created() {
    // this.loadList()
    this.loadAllComboJson()
    this.payResult = ''
  },
  methods: {
    savePayTime() {
      this.$refs['_payTimeForm'].validate((valid) => {
        if (!valid) return
        this.payTimeLoading = true
        const selectedRows =
          this.$refs['_geverTableRef'].$refs['elTable'].selection
        const ids = selectedRows.map((cur) => cur.id)
        updateActualPayTime({
          id: ids.join(),
          actualPayDate: this.payTimeForm.actualPayDate
        })
          .then((res) => {
            if (res.returnCode === '0') {
              this.$message.success(res.message)
              this.handleClosePayTime()
              this.loadList()
            }
          })
          .finally(() => {
            this.payTimeLoading = false
          })
      })
    },
    handleClosePayTime() {
      this.updatePayTime = false
      this.payTimeForm.actualPayDate = ''
    },
    handleEditPayTime() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (!selectedRows.length) {
        return this.$message.warning(
          this.$t('请至少选择一条数据再修改支付结果时间')
        )
      }
      this.updatePayTime = true
    },
    // 办理支付
    handlePayment(row, isPay = false) {
      this.isPay = isPay
      this.paymentTitle = '办理支付'
      this.currentOrderId = row.id
      this.payVisible = true
      // this.paymentTitle = '办理支付'
      // this.currentOrderId = row.id
      // this.payVisible = true
    },
    // 确认支付结果
    confirmThePaymentResult(row) {
      this.paymentTitle = '确认支付结果'
      this.currentOrderId = row.id
      this.comfirmResVisible = true
    },
    // 确认支付失败
    confirmThePaymentResultFail(row) {
      this.paymentTitle = '确认支付失败'
      this.currentOrderId = row.id
      this.comfirmResVisibleFail = true
    },
    loadAllComboJson() {
      comboJson({ path: '/payment/business_type/' }).then((res) => {
        this.mapOptins.businessType = res.data
      })
      // 结算方式
      billingMethodPage({ page: 1, rows: 100, parentId: '0' }).then((res) => {
        this.mapOptins.settlementMethod = res.data.rows
      })
      comboJson({ path: '/system/organization_type/' }).then((res) => {
        this.orgTypeOptions.push(...res.data)
      })
      comboJson({ path: '/financial/bank_account_type/' }).then((res) => {
        this.accountTypeOptions.push(...res.data)
      })
      comboJson({ path: '/payment/entrustType/' }).then((res) => {
        this.mapOptins.entrustTypeOptions = res.data
      })
      comboJson({ path: '/payment/payMethod/' }).then((res) => {
        this.mapOptins.payMethod = res.data
      })
    },
    getText(id, optionsData) {
      const cur = this.mapOptins[optionsData].find((item) => item.id == id)
      return cur?.text ?? ''
    },
    getOrgTypePayment(val) {
      if (val === undefined) return ''
      const state = this.orgTypeOptions.find((item) => item.id == val + '')
      return state ? state.text : ''
    },
    getAccountType(val) {
      if (val === undefined) return ''
      const state = this.accountTypeOptions.find((item) => item.id == val + '')
      return state ? state.text : ''
    },
    handleBankType(temp) {
      this.bankType = temp
    },
    getOrder(data) {
      this.order = data
      console.log(data, 'data')
    },
    indexMethod(index) {
      return (this.queryParams.page - 1) * this.queryParams.rows + index + 1
    },
    // eslint-disable-next-line complexity
    getButtons(row) {
      const arr = [
        {
          text: '查看',
          hasPermi: 'financial.payment.manager.payOrder.view',
          callback: (v) => this.handleView(v),
          show: true
        },
        // {
        //   text: '支付',
        //   hasPermi: 'financial.payment.manager.payOrder.pay',
        //   callback: (v) => this.handleRepay2(v),
        //   show: row.payStatusCode === 0 && row.retryType === 2
        // },
        // {
        //   text: '支付',
        //   hasPermi: 'financial.payment.manager.payOrder.pay',
        //   callback: (v) => this.handlePay(v),
        //   show: row.payStatusCode === 0 && row.retryType !== 2
        // },
        {
          text: '办理支付',
          hasPermi: 'financial.payment.manager.payOrder.pay',
          // callback: (v) => this.confirmThePaymentResult(v),
          callback: (v) => this.handlePayment(v, true),
          show: row.payStatusCode === 0 && row.tempFlag
        },
        {
          text: '终止支付',
          hasPermi: 'financial.payment.manager.payOrder.terminate',
          callback: (v) => this.handleTerminate(v),
          show: row.payStatusCode === 0
        },
        // {
        //   text: '重新支付',
        //   hasPermi: 'financial.payment.manager.payOrder.pay',
        //   callback: (v) => this.handleRepay(v),
        //   show:
        //     row.businessType === 1 &&
        //     [-9, -1, 3].includes(row.payStatusCode) &&
        //     row.retryType === 0
        // },
        {
          text: '确认支付结果',
          hasPermi: 'financial.payment.manager.payOrder.pay',
          callback: (v) => this.confirmThePaymentResult(v),
          show: row.payStatusCode === 1 && row.businessType !== 1
        },
/*        {
          text: '忽略',
          hasPermi: 'financial.payment.manager.payQuery.ignoreRepay',
          callback: (v) => this.handleIgnoreRepay(v),
          // show: (row.payStatusCode === -9 || row.payStatusCode === -1 || row.payStatusCode === 3) && row.retryType === 0
          show:
            row.businessType === 1 &&
            [-9, -1, 3].includes(row.payStatusCode) &&
            row.retryType === 0
        },*/
        {
          text: row.payMethod == 2 ? '导出委托明细' : '导出明细',
          hasPermi: 'financial.payment.manager.payOrder.export',
          callback: (v) => this.handleExportDetail(v),
          show:
            row.orderType !== '单笔支付' &&
            (row.payStatusCode === 1 ||
              row.payStatusCode === 2 ||
              row.payStatusCode === 3 ||
              row.payStatusCode === -1)
        },
        {
          text: '确认支付失败',
          hasPermi: 'financial.payment.manager.payOrder.confirmPaymentFail',
          callback: (v) => this.confirmThePaymentResultFail(v),
          show:
            (row.settlementMethodName !== '银农直连' || row.applicationType == 5) &&
            (row.payStatusCode === 2 || row.payStatusCode === 3)
        }
      ]
      const buttons = arr.filter((l) => l.show)
      const result = {
        buttons1: [],
        buttons2: []
      }
      if (buttons.length > 3) {
        buttons.forEach((l, i) => {
          if (i <= 2) {
            result.buttons1.push(l)
          } else {
            result.buttons2.push(l)
          }
        })
      } else {
        result.buttons1 = buttons
      }
      return result
    },
    // 时间截取
    settingDate(value) {
      return value.substring(0, 10)
    },
    getToken,
    loadList() {
      const startCreateTime = this.queryParams.startCreateTime
        ? new Date(this.queryParams.startCreateTime).getTime()
        : 0
      const endCreateTime = this.queryParams.endCreateTime
        ? new Date(this.queryParams.endCreateTime).getTime()
        : 0
      if (startCreateTime && endCreateTime) {
        if (startCreateTime > endCreateTime) {
          return this.$message.warning('申请开始日期不能大于申请结束日期')
        }
      }
      const startActualPayTime = this.queryParams.startActualPayTime
        ? new Date(this.queryParams.startActualPayTime).getTime()
        : 0
      const endActualPayTime = this.queryParams.endActualPayTime
        ? new Date(this.queryParams.endActualPayTime).getTime()
        : 0
      if (startActualPayTime && endActualPayTime) {
        if (startActualPayTime > endActualPayTime) {
          return this.$message.warning('支出开始日期不能大于支出结束日期')
        }
      }
      this.loading = true
      loadPayOrderList(this.queryParams)
        .then((res) => {
          this.table.dataList = res.data.rows
          this.table.total = res.data.total
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleViewLog() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (selectedRows.length !== 1) {
        return this.$message.warning(this.$t('请先选择其中一条记录查看'))
      }
      this.currentOrderId = selectedRows[0].id
      this.logVisible = true
    },
    handleView(row) {
      this.businessType = row.businessType
      this.currentOrderId = row.id
      this.drawerTitle = '查看支付申请单'
      this.drawerVisible = true
    },
    closeSms(val = false) {
      this.smsVisible = false
      // if (val) {
      //   this.paidDrawerVisible = false
      this.loadList()
      // }
    },
    // 发送按钮操作
    changeRetryPayeeFun(callback) {
      console.log('changeRetryPayeeFun')
      if (this.changedFlag && !this.isRepay) {
        // 判断数据是否被修改过，修改过则提交保存修改
        const temp = {}
        const tableList =
          this.paymentTitle === '办理支付'
            ? this.$refs['_payDrawerRef'].recipientList
            : this.$refs['_orderRef'].table.dataList
        tableList.forEach((item) => {
          temp[item.id] = item
        })
        changeRetryPayee(this.currentOrderId, temp).then((res) => {
          if (res.returnCode == '0') {
            this.changedFlag = false
          }
        })
      }
      if (this.isRepay) {
        const temp = {}
        const tableList =
          this.paymentTitle === '办理支付'
            ? this.$refs['_payDrawerRef'].recipientList
            : this.$refs['_orderRef'].table.dataList
        tableList.forEach((item) => {
          temp[item.id] = item
        })
        this.currentOrderIdtemp = temp
        callback(this.currentOrderId, temp)
        // this.handleRepayTempSave(true)
      }
    },
    handleRepay2(row) {
      this.isRepay = false
      this.allowModify = true
      this.paymentTitle = this.$t('支付')
      this.callback = 'repay2'
      this.tempSaveCallBack = 'tempSave2'
      this.currentOrderId = row.id
      this.paymentAccountVisible = true
    },
    repay2() {
      if (!this.validateReceiver()) return // 收款人信息校验
      if (this.changedFlag) {
        // 判断数据是否被修改过，修改过则提交保存修改
        const temp = {}
        const tableList =
          this.paymentTitle === '办理支付'
            ? this.$refs['_payDrawerRef'].recipientList
            : this.$refs['_orderRef'].table.dataList
        tableList.forEach((item) => {
          temp[item.id] = item
        })
        if (this.bankType == '305') {
          this.pay()
        } else {
          changeRetryPayee(this.currentOrderId, temp).then((res) => {
            if (res.returnCode == '0') {
              this.changedFlag = false
              this.pay()
            }
          })
        }
      } else {
        this.pay()
      }
    },
    tempSave2() {
      if (!this.validateReceiver()) return // 收款人信息校验
      if (this.changedFlag) {
        // 判断数据是否被修改过，修改过则提交保存修改
        const temp = {}
        const tableList =
          this.paymentTitle === '办理支付'
            ? this.$refs['_payDrawerRef'].recipientList
            : this.$refs['_orderRef'].table.dataList
        tableList.forEach((item) => {
          temp[item.id] = item
        })
        changeRetryPayee(this.currentOrderId, temp).then((res) => {
          if (res.returnCode == '0') {
            this.changedFlag = false
            this.paidDrawerVisible = false
            this.$message.success(this.$t('保存成功'))
            this.loadList()
          } else {
            this.$message.error(res.message)
          }
        })
      } else {
        this.paidDrawerVisible = false
        this.$message.success(this.$t('保存成功'))
        this.loadList()
      }
    },
    handlePay(row) {
      this.isRepay = false
      this.allowModify = false
      this.paymentTitle = this.$t('支付')
      this.callback = 'pay'
      this.currentOrderId = row.id
      this.paymentAccountVisible = true
    },
    handleRepay(row) {
      this.isRepay = true
      this.allowModify = true
      this.paymentTitle = this.$t('重新支付')
      this.callback = 'repay'
      this.tempSaveCallBack = 'tempSave'
      this.currentOrderId = row.id
      this.businessType = row.businessType
      this.selectedAccountId = row.payAccountId
      this.handleAccountSelect(row.payAccountId)
      // this.paymentAccountVisible = true
    },
    handleAccountSelect(accountId) {
      this.paymentAccountVisible = false
      this.selectedAccountId = accountId
      this.paidDrawerVisible = true
    },
    repay(val) {
      // 重新支付
      if (!this.validateReceiver()) return
      const temp = {}
      this.$refs['_orderRef'].table.dataList.forEach((item) => {
        if (!item.parentKey) {
          temp[item.id] = item
        }
      })
      if (this.bankType === '305') {
        this.pay(val)
      } else {
        tempSaveRepay(this.currentOrderId, temp).then((res) => {
          if (res.returnCode == '0') {
            this.currentOrderId = res.data
            this.changedFlag = false
            this.pay(val)
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    tempSave() {
      if (!this.validateReceiver()) return // 收款人信息校验
      if (this.changedFlag) {
        // 判断数据是否被修改过，修改过则提交保存修改
        const temp = {}
        const tableList =
          this.paymentTitle === '办理支付'
            ? this.$refs['_payDrawerRef'].recipientList
            : this.$refs['_orderRef'].table.dataList
        tableList.forEach((item) => {
          temp[item.id] = item
        })
        tempSaveRepay(this.currentOrderId, temp).then((res) => {
          if (res.returnCode == '0') {
            this.changedFlag = false
            this.paidDrawerVisible = false
            this.loadList()
            this.$message.success(this.$t('保存成功'))
          } else {
            this.$message.error(res.message)
          }
        })
      } else {
        this.paidDrawerVisible = false
        this.$message.success(this.$t('保存成功'))
        this.loadList()
      }
    },

    handleConfirmPay(payAccountId, callback) {
      if (payAccountId) {
        this.selectedAccountId = payAccountId
      }
      if (callback) {
        this.callback = callback
      }
      querySMS(payAccountId || this.selectedAccountId).then((res) => {
        if (res.data == '1') {
          this.smsVisible = true
        } else {
          this[this.callback]()
        }
      })
      // this[this.callback]()
    },
    handleRepayTempSave() {
      this[this.tempSaveCallBack]()
    },
    handleTerminate(row) {
      this.currentOrderId = row.id
      this.dialogVisible = true
    },
    // eslint-disable-next-line complexity
    validateReceiver() {
      let result = true
      const tableList =
        this.paymentTitle === '办理支付'
          ? this.$refs['_payDrawerRef'].recipientList
          : this.$refs['_orderRef'].table.dataList
      const data = JSON.parse(JSON.stringify(tableList))
      const message = {}
      for (const i in data) {
        const dataIndex = parseInt(i) + 1
        message[dataIndex] = []
        for (let j = 0; j < data.length; j++) {
          if (
            !data[i].parentKey &&
            j != i &&
            data[i].account &&
            data[i].account == data[j].account
          ) {
            message[dataIndex].push('收款方银行账号不能重复')
          }
        }
        if (!data[i].parentKey && !data[i].account) {
          message[dataIndex].push('收款方银行账号不能为空')
        }
        if (
          !data[i].parentKey &&
          (!data[i].accountBank || !data[i].accountBankNumber)
        ) {
          message[dataIndex].push('收款方开户银行不能为空')
        }

        data[i].key = new Date().getTime() + i
      }
      if (JSON.stringify(message) !== '{}') {
        let msg = ''
        for (const key in message) {
          if (message[key].length) {
            msg += message[key].reduce((total, curr) => {
              if (curr) {
                const temp = `第${key}条收款人信息, ${curr}<br />`
                return total + temp
              } else {
                return total
              }
            }, '')
          }
        }
        if (msg) {
          result = false
          this.$confirm(msg, {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t('确定'),
            showClose: false,
            showCancelButton: false,
            customClass: 'receiver-alert',
            type: 'warning'
          })
        } else {
          result = true
        }
      } else {
        result = true
      }
      return result
    },
    handleIgnoreRepay(row) {
      this.$confirm(this.$t('忽略后，无法再发起重新支付，是否确定忽略?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          ignoreRepay(row.id).then((res) => {
            this.$message.success(res.message)
            this.loadList()
          })
        })
        .catch(() => {})
    },
    handleExportDetail(row) {
      const url =
        process.env.VUE_APP_BASE_API +
        '/payment/manager/payOrder/exportDetail/' +
        row.id +
        '?access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.open(url)
    },
    handleConfirmTerminate() {
      // 终止支付
      if (!this.payResult) {
        return this.$message.warning(this.$t('请输入终止原因'))
      }
      this.$confirm(this.$t('终止支付后，无法再发起支付，是否确定终止?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          terminatePayOrder(this.currentOrderId, {
            payResult: this.payResult
          }).then((res) => {
            this.$message.success(res.message)
            this.dialogVisible = false
            this.payResult = ''
            this.loadList()
          })
        })
        .catch(() => {})
    },
    getAmount(val) {
      if (val === undefined) return ''
      return Number(val).toFixed(2)
    },
    changeIsPayable(val) {
      this.isPayable = val
    },
    pay(val) {
      // 支付
      console.log(this.currentOrderId, '00000')
      const params = {
        id: val?.id ? val?.id : this.currentOrderId,
        accountId: this.selectedAccountId,
        smsId: val?.smsId || '',
        smsCode: val?.smsCode || ''
      }
      this.loadingInstance = Loading.service({
        fullscreen: true,
        text: '支付中，正在查询结果...'
      })
      pay(params)
        .then((res) => {
          if (res.message === '支付成功' || res.message === '支付失败') {
            this.$message({
              type: res.message === '支付成功' ? 'success' : 'warning',
              message: res.message
            })
            this.$nextTick(() => {
              this.loadingInstance.close()
              this.payVisible = false
            })
            this.smsVisible = false
            this.paidDrawerVisible = false
            this.loadList()
          } else {
            this.smsVisible = false
            this.paidDrawerVisible = false
            this.queryResult(1, 500)
            this.$nextTick(() => {
              this.loadingInstance.close()
              this.payVisible = false
            })
          }
        })
        .catch((error) => {
          this.smsVisible = false
          this.$nextTick(() => {
            this.loadingInstance.close()
          })
          console.log(error, 'error')
        })
    },
    queryResult(count, delay) {
      queryPaymentResult(this.currentOrderId)
        .then((res) => {
          if (res.returnCode === '0') {
            if (res.message === '支付成功') {
              this.$message.success(res.message)
              this.$nextTick(() => {
                this.loadingInstance.close()
              })
              this.smsVisible = false
              this.paidDrawerVisible = false
              this.loadList()
            } else if (res.message === '部分成功') {
              this.$message.warning('支付存在部分失败，请查看具体支付明细')
              this.$nextTick(() => {
                this.loadingInstance.close()
              })
              this.smsVisible = false
              this.paidDrawerVisible = false
              this.loadList()
            } else if (res.message === '支付失败') {
              this.$message.warning(res.message)
              this.$nextTick(() => {
                this.loadingInstance.close()
              })
              this.smsVisible = false
              this.paidDrawerVisible = false
              this.loadList()
            } else {
              if (count <= 0) {
                this.$message.warning('未能查询到支付结果，请留意支付结果')
                this.$nextTick(() => {
                  this.loadingInstance.close()
                })
                this.smsVisible = false
                this.paidDrawerVisible = false
                this.loadList()
              } else {
                const nextDelay = delay * 2
                setTimeout(() => {
                  this.queryResult(--count, nextDelay)
                }, delay)
              }
            }
          } else {
            this.$message.warning('支付结果查询失败，失败原因：' + res.message)
            this.$nextTick(() => {
              this.loadingInstance.close()
            })
            this.smsVisible = false
            this.paidDrawerVisible = false
            this.loadList()
          }
        })
        .catch((error) => {
          this.$nextTick(() => {
            this.loadingInstance.close()
          })
          console.log(error, 'error1')
        })
    },
    handleDrawerClose() {
      this.currentApplication = {}
    },
    // 查看申请单
    handleViewApplicationForm(row) {
      this.loading = true
      this.applicationDrawerVisible = true
      getApplication(row.sourceId)
        .then((res) => {
          this.currentApplication = res.data[0]
          this.receiverList = res.data[1]
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-link {
  margin-left: 8px;
}
.name-detailSpan {
  cursor: pointer;
  color: #1890ff;
}
</style>
