<template>
  <div>
    <el-form inline>
      <el-form-item :label="$t('账户名称：')">
        <el-input v-model="receiverName" />
      </el-form-item>
      <el-form-item
        :label="$t('是否银行账户')"
      >
        <gever-select
          v-model="queryParams.isBankAccount"
          :options="optionsMap.isBankAccount"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          round
          plain
          type="primary"
          icon="el-icon-search"
          @click="handleSearchReceiverList"
        >
          {{ $t('搜索') }}
        </el-button>
      </el-form-item>

      <el-button
        round
        type="primary"
        icon="el-icon-plus"
        style="float: right"
        @click="handleAddReceiver"
      >
        {{ $t('新增') }}
      </el-button>
    </el-form>
    <gever-table
      ref="_geverTableRef"
      :columns="tableColumns"
      :data="receiverList"
      :total="pagiOptions.total"
      :pagi="pagiOptions"
      :height="'100%'"
      :loading="loading"
      @p-current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #bankType="{ row }">
        {{ row.bankType | fillterBankType }}
      </template>
      <template #isBankAccount="{ row }">
        {{ row.isBankAccount === 1 ? $t('是') : $t('否') }}
      </template>
    </gever-table>
    <el-dialog
      :title="$t(dialogTitle)"
      :visible.sync="dialogVisible"
      width="500px"
      class="payee-dialog"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="dialogVisible"
        ref="_receiverInfoFormRef"
        :rules="rules"
        :model="currentReceiverInfo"
        label-width="130px"
        :disabled="status === 'view'"
      >
        <el-form-item
          :label="$t('机构')"
          prop="orgCode"
          :rules="{
            required: true,
            message: $t('请选择机构'),
            trigger: 'change'
          }"
        >
          <el-select
            ref="_orgSelectRef"
            v-model="currentReceiverInfo.orgCode"
            clearable
            class="w300"
          >
            <el-option
              :value="treeNodeData.code"
              :label="treeNodeData.text"
              style="height: auto"
            >
              <area-tree
                ref="_treeRef"
                :tree-data="treeData"
                @selectedNodeChange="handleOrgChange"
              />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('是否银行账户')"
          prop="isBankAccount"
          :rules="[
            {
              required: true,
              message: $t('请选择是否银行账户'),
              trigger: 'blur'
            }
          ]"
        >
          <gever-select
            v-model="currentReceiverInfo.isBankAccount"
            :options="optionsMap.isBankAccount"
            number-key
            clearable
            class="w300"
            @change="handleIsBankAccount"
          />
        </el-form-item>
        <el-form-item
          :label="$t('银行账号')"
          prop="bankAccount"
          :rules="[
            {
              required:
                currentReceiverInfo && currentReceiverInfo.isBankAccount == 1,
              message: $t('请输入银行账号'),
              trigger: 'blur'
            }
          ]"
        >
          <el-input
            v-model.trim="currentReceiverInfo.bankAccount"
            class="w300"
            :disabled="!currentReceiverInfo.isBankAccount || currentReceiverInfo.isBankAccount == 0"
          />
        </el-form-item>
        <el-form-item
          :label="$t('收款方')"
          prop="receiverName"
          :rules="[
            {
              required: true,
              message: $t('请输入收款方名称'),
              trigger: 'blur'
            },
            {
              min: 1,
              max: 70,
              message: this.$t('收款方名称长度不能大于70个字符'),
              trigger: 'blur'
            }
          ]"
        >
          <el-input
            v-model.trim="currentReceiverInfo.receiverName"
            class="w300"
          />
        </el-form-item>
        <el-form-item
          :label="$t('开户网点名称')"
          prop="bankName"
          :rules="{
             required: currentReceiverInfo && currentReceiverInfo.isBankAccount == 1,
            message: $t('请选择开户网点名称'),
            trigger: 'change'
          }"
        >
          <el-select
            v-model="currentReceiverInfo.bankName"
            class="w300"
            clearable
            filterable
            remote
            :disabled="!currentReceiverInfo.isBankAccount || currentReceiverInfo.isBankAccount == 0"
            :remote-method="handleSearchBankType"
            :loading="selectLoading"
            @change="handleBankChange"
          >
            <el-option
              v-for="item in selectOptions"
              :key="item.number"
              :label="item.bankName"
              :value="item.bankName"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('银行类型')"
          prop="bankType"
          :rules="{
             required: currentReceiverInfo && currentReceiverInfo.isBankAccount == 1,
            message: $t('请选择银行类型'),
            trigger: 'change'
          }"
        >
          <el-select
            v-model="currentReceiverInfo.bankType"
            class="w300"
            disabled
          >
            <el-option
              v-for="item in bankTypeOptions"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('收款方银行联行号')"
          prop="bankType"
          :rules="{
             required: currentReceiverInfo && currentReceiverInfo.isBankAccount == 1,
            message: $t('请输入收款方银行联行号'),
            trigger: 'change'
          }"
        >
          <el-input
            v-model="currentReceiverInfo.bankNumber"
            class="w300"
            disabled
          />
        </el-form-item>
      </el-form>

      <span v-if="status !== 'view'" slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('关 闭') }}
        </el-button>
        <el-button type="primary" @click="handleSave">
          {{ $t('保 存') }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
let _this = null
import { comboJson, authTreeDataAll } from '../../../api/gever/common.js'
import {
  loadReceiverList,
  searchBankNumber,
  saveReceiver
} from '../../../api/payment/approval.js'
import { selectReceiverTableColumns } from '../../../views/payment/approval/approvalApplication/config'
export default {
  filters: {
    fillterBankType(type) {
      const typeItem = _this.bankTypeOptions.find((item) => item.id === type)
      return typeItem ? typeItem.text : ''
    }
  },
  props: {
    orgCode: { type: String, default: '' }
  },
  data() {
    return {
      tableColumns: selectReceiverTableColumns,
      dialogVisible: false,
      dialogTitle: '',
      status: 'view',
      receiverName: '',
      isBankAccount: '',
      TableColumns: selectReceiverTableColumns,
      pagiOptions: {
        page: 1,
        rows: 20,
        total: 0
      },
      queryParams: {
        codes: '',
        region: '',
        receiverName: '',
        isBankAccount: '',
        page: 1,
        rows: 20
      },
      loading: false,
      receiverList: [],
      selectOptions: [],
      bankTypeOptions: [],
      selectLoading: false,
/*      rules: {
        bankAccount: [
          {
            required: true,
            message: this.$t('请输入银行账号'),
            trigger: 'blur'
          }
          // { validator: (rule, value, callback) => {
          //     var regx = /^\d{14,20}/
          //     if (regx.test(value)) {
          //       callback()
          //     } else {
          //       return callback(new Error(this.$t('请输入正确的银行账户')))
          //     }
          //   }
          // }
        ]
      },*/
      currentReceiverInfo: {
        id: '',
        bankAccount: '',
        bankName: '',
        bankNumber: '',
        bankType: '',
        orgCode: '',
        receiverName: ''
      },
      optionsMap: {
        isBankAccount: [
          {
            id: 1,
            text: '是'
          },
          {
            id: 0,
            text: '否'
          }
        ]
      }
    }
  },
  computed: {
    // ...mapState(['paymentLogin'])
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  watch: {
    paymentLogin: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val) {
          this.$set(this.queryParams, 'orgCode', val.code || '')
          this.loadList()
        }
      }
    }
  },
  created() {
    _this = this
    this.loadBankTypeOptions()
    // this.loadList()
    this.loadTreeData()
  },
  methods: {
    loadBankTypeOptions() {
      comboJson({ path: '/payment/pay_bank_type/' }).then((res) => {
        this.bankTypeOptions = res.data
      })
    },
    loadTreeData() {
      authTreeDataAll().then((res) => {
        this.treeData = res.data
      })
    },
    loadList() {
      const params = {
        orgCode: this.queryParams.orgCode,
        receiverName: this.receiverName,
        isBankAccount: this.queryParams.isBankAccount,
        page: this.pagiOptions.page,
        rows: this.pagiOptions.rows
      }
      this.loading = true
      loadReceiverList(params)
        .then((res) => {
          this.receiverList = res.data.rows
          this.pagiOptions.total = res.data.total
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSearchReceiverList() {
      this.pagiOptions.page = 1
      this.loadList()
    },
    handleCurrentChange(val) {
      this.pagiOptions.page = val
      this.loadList()
    },
    handleSizeChange(val) {
      this.pagiOptions.page = 1
      this.pagiOptions.rows = val
      this.loadList()
    },
    handleAddReceiver() {
      // 新增收款人信息
      this.dialogTitle = '收款人信息'
      this.status = 'add'
      this.currentReceiverInfo = {
        id: '',
        bankAccount: '',
        bankName: '',
        bankNumber: '',
        bankType: '',
        orgCode: '',
        receiverName: ''
      }
      this.dialogVisible = true
      if (this.paymentLogin.code && this.paymentLogin.type === 'Organization') {
        this.traversalTree(this.treeData, this.paymentLogin.code)
        this.currentReceiverInfo.orgCode = this.paymentLogin.code
      }
    },
    traversalTree(arr, baseValue) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].code == baseValue) {
          this.treeNodeData = arr[i]
          return
        } else if (arr[i].children) {
          this.traversalTree(arr[i].children, baseValue)
        }
      }
      return
    },
    handleOrgChange(val) {
      if (val.type !== 'Organization') return
      else {
        this.currentReceiverInfo.orgCode = val.code
        this.treeNodeData = val
        this.$nextTick(() => {
          this.$refs['_orgSelectRef'].blur()
        })
      }
    },
    handleSave() {
      this.$refs['_receiverInfoFormRef'].validate((valid) => {
        if (!valid) return
        const params = this.currentReceiverInfo
        saveReceiver(params).then((res) => {
          this.$message.success(res.message)
          this.dialogVisible = false
          this.loadList()
        })
      })
    },
    handleSearchBankType(query) {
      if (query !== '') {
        this.selectLoading = true
        searchBankNumber({ keyword: query }).then((res) => {
          this.selectOptions = res.data.rows
          this.selectLoading = false
        })
      } else {
        this.selectOptions = []
      }
    },
    handleBankChange(val) {
      const bankInfo = this.selectOptions.find((item) => item.bankName === val)
      this.currentReceiverInfo.bankType = bankInfo ? bankInfo.bankType : ''
      this.currentReceiverInfo.bankName = bankInfo ? bankInfo.bankName : ''
      this.currentReceiverInfo.bankNumber = bankInfo ? bankInfo.number : ''
    },
    handleIsBankAccount(val) {
      if (val === 0) {
        this.$set(this.currentReceiverInfo, 'bankType', '')
        this.$set(this.currentReceiverInfo, 'bankName', '')
        this.$set(this.currentReceiverInfo, 'bankNumber', '')
        this.$set(this.currentReceiverInfo, 'bankAccount', '')
        this.currentReceiverInfo.bankType = ''
        this.currentReceiverInfo.bankName = ''
        this.currentReceiverInfo.bankNumber = ''
        this.currentReceiverInfo.bankAccount = ''
      }
    }
  }
}
</script>

<style></style>
