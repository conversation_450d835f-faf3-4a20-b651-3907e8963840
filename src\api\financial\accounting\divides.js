
import request from '@/utils/request'
/**
 * 按单生成凭证列表
 * @param {*} data
 * @returns
 */
export function dividesPage (data) {
  return request({
    url: '/financial/accounting/business/accountingDivides/page',
    method: 'post',
    data: data
  })
}

/**
 * 保存凭证列表修改
 * @param {*} data
 * @returns
 */
export function saveDetail (id, data) {
  return request({
    url: '/financial/accounting/business/accountingDivides/saveDetail?booksId=' + id,
    method: 'post',
    data: data,
    payload: true
  })
}

/**
 * 一对一生成凭证列表
 * @param {*} data
 * @returns
 */
export function dividesList (data) {
  return request({
    url: '/financial/accounting/business/accountingDivides/dividesList',
    method: 'post',
    data: data
  })
}

/**
 * 多对一生成凭证列表
 * @param {*} data
 * @returns
 */
export function dividesMultipleList (data) {
  return request({
    url: '/financial/accounting/business/accountingDivides/dividesMultipleList',
    method: 'post',
    data: data
  })
}

/**
 * 结算方式options
 * @param {*} data
 * @returns
 */
export function getModelList (data) {
  return request({
    url: '/financial/books/basedata/cashiersettlementtype/getModelList',
    method: 'post',
    data: data
  })
}

/**
 * 生成凭证
 * @param {*} data
 * @returns
 */
export function saveVoucher (booksId, period, type, isWriteOff, data) {
  return request({
    url: `/financial/accounting/business/accountingDivides/saveVoucher?booksId=${booksId}&period=${period}&type=${type}&isWriteOff=${isWriteOff}`,
    method: 'post',
    payload: true,
    data: data
  })
}
// 获取标题
export function inits (voucherType, id, data) {
  return request({
    url: `/financial/cashier/cashierVoucher/view/init/${voucherType}/${id}`,
    method: 'get',
    params: data
  })
}

// 获取票据
export function isShow (data) {
  return request({
    url: `/financial/accounting/business/accountingDivides/isShow`,
    method: 'post',
    payload: true,
    data: data
  })
}

// 查看票据
export function bill (id, data = {}) {
  return request({
    url: `/financial/accounting/business/accountingDivides/view/bill/${id}`,
    method: 'get',
    params: data
  })
}
// 
// 查看预算支出
export function selectBudgetByCashier (data) {
  return request({
    url: '/payment/manager/payOrderPayee/selectBudgetByCashier',
    method: 'post',
    data: data
  })
}

// 获取出纳单关联的明细项目列表
export function getDetailItems(params) {
  return request({
    url: '/financial/accounting/business/accountingDivides/getAccountingCashierDetailList',
    method: 'get',
    params: params
  })
}

// 保存出纳单关联的明细项目
export function saveDetailItems(booksId, data) {
  return request({
    url: `/financial/accounting/business/accountingDivides/saveDetailListBySetting?booksId=${booksId}`,
    method: 'post',
    data: data,
    payload: true
  })
}

// 导入专项资金科目
export function importSpecialFundSubject(data){
  return request({
    url: `/financial/accounting/business/accountingDivides/importSpecialFundSubject`,
    method: 'post',
    params: data
  })
}