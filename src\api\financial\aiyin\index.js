
import request from '@/utils/request'

// 票据迁移
export function pageList(data) {
  return request({
    url: '/bill/ayBillTransfer/pageList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 财政支出列表
export function czzcPageList(data) {
  return request({
    url: '/financial/ayFinancialExpenditureTransfer/pageList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 财政收入列表
export function czsrPageList(data) {
  return request({
    url: '/financial/ayFinancialIncomeTransfer/pageList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 合同列表
export function htPageList(data) {
  return request({
    url: '/financial/ayContractTransfer/pageList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 预算列表
export function ysPageList(data) {
  return request({
    url: '/financial/ayBudgetTransfer/pageList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 出纳日列表
export function cnrPageList(data) {
  return request({
    url: '/financial/ayCashierJournalTransfer/pageList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 出纳日列表
export function pjPageList(data) {
  return request({
    url: '/bill/ayBillTransfer/pageList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
