import request from '@/utils/request'

/* 列表 */
export function page(data) {
  return request({
    url: '/invoice/invoiceStockIn/page',
    method: 'post',
    data: data,
  })
}

/* 签收 */
export function invoiceSignFor(data) {
  return request({
    url: '/invoice/invoiceStockIn/invoiceSignFor',
    method: 'post',
    data: data,
  })
}

/* 取消签收 */
export function invoiceCancelSignFor(data) {
  return request({
    url: `/invoice/invoiceStockIn/invoiceCancelSignFor`,
    method: 'post',
    data
  })
}

