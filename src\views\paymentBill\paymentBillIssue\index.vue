<!--
 * @Description:
 * @Version:
 * @Author: luozc
 * @Date: 2023-09-19 11:23:11
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2024-01-17 11:44:55
-->
<template>
  <div>
    <fold-box :left-title="$t('银行账户')" :right-title="$t('支付票据开出')">
      <template #left>
        <ul v-if="bankAccountList.length > 0">
          <li
            v-for="(bankAccount, key) in bankAccountList"
            :key="key"
            :class="[
              'choose-tab',
              { active: currentAccount.id === bankAccount.id }
            ]"
            @click="currentAccount = bankAccount"
          >
            <el-row>
              <el-col>银行名称：{{ bankAccount.bankName }}</el-col>
              <el-col>银行账户：{{ bankAccount.accountNumber }}</el-col>
            </el-row>
          </li>
        </ul>
      </template>
      <template #right>
        <div class="right-box">
          <!-- :rules="rules" -->
          <el-form ref="_searchFormRef" :model="queryParams" inline>
            <el-form-item :label="$t('支付凭证号')">
              <el-input v-model="queryParams.paymentBillNo" />
            </el-form-item>
            <el-form-item prop="paymentBillType" label="支付凭证类型">
              <el-select
                v-model="queryParams.paymentBillType"
                placeholder="请选择支付凭证类型"
              >
                <el-option
                  v-for="item in optionsMap.paymentBillType"
                  :key="item.id"
                  :title="item.text"
                  :label="item.text"
                  :value="item.id * 1"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="paymentStatus" label="支付凭证状态">
              <el-select
                v-model="queryParams.paymentStatus"
                placeholder="请选择支付凭证类型"
              >
                <el-option
                  v-for="item in optionsMap.paymentStatus"
                  :key="item.id"
                  :title="item.text"
                  :label="item.text"
                  :value="item.id * 1"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="printStatus" label="打印状态">
              <el-select
                v-model="queryParams.printStatus"
                placeholder="请选择支付凭证类型"
              >
                <el-option
                  v-for="item in optionsMap.printStatus"
                  :key="item.id"
                  :title="item.text"
                  :label="item.text"
                  :value="item.id * 1"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                v-hasPermi="'financial.paymentbill.paymentBillIssue.list'"
                type="primary"
                plain
                round
                icon="el-icon-search"
                @click="getList"
              >
                {{ $t('搜 索') }}
              </el-button>
            </el-form-item>
          </el-form>
          <div class="btn-container">
            <el-button
              v-hasPermi="'financial.paymentbill.paymentBillIssue.add'"
              round
              type="primary"
              icon="el-icon-plus"
              @click="handleIssueStockIn"
            >
              {{ $t('开出') }}
            </el-button>
          </div>
          <gever-table
            ref="_geverTableRef"
            :loading="tableLoading"
            pagination
            :columns="tableColumns"
            :data="table.rows"
            :total="table.total"
            :pagi="queryParams"
            :options-map="optionsMap"
            highlight-current-row
            @pagination-change="getList"
          >
            <template #accountNumber="{ row }">
              <span>{{ accountNumber }}</span>
            </template>
            <template #operation="{ row }">
              <el-button
                v-hasPermi="'financial.paymentbill.paymentBillIssue.view'"
                type="text"
                @click="handleView(row)"
              >
                {{ $t('查看') }}
              </el-button>
              <el-button
                v-if="[0, 1].includes(row.paymentStatus)"
                v-hasPermi="'financial.paymentbill.paymentBillIssue.cancel'"
                type="text"
                @click="handleCancel(row)"
              >
                {{ $t('作废') }}
              </el-button>
              <el-button
                v-if="row.paymentStatus != -1"
                v-hasPermi="'financial.paymentbill.paymentBillIssue.rePrint'"
                type="text"
                @click="handlePatchwork(row)"
              >
                {{ row.printStatus === 0 ? $t('打印') : $t('补打') }}
              </el-button>
              <!-- <el-button
                v-if="row.printStatus === 0"
                v-hasPermi="'financial.paymentbill.paymentBillIssue.cancel'"
                type="text"
                @click="handleSendBank(row)"
              >
                {{ $t('发送银行') }}
              </el-button> -->
              <!-- <el-button v-hasPermi="''" type="text" @click="handleIssueStockIn(row)">{{
                $t('支付入库记录') }}</el-button> -->
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <!-- 作废/查看窗口 -->
    <issueCancelStorage
      v-if="ifAdd"
      :id="billIssueId"
      ref="_billIssueRef"
      :state="state"
      :bill-issue-storage="billIssueStorage"
      @success="getList"
    />

    <!-- 开出支付票据入库窗口 -->
    <IssueBillStockIn
      ref="_issueBillStockInfoRef"
      :state="state"
      :bill-issue-storage="billIssueStorage"
      @success="getList"
    />
  </div>
</template>

<script>
import {
  openBillPrintDesign,
  handleSpecialStyle
} from '../print.js'
import {
  paymentBillType,
  paymentStatus,
  printStatus
} from '../paymentBillDict.js'
import { loadAccountList } from '@/api/payment/capital.js'
import { getBillTemplate } from '@/api/paymentBill/template'
import { paymentExpenditureTypeTree as expenseTypePage } from '@/api/payment/expenseType'
import {
  getPaymentBillIssueList,
  getIssueDetail,
  print,
  rePrint
} from '@/api/paymentBill/paymentBillIssue.js'
import issueCancelStorage from './component/issueCancelStorage'
import IssueBillStockIn from '@/views/paymentBill/paymentBillIssue/component/issueBillStockIn'
import { tableColumns } from './config'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
export default {
  components: {
    issueCancelStorage,
    IssueBillStockIn
  },
  // beforeRouteEnter: (to, from, next) => {
  //   next((vm) => {
  //     if (vm.areaLogin.type === 'Organization') {
  //       window.billLoginShow()
  //       vm.$message.warning(vm.$t('请选择地区'))
  //     }
  //   })
  // },
  data() {
    return {
      billIssueId: '', // 获取票据开出的ID
      dividesVisible: false, // 判断新增弹框是否显示
      tableLoading: false,
      netherlands: '', // 地区名
      state: '', // 储存点击的状态
      billIssueStorage: {}, // 储存弹出框接受的数据
      selectParams: {}, // 支付入库记录查询参数
      ifAdd: true, // 如果弹框关闭就销毁弹框
      bankAccountList: [], // 银行账户
      currentAccount: {},
      optionsMap: {
        paymentBillType,
        expenditureType: [],
        paymentStatus,
        printStatus
      },
      tableColumns: tableColumns,
      table: {
        rows: [],
        total: 0
      },
      queryParams: {
        page: 1,
        rows: 10,
        paymentBillType: 9,
        paymentStatus: 1,
        printStatus: 0
      }
    }
  },
  computed: {
    ...mapState(['areaLogin']), // 地区机构数据
    areaLevel() {
      return this.areaLogin.areaLevel // 获取区域等级
    },
    accountNumber() {
      return this.currentAccount.accountNumber // 获取银行账户
    },
    holderType() {
      const obj = this.areaLogin
      const { areaLevel, type } = obj
      let holderType = ''
      // type  Area -地区 Organization - 机构
      // level 3 - 镇    4 - 乡  5 -机构
      // areaLevel 4 - 镇    5&&type==Area - 乡  5&&type==Organization -机构
      if (type === 'Area' && areaLevel === 4) {
        holderType = 1
      } else if (type === 'Area' && areaLevel === 5) {
        holderType = 2
      } else if (type === 'Organization' && areaLevel >= 4) {
        holderType = 3
      } else {
        holderType = null
      }
      return holderType
    }
  },
  watch: {
    areaLogin: {
      handler(val) {
        if (val.id) {
          // 获取银行账户信息
          this.getBankAccountList()
        }
      },
      deep: true
    },
    currentAccount: {
      handler(newVal) {
        if (newVal) {
          this.getList()
        }
      }
    }
  },
  created() {
    this.getExpenditureType()
    this.getBankAccountList()
  },
  mounted() {},
  methods: {
    async getExpenditureType() {
      // 支出类型
      const expenditureType = await expenseTypePage({ page: 1, rows: 100 })
      if (expenditureType.returnCode === '0') {
        this.optionsMap.expenditureType = expenditureType.data.filter(
          (cur) => cur.id !== '0'
        )
      }
    },
    // 获取当前地区银行账户信息
    async getBankAccountList() {
      const res = await loadAccountList({ state: 1 })
      if (res.data && res.data.rows.length > 0) {
        this.bankAccountList = res.data.rows
        this.currentAccount = this.bankAccountList[0]
      } else {
        this.bankAccountList = []
        this.currentAccount = {}
        this.getList()
      }
    },
    // 获取表格内容以及搜索
    async getList() {
      this.tableLoading = true
      if (Object.keys(this.currentAccount).length > 0) {
        const res = await getPaymentBillIssueList({
          ...this.queryParams,
          bankType: this.currentAccount.bankType,
          accountId: this.currentAccount.id,
          loginAreaOrgId: this.areaLogin.id,
          loginAreaOrgType: this.areaLogin.type,
          areaId: this.areaLogin.id
        })
        this.table = res.data
      } else {
        this.table = {
          rows: [],
          total: 0
        }
        this.$message.warning('请选择银行账户')
      }
      this.tableLoading = false
    },
    // 查看
    async handleView(row) {
      const res = await getIssueDetail(row.id)
      this.billIssueStorage = {
        ...res.data,
        accountId: this.currentAccount.id,
        accountNumber: this.currentAccount.accountNumber,
        ownerName: this.currentAccount.ownerName,
        bankName: this.currentAccount.bankName,
        bankType: this.currentAccount.bankType
      }
      this.$refs['_billIssueRef'].dialogVisible = true
      this.state = 'Check'
    },
    // 作废
    async handleCancel(row) {
      const res = await getIssueDetail(row.id)
      this.billIssueStorage = {
        ...res.data,
        accountId: this.currentAccount.id,
        accountNumber: this.currentAccount.accountNumber,
        ownerName: this.currentAccount.ownerName,
        bankName: this.currentAccount.bankName,
        bankType: this.currentAccount.bankType
      }
      this.$refs['_billIssueRef'].dialogVisible = true
      this.state = 'Cancel'
    },
    // 发送银行
    handleSendBank(row) {
      this.$confirm('单据还没有打印，是否发送银行？', this.$t('提 示'), {
        confirmButtonText: this.$t('确 定'),
        cancelButtonText: this.$t('取 消'),
        type: 'warning'
      })
        .then(() => {
          print(row.id).then((res) => {
            if (res.returnCode === '0') {
              this.$message.success(res.message)
              this.getList()
            }
          })
        })
        .catch(() => {})
    },
    // 补打
    async handlePatchwork(row) {
      this.$confirm(
        this.$t('确定') +
          (row.printStatus === 0 ? this.$t('打印') : this.$t('补打')) +
          this.$t('当前数据吗？'),
        this.$t('提 示'),
        {
          confirmButtonText: this.$t('确 定'),
          cancelButtonText: this.$t('取 消'),
          type: 'warning'
        }
      ).then(async () => {
        // 查询模板信息
        const resultData = await getBillTemplate(row.templateId)
        // 进行样式特殊处理
        resultData.data.templateContent = handleSpecialStyle(resultData.data.templateContent, row)

        let Fn
        if (row.printStatus === 1) {
          Fn = rePrint
        } else {
          Fn = print
        }
        try {
          const printDataArray = []
          const paymentAmount = row.paymentAmount
          printDataArray.push({
            ...row,
            paymentAmount: paymentAmount.toFixed(2),
            ownerName: this.currentAccount.ownerName,
            accountNumber: this.currentAccount.accountNumber,
            bankName: this.currentAccount.bankName
          })
          const _than = this
          openBillPrintDesign(
            function (printTime) {
              console.log('======》1' + printTime, typeof printTime)

              if (printTime === '1') {
                Fn(row.id).then((res) => {
                  if (res.returnCode === '0') {
                    _than.$message.success(res.message)
                    _than.getList()
                  } else if (res.returnCode === '-1') {
                    _than.$message.error(res.message)
                  }
                })
              }
            },
            resultData.data,
            printDataArray
          )
        } catch (e) {
          console.log('🚀 ~ file: index.vue:404 ~ ).then ~ e:', e)
          this.$message.warning(this.$t('打印组件初始化失败'))
        }
      })
    },

    // 开出票据入库信息
    handleIssueStockIn() {
      if (Object.keys(this.currentAccount).length == 0) {
        this.$message.warning('当前区域暂无银行账户，无法新增票据入库开出信息')
        return
      }
      this.state = 'PaymentBill'
      this.billIssueStorage = {
        accountId: this.currentAccount.id,
        accountNumber: this.currentAccount.accountNumber,
        ownerName: this.currentAccount.ownerName,
        bankName: this.currentAccount.bankName
      }
      this.$refs['_issueBillStockInfoRef'].dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-tab {
  height: 60px;
  font-size: 11px;
  border-bottom: 1px solid #d7d7d7;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 10px;
  cursor: pointer;
}

.active {
  border-left: 2px solid #409eff;
  background: #e6f7ff;
}

.btn-container {
  width: 100%;
  margin-bottom: 5px;
  display: flex;
  justify-content: right;
}
</style>
