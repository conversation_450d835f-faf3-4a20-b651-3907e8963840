<!--
 * @Descripttion: 常用信息-不动产租赁
-->
<template>
  <gever-dialog
    :title="$t(`不动产租赁（${title}）`)"
    :visible="dialogVisible"
    width="800px"
    class="subject-dialog"
    @close="handleClose"
  >
    <div v-loading="loading" class="dialog-box">
      <el-form
        ref="_formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        label-position="right"
        label-width="115px"
        class="gever-form"
        :disabled="statusType === 'view'"
      >
        <div class="form-sg">
          <el-form-item :label="$t('合同编号：')" prop="htBh">
            <el-input v-model="form.htBh" type="text" :maxlength="50" />
          </el-form-item>
          <el-form-item :label="$t('机构名称：')" prop="orgName">
            <gever-input v-model="form.orgName" type="text" disabled />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('地址：')" prop="bdcSf">
            <gever-select
              v-if="inputState === true"
              v-model="form.bdcSf"
              :class="statusType !== 'view' ? 'rowSf' : ''"
              :options="dqAreaOptions"
            />
            <el-input
              v-else
              v-model="form.bdcSf"
              type="text"
              :maxlength="50"
              :class="statusType !== 'view' ? 'rowSf' : ''"
            />
            <span v-if="statusType !== 'view'" class="rowSfSpan" @click="handleRowState()">
              {{ inputState ? $t('输入') : $t('选择') }}
            </span>
          </el-form-item>
          <el-form-item :label="$t('跨市标识：')" prop="bdcKsbz">
            <gever-select
              v-model="form.bdcKsbz"
              number-key
              :clearable="false"
              :options="yesOrNoOptions"
            />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('详细地址：')" prop="bdcXxdz">
            <el-input v-model="form.bdcXxdz" type="text" :maxlength="100" />
          </el-form-item>
        </div>
        <div class="form-db">
          <el-form-item :label="$t('租赁开始日期：')" prop="bdcQsrq">
            <gever-date-picker
              v-model="form.bdcQsrq"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item :label="$t('租赁结束日期：')" prop="bdcJzrq">
            <gever-date-picker
              v-model="form.bdcJzrq"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('产权证号：')" prop="bdcCqjh">
            <el-input v-model="form.bdcCqjh" type="text" :maxlength="50" placeholder="没有则填写“无”" />
          </el-form-item>
          <el-form-item :label="$t('面积单位：')" prop="bdcPjdw">
            <gever-select v-model="form.bdcPjdw" :options="bdcUnitOptions" />
          </el-form-item>
        </div>
      </el-form>
    </div>
    <template v-if="statusType !== 'view'" #footer>
      <el-button @click="handleClose">{{ $t('取 消') }}</el-button>
      <el-button type="primary" @click="handleSelect">
        {{ $t('保 存') }}
      </el-button>
    </template>
  </gever-dialog>
</template>

<script>
import { save } from '@/api/financial/invoice/invoiceInformation/leasehold.js'
import { createNamespacedHelpers } from 'vuex'
import { comboJson } from "@/api/gever/common"

const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'Details',
  props: {
    dialogVisible: { type: Boolean, default: false },
    rowData: {
      type: Object, default: () => { }
    },
    statusType: { type: String, default: 'view' }
  },
  data() {
    return {
      form: {
        orgId: '',
        orgName: '',
        htBh: '',
        bdcSf: '',
        bdcXxdz: '',
        bdcQsrq: '',
        bdcJzrq: '',
        bdcKsbz: 0,
        bdcCqjh: '',
        bdcPjdw: ''
      },
      rules: {
        htBh: [
          { required: true, message: '请输入合同编号', trigger: ['change'] }
        ],
        bdcSf: [
          { required: true, message: '请选择或输入地址', trigger: ['change'] }
        ],
        bdcXxdz: [
          { required: true, message: '请输入详细地址', trigger: ['change'] }
        ],
        bdcQsrq: [
          { required: true, message: '请选择开始日期', trigger: ['change'] }
        ],
        bdcJzrq: [
          { required: true, message: '请选择结束日期', trigger: ['change'] }
        ],
        bdcKsbz: [
          { required: true, message: '请选择跨市标识', trigger: ['change'] }
        ],
        bdcCqjh: [
          { required: true, message: '请输入产权证号', trigger: ['change'] }
        ],
        bdcPjdw: [
          { required: true, message: '请选择面积单位', trigger: ['change'] }
        ]
      },
      loading: false,
      title: '',
      inputState: true,
      yesOrNoOptions: [
        { id: 1, text: '是' },
        { id: 0, text: '否' }
      ],
      dqAreaOptions: [
        { id: '广东省-佛山市-禅城区', text: '广东省-佛山市-禅城区' },
        { id: '广东省-佛山市-南海区', text: '广东省-佛山市-南海区' },
        { id: '广东省-佛山市-顺德区', text: '广东省-佛山市-顺德区' },
        { id: '广东省-佛山市-高明区', text: '广东省-佛山市-高明区' },
        { id: '广东省-佛山市-三水区', text: '广东省-佛山市-三水区' }
      ],
      bdcUnitOptions: []
    }
  },
  computed: {
    ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    statusType: {
      deep: true,
      immediate: true,
      handler(newVal) {
        if (newVal === 'view') {
          this.title = '查看'
          this.form = {
            ...this.rowData,
            orgName: this.areaLogin.fname
          }
        } else if (newVal === 'edit') {
          this.title = '编辑'
          this.form = {
            ...this.rowData,
            orgName: this.areaLogin.fname
          }
        } else {
          this.title = '新增'
          this.form.orgId = this.areaLogin.id
          this.form.orgName = this.areaLogin.fname
        }
      }
    }
  },
  created() {
    comboJson({ path: '/financial/invoice/digital/bdcDw/' }).then((res) => {
      this.bdcUnitOptions = res.data
    })
  },
  mounted() { },
  methods: {
    /* 关闭弹窗 */
    handleClose() {
      this.$emit('updateList')
      this.$emit('update:dialogVisible', false)
    },
    handleRowState() {
      this.inputState = !this.inputState
    },
    /* 确认按钮 */
    handleSelect() {
      this.$refs['_formRef'].validate((valid) => {
        if (valid) {
          const params = {
            orgId: this.areaLogin.id,
            ...this.form
          }
          this.loading = true
          save(params)
            .then((res) => {
              this.$message.success(res.message)
              this.$emit('updateList')
              this.$emit('update:dialogVisible', false)
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.rowSf {
  width: calc(100% - 40px) !important;
}

.rowSfSpan {
  cursor: pointer;
  color: #1890ff;
  line-height: 27px;
  width: 40px;
}

</style>
