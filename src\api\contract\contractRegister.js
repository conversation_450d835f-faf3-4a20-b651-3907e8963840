/*
 * @Descripttion:
 * @version:
 * @Author: Tanronglong
 * @Date: 2021-10-11 14:25:11
 * @LastEditors: Tanronglong
 * @LastEditTime: 2021-12-09 20:31:54
 */
import request from '@/utils/request'
// import { data } from 'jquery'
// 合同列表数据
export function loadContractList(data) {
  return request({
    url: '/contract/contractRegistration/pageX',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 获取机构名称
export function loginAreaOrg(data) {
  return request({
    url: '/system/organization/loginAreaOrg',
    method: 'get',
    params: data
    // withCredentials: true
  })
}
// 获取审核状态 /contract/contractCommon/createStatus
// 获取合同状态 /contract/contractCommon/contractStatus
// 获取收款状态 /contract/collectionContract/collectionStatus
// 获取是否关联资产 /contract/collectionContract/relatedAssets
// 获取付款顺序 /contract/collectionContract/collectionType
// 获取操作人 /contract/collectionContract/secondType
// 乙方交易 /contract/collectionContract/terminationDetail
// 获取计算年限 /contract/collectionContract/paymentCycleAlgorithm
// 获取纠纷解决方式  /contract/collectionContract/solutionToContractDispute
// 获取合同开始与计租 /contract/collectionContract/collectionStartDate
// 获取计算方式 /contract/collectionContract/calculationMethodOfOverdueFine
// 获取计算类型 /contract/collectionContract/paymentAmountAlgorithm
// 获取加租与减租 /contract/collectionContract/changesType
export function createStatus(data) {
  return request({
    url: `/system/dictionary/combotree?path=${data.path}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 获取合同类型
export function getContractTypeDict(data) {
  return request({
    url: '/contract/contractType/getContractTypeDict',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 获取地区数据
export function getAssetsData(data) {
  return request({
    url: '/contract/contractRegistration/getAssetsData',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 获取资产资源数据
export function assetType(data) {
  return request({
    url: `/system/dictionary/combotree?path=${data.path}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 获取地区
export function getSecondAddresssTree(data) {
  return request({
    url: '/contract/contractRegistration/getSecondAddresssTree',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
export function getSecondAddresssTreeOne(data) {
  return request({
    url: `/contract/contractRegistration/getSecondAddresssTree?id=${data.id}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 表单数据接口
export function getFormEdit(data) {
  return request({
    url: `/contract/contractRegistration/loadX/${data}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 合同递增周期表格
export function createIncrementalDetail(data) {
  return request({
    url: '/contract/contractExpenseIncremental/create_incremental_detail',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同递增周期表格
export function contractRegistration(data) {
  return request({
    url: '/contract/contractRegistration/create_receivables_detail',
    method: 'post',
    data: data,
    payload: true,
    withCredentials: true
  })
}
// 合同变更递增周期表格
export function contractRegistrationChanges(data) {
  return request({
    url: '/contract/contractChanges/create_receivables_detail ',
    method: 'post',
    data: data,
    payload: true,
    withCredentials: true
  })
}
// 获取合同年限
export function getDiffDate(data) {
  return request({
    url: `/contract/tools/getDiffDate?beginDate=${data.beginDate}&endDate=${data.endDate}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 保存
export function contractRegistrationSave(data) {
  return request({
    url: '/contract/contractRegistration/saveX',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 提交接口
export function submitBatch(data) {
  return request({
    url: '/contract/workflow/submitBatch',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 打印
export function print(data) {
  return request({
    url: `/contract/contractRegistration/print/${data}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 撤销办理
export function revocation(data) {
  return request({
    url: '/contract/workflow/revocation',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同终止 /contract/contractTermination/checkRecevise?id=2c383d561ff64e73a5c15e2304eb9a97&terminateDate=2021-10-20
export function checkContractTermStatus(data) {
  return request({
    url: `/contract/contractTermination/checkRecevise?id=${data.id}&terminateDate=${data.terminateDate}`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同终止保存提交接口/contract/contractTermination/saveAndSubmit
export function saveAndSubmit(data) {
  return request({
    url: '/contract/contractTermination/saveAndSubmit',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 表单数据接口
export function getFormEditLoad(data) {
  return request({
    url: `/contract/contractRegistration/loadX/${data.id}?_ood_=${data._ood_}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 每期因收款
export function getReceivablesDetail(data) {
  return request({
    url: `/contract/contractRegistration/getReceivablesDetail/${data}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 变更记录
// 合同变更编辑接口 /contract/contractChanges/load
export function load(data) {
  return request({
    url: '/contract/contractChanges/load',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 终止记录
// 删除 /contract/contractRegistration/deleteX
export function deleteX(data) {
  return request({
    url: '/contract/contractRegistration/deleteX',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 删除付款 /contract/contractRegistration/deleteX
export function deleteXs(data) {
  return request({
    url: '/contract/paymentContract/deleteX',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同变更页面 /contract/contractChanges/pageList
export function pageList(data) {
  return request({
    url: '/contract/contractChanges/pageList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 获取资产资源详情 /contract/contractRegistration/getAssetsDataPageData
export function getAssetsDataPageData(data) {
  return request({
    url: '/contract/contractRegistration/getAssetsDataPageData',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 获取乙方信息 /contract/contractRegistration/getSecondMsgList
export function getSecondMsgList(data) {
  return request({
    url: '/contract/contractRegistration/getSecondMsgList',
    method: 'post',
    data: data
  })
}
// 获取乙方行业类型 /contract/contractRegistration/getIndustryTypeTree
export function getIndustryTypeTree(data) {
  return request({
    url: '/contract/contractRegistration/getIndustryTypeTree',
    method: 'get'
  })
}
// 合同终止校验 /contract/contractRegistration/checkContractTermStatus
export function checkContractTerm(data) {
  return request({
    url: '/contract/contractRegistration/checkContractTermStatus',
    method: 'get',
    params: data
  })
}
// /contract/contractRegistration/checkIsCollectedDone
export function checkIsCollectedDone(data) {
  return request({
    url: '/contract/contractRegistration/checkIsCollectedDone',
    method: 'get',
    params: data
  })
}
// 跟踪流程校验  /contract/contractRegistration/checkTrackType
export function checkTrackType(data) {
  return request({
    url: '/contract/contractRegistration/checkTrackType',
    method: 'post',
    data: data
  })
}
// 编辑流程校验  /contract/contractRegistration/checkToEdit
export function checkToEdit(data) {
  return request({
    url: '/contract/contractRegistration/checkToEdit',
    method: 'post',
    data: data
  })
}
// 终止记录校验  /contract/contractTermination/contractTerminationInfoCheck
export function contractTerminationInfoCheck(data) {
  return request({
    url: '/contract/contractTermination/contractTerminationInfoCheck',
    method: 'post',
    data: data
  })
}
// 合同资源关联校验 /contract/contractRegistration/checkAssetContractIsDuplicate
export function checkAssetContractIsDuplicate(data) {
  return request({
    url: '/contract/contractRegistration/checkAssetContractIsDuplicate',
    method: 'post',
    data: data
  })
}
// 获取递增周期 /contract/contractExpenseIncremental/list
export function list() {
  return request({
    url: '/contract/contractExpenseIncremental/list',
    method: 'post'
  })
}
// 新增变更的时候要获取页面参数 GET  /contract/contractChanges/addContract/init/合同id
export function addContractInit(data) {
  return request({
    url: `/contract/contractChanges/addContract/init/${data}`,
    method: 'get'
  })
}
// 编辑变更的时候获取页面参数 GET  /contract/contractChanges/editChanges/init/合同id
export function editChangesInit(data) {
  return request({
    url: `/contract/contractChanges/editChanges/init/${data}`,
    method: 'get'
  })
}
//
export function abend(data) {
  return request({
    url: '/contract/contractRegistration/abend',
    method: 'post',
    data: data
  })
}

