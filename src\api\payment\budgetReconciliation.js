import request from '@/utils/request'

/**
 * @name:获取数据页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(params) {
  return request({
    url: '/payment/paymentBudgetItemAdjust/page',
    method: 'post',
    params,
    withCredentials: true
  })
}
// 新增初始化
export function init(params) {
  return request({
    url: '/payment/paymentBudgetItemAdjust/add/init',
    method: 'get',
    params,
    withCredentials: true
  })
}
// 详情
export function load(id) {
  return request({
    url: `/payment/paymentBudgetItemAdjust/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}
// 保存
export function save(data) {
  return request({
    url: '/payment/paymentBudgetItemAdjust/save',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 删除
export function postDeletePost(data) {
  return request({
    url: '/payment/paymentBudgetItemAdjust/delete',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 提交/审核
export function submitOrAudit(data) {
  return request({
    url: '/payment/paymentBudgetItemAdjust/submitOrAudit',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 撤销提交/撤销审核

export function revokeSubmitOrAudit(data) {
  return request({
    url: '/payment/paymentBudgetItemAdjust/revokeSubmitOrAudit',
    method: 'post',
    data,
    withCredentials: true
  })
}
