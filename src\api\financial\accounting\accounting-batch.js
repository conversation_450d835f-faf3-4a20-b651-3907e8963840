import request from '@/utils/request'

/**
 * 获取列表
 */
export function page(data) {
    return request({
        url: '/financial/accounting/accountingBatch/pageForBatch',
        method: 'post',
        data: data
    })
}


/**
 * 批量过账
 */
export function batchPosting(data) {
    return request({
        url: '/financial/accounting/accountingBatch/batchPosting',
        method: 'post',
        data: data
    })
}
/**
 * 批量审批
 */
export function batchAudit(data) {
    return request({
        url: '/financial/accounting/accountingBatch/batchAudit',
        method: 'post',
        data: data
    })
}


/**
   * 凭证id查询
   */
export function loadPrintData(params) {
    return request({
        url: '/financial/accounting/accountingVoucher/loadPrintData',
        method: 'post',
        params: params
    })
}
/**
   * 凭证查询
   */
export function pageForReview(params) {
    return request({
        url: '/financial/accounting/accountingBatch/pageForReview',
        method: 'post',
        params: params
    })
}

/**
 * 会计凭证来源
 */
export function accountingLoadView(id) {
    return request({
        url: `/financial/accounting/accountingBusinessRelation/loadView/${id}`,
        method: 'get'
    })
}