import request from '@/utils/request'

// 列表
export function page(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/page',
    method: 'post'
  })
}

//导出

export function exportBudgetExcel(params) {
  return request({
    params,
    url: '/financial/budget/budgetPlait/exportBudgetExcel',
    method: 'get'
  })
}

//导出预算编制附件（包含上年数）
export function exportBudgetExcelIncludeLastYearAmount(params) {
  return request({
    params,
    url: '/financial/budget/budgetPlait/exportBudgetExcelIncludeLastYearAmount',
    method: 'get'
  })
}

// 详情
export function details(id) {
  return request({
    url: `/financial/budget/budgetPlait/get/${id}`,
    method: 'get'
  })
}
// 保存
export function save(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/save',
    method: 'post',
    payload: true
  })
}
// 删除
export function deletePost(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/delete',
    method: 'post'
  })
}
// 提交
export function submit(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/submit',
    method: 'post'
  })
}
// 撤销提交
export function unSubmit(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/unSubmit',
    method: 'post'
  })
}
// 审核
export function verify(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/verify',
    method: 'post'
  })
}
// 重新提交
export function reSubmit(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/reSubmit',
    method: 'post'
  })
}
// 查询预算设置列表
export function checkCanAdd(params) {
  return request({
    params,
    url: '/financial/budget/budgetPlait/checkCanAdd',
    method: 'get'
  })
}
// 预算编制-查询子表数据
export function getItemData(params) {
  return request({
    params,
    url: '/financial/budget/budgetPlait/getItemData',
    method: 'get'
  })
}
// 预算编制-导出文件
export function exportFile(params) {
  return request({
    params,
    url: '/financial/budget/budgetPlait/exportFile',
    method: 'get'
  })
}
// 预算编制-保存文件
export function saveFile(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/saveFile',
    method: 'post'
  })
}
// 获取合同
export function contractPage(data) {
  return request({
    data,
    url: '/weixun/contract/page',
    method: 'post'
  })
}
// 获取导入预算编制内容
export function getImportContent(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/readImportContent',
    method: 'post'
  })
}

// 获取过滤掉预算支出的非损益下拉
export function getItemList(data) {
  return request({
    data,
    url: '/financial/books/basedata/accountingItem/getItemList',
    method: 'post'
  })
}

// 获取预算编制下拉
export function getPlaitLeafItem(data) {
  return request({
    params:data,
    url: '/financial/budget/budgetPlait/getLeafItem',
    method: 'get'
  })
}

// 获取预算调整下拉
export function getAdjustmentLeafItem(data) {
  return request({
    params:data,
    url: '/financial/budget/budgetAdjustment/getLeafItem',
    method: 'get'
  })
}

// 更新预算编制设置
export function updateSetting(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/updateSetting',
    method: 'post'
  })
}