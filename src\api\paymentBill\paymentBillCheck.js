/*
 * @Description: 
 * @Version: 
 * @Author: luozc
 * @Date: 2023-09-27 16:49:07
 * @LastEditors: luozc
 * @LastEditTime: 2023-10-09 19:30:35
 */
import request from '@/utils/request'

/**
 * 查询支付票据对账列表
 * @param {*} data 
 * @returns 
 */
export function getPaymentBillCheckList(data) {
    return request({
        url: '/paymentbill/paymentBillCheck/page',
        method: 'post',
        data
    })
}

/**
 * 支付票据对账列表
 * @param {*} params 
 * @returns 
 */
export function billCheckList(params) {
    return request({
        url: '/paymentbill/paymentBillCheck/check',
        method: 'post',
        params
    })
}

export function delBillCheck (id) {
    return request({
        url: `/paymentbill/paymentBillCheck/delete?id=${id}`,
        method: 'post'
    })
}

/**
 * 导入对账数据
 * @param {*} params 
 * @returns 
 */
export function saveExport(params) {
    return request({
        url: '/paymentbill/paymentBillCheck/saveImportTemp',
        method: 'post',
        params
    })
}

/**
 * 校验当前账户是否已存在数据
 * @param {*} params 
 * @returns 
 */
export function checkImport(params) {
    return request({
        url: '/paymentbill/paymentBillCheck/checkImport',
        method: 'post',
        params,
    })
}