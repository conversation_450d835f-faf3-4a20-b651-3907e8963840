/*
 * @Descripttion: 
 * @version: 
 * @Author: 未知
 * @Date: 2023-10-09 16:59:30
 * @LastEditors: Andy
 * @LastEditTime: 2023-12-04 14:15:28
 */
import request from '@/utils/request'

/*
export function demo(data) {
  return request({
    url: ``,
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}
*/

/*
export function demo(params) {
  return request({
    url: ``,
    method: 'get',
    params,
    // payload: true,
  })
}
*/

/* 列表 */
export function page(data) {
  return request({
    url: '/bill/billKeyword/page',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 保存/编辑 */
export function save(data) {
  return request({
    url: '/bill/billKeyword/save',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 查看 */
export function getLoad(id) {
  return request({
    url: `/bill/billKeyword/get/${id}`,
    method: 'get'
  })
}


/* 删除 */
export function deleteData(data) {
  return request({
    url: `/bill/billKeyword/delete`,
    method: 'post',
    data
  })
}

/* 根据机构编码查询所属街道的关键词 */
export function selectByOrgCode(params) {
  return request({
    url: `/bill/billKeyword/selectByOrgCode`,
    method: 'get',
    params
  })
}
