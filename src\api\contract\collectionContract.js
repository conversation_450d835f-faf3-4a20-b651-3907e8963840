import request from '@/utils/request'

// 收款合同列表数据
export function loadContractList(data) {
  return request({
    // url: '/contract/contractRegistration/pageX',
    url: '/contract/fsContract/findList',
    method: 'post',
    data: data
  })
}

// 获取收款合同类型
export function getContractTypeDict() {
  return request({
    url: '/contract/contractType/getContractTypeDict',
    method: 'get'
  })
}

// 编辑前校验
export function checkToEdit(data) {
  return request({
    url: '/contract/contractRegistration/checkToEdit',
    method: 'post',
    data: data
  })
}

// 获取收款合同详情
export function loadContract(id, _ood) {
  return request({
    // url: `/contract/contractRegistration/loadX/${id}` + (_ood ? '?_ood_=true' : ''),
    url: `/contract/fsContract/findDetail`,
    method: 'post',
    data: { contractId: id }
  })
}

// 获取乙方信息列表
export function getSecondMsgList(data) {
  return request({
    url: '/contract/contractRegistration/getSecondMsgList',
    method: 'post',
    data: data
  })
}

// 获取乙方地址
export function getSecondAddresssTree(params) {
  return request({
    url: `/contract/contractRegistration/getSecondAddresssTree`,
    method: 'get',
    params: params
  })
}

// 获取乙方行业类型
export function getIndustryTypeTree() {
  return request({
    url: '/contract/contractRegistration/getIndustryTypeTree',
    method: 'get'
  })
}

// 获取资产列表
export function getAssetsData(data) {
  return request({
    url: '/contract/contractRegistration/getAssetsData/0',
    method: 'post',
    data: data
  })
}

// 获取合同年限
export function getDiffDate(data) {
  return request({
    url: `/contract/tools/getDiffDate?beginDate=${data.beginDate}&endDate=${data.endDate}`,
    method: 'get',
    data: data
  })
}

// 获取每期应收款列表
export function receivablesDetail(data) {
  return request({
    // url: '/contract/contractRegistration/create_receivables_detail',
    url: '/contract/fsContract/calculationContractPrice',
    method: 'post',
    data: data
    // payload: true
  })
}

// 合同递增周期表格
export function incrementalDetail(data) {
  return request({
    url: '/contract/contractExpenseIncremental/create_incremental_detail',
    method: 'post',
    data: data
  })
}

// 合同关联资产校验
export function checkAssetDuplicate(data) {
  return request({
    url: '/contract/contractRegistration/checkAssetContractIsDuplicate',
    method: 'post',
    data: data
  })
}

// 保存合同
export function saveContract(data) {
  return request({
    // url: '/contract/contractRegistration/saveX',
    url: '/contract/fsContract/saveA',
    method: 'post',
    data: data,
    payload: true
  })
}

// 删除合同
export function removeContract(data) {
  return request({
    url: '/contract/contractRegistration/deleteX',
    method: 'post',
    data: data
  })
}

// 合同提交
export function submitBatch(data) {
  return request({
    url: '/contract/workflow/submitBatch',
    method: 'post',
    data: data
  })
}

// 合同撤销提交
export function unSubmit(data) {
  return request({
    url: '/contract/workflow/revocation',
    method: 'post',
    data: data
  })
}

// 检查合同终止状态
export function checkContractTermStatus(id) {
  return request({
    url: '/contract/contractRegistration/checkContractTermStatus?id=' + id,
    method: 'get'
  })
}

// 检查合同应收款是否收完
export function checkIsCollectedDone(id) {
  return request({
    url: '/contract/contractRegistration/checkIsCollectedDone?id=' + id,
    method: 'get'
  })
}

// 合同直接终止，不走流程
export function abendContract(data) {
  return request({
    url: '/contract/contractRegistration/abend',
    method: 'post',
    data
  })
}

// 检查合同收款
export function checkRecevise(params) {
  return request({
    url: '/contract/contractTermination/checkRecevise',
    method: 'post',
    params
  })
}

// 合同终止并提交
export function saveAndSubmit(data) {
  return request({
    url: '/contract/contractTermination/saveAndSubmit',
    method: 'post',
    data
  })
}

// 获取合同每期应收款
export function getReceivablesDetail(id) {
  return request({
    url: '/contract/contractRegistration/getReceivablesDetail/' + id,
    method: 'get'
  })
}

// 获取合同变更记录
export function getRecord(data) {
  return request({
    url: '/contract/contractRegistration/changeRecordData',
    method: 'post',
    data
  })
}

// 获取合同关联的资产
export function getAssetsDataPageData(data) {
  return request({
    url: '/contract/contractRegistration/getAssetsDataPageData',
    method: 'post',
    data
  })
}

// 检查合同终止
export function contractTerminationInfoCheck(data) {
  return request({
    url: '/contract/contractTermination/contractTerminationInfoCheck',
    method: 'post',
    data
  })
}

// 获取合同打印数据
export function getPrintInfo(id) {
  return request({
    url: '/contract/contractRegistration/print/' + id,
    method: 'get'
  })
}

// 获取已变更合同列表
export function contractChangesList(data) {
  return request({
    url: '/contract/contractChanges/pageList',
    method: 'post',
    data
  })
}

// 获取待变更合同列表
export function pageContractForChanges(data) {
  return request({
    url: '/contract/contractChanges/pageContractForChanges',
    method: 'post',
    data
  })
}

// 获取合同变更的新、旧合同信息
export function loadContractChanges(data) {
  return request({
    url: '/contract/contractChanges/load',
    method: 'post',
    data
  })
}

// 合同查询列表
export function enquiryContractList(data) {
  return request({
    url: '/contract/enquiryContract/pageX',
    method: 'post',
    data
  })
}

// 合同跟踪前判断
export function checkTrackType(data) {
  return request({
    url: '/contract/contractRegistration/checkTrackType',
    method: 'post',
    data
  })
}

// 获取合同变更时后台参数
export function getContractChangeInfo(id) {
  return request({
    url: '/contract/contractChanges/addContract/init/' + id,
    method: 'get'
  })
}

// 合同变更保存
export function saveContractChange(data) {
  return request({
    url: '/contract/contractChanges/saveChanges',
    method: 'post',
    data
  })
}

// 提交合同变更
export function submitContractChange(data) {
  return request({
    url: '/contract/workflow/submit',
    method: 'post',
    data
  })
}

// 撤销提交合同变更
export function unSubmitContractChange(data) {
  return request({
    url: '/contract/workflow/revocation',
    method: 'post',
    data
  })
}

// 删除合同变更
export function removeContractChange(data) {
  return request({
    url: '/contract/contractChanges/deleteChanges',
    method: 'post',
    data
  })
}

// 合同收款列表
export function contractCollectionList(data) {
  return request({
    url: '/contract/contractCollection/list',
    method: 'post',
    data
  })
}

// 合同待收款列表
export function collectionList(data, params) {
  return request({
    url: '/contract/contractCollection/regisitrationList',
    method: 'post',
    data,
    params
  })
}

// 合同收款获取合同原始数据
export function collectionInitData(data, params) {
  return request({
    url: '/contract/contractCollection/collectionInitData',
    method: 'post',
    data,
    params
  })
}

// 获取合同的全部收款项目
export function contractCollectionCombobox(params) {
  return request({
    url: '/contract/contractCollection/combobox',
    method: 'get',
    params
  })
}

// 获取合同应收款列表
export function loadReceivalbesDetails(data) {
  return request({
    url: '/contract/contractCollection/loadReceivalbesDetails',
    method: 'post',
    data
  })
}

// 保存合同收款
export function saveCollection(data) {
  return request({
    url: '/contract/contractCollection/saveCollection',
    method: 'post',
    data
  })
}

// 获取合同首付款详情
export function getOpreationDetial(data) {
  return request({
    url: '/contract/contractCollection/getOpreationDetial',
    method: 'post',
    data
  })
}

// 获取合同历史收款列表
export function historyCollection(data) {
  return request({
    url: '/contract/contractCollection/historyCollection',
    method: 'post',
    data
  })
}

// 保存合同退款
export function saveRefund(data) {
  return request({
    url: '/contract/contractCollection/saveRefund',
    method: 'post',
    data
  })
}

// 合同收付款删除
export function deleteColRefund(data) {
  return request({
    url: '/contract/contractCollection/delete',
    method: 'post',
    data
  })
}

// 保存交易合同
export function saveContractSign(data) {
  return request({
    url: '/trading/deal/saveContractSign',
    method: 'post',
    data
  })
}
// 导出合同清查 明细
export function exportCheckExcel(data) {
  return request({
    url: '/contract/fsContract/exportCheckExcel',
    method: 'get',
    params: data,
    responseType: 'blob' // 这个不能少
  })
}
// 导出合同清查 汇总
export function exportSummaryExcel(data) {
  return request({
    url: '/contract/fsContract/exportSummaryExcel',
    method: 'get',
    params: data,
    responseType: 'blob' // 这个不能少
  })
}
// 合同清查 list
export function contractInventory(data) {
  return request({
    url: '/contract/fsContract/contractInventory',
    method: 'post',
    data
  })
}
// 合同清查 汇总list
export function contractInventoryCount(data) {
  return request({
    url: '/contract/fsContract/contractInventoryCount',
    method: 'post',
    data
  })
}
