/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-11-08 10:56:09
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-08 13:58:31
 */
import request from '@/utils/request'

/**
 * @name:获取账套是否初始化的标志
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getCashierFlag(params) {
  return request({
    url: '/financial/cashier/cashierItemBalance/getCashierFlag',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:获取出纳核算列表
 * @param {String} booksId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/cashier/cashierItemBalance/page',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存初始化结果
 * @param {String} booksId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveTableList(data) {
  return request({
    url: '/financial/cashier/cashierItemBalance/saveTableList',
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:保存支出类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/cashier/cashierItemBalance/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function init(data) {
  return request({
    url: `/financial/cashier/cashierItemBalance/init`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:撤销初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function revokeInit(data) {
  return request({
    url: `/financial/cashier/cashierItemBalance/revokeInit`,
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:撤销初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function checkBalance(data) {
  return request({
    url: `/financial/cashier/cashierItemBalance/checkBalance`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:同步余额
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function synchronize(data) {
  return request({
    url: `/financial/cashier/cashierItemBalance/synchronize`,
    method: 'post',
    data,
    withCredentials: true
  })
}
