
import request from '@/utils/request'

// 库存数据-不分页
export function seleteList (data) {
  return request({
    url: '/bill/billStock/seleteList',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 获取常用收款人信息列表
export function billCommonInfoPayeeList (data) {
  return request({
    url: '/bill/billCommonInfoPayee/getInfoList',
    method: 'post',
    data
  })
}
// 保存常用收款人信息
export function billCommonInfoPayeeSave (data) {
  return request({
    url: '/bill/billCommonInfoPayee/save',
    method: 'post',
    data
  })
}
// 删除收款项目信息
export function billProjectDelete (data) {
  return request({
    url: '/bill/billCommonInfoChargeItem/delete',
    method: 'post',
    data
  })
}
// 删除常用收款人信息
export function billCommonInfoPayeeDelete (data) {
  return request({
    url: '/bill/billCommonInfoPayee/delete',
    method: 'post',
    data
  })
}
// 获取摘要信息
export function getHistorySummary (params) {
  return request({
    url: '/bill/billDetail/getHistorySummary',
    method: 'get',
    params
  })
}
// 保存常用项目信息列表
export function billCommonInfoChargeItemList (data) {
  return request({
    url: '/bill/billCommonInfoChargeItem/getInfoList',
    method: 'post',
    data
  })
}
// 获取常用项目信息
export function billCommonInfoChargeItemSave (data) {
  return request({
    url: '/bill/billCommonInfoChargeItem/save',
    method: 'post',
    data
  })
}
// initPage
export function billDetailInit (data) {
  return request({
    url: '/bill/billDetail/list/init',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
export function billDetailpage (data) {
  return request({
    url: '/bill/billDetail/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 旧系统红冲保存
export function formerBillRefund (data) {
  return request({
    url: '/bill/billDetail/formerBillRefund',
    method: 'post',
    data: data
    // withCredentials: true
  })
}
// 开出收款收据
export function billDetailAddReceipt (data) {
  return request({
    url: '/bill/billDetail/saveWithItems',
    method: 'post',
    data: data
    // withCredentials: true
  })
}
// 批量开出收款收据
export function billDetailBatchReceipt (data) {
  return request({
    url: '/bill/billDetail/batchInsertWithItems',
    method: 'post',
    data: data
    // withCredentials: true
  })
}
export function billDetailbillTypeLoad (id) {
  return request({
    url: `/bill/billType/load/${id}`,
    method: 'get'
    // withCredentials: true
  })
}
// 开出定额收据
export function billDetailAddQuota (data) {
  return request({
    url: `/bill/billDetail/save`,
    method: 'post',
    data
    // withCredentials: true
  })
}
export function getPrintBills (data) {
  return request({
    url: `/bill/billDetail/loadPrintBills?detailId=${data.id}`,
    method: 'get',
    data,
    withCredentials: true
  })
}
export function getbillUserDetail (data) {
  return request({
    url: `/bill/billDetail/loadDetail?detailId=${data.id}`,
    method: 'get',
    data,
    withCredentials: true
  })
}
// 票据详情
export function billDetailLoad (id) {
  return request({
    url: `/bill/billDetail/load/${id}`,
    //  url: `/bill/billDetail/loadDetail/detailId=${id}`,
    method: 'get'
  })
}
// 获取打印样式
export function getbillPrintStyle (data) {
  return request({
    url: `/bill/billPrintStyle/loadPrintStyle?templateId=&billTypeId=${data.billTypeId}`,
    method: 'get',
    data
  })
}
// 校验票据上一张是否打印
export function checkPrint (data) {
  return request({
    url: `/bill/billDetail/checkPrint?detailId=${data.id}`,
    method: 'get',
    data
  })
}
// 更新打印状态
export function updatePrint (data) {
  return request({
    url: `/bill/billDetail/updatePrintState`,
    method: 'post',
    data
  })
}
// 票据作废
export function execInvalid (data) {
  return request({
    url: `/bill/billDetail/execInvalid`,
    method: 'post',
    data
  })
}
// 票据反作废
export function execUnInvalid (id) {
  return request({
    url: `/bill/billDetail/execUnInvalid/${id}`,
    method: 'post'
  })
}
// 获取明细表格
export function getItemList (id) {
  return request({
    url: `/bill/billDetail/getItemList/${id}`,
    method: 'get'
  })
}
// 结算
export function billSettle (data) {
  return request({
    url: `/bill/billDetail/settle`,
    method: 'post',
    data
  })
}
// 取消结算 cancelSettle
export function cancelBillSettle (data) {
  return request({
    url: `/bill/billDetail/cancelSettle`,
    method: 'post',
    data
  })
}
// 获取作废原因枚举
export function billCombotree (prarms) {
  return request({
    url: `/system/dictionary/combotree?path=${prarms}`,
    method: 'get',
    data: prarms,
    withCredentials: true
  })
}

/**
 * @name:收款票据那里获取预算列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getListPlaitItem (params) {
  return request({
    url: `/bill/billDetail/getListPlaitItem`,
    method: 'get',
    params
  })
}

export function getContractList (data) {
  return request({
    url: `/bill/billDetail/getContractList`,
    method: 'post',
    data
  })
}
/**
 * @name:获取当前登录的机构
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getLoginAreaOrg (params) {
  return request({
    url: `/bill/billDetail/getLoginAreaOrg`,
    method: 'get',
    params
  })
}
/**
 * @name:获取预算项目控制
 * @param {String} orgId 机构id
 * @param {String} year 年份
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBudgetInfo (params) {
  return request({
    url: `/bill/billDetail/getBudgetInfo`,
    method: 'get',
    params
  })
}

/**
 * @name:获取合同
 * @param {String} orgId 机构id
 * @param {String} year 年份
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getWXContractList (data) {
  return request({
    url: `/bill/billDetail/getWXContractList`,
    method: 'post',
    data
  })
}

/**
 * @name:获取合同收款详情
 * @param {String} orgId 机构id
 * @param {String} year 年份
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getWXContractCollectList (data) {
  return request({
    url: `/bill/billDetail/getWXContractCollectList`,
    method: 'post',
    data
  })
}

/**
 * @name:获取合同收款项目列表
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getWXCollectionItem (params) {
  return request({
    url: `/bill/billDetail/getWXCollectionItem`,
    method: 'get',
    params
  })
}

/**
 * @name:获取已收金额列表
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getActualAmountList (params) {
  return request({
    url: `/bill/billDetail/getActualAmountList`,
    method: 'get',
    params
  })
}

/**
 * @name:反作废
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancelInvalid (data) {
  return request({
    url: `/bill/billDetail/cancelInvalid`,
    method: 'post',
    data
  })
}


//金额作废

export function invalidAmount (data) {
  return request({
    url: `/bill/billDetail/invalidAmount`,
    method: 'post',
    data
  })
}


//金额反作废

export function invalidAmountCancel (data) {
  return request({
    url: `/bill/billDetail/invalidAmountCancel`,
    method: 'post',
    data
  })
}

//红冲

export function billOffset (data) {
  return request({
    url: `/bill/billDetail/fullRefund`,
    method: 'post',
    data
  })
}
//红冲未收款

export function unpaidRefund (data) {
  return request({
    url: `/bill/billDetail/unpaidRefund`,
    method: 'post',
    data
  })
}

//修改开票方式
export function updateInvoicingStage (data) {
  return request({
    url: `/bill/billDetail/updateInvoicingStage`,
    method: 'post',
    data
  })
}

//修改开票方式
export function updateInvoicingStage2 (data) {
  return request({
    url: `/bill/billDetail/updateInvoicingStage2`,
    method: 'post',
    data
  })
}
