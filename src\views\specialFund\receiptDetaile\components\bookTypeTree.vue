<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-26 14:15:56
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-04 10:02:23
-->
<template>
  <el-dialog title="关联会计科目" :visible.sync="dialogVisible" width="45%" :before-close="handleClose">
    <div>
      <!-- <div>
        <el-radio-group v-model="itemTypeId" size="medium" @change="itemTypeChange">
          <el-radio-button
            v-for="(item,index) in itemTypes"
            :key="index"
            :label="item.accountingItemTypeId"
          >{{ item.accountingItemTypeName }}</el-radio-button>
          <el-radio-button label>全部</el-radio-button>
        </el-radio-group>
      </div> -->
      <div style="height:420px;margin-top:5px ;">
        <el-tree
          :data="dataTree"
          :props="defaultProps"
          :default-expanded-keys="[0]"
          node-key="id"
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <span v-if="data.id == 0" class="el-icon-folder">{{ node.label }}</span>
            <span
              v-else-if="data.id != 0 && data.children != undefined"
              class="el-icon-folder"
            >{{ data.itemCode }}-{{ data.itemTitle }}</span>
            <span v-else class="el-icon-document">{{ data.itemCode }}-{{ data.itemTitle }}</span>
            <!-- <span
              v-else-if="data.id != 0 && data.children != undefined"
              class="el-icon-folder"
            >({{ data.itemCode }}){{ data.itemTitle }}</span>
            <span v-else class="el-icon-document">({{ data.itemCode }}){{ data.itemTitle }}</span> -->
          </span>
        </el-tree>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getAccountingItemList, updateAccountingItem } from '@/api/specialFund/receipt.js'
export default {
  data () {
    return {
      dialogVisible: false,
      itemTypes: [],
      itemTypeId: '',
      data: [],
      dataTree: [],
      defaultProps: {
        children: 'children',
        label: 'itemName',
        itemCode: 'itemCode'
      },
      nodeData: {},
      valueName: '',
      ids: '',
      orgId: '',
      year: ''
    }
  },
  methods: {
    open (ids, orgId, arrivalDate) {
      this.nodeData = {}
      this.ids = ids
      this.orgId = orgId
      this.year = arrivalDate.split('-')[0]
      this.getBaseItems()
      this.dialogVisible = true
    },
    save () {
      if (this.nodeData.itemCode == undefined || this.nodeData.id == undefined) {
         return this.$message.warning(this.$t('请选择会计科目！'))
      }
      if (this.nodeData.children == undefined) {
        updateAccountingItem({ receiptIds: this.ids, accountingItemName: this.nodeData.itemTitle, accountingItemId: this.nodeData.id, accountingItemCode: this.nodeData.itemCode, year: this.year }).then(res => {
          if (res.returnCode == 0) {
            this.$emit('refreshTable')
            this.dialogVisible = false
          }
        })
      } else {
         return this.$message.warning(this.$t('请选择会计科目！'))
      }
    },
    // getBaseItemTypes () {
    //   getBaseItemTypes().then(res => {
    //     if (res.returnCode == 0) {
    //       this.itemTypes = res.data
    //       this.itemTypeId = res.data[0].id
    //       this.getBaseItems()
    //     }
    //   })
    // },
    getBaseItems () {
      const params = {
        orgId: this.orgId,
        year: this.year
      }
      getAccountingItemList(params).then(res => {
        if (res.returnCode == 0) {
          this.itemTypes = [
            ...new Map(
              res.data.map(item => [item.accountingItemTypeId, item])
            ).values()
          ]
          this.itemTypeId = this.itemTypes[0].accountingItemTypeId
          this.data = this.convertToJsonTree(res.data, '0')
          this.dataTree = this.data
          // 保存tree数据
          // const dataTree = this.data.filter(item => item.accountingItemTypeId == this.itemTypeId)
          // this.dataTree = [
          //   {
          //     id: 0,
          //     itemName: '会计科目',
          //     children: dataTree
          //   }
          // ]
        }
      })
    },
    handleClose () {
      this.dialogVisible = false
    },
    handleNodeClick (data) {
      this.nodeData = data
    },
    // 处理会计科目数据
    convertToJsonTree (list, parent) {
      const tree = []
      list.forEach(item => {
        if (item.parentId == parent) {
          const children = this.convertToJsonTree(list, item.id) // 递归查找子节点
          if (children.length) {
            item.children = children
          }
          tree.push(item)
        }
      })
      return tree
    },
    itemTypeChange (val) {
      let dataTree = []
      if (val == '') {
        dataTree = this.data
      } else {
        dataTree = this.data.filter(item => item.accountingItemTypeId == val)
      }
      this.dataTree = [
        {
          id: 0,
          itemName: '会计科目',
          children: dataTree
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>

::v-deep .el-dialog__body {
  border: 1px solid #ccc;
  margin: 10px;
}
::v-deep .el-tree-node.is-current > .el-tree-node__content {
  background-color: #c8e4fe !important;
}
</style>
