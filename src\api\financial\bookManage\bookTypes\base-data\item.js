/*
 * @Descripttion:会计科目接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-26 11:31:32
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-04 15:09:28
 */

import request from '@/utils/request'

/**
 * @name:获取基础设置的科目类型
 * @param {Object} data
 * {
    itemCode: '编号'
    itemTitle: '名称'
    booktypesId: '登陆的账套id'
    accountingItemTypeId: '账套类型id'
    parentId: '父级id'
    page: 1
    rows: 20
  }
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/booktypes/basedata/item/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:根据bookId和itemTypeId获取会计科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getAccountingItemList(data) {
  return request({
    url: '/financial/booktypes/basedata/item/getAccountingItemList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
/**
 * @name:获取会计科目树
 * @param {String} booktypesId 登录的账套id
 * @param {String} nodeType
 * @param {String} itemTypeId 账套类型id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function tree({ id = 0, ...rest }) {
  return request({
    url: '/financial/booktypes/basedata/item/tree',
    method: 'post',
    params: rest,
    data: { id },
    withCredentials: true
  })
}

/**
 * @name:获取会计科目新增初始化信息
 * @param {*} id 详情的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function init(params) {
  return request({
    url: `/financial/booktypes/basedata/item/add/init`,
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:获取会计科目详情
 * @param {*} id 详情的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/booktypes/basedata/item/load/${id}`,
    method: 'get',
    params: {},
    withCredentials: true
  })
}

/**
 * @name:保存会计科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/booktypes/basedata/item/save`,
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:删除会计科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/booktypes/basedata/item/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:批量设置辅助核算
 * @param {*} params
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveAssist({ params, data }) {
  return request({
    url: `/financial/booktypes/basedata/item/saveAssist`,
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:批量设置辅助核算
 * @param {*} params
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveImportTemp(data) {
  return request({
    url: `/financial/booktypes/basedata/item/saveImportTemp`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取基础的会计科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBaseItemList(data) {
  return request({
    url: `/financial/booktypes/basedata/item/getBaseItemList`,
    method: 'post',
    data,
    withCredentials: true
  })
}

