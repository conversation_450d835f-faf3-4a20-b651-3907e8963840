<template>
  <div v-loading="loading" class="gever-form detail">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="170px"
      :disabled="type == 'view'"
    >
		<div class="gever-title">{{ $t('收款记录明细') }}</div>
		<div class="form-db">
			<el-form-item :label="$t('FID')" prop="fid">
				<gever-input v-model="contentForm.fid" />
			</el-form-item>
			<el-form-item :label="$t('收款记录ID')" prop="receiveId">
				<gever-input v-model="contentForm.receiveId" />
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('序号')" prop="序号">
				<gever-input v-model="contentForm.序号" />
			</el-form-item>
			<el-form-item :label="$t('收款类别编号')" prop="receiveItemCode">
				<gever-input v-model="contentForm.receiveItemCode" />
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('收款类别名称')" prop="receiveItemName">
				<gever-input v-model="contentForm.receiveItemName" />
			</el-form-item>
			<el-form-item :label="$t('金额')" prop="money">
				<gever-input v-model="contentForm.money" />
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('备注')" prop="remark">
				<gever-input v-model="contentForm.remark" />
			</el-form-item>
		</div>
    </el-form>
  </div>
</template>

<script>
import {
  loadQrPaymentReceiveDetailInfoApi,saveQrPaymentReceiveDetailApi,updateQrPaymentReceiveDetailApi} from '@/api/paymentQr/qrPaymentReceiveDetail.js'
export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
	  loading: false,
      contentForm: {},
      rules: {
        
      }
    }
  },
  created () {
    this.initPage()
  },
  methods: {
    initPage() {
      if (this.type === 'add') {
        // 新增页面初始化
      } else if (this.type === 'view' || this.type === 'edit') {
        // 查看、编辑页面初始化
        this.loading = true
        loadQrPaymentReceiveDetailInfoApi(this.id).then(res => {
          this.contentForm = res.data
        }).finally(() => {
          this.loading = false
        })
      }
    },
    handleBatchIdChange(systemBatchId) {
      this.contentForm.systemBatchId = systemBatchId
    },
    
    save() {
      this.$refs['_geverFormRef'].validate(valid => {
        if (!valid) {
          return this.$message.warning(this.$t('请完善表单信息'))
        }
        this.loading = true
        if (this.contentForm.id) {
          updateQrPaymentReceiveDetailApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        } else {
          saveQrPaymentReceiveDetailApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>