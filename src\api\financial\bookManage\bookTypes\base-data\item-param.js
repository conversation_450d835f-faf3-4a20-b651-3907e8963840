/*
 * @Descripttion:会计参数接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-26 10:43:17
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-02 17:32:14
 */
import request from '@/utils/request'

/**
 * @name:获取基础设置的科目类型
 * @param {Object} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/booktypes/basedata/itemParam/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:加载
 * @param {String} booktypesId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/booktypes/basedata/itemParam/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:
 * @param {*} id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listByBooksTypeId(data) {
  return request({
    url: `/financial/booktypes/basedata/itemParam/listByBooksTypeId`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存
 * @param {String} booktypesId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/booktypes/basedata/itemParam/save`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:保存
 * @param {String} booktypesId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/booktypes/basedata/itemParam/delete`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取会计参数设置
 * @param {String} booktypesId 账套类型id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getByBooksTypeId(params) {
  return request({
    url: `/financial/booktypes/basedata/itemParam/getByBooksTypeId`,
    method: 'get',
    params,
    withCredentials: true
  })
}
