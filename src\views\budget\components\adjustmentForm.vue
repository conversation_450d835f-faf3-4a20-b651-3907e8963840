<template>
  <div>
    <public-drawer
      :title="title"
      :visible.sync="dialogVisible"
      :size="70"
      destroy-on-close
      custom-class="budget-adjustment-dialog"
      :close-on-click-modal="false"
      :append-to-body="false"
      :buttons="
        buttons.length
          ? buttons
          : [
            {
              type: 'default',
              text: '关闭',
              buttonStatus: type !== '查看',
              callback: () => {
                dialogVisible = false
              }
            },
            {
              type: 'primary',
              text: '保存',
              loading: saveLoading,
              buttonStatus: type !== '查看',
              callback: handleSure
            }
          ]
      "
      @closed="closed"
    >
      <el-form
        ref="_formSubmitRef"
        :model="form"
        label-width="140px"
        :rules="formRules"
        :disabled="activeTab === 'file' ? false : type === '查看'"
      >
        <el-row>
          <el-col :span="24" class="gever-title">{{ $t('调整信息') }}</el-col>
          <el-col :span="8">
            <el-form-item prop="adjType" label="调整类型">
              <gever-select
                v-model="form.adjType"
                :options="optionsMap.adjType"
                :clearable="false"
                @change="handleAdjType"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="projectName" label="项目名称">
              <gever-input v-model="form.projectName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="applyTime" label="申请时间">
              <el-date-picker
                v-model="form.applyTime"
                type="date"
                value-format="yyyy-MM-dd"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="adjResonse" label="调整原因">
              <gever-input
                v-model="form.adjResonse"
                type="textarea"
                :rows="3"
                :maxlength="300"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          style="min-height: 580px"
          :class="
            $route.path === '/financial/budget/budgetApproval' && istodolist
              ? 'approval'
              : ''
          "
        >
          <el-col :span="24" class="gever-title">{{ $t('高级设置') }}</el-col>
          <el-col :span="24">
            <SeniorTabSetting
              ref="_seniorTabRef"
              :is-adj="true"
              :type="type"
              :tab-map="tabMap"
              :tab-table="tabTable"
              :adj-type="form.adjType"
              :loading="tabTable.loading"
              :subject-list="subjectList"
              :contracts-visible.sync="contractsVisible"
              :accounting-item-type="optionsMap.accountingItemType"
              :non-profit-and-loss-items="tabTable.data.nonProfitAndLossItems"
              @fillSubjectData="fillSubjectData"
              @handleActiveTab="handleActiveTab"
              @handleAddRow="handleAddRow"
              @handleDelRow="handleDelRow"
              @handleSubmitSave="handleSubmitSave"
              @handleFileChange="handleFileChange"
              @handleSalaryAddRow="handleSalaryAddRow"
              @initNonProfit="initNonProfit"
              @disableOne="disableOne"
              @handleNonProfitAndLossAddRow="handleNonProfitAndLossAddRow"
              @handleTotalAdjustAmountDelta="handleTotalAdjustAmountDelta"
            />
          </el-col>
          <el-col v-show="activeTab === 'file'" :span="24">
            <!-- 附件 -->
            <FileDetail
              :business-id="form.id"
              :disabled="type === '查看'"
              type="adjustmentForm"
              @handleFileChange="handleFileChange"
            />
          </el-col>
          <el-col
            v-if="
              ['incomeItems', 'expendItems', 'nonProfitAndLossItems'].includes(
                activeTab
              )
            "
            :span="24"
          >
            <h3>
              {{ $t('本年计划数合计金额：') }}
              <span class="text-danger font16">
                {{ $t(formatMoney(amountCount)) }}
              </span>
              {{ $t(' 元') }}
            </h3>
          </el-col>
        </el-row>
      </el-form>
      <slot name="footer" />
    </public-drawer>
    <!-- 选择合同-->
    <budget-choose :visible.sync="contractsVisible" @confirm="saveContracts" />
  </div>
</template>
<script>
import mixins from './mixins'
import SeniorTabSetting from './seniorTabSetting.vue'
import { formatMoney, incrementCode } from '@/utils/index.js'
import { formRules, tabMap } from '../budgetAdjustment/config'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('financial')
import _ from 'lodash'
import {
  save,
  details,
  getItemData,
  checkCanAdd
} from '@/api/budget/budgetAdjustment'
import {
  details as settingDetails,
  getByBooksTypeId
  // getRentalIncomeItemList
} from '@/api/budget/budgetItemSetting'
export default {
  name: 'AdjustmentForm',
  components: {
    SeniorTabSetting,
    BudgetChoose: () => import('./BudgetChoose.vue'),
    FileDetail: () => import('./FileDetail.vue')
  },
  mixins: [mixins],
  props: {
    istodolist: {
      type: Boolean,
      default: false
    },
    optionsMap: {
      type: Object,
      default: () => ({})
    },
    buttons: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: '查看'
    },
    propsTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formRules,
      batchId: '',
      tabMap: _.cloneDeep(tabMap),
      form: {},
      booksLevel: {},
      activeTab: 'incomeItems',
      drawerLoading: false,
      dialogVisible: false,
      saveLoading: false,
      contractsVisible: false,
      serverItemData: {
        incomeItems: [], // 年度预算收入
        expendItems: [], // 年度预算支出
        rentalIncomes: [], // 租金明细
        adjExpContracts: [], // 期满合同租金标准调整
        salaryBudgets: [], // 工资预算
        nonProfitAndLossItems: [] // 非损益类明细科目
      },
      tabTable: {
        data: {
          incomeItems: [], // 年度预算收入
          expendItems: [], // 年度预算支出
          rentalIncomes: [], // 租金明细
          adjExpContracts: [], // 期满合同租金标准调整
          salaryBudgets: [], // 工资预算
          nonProfitAndLossItems: [] // 非损益类明细科目
        },
        loading: false
      },
      ids: {},
      annualBalance: null
    }
  },
  computed: {
    ...mapState(['books']),
    title() {
      if (!this.propsTitle) {
        return this.$t(`${this.type}预算调整`)
      }
      return this.$t(this.propsTitle)
    },
    incomeCount() {
      const allData = this.tabTable.data.incomeItems.filter(
        (cur) => cur.accountingItemLevel === 1
      )
      const count = allData.reduce(
        (total, item) => total + (item.adjustAmountAfter * 100 ?? 0),
        0
      )
      return count / 100
    },
    expendCount() {
      const allData = this.tabTable.data.expendItems.filter(
        (cur) => cur.accountingItemLevel === 1
      )
      const count = allData.reduce(
        (total, item) => total + (item.adjustAmountAfter * 100 ?? 0),
        0
      )
      return count / 100
    },
    nonProfitAndLossCount() {
      const allData = this.tabTable.data.nonProfitAndLossItems.filter(
        (cur) => cur.accountingItemLevel === 1
      )
      const count = allData.reduce(
        (total, item) => total + (item.adjustAmountAfter * 100 ?? 0),
        0
      )
      return count / 100
    },
    amountCount() {
      if (this.activeTab === 'incomeItems') {
        return this.incomeCount
      }
      if (this.activeTab === 'expendItems') {
        return this.expendCount
      }
      if (this.activeTab === 'nonProfitAndLossItems') {
        return this.nonProfitAndLossCount
      }
      return 0
    }
  },
  watch: {
    'form.adjType'(val) {
      console.log('this.books is ', this.books)
      if (this.books?.createYear) {
        const row = this.optionsMap.adjType.find((cur) => cur.id === val)
        this.$set(
          this.form,
          'projectName',
          `${this.books.createYear}年${row?.text}预算`
        )
      }
    }
  },
  created() {
    console.log(this.optionsMap, '---------------------------------')
  },
  methods: {
    formatMoney,
    // 处理调整类型切换
    handleAdjType() {
      this.setItemData(this.serverItemData)
    },
    // 新增一行工资预算明细
    handleSalaryAddRow() {
      this.tabTable.data[this.activeTab].push({
        level: '',
        postName: '',
        accountintItemTitle: '',
        accountintItemCode: '',
        totalSalary: 0,
        baseSalary: 0,
        accruedSalary: 0,
        performanceAward: 0,
        otherSalary: 0,
        tranCommSubsidy: 0,
        holidaySubsidy: 0,
        overtimeDutySubsidy: 0,
        postSubsidy: 0,
        annualLeaveSubsidy: 0,
        priceSubsidy: 0,
        highTemperatureSubsidy: 0,
        senioritySubsidy: 0,
        retirementSubsidy: 0,
        otherSubsidy: 0
      })
    },
    // 保存选择合同
    saveContracts(row) {
      // 当前row已在已选数据里
      if (
        this.tabTable.data?.[this.activeTab]?.some((cur) => cur.id === row.id)
      ) {
        return
      }
      const {
        id,
        contractCode,
        contractName,
        secondName,
        startDate,
        endDate,
        totalMoney,
        rentTotalMoney
      } = row
      let data = {}
      // 租金明细
      if (this.activeTab === 'rentalIncomes') {
        data = {
          id,
          contractCode,
          contractName,
          secondName,
          startDate,
          endDate,
          totalMoney,
          rentTotalMoney,
          rentYearMoney: 0
        }
      }
      // 期满合同
      if (this.activeTab === 'adjExpContracts') {
        data = {
          id,
          endDate,
          propertyName: '',
          originalLessee: secondName,
          openingArea: 0,
          originalPrice: 0,
          openingAreaPlanPrice: 0,
          planMonthRent: 0,
          leaseTerm: 0,
          rentIncrease: 0,
          contractTotalObject: 0,
          purposeRequire: '',
          bidBond: 0,
          disposalMethod: '',
          remark: ''
        }
      }
      // 租金收入 / 期满合同
      this.tabTable.data[this.activeTab].push(data)
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    },
    // 打开drawer
    showDrawer(data) {
      console.log('data is ', data)
      if (!data) {
        this.handleCheckCanAdd()
      } else {
        this.form = data
        this.dialogVisible = true
        this.getDetails()
      }
    },
    // 校验是否能新增
    handleCheckCanAdd() {
      checkCanAdd({ booksId: this.books.id }).then((res) => {
        if (res.returnCode === '0') {
          const { budgetSettingId, budgetPlaitId, annualBalance, isDeficit } = res.data
          this.annualBalance = annualBalance
          this.ids = { budgetSettingId, budgetPlaitId }
          this.dialogVisible = true
          this.handleSettingDetails(budgetSettingId, budgetPlaitId, isDeficit)
        } else {
          this.$message.warning(this.$t(res.message))
        }
      })
    },
    // 获取单条数据详情
    getDetails() {
      this.drawerLoading = true
      details(this.form.id)
        .then((res) => {
          if (res.returnCode === '0') {
            this.form = res.data
            this.$emit('getUpload', this.form.uploadFile)
            this.form.isDeficit = res.data.isDeficit
            this.annualBalance = res.data.annualBalance
            const budgetSetting = res.data.budgetSetting
            getByBooksTypeId({ booksTypeId: budgetSetting.booksTypeId }).then(
              (res) => {
                if (res.returnCode === '0') {
                  this.booksLevel = res.data
                }
              }
            )
            this.tabMap = this.tabMap.filter((cur) => budgetSetting[cur.key])
            this.getChildrenItemData(res.data.id, res.data.budgetPlaitId)
          } else {
            this.form = {
              ...this.form,
              id: '',
              budgetTotalIncome: 0,
              budgetTotalExpend: 0,
              budgetDifference: 0
            }
            this.$message.warning(this.$t(res.message))
          }
        })
        .finally(() => {
          console.log(this.form.procInstId, 2223)
          this.$emit('changebId', this.form.procInstId)
          this.drawerLoading = false
        })
    },
    // 设置子表数据
    setItemData(data) {
      const {
        incomeItems,
        expendItems,
        rentalIncomes,
        adjExpContracts,
        salaryBudgets,
        nonProfitAndLossItems
      } = data
      this.tabTable.data = {
        rentalIncomes:
          rentalIncomes?.map((cur) => {
            if (!cur?.items?.length) return cur
            const item = cur
            cur.items.forEach((row) => {
              const key =
                'accountingItemCode_' +
                row.accountingItemCode +
                '_' +
                row.accountingItemTitle
              cur[key] = row.value
            })
            delete cur.items
            return item
          }) ?? [],
        adjExpContracts: adjExpContracts ?? [],
        salaryBudgets:
          salaryBudgets?.map((cur) => {
            const code = []
            if (cur.accountintItemCode?.length > this.booksLevel.levelItem1) {
              let bookLen = 0
              for (const key in this.booksLevel) {
                if (
                  key.includes('levelItem') &&
                  this.booksLevel[key] &&
                  bookLen < cur.accountintItemCode.length
                ) {
                  bookLen += this.booksLevel[key]

                  code.push(cur.accountintItemCode.slice(0, bookLen))
                }
              }
            } else {
              code.push(cur.accountintItemCode)
            }
            return {
              ...cur,
              accountintItemCode: code
            }
          }) ?? [],
        nonProfitAndLossItems: nonProfitAndLossItems?.map((cur) => {
          const budgetAmount = cur?.budgetAmount ?? 0
          const lastYearAmount = cur?.lastYearAmount ?? 0
          return {
            ...cur,
            lastYearAmount: cur?.lastYearAmount ?? 0,
            difference: budgetAmount - lastYearAmount,
            ratio: isNaN(lastYearAmount / budgetAmount)
              ? 0
              : (lastYearAmount / budgetAmount) * 100,
            rentalIncome: cur.rentalIncome ?? 0
          }
        }),
        incomeItems: incomeItems.map((cur) => {
          const budgetAmount = cur?.budgetAmount ?? 0
          const lastYearAmount = cur?.lastYearAmount ?? 0
          return {
            ...cur,
            lastYearAmount: cur?.lastYearAmount ?? 0,
            difference: budgetAmount - lastYearAmount,
            ratio: isNaN(lastYearAmount / budgetAmount)
              ? 0
              : (lastYearAmount / budgetAmount) * 100,
            rentalIncome: cur.rentalIncome ?? 0
          }
        }),
        expendItems: expendItems.map((cur) => {
          const budgetAmount = cur?.budgetAmount ?? 0
          const lastYearAmount = cur?.lastYearAmount ?? 0
          return {
            ...cur,
            lastYearAmount: cur?.lastYearAmount ?? 0,
            difference: budgetAmount - lastYearAmount,
            ratio: isNaN(lastYearAmount / budgetAmount)
              ? 0
              : (lastYearAmount / budgetAmount) * 100,
            rentalIncome: cur.rentalIncome ?? 0
          }
        })
      }
    },
    // 获取子表数据
    getChildrenItemData(budgetAdjustmentId, budgetPlaitId) {
      this.tabTable.loading = true
      getItemData({
        budgetAdjustmentId,
        budgetPlaitId
      })
        .then((res) => {
          if (res.returnCode === '0') {
            this.serverItemData = res.data
            this.setItemData(res.data)
          } else {
            this.tabTable.data = {
              incomeItems: [], // 年度预算收入
              expendItems: [], // 年度预算支出
              rentalIncomes: [], // 租金明细
              adjExpContracts: [], // 期满合同租金标准调整
              salaryBudgets: [], // 工资预算
              nonProfitAndLossItems: [] // 非损益类明细科目
            }
            this.$message.warning(this.$t(res.message))
          }
        })
        .finally(() => {
          this.tabTable.loading = false
        })
    },
    // 获取预算设置数据
    handleSettingDetails(budgetSettingId, budgetPlaitId, isDeficit) {
      this.drawerLoading = true
      settingDetails(budgetSettingId)
        .then((res) => {
          if (res.returnCode === '0') {
            const { budgetYear, projectName } = res?.data ?? {}
            // if (res.data.zjsrmx) {
            //   this.handleRentalIncomeItemList(budgetSettingId)
            // }
            getByBooksTypeId({ booksTypeId: res.data.booksTypeId }).then(
              (res) => {
                if (res.returnCode === '0') {
                  this.booksLevel = res.data
                }
              }
            )
            if (this.type === '新增') {
              this.form = {
                id: '',
                adjType: 1,
                adjResonse: '',
                applyTime: new Date(),
                booksId: this.books.id,
                budgetPlaitId,
                unitName: this.books.booksName,
                isDeficit: isDeficit,
                projectName: `${budgetYear}年追加预算`
              }
            } else {
              this.form = {
                ...this.form,
                budgetPlaitId,
                projectName
              }
            }
            this.tabMap = this.tabMap.filter((cur) => res.data[cur.key])
            this.getChildrenItemData(
              this.type === '新增' ? '' : this.form.id,
              budgetPlaitId
            )
          }
        })
        .finally(() => {
          this.drawerLoading = false
        })
    },
    // 处理父节点和
    handleParentNode(code, level, budgetType, parentAccountingItemCode) {
      // 当前行父节点index
      const parentNodeInex = this.tabTable.data[budgetType].findIndex(
        (cur) => cur?.accountingItemCode === parentAccountingItemCode
      )
      // 当前行兄弟节点(含当前节点)
      const brotherNode = this.tabTable.data[budgetType].filter(
        (cur) =>
          cur?.accountingItemCode.includes(parentAccountingItemCode) &&
          cur?.accountingItemCode?.length === code.length
      )
      // 兄弟节点总额
      const brotherNodeTotal = brotherNode.reduce(
        (total, cur) => total + Number(cur?.adjustAmountDelta ?? 0),
        0
      )
      // 更新父节点的和
      this.$set(
        this.tabTable.data[budgetType][parentNodeInex],
        'adjustAmountDelta',
        brotherNodeTotal.toFixed(2) * 1
      )
      // 更新父节点其他数据
      this.$nextTick(() => {
        const row = this.tabTable?.data?.[budgetType]?.[parentNodeInex] ?? {}
        this.handleTotalAdjustAmountDelta({
          row,
          index: parentNodeInex,
          budgetType
        })
      })
      // 非一级科目继续递归计算父节点和
      if (level - 1 > 1) {
        // 当前行父节点code
        const parentCode =
          this.tabTable.data?.[budgetType]?.[parentNodeInex]
            .parentAccountingItemCode
        this.handleParentNode(parentCode, level - 1, budgetType, parentCode)
      }
    },
    // 计算本次调整数
    handleTotalAdjustAmountDelta(
      { row = {}, index, budgetType },
      isDelete = false
    ) {
      if (this.form.adjType == 2 && row.budgetAmount < 0) {
        this.$set(this.tabTable.data[budgetType], index, row)
      } else {
        if (!this.tabTable?.data[budgetType][index].adjustAmountDelta) {
          this.$set(
            this.tabTable?.data[budgetType][index],
            'adjustAmountDelta',
            0
          )
        }
        // 当前行时子科目则计算所有父科目总额
        if (row.accountingItemLevel > 1 && row.leafItem !== 2) {
          this.handleParentNode(
            row.accountingItemCode,
            row.accountingItemLevel,
            budgetType,
            row.parentAccountingItemCode
          )
        }
        if (isDelete) return
        // 调整后的预算 adjustAmountAfter = budgetAmount 本年计划 + adjustAmountDelta 本次调整
        // 本年调整数 totalAdjustAmountDelta = adjustAmountAfter 调整后预算数 - originalBudgetAmount 原始本年计划数
        const adjustAmountAfter =
          (Number(row.budgetAmount) + Number(row.adjustAmountDelta)).toFixed(
            2
          ) * 1
        const totalAdjustAmountDelta =
          (
            Number(adjustAmountAfter) - Number(row.originalBudgetAmount ?? 0)
          ).toFixed(2) * 1
        this.$set(
          this.tabTable.data[budgetType][index],
          'adjustAmountAfter',
          adjustAmountAfter
        )
        this.$set(
          this.tabTable.data[budgetType][index],
          'totalAdjustAmountDelta',
          totalAdjustAmountDelta
        )
      }
    },
    // 附件
    handleFileChange(batchId) {
      this.batchId = batchId
    },
    // 新增行
    handleAddRow({ sourceName, row, index }) {
      // 科目编码
      // let code = ''
      // 添加的索引
      let addIndex = index
      const curBrother = this.tabTable.data[sourceName].filter(
        (cur) =>
          cur.accountingItemCode.startsWith(row.accountingItemCode) &&
          cur.accountingItemCode.length > row.accountingItemCode.length
      )
      let code = `${row.accountingItemCode}001`
      if (curBrother?.length) {
        const item = curBrother[curBrother.length - 1]
        addIndex = this.tabTable.data[sourceName].findIndex(
          (cur) => cur.accountingItemCode === item.accountingItemCode
        )
        code = incrementCode(item.accountingItemCode)
      }

      const item = {
        leafItem: 1,
        budgetPrjType: 2,
        accountingItemParentId: row.accountingItemId,
        accountingItemId: row.accountingItemId,
        budgetAmount: !curBrother?.length ? row.budgetAmount : 0,
        accountingItemType: row.accountingItemType,
        adjustAmountAfter: !curBrother?.length ? row.adjustAmountAfter : 0,
        adjustAmountDelta: !curBrother?.length ? row.adjustAmountDelta : 0,
        curYearAmount: !curBrother?.length ? row.curYearAmount : 0,
        lastYearAmount: 0,
        originalBudgetAmount: 0,
        totalAdjustAmountDelta: !curBrother?.length
          ? row.totalAdjustAmountDelta
          : 0,
        accountingItemLevel: row.accountingItemLevel + 1,
        parentAccountingItemCode: row.accountingItemCode,
        accountingItemTitle: '',
        accountingItemCode: code
      }
      if (sourceName === 'expendItems') {
        item.extExp = 0
      }
      if (row.budgetPrjType === 3) {
        this.$set(this.tabTable.data[sourceName][index], 'budgetPrjType', 1)
        this.$set(this.tabTable.data[sourceName][index], 'projectBudget', 0)
      }
      this.tabTable.data[sourceName].splice(addIndex + 1, 0, item)
    },
    // 修改选中tab
    handleActiveTab(val) {
      this.activeTab = val
    },
    // 关闭抽屉清空数据
    closed() {
      this.tabTable.data = {
        incomeItems: [], // 年度预算收入
        expendItems: [], // 年度预算支出
        rentalIncomes: [], // 租金明细
        adjExpContracts: [], // 期满合同租金标准调整
        salaryBudgets: [], // 工资预算
        nonProfitAndLossItems: [] // 非损益类明细科目
      }
      this.serverItemData = {
        incomeItems: [], // 年度预算收入
        expendItems: [], // 年度预算支出
        rentalIncomes: [], // 租金明细
        adjExpContracts: [], // 期满合同租金标准调整
        salaryBudgets: [], // 工资预算
        nonProfitAndLossItems: [] // 非损益类明细科目
      }
      this.tabMap = _.cloneDeep(tabMap)
      this.form = { id: '' }
      this.ids = {}
      this.$refs?._seniorTabRef?.resetActiveTab()
      this.activeTab = 'incomeItems'
    },
    // 保存
    // eslint-disable-next-line complexity
    handleSure() {
      const bigType = ['incomeItems', 'expendItems', 'nonProfitAndLossItems']
      if (this.form.adjType === 1) {
        for (
          let index = 0;
          index <
          ['incomeItems', 'expendItems', 'nonProfitAndLossItems'].length;
          index++
        ) {
          const type = bigType[index]
          // eslint-disable-next-line no-unreachable-loop
          for (let i = 0; i < this.tabTable.data[type].length; i++) {
            const item = this.tabTable.data[type][i]
            if (item?.adjustAmountDelta < 0) {
              const typeEnum = {
                incomeItems: '年度预算收入',
                expendItems: '年度预算支出',
                nonProfitAndLossItems: '非损益类'
              }
              return this.$message.warning(
                `当调整类型为追加时, 【${typeEnum[type]}】第 ${
                  i + 1
                } 行的本次调整数不能小于0`
              )
            }
          }
        }
      }
      const budgetIncomeAmountData = this.tabTable.data.incomeItems.filter(
        (cur) => cur.accountingItemLevel === 1
      )
      const budgetExpendAmountData = this.tabTable.data.expendItems.filter(
        (cur) => cur.accountingItemLevel === 1
      )
      // * 100  和  / 100  的目的是处理浮点数精度（例如避免0.1 + 0.2 !== 0.3的问题）
      // 本年计划数合计-收入
      const budgetTotalIncome = budgetIncomeAmountData.reduce(
        (total, item) => {
          const amount = Number(item.budgetAmount ?? 0)
          return isNaN(amount) ? total : total + (amount * 100)
        },
        0
      ) / 100
      // 本年计划数合计-支出
      const budgetTotalExpend = budgetExpendAmountData.reduce(
        (total, item) => {
          const amount = Number(item.budgetAmount ?? 0)
          return isNaN(amount) ? total : total + (amount * 100)
        },
        0
      ) / 100
      // 本年计划数合计-非损益类
      // 预算调整时，选择追加，收入、支出至少其中一个大于追加前，且另一个不能小于追加前；但有个原则是收入-支出差额不能是负数；选择调整，收入、支出都不能大于调整前，收入-支出差额不能是负数
      if (
        !this.form.isDeficit &&
        this.form.adjType === 1 &&
        this.incomeCount < budgetTotalIncome &&
        this.expendCount < budgetTotalExpend
      ) {
        return this.$message.warning(
          this.$t(
            '保存失败! 当预算类型为【追加】时,年度预算收入合计或支出合计金额不能小于追加前'
          )
        )
      }
      if (
        this.form.adjType === 2 &&
        (this.incomeCount > budgetTotalIncome ||
          this.expendCount > budgetTotalExpend)
      ) {
        return this.$message.warning(
          this.$t(
            '保存失败! 当预算类型为【调整】时,调整后的预算收入合计或支出合计不能大于调整前'
          )
        )
      }

      if (
        !this.form.isDeficit &&
        this.expendCount > this.incomeCount + this.annualBalance
      ) {
        return this.$message.warning(
          this.$t(
            `【${
              this.form.adjType === 1 ? '追加' : '调整'
            }】年度预算支出合计金额不能大于年度预算收入合计金额`
          )
        )
      }
      this.$refs._formSubmitRef.validate((valid) => {
        if (!valid) return
        this.$refs._seniorTabRef.rulePass()
      })
    },
    // 保存提交
    handleSubmitSave() {
      this.saveLoading = true
      const { id, budgetPlaitId, booksId, projectName, adjType, adjResonse } =
        this.form

      const {
        incomeItems,
        expendItems,
        nonProfitAndLossItems,
        rentalIncomes,
        adjExpContracts,
        salaryBudgets
      } = this.tabTable.data
      save({
        id,
        budgetPlaitId,
        booksId,
        projectName,
        adjType,
        adjResonse,
        orgId: this.books.orgId,
        incomeItems: incomeItems?.map((cur) => {
          delete cur.isTemp
          return cur
        }),
        expendItems: expendItems?.map((cur) => {
          delete cur.isTemp
          return cur
        }),
        nonProfitAndLossItems: nonProfitAndLossItems?.map((cur) => {
          delete cur.isTemp
          return { ...cur, extExp: 0 }
        }),
        rentalIncomes: rentalIncomes.map((cur) => {
          const {
            contractId,
            contractCode,
            contractName,
            secondName,
            startDate,
            endDate,
            totalMoney,
            rentTotalMoney,
            rentYearMoney
          } = cur
          const items = []
          for (const key in cur) {
            if (key.startsWith('accountingItemCode_')) {
              const rowArr = key.split('_')
              items.push({
                accountingItemCode: +rowArr[1],
                accountingItemTitle: rowArr[2],
                value: cur[key]
              })
            }
          }
          return {
            id: cur.id,
            budgetPlaitId: id,
            contractId,
            contractCode,
            contractName,
            secondName,
            startDate,
            endDate,
            totalMoney,
            rentTotalMoney,
            rentYearMoney,
            items
          }
        }),
        adjExpContracts,
        salaryBudgets: salaryBudgets.map((cur) => {
          const { accountintItemCode } = cur
          return {
            level: cur.level,
            postName: cur.postName,
            totalSalary: cur.totalSalary,
            baseSalary: cur.baseSalary,
            accruedSalary: cur.accruedSalary,
            performanceAward: cur.performanceAward,
            otherSalary: cur.otherSalary,
            tranCommSubsidy: cur.tranCommSubsidy,
            holidaySubsidy: cur.holidaySubsidy,
            overtimeDutySubsidy: cur.overtimeDutySubsidy,
            postSubsidy: cur.postSubsidy,
            annualLeaveSubsidy: cur.annualLeaveSubsidy,
            priceSubsidy: cur.priceSubsidy,
            highTemperatureSubsidy: cur.highTemperatureSubsidy,
            senioritySubsidy: cur.senioritySubsidy,
            retirementSubsidy: cur.retirementSubsidy,
            otherSubsidy: cur.otherSubsidy,
            budgetPlaitId: id,
            accountintItemCode:
              accountintItemCode[accountintItemCode.length - 1],
            accountintItemTitle: accountintItemCode
              ? expendItems.find(
                  (cur) => cur.accountingItemCode === accountintItemCode
                )?.accountingItemPathTitle
              : ''
          }
        }),
        batchId: this.batchId
      })
        .then((res) => {
          if (res.returnCode === '0') {
            this.$message.success(this.$t(res.message))
            this.dialogVisible = false
            this.$parent.getList()
          } else {
            this.$message.warning(this.$t(res.message))
          }
        })
        .finally(() => {
          this.saveLoading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-input-number,
.el-input-number--mini {
  width: 100% !important;
}
::v-deep.append-ratio {
  position: absolute;
  right: 5px;
}
::v-deep .import-upload .el-upload {
  width: 100%;
  margin-bottom: 20px;
  .el-upload-dragger {
    width: 100%;
  }
}
.approval {
  margin-bottom: 280px;
}
</style>
