<template>
  <div>
    <fold-box right-title="收款明细">
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('订单号')" prop="orderNo">
              <gever-input v-model="queryParams.orderNo"  />
            </el-form-item>
            <el-form-item :label="$t('交易状态')" prop="status">
              <el-select v-model="queryParams.status" clearable multiple placeholder="请选择">
                <el-option
                  v-for="item in optionsMap.payStatus"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('收款项目')" prop="receiveItemId">
              <el-select v-model="queryParams.receiveItemId" clearable  placeholder="请选择">
                <el-option
                  v-for="item in optionsMap.receiveItem"
                  :key="item.id"
                  :label="item.receiveItemName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('付款方')" prop="payerAccountName">
              <gever-input v-model="queryParams.payerAccountName"  />
            </el-form-item>
            <el-form-item :label="$t('创建时间')" prop="createTimeStart">
              <el-date-picker
                v-model="queryParams.createTimeStart"
                class="w45%"
                type="date"
                value-format="yyyy-MM-dd"
              /> -
              <el-date-picker
                v-model="queryParams.createTimeEnd"
                class="w45%"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            <el-form-item :label="$t('金额范围')" prop="moneyMin">
              <gever-input v-model="queryParams.moneyMin" style="width:45%" @input="handleAmountInput('moneyMin', $event)" /> -
              <gever-input v-model="queryParams.moneyMax" style="width:45%" @input="handleAmountInput('moneyMax', $event)" />
            </el-form-item>
            <el-form-item>
              <el-button plain icon="el-icon-search" type="primary" round @click="handleSearch">
                {{ $t('搜索') }}
              </el-button>
              <el-button
                  plain
                  v-hasPermi="'financial.qrPayment.receiveDetail.export'"
                  type="primary"
                  round
                  icon="el-icon-download"
                  @click="handleExport"
                >
                  {{ $t('导出') }}
                </el-button>
            </el-form-item>
            <div style="float: right;">
              <el-form-item>
                
              </el-form-item>
            </div>
          </el-form>
          <gever-table
		    :loading="tableLoading"
            ref="_geverTableRef"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
            style="clear: both;"
          >
          <template #status="{ row }"> 
              {{ getTitle(row.status, optionsMap.payStatus) }}
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <gever-dialog
      :title="detailTitle"
      append-to-body
      :visible.sync="detailVisible"
      :buttons="detailButtons"
      custom-class="photograph-dialog"
      width="80%"
      perch-height="400"
    >
      <detail
        v-if="detailVisible"
        :detail-visible.sync="detailVisible"
        ref="_detailContentRef"
        :type="type"
        :id="currentId"
        @refreshTable="getList"
      />
    </gever-dialog>
  </div>
</template>

<script>
import { loadQrPaymentReceiveDetailListApi, deleteQrPaymentReceiveDetailApi } from '@/api/paymentQr/qrPaymentReceiveDetail.js'
import { loadQrPaymentReceiveItemOptionsApi } from '@/api/paymentQr/qrPaymentReceiveItem.js'
import detail from './components/detail'
import { createNamespacedHelpers } from 'vuex'
import { getDictionary } from '@/api/gever/common.js' // 引入获取字典的方法
const { mapState } = createNamespacedHelpers('area')
import axios from 'axios'
import { getToken } from '@/utils/auth'

export default {
  name: 'QrPaymentReceiveDetail',
  components: {
    detail
  },
  data() {
    return {
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      optionsMap: {
        receiveItem: [],
        payStatus: []
      },
      queryParams: {
        page: 1,
        rows: 20,
        createTimeStart: this.getYesterday()
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50' },
          { type: 'index', label: '序号', width: '50' },
          { label: '组织机构', prop: 'orgName' , minWidth: '350' ,resizable: true},
          { label: '订单号', prop: 'orderNo' , minWidth: '200' ,resizable: true},
          { label: '交易状态', prop: 'status' , minWidth: '100' ,resizable: true},
          { label: '收款项目', prop: 'receiveItemName' , minWidth: '200' ,resizable: true},
          { label: '金额', prop: 'money' , minWidth: '100' ,resizable: true},
          { label: '收款账号名称', prop: 'payeeAccountName' , minWidth: '350' ,resizable: true},
          { label: '收款账号', prop: 'payeeAccountNo' , minWidth: '200' ,resizable: true},
          { label: '收款方开户行', prop: 'payeeBank' , minWidth: '250' ,resizable: true},
          { label: '付款方', prop: 'payerAccountName' , minWidth: '350' ,resizable: true},
          { label: '交易时间', prop: 'payTime' , minWidth: '200' ,resizable: true},
          { label: '创建人', prop: 'createUsername' , minWidth: '100' ,resizable: true},
          { label: '创建时间', prop: 'createTime' , minWidth: '200' ,resizable: true},
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
	  currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
    }
  },
  computed: {
    ...mapState(['areaLogin']),
  },
  watch: {
    areaLogin: {
      handler: function (val) {
        console.log("初始化", val);
        this.getList()
        this.getOptions()
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // this.getList()
    // this.getOptions()
  },
  methods: {
    getList() {
      console.log(this.areaLogin)
      const areaCode = this.areaLogin.areaCode || this.areaLogin.code
      const orgCode = this.areaLogin.code
      const orgId = this.areaLogin.id

      if(!this.queryParams.createTimeStart){
        this.$set(this.queryParams, 'createTimeStart', '')
      }
      if(!this.queryParams.createTimeEnd){
        this.$set(this.queryParams, 'createTimeEnd', '')
      }

      if (this.queryParams.status) {
        if(Array.isArray(this.queryParams.status)){
          this.queryParams.statusList = this.queryParams.status.join(',');
        }else{
          this.queryParams.statusList = this.queryParams.status
        }
      }

      if(areaCode){
        this.tableLoading = true
        if(this.areaLogin.type == 'Organization'){
          this.queryParams.orgCode = orgCode
          this.queryParams.areaCode = ''
        }else{
          this.queryParams.areaCode = areaCode
          this.queryParams.orgCode = ''
        }
        loadQrPaymentReceiveDetailListApi(this.queryParams).then(res => {
          this.table.tableList = res.data.rows
          this.table.total = res.data.total
        })
        .catch(err => {
          this.table.tableList = []
          this.table.total = 0
        })
        .finally(() => {
          this.tableLoading = false
        })
      }
    },
    async getOptions () {
      const areaCode = this.areaLogin.areaCode || this.areaLogin.code
      // 获取收款项目选项
      const { data: receiveItem = [] } = await loadQrPaymentReceiveItemOptionsApi({areaCode:areaCode})
      this.optionsMap.receiveItem = receiveItem
      // 获取支付状态字典数据
      const { data: payStatus = [] } = await getDictionary('/financial/qrPayment/status/')
      this.optionsMap.payStatus = payStatus
    },
    handleSearch() {
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleAdd() {
      this.detailTitle = '新增收款记录明细'
      this.type = 'add'
	  this.currentId = ''
      this.detailVisible = true
    },
    handleView(row) {
      this.detailTitle = '查看收款记录明细'
      this.type = 'view'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleEdit(row) {
      this.detailTitle = '编辑收款记录明细'
      this.type = 'edit'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleRemove(row, batchFlag) {
      let ids
      if (batchFlag) {
        ids = row.id
      } else {
        if (this.tableIdSelection.length == 0) {
          return this.$message.warning(this.$t('请先选择要删除的数据！'))
        }
        ids = this.tableIdSelection.join(',')
      }
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        deleteQrPaymentReceiveDetailApi({ ids }).then(res => {
          if (res) {
            this.$message.success(res.message)
            this.getList()
          }
        })
      }).catch(() => {})
    },
    handleCloseDetail() {
      this.detailVisible = false
    },
    handleSave() {
      this.$refs._detailContentRef.save()
    },
    handleExport() {
      let ids = '';
      let msg = '';

      if (this.tableIdSelection.length === 0) {
        this.getList();
        msg = '没有选择数据，确定导出所有收款明细？';
      } else {
        ids = this.tableIdSelection.join(',');
        msg = '确定导出已选择的收款明细？';
      }

      this.$confirm(msg, {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: '导出中，请稍后...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        let _data_ = '';
        let index = 0;

        let param = {
          ...this.queryParams
        };

        param.ids = ids;

        const keys = Object.keys(param);
        keys.forEach((d) => {
          if (param[d] !== undefined) {
            _data_ += d + '=' + param[d];
            if (Object.keys(param).length - 1 !== index) {
              _data_ += '&';
            }
          }
          index++;
        });

        const api = '/qrpayment/detail/export';

        axios({
          method: 'get',
          url: `${process.env.VUE_APP_BASE_API}${api}?${_data_}&access_token=${getToken()}&tenant_id=${this.$Cookies.get('X-tenant-id-header')}`,
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/json',
            'X-user-token-header': getToken()
          }
        })
          .then((res) => {
            if (res.status === 200) {
              const blob = new Blob([res.data], {
                type: res.headers['content-type']
              });
              if (blob.size === 0) {
                this.$message.warning('当前暂无数据');
                loading.close();
                return;
              }
              const fileName = decodeURI(
                res.headers['content-disposition'].split('=')[1]
              ).replace(new RegExp('"', 'g'), '');
              const elink = document.createElement('a');
              elink.download = fileName;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(blob);
              document.body.appendChild(elink);
              elink.click();
              URL.revokeObjectURL(elink.href);
              document.body.removeChild(elink);
              loading.close();
            }
          })
          .catch((err) => {
            console.log(err);
            this.$message.warning('导出失败！');
            loading.close();
          });
      }).catch(() => {});
    },
    handleAmountInput(field, value) {
      // 新的正则表达式，限制连续的点号、不合理的数字格式以及以点号开头的情况
      const sanitizedValue = value.replace(/[^0-9.]/g, ''); // 移除非数字和点号
      const finalValue = sanitizedValue.replace(/^\./, ''); // 删除以点号开头的情况
      const parts = finalValue.split('.');
      if (parts.length > 2) {
        // 如果有超过一个点号，保留第一个点号及其后面的数字
        this.queryParams[field] = parts[0] + '.' + parts[1];
      } else {
        this.queryParams[field] = finalValue;
      }
    },
    // 添加获取前一天日期的方法
    getYesterday() {
      const date = new Date();
      date.setDate(date.getDate() - 1);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  }
}
</script>