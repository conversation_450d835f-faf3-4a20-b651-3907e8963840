/*
 * @Descripttion:
 * @version:
 * @Author: Tanronglong
 * @Date: 2021-10-28 11:56:47
 * @LastEditors: Tanronglong
 * @LastEditTime: 2021-11-12 15:49:44
 */
import request from '@/utils/request'
// 待办事项   /workflow/getTodoList?path=/contract/workflow/contract&subArea=true
export function collectionList(data) {
  return request({
    url: '/contract/paymentContract/pageX',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 获取收款合同合同类型
export function getContractTypeDictPayment(data) {
  return request({
    url: '/contract/contractType/getContractTypeDictPayment',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 编辑表单详情
export function checkToEdit(data) {
  return request({
    url: `/contract/paymentContract/loadX/${data}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 付款金额详情 /contract/paymentContract/getPaymentDetail/2d24979ac079445ca60a741127516884
export function getPaymentDetail(data) {
  return request({
    url: `/contract/paymentContract/getPaymentDetail/${data.id}`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 扣款费用保存接口/contract/paymentContract/saveDeduction
export function saveDeduction(data) {
  return request({
    url: '/contract/paymentContract/saveDeduction',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同付款数据/contract/contractPayment/page
export function contractPayment(data) {
  return request({
    url: '/contract/contractPayment/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同选择数据 /contract/contractPayment/paymentList
export function paymentList(data) {
  return request({
    url: '/contract/contractPayment/paymentList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 付款合同保存 /contract/contractPayment/save
export function saveX(data) {
  return request({
    url: '/contract/paymentContract/saveX',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 付款列表删除 /contract/contractPayment/delete
export function removeList(data) {
  return request({
    url: '/contract/contractPayment/deleteX',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 获取付款项目  contract/contractPayment/combobox
export function paymentProject(data) {
  return request({
    url: `/contract/contractPayment/combobox?paymentId=${data.paymentId}&orgId=${data.orgId}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 回收保存 /contract/contractPayment/saveRefund
export function saveRefund(data) {
  return request({
    url: '/contract/contractPayment/saveRefund',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 编辑校验接口
export function checkToEditVerify(data) {
  return request({
    url: `/contract/paymentContract/checkToEdit/${data}`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 保存按钮
export function save(data) {
  return request({
    url: '/contract/contractPayment/saveX',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/** 合同付款变更接口*/
// 查询合同付款变更选择数据 /contract/paymentContractChange/getContractForChange
export function getContractForChange(data) {
  return request({
    url: '/contract/paymentContractChange/getContractForChange',
    method: 'post',
    data: data
  })
}
// 合同付款列表展示数据 /contract/paymentContractChange/pageX
export function paymentChangepageX(data) {
  return request({
    url: '/contract/paymentContractChange/pageX',
    method: 'post',
    data: data
  })
}
// 跳转付款合同详情页面  /contract/paymentContractChange/loadX/2d24979ac079445ca60a741127516884?getChange=true&isAdd=true
export function loadX(id, data) {
  return request({
    url: `/contract/paymentContractChange/loadX/${id}`,
    method: 'get',
    params: data
  })
}
// 跳转合同终止记录 /contract/paymentContract/loadTerm/6ffe181b92274bd4a074c7472e561294?_ood_=true
export function loadTerm(id, data) {
  return request({
    url: `/contract/paymentContract/loadTerm/${id}`,
    method: 'get',
    params: data
  })
}
// 合同终止记录校验 /contract/paymentContract/checkTerminationInfo/
export function checkTerminationInfo(data) {
  return request({
    url: `/contract/paymentContract/checkTerminationInfo/${data.id}`,
    method: 'post',
    data: data
  })
}
// 合同终止记录 /contract/paymentContract/checkChangeRecord/
export function terminationCheck(id) {
  return request({
    url: `/contract/paymentContract/terminationCheck/${id}`,
    method: 'post'
  })
}
// 合同终止提交校验 /contract/paymentContract/checkPayment
export function checkPayment(data) {
  return request({
    url: '/contract/paymentContract/checkPayment',
    method: 'post',
    params: data
  })
}
// 合同保存 /contract/paymentContract/saveAndSubmitTerms
export function saveAndSubmitTerms(data) {
  return request({
    url: '/contract/paymentContract/saveAndSubmitTerm',
    method: 'post',
    data: data
  })
}
// 付款合同变更 /contract/paymentContract/getChangeRecordData
export function getChangeRecordData(data) {
  return request({
    url: '/contract/paymentContract/getChangeRecordData',
    method: 'post',
    data: data
  })
}
// 付款扣款 /contract/paymentContract/getDeduction
export function getDeduction(data) {
  return request({
    url: '/contract/paymentContract/getDeduction',
    method: 'post',
    data: data
  })
}
// 扣款合同详情 /contract/paymentContract/getDeductionDetail
export function getDeductionDetail(query, data) {
  return request({
    url: `/contract/paymentContract/getDeductionDetail/${query.id}/${query.receivablesId}`,
    method: 'post',
    data: data
  })
}
// 扣款记录删除  /contract/paymentContract/delDeductionDetail/194b14743eb14731b196701b9c77c15a
export function delDeductionDetail(data) {
  return request({
    url: `/contract/paymentContract/delDeductionDetail/${data.id}`,
    method: 'post',
    data: data
  })
}

// /contract/paymentContract/checkChangeRecord/6ffe181b92274bd4a074c7472e561294
export function checkChangeRecord(id) {
  return request({
    url: `/contract/paymentContract/checkChangeRecord/${id}`,
    method: 'post'
  })
}
// 跟踪流程校验  /contract/paymentContract/checkTrackType
export function checkTrackType(data) {
  return request({
    url: '/contract/paymentContract/checkTrackType',
    method: 'post',
    data: data
  })
}
// 付款合同编辑校验 /contract/paymentContractChange/checkToEdit/f9585e4936754ea58b6a892a0cfef464
export function changeCheckToEdit(id) {
  return request({
    url: `/contract/paymentContractChange/checkToEdit/${id}`,
    method: 'post'
  })
}
// 付款合同变更保存 /contract/paymentContractChange/saveX
export function saveXChange(data) {
  return request({
    url: '/contract/paymentContractChange/saveX',
    method: 'post',
    data: data
  })
}
// 付款合同变删除 /contract/paymentContractChange/deleteX
export function deleteX(data) {
  return request({
    url: '/contract/paymentContractChange/deleteX',
    method: 'post',
    data: data
  })
}
// 扣减费用校验 /contract/paymentContract/checkDeducte
export function checkDeducte(data) {
  return request({
    url: '/contract/paymentContract/checkDeducte',
    method: 'post',
    data: data
  })
}
// 获取表单tab切换数据 /contract/paymentContract/plan_detail
export function planDetail(data) {
  return request({
    url: '/contract/paymentContract/plan_detail',
    method: 'post',
    data: data,
    payload: true
  })
}
