import request from '@/utils/request'

/**
 * 获取会计科目的期间余额
 * @param {*} params { booksId: '', period: '' }
 * @returns
 */
export function pageVoucherShow(data) {
  return request({
    url: '/financial/accounting/accountingVoucherNC/pageVoucherShow',
    method: 'post',
    data: data
  })
}

/**
 * 获取凭证信息
 * @param {*} data { booksId: '', noPager:  }
 * @returns
 */
export function loadVoucher(params) {
  return request({
    url: '/financial/accounting/accountingVoucherNC/loadVoucher',
    method: 'get',
    params: params
  })
}

export function orgBooksYearNC(data) {
  return request({
    url: '/financial/accounting/accountingVoucherNC/orgBooksYear',
    method: 'post',
    data: data
  })
}

export function orgBooksPeriodNC(data) {
  return request({
    url: '/financial/accounting/accountingVoucherNC/orgBooksPeriod',
    method: 'post',
    data: data
  })
}

export function loginNC(data) {
  return request({
    url: '/financial/accounting/accountingVoucherNC/login',
    method: 'post',
    data: data
  })
}

export function authOrgTree(data) {
  return request({
    url: '/financial/accounting/accountingVoucherNC/authOrgTree',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

//导出

export function exportTemp(params) {
  return request({
    params,
    url: '/financial/accounting/accountingVoucherNC/exportTemp',
    method: 'get',
    responseType: 'blob'
  })
}
