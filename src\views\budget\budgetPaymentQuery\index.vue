<template>
  <div>
    <fold-box :right-title="$t('预算账查询')">
      <template #right>
        <div class="right-box">
          <el-form ref="_formRef" :model="queryParams" inline>
            <el-form-item label="年份">
              <gever-select
                v-model="queryParams.year"
                :options="optionsMap.listYear"
                :clearable="false"
                label-prop="createYear"
                value-prop="createYear"
              />
            </el-form-item>
            <el-form-item :label="$t('地区/机构')">
              <el-select
                v-model="queryParams.areaOrgName"
                :title="queryParams.areaOrgName"
                :clearable="false"
              >
                <el-option
                  :value="treeNodeAreaName"
                  style="height: auto; padding: 0"
                >
                  <area-tree
                    :is-lazy-load="true"
                    :tree-data="treeData"
                    :expanded-nodes="expandedNodes"
                    @loadChildNode="loadChildNode"
                    @selectedNodeChange="handleNodeChange"
                  />
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="预算类型">
              <gever-select
                v-model="queryParams.budgetType"
                :options="budgetTypeOptions"
                clearable
              />
            </el-form-item>
            <el-form-item label="预算项目名称">
              <gever-input
                v-model="queryParams.budgetProjectName"
                placeholder="请输入预算项目名称"
              />
            </el-form-item>
            <el-form-item label="会计科目编号">
              <gever-input
                v-model="queryParams.accountingItemCode"
                placeholder="请输入会计科目编号"
              />
            </el-form-item>
            <el-form-item label="显示无预算数">
              <gever-select
                v-model="queryParams.noBudgetAmount"
                :clearable="false"
                :options="[
                  {
                    id: '1',
                    text: '是'
                  },
                  {
                    id: '0',
                    text: '否'
                  }
                ]"
              />
            </el-form-item>
            <el-form-item label="是否工程预算">
              <gever-select
                v-model="queryParams.projectBudget"
                :options="[
                  {
                    id: '0',
                    text: '否'
                  },
                  {
                    id: '1',
                    text: '是'
                  }
                ]"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                round
                plain
                icon="el-icon-search"
                @click="handleSearch"
              >
                {{ $t('搜索') }}
              </el-button>
              <el-button
              v-hasPermi="'financial.budget.budgetPaymentQuery.export'"
              round
              plain
              type="primary"
              icon="el-icon-download"
              @click="handleExportTemp"
            >
              {{ $t('导出') }}
            </el-button>
            </el-form-item>
          </el-form>
          <gever-table
            ref="_tableRef"
            :columns="table.columns"
            :data="table.rows"
            :total="table.total"
            row-key="id"
            default-expand-all
            show-summary
            :summary-method="handleSummary"
            :options-map="optionsMap"
            :pagi="queryParams"
            :loading="table.loading"
            :page-sizes="[20, 50, 100, 500, 1000]"
            @pagination-change="getList"
          >
            <!-- <template #completeRate="{ row }">
              {{
                row.execAmount
                  ? (row.execAmount / row.budgetAmount) * 100
                  : 0 | formatMoney
              }}
            </template> -->
            <template #projectBudget="{ row }">
              {{ row.projectBudget === 1 ? '是' : '否' }}
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
  </div>
</template>

<script>
import { listYear } from '@/api/budget/budgetItemSetting'
import { pageBudgetPaymentQuery } from '@/api/budget/budgetPaymentQuery'
import { loadAuthTreeData } from '@/api/gever/common.js'
import { getToken } from '@/utils/auth'
import { comma } from '@/utils/gever'
import moment from 'moment'

export default {
  name: 'BudgetPaymentQuery',
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      queryParams: {
        page: 1,
        rows: 20,
        noBudgetAmount: '0',
        year: moment().format('YYYY')
      },
      optionsMap: { listYear: [] },
      table: {
        columns: [
          { prop: 'orgName', label: '单位名称', minWidth: 150 },
          { prop: 'budgetProjectName', label: '预算项目', minWidth: 150 },
          { prop: 'accountingItemCode', label: '会计科目编号', minWidth: 100 },
          { prop: 'accountingItemName', label: '会计科目名称', minWidth: 150 },
          { prop: 'projectBudget', label: '是否工程预算', minWidth: 100 },
          {
            prop: 'yearInitBudgetAmount',
            label: '年初预算数',
            minWidth: 120,
            align: 'right',
            filter: 'money'
          },
          {
            prop: 'budgetAdjustmentAmount',
            label: '预算调整数',
            minWidth: 120,
            align: 'right',
            filter: 'money'
          },
          {
            prop: 'budgetAmount',
            label: '总预算数',
            minWidth: 120,
            align: 'right',
            filter: 'money'
          },
          {
            prop: 'applyAmount',
            label: '申请数',
            minWidth: 120,
            align: 'right',
            filter: 'money'
          },
          {
            prop: 'availableBudgetAmount',
            label: '可用预算数',
            minWidth: 120,
            align: 'right',
            filter: 'money'
          },
          {
            prop: 'execAmount',
            label: '执行数',
            minWidth: 120,
            align: 'right',
            filter: 'money'
          },
          {
            prop: 'surplusAmount',
            label: '剩余预算数',
            minWidth: 120,
            align: 'right',
            filter: 'money'
          },
          {
            prop: 'completeRate',
            label: '完成率（%）',
            minWidth: 120,
            align: 'right',
            filter: 'money'
          }
        ],
        rows: [],
        total: 0,
        loading: false
      },
      treeNodeAreaName: '',
      treeData: [],
      budgetTypeOptions: [
        { id: '1', text: '收入' },
        { id: '2', text: '支出' },
        { id: '3', text: '非损益类' }
      ]
    }
  },
  computed: {
    expandedNodes() {
      const ids = []
      if (this.treeData.length > 0) {
        this.treeData.forEach((levelOneNode) => {
          ids.push(levelOneNode.id)
        })
      }
      return ids
    }
  },
  mounted() {
    this.getUserAreaTreeData()
    this.getYearList()
  },
  methods: {
    getToken,
    // 合计行
    handleSummary(param) {
      const { columns, data } = param
      const sums = []
      const countIndex = [5, 6, 7, 8, 9, 10, 11, 12]
      const countEnum = {
        yearInitBudgetAmount: 0,
        budgetAdjustmentAmount: 0,
        budgetAmount: 0,
        applyAmount: 0,
        availableBudgetAmount: 0,
        execAmount: 0,
        surplusAmount: 0,
        completeRate: 0
      }
      data.forEach((item) => {
        for (const key in item) {
          if (Object.keys(countEnum).includes(key)) {
            countEnum[key] += item[key] || 0
          }
        }
      })
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }
        if (index === 12) {
          sums[index] = countEnum.execAmount
            ? ((countEnum.execAmount / countEnum.budgetAmount) * 100).toFixed(2)
            : 0
          return
        }
        if (countIndex.includes(index)) {
          let value = countEnum[column.property] || 0

          value = (Math.round(value * 100) / 100).toFixed(2)
          sums[index] = comma(value * 1, 2)
        }
      })
      return sums
    },
    getList() {
      this.table.loading = true
      pageBudgetPaymentQuery(this.queryParams)
        .then((res) => {
          if (res.returnCode !== '0') return
          this.table.rows = res.data.rows
          this.table.total = res.data.total
        })
        .finally(() => {
          this.table.loading = false
        })
    },
    // 搜索
    handleSearch() {
      this.queryParams.page = 1
      this.getList()
    },
    // 获取年份
    getYearList() {
      listYear().then((res) => {
        if (res.returnCode === '0') {
          this.optionsMap.listYear = res.data
        } else {
          this.optionsMap.listYear = []
        }
      })
    },
    // 获取地区数据
    async getUserAreaTreeData() {
      const { data: treeData } = await loadAuthTreeData({
        areaCode: this.areaCode
      })
      this.treeData = treeData
      this.handleNodeChange(treeData[0])
      this.getList()
    },
    loadChildNode(id, resolve) {
      this.loadTree({ id: id }).then((res) => {
        resolve(res.data)
      })
    },
    async loadTree() {
      const params = {}
      if (arguments[0]) {
        params.id = arguments[0]
      }
      return await loadAuthTreeData(params.id)
    },
    handleNodeChange(data) {
      if (data.type === 'Organization') {
        this.$set(this.queryParams, 'orgId', data.id)
        this.$set(this.queryParams, 'areaCode', '')
      } else {
        this.$set(this.queryParams, 'areaCode', data.code)
        this.$set(this.queryParams, 'orgId', '')
      }
      this.$set(this.queryParams, 'areaOrgName', data.text)
    },
    handleExportTemp(){
      if (this.table.rows.length == 0) {
        this.$message.warning(this.$t('没有需要导出的数据！'))
        return false
      }
      let str = ''
      const obj = { ...this.queryParams }
      delete obj.rows
      delete obj.page
      for (var i = 0; i < Object.keys(obj).length; i++) {
        if (str) {
          str += '&'
        }
        str += Object.keys(obj)[i] + '=' + Object.values(obj)[i]
      }
      const url =
        this.baseUrl +
        `/financial/budget/budgetItem/export?` +
        str +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.location.href = url
    }
  }
}
</script>
