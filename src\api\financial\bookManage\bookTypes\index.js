/*
 * @Descripttion:账套类型维护接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-25 09:47:29
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-03 10:58:35
 */

import request from '@/utils/request'

/**
 * @name:获取账套类型列表
 * @param {Object} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/bookTypes/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:保存账套类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: '/financial/bookTypes/save',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:删除账套类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: '/financial/bookTypes/delete',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:设置完成
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function setComplete(data) {
  return request({
    url: '/financial/bookTypes/setComplete',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:根据地区或者机构id 以及当前选择的账套类型获取账套类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listByDivOrOrgId(params) {
  return request({
    url: '/financial/bookTypes/listByDivOrOrgId',
    method: 'get',
    params,
    withCredentials: true
  })
}

