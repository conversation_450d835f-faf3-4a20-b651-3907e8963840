import request from '@/utils/request'

// 已办事项列表
export function getDoneList(data, params) {
  return request({
    url: '/workflow/getHasDoneTaskList',
    method: 'post',
    data,
    params
  })
}

// 待办事项列表
export function getToDoList(data, params) {
    return request({
      url: '/workflow/getTodoList',
      method: 'post',
      data,
      params
    })
}

// 获取已办、待办的init数据
export function init(params) {
    return request({
        url: '/contract/workflow/verify/init',
        method: 'get',
        params
    })
}

// 流程审批
export function verify(data) {
    return request({
        url: '/contract/workflow/verify',
        method: 'post',
        data
    })
}