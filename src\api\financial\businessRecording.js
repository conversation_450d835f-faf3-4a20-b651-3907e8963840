import request from '@/utils/request'

//获取预算项目列表
export function listBudgetItem (params) {
  return request({
    url: '/financial/business/base/listBudgetItem',
    method: 'get',
    params,
    withCredentials: true
  })
}
//业务类型
export function getModule (params) {
  return request({
    url: '/financial/accounting/business/getModule',
    method: 'get',
    params,
    withCredentials: true
  })
}

//列表
export function noPage (type, data) {
  return request({
    url: `/financial/${type}/business/noPage`,
    method: 'post',
    data,
    withCredentials: true
  })
}
//生成凭证前检测
export function checkBeforeGenerate (type, data) {
  return request({
    url: `/financial/${type}/business/checkBeforeGenerate`,
    method: 'post',
    data,
    withCredentials: true
  })
}
//设置转凭证页面初始化
export function choicesInit (type, params) {
  return request({
    url: `/financial/${type}/business/choicesInit`,
    method: 'get',
    params,
    withCredentials: true
  })
}
//生成凭证
export function generateVoucher (type, data) {
  return request({
    url: `/financial/${type}/business/generateVoucher`,
    method: 'post',
    data,
    withCredentials: true
  })
}

//凭证保存
export function saveVoucher (type, data, params) {
  return request({
    url: `/financial/${type}/business/saveVoucher?booksId=${params.booksId}&businessCode=${params.businessCode}&period=${params.period}`,
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

//撤销凭证
export function cancelGenerateVoucher (type, data) {
  return request({
    url: `/financial/${type}/business/cancelGenerateVoucher`,
    method: 'post',
    data,
    withCredentials: true
  })
}

//获取关联凭证条件
export function relateAccountingInit (type, data) {
  return request({
    url: `/financial/${type}/business/relateAccounting/init`,
    method: 'post',
    data,
    withCredentials: true
  })
}

//获取关联凭证列表
export function pageVoucher (data) {
  return request({
    url: `/financial/cashier/business/pageVoucher`,
    method: 'post',
    data,
    withCredentials: true
  })
}

//获取会计关联凭证列表
export function pageEntry (data) {
  return request({
    url: `/financial/accounting/business/pageEntry`,
    method: 'post',
    data,
    withCredentials: true
  })
}

//关联凭证
export function relateEntry (type, data) {
  return request({
    url: `/financial/${type}/business/relateEntry`,
    method: 'post',
    data,
    withCredentials: true
  })
}

// 业务控制
export function getBankStatementFlag (params) {
  return request({
    url: '/financial/business/base/getControlFlag',
    method: 'get',
    params,
    withCredentials: true
  })
}


//银行流水-高级搜索-账户列表
export function listBankAccount (params) {
  return request({
    url: '/financial/business/base/listBankAccount',
    method: "get",
    params,
    withCredentials: true,
  })
}


//用款申请单-高级搜索-支出类型
export function listPaymentExpendType (params) {
  return request({
    url: '/financial/business/base/listPaymentExpendType',
    method: "get",
    params,
    withCredentials: true,
  })
}