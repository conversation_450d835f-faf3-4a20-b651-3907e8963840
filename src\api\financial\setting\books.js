/*
 * @Descripttion:账套维护接口
 * @version:
 * @Author: 未知
 * @Date: 2021-11-01 13:50:29
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-06-18 19:59:27
 */

import request from '@/utils/request'

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/books/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取账套维护详情
 * @param {String} id 账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id = '' }) {
  return request({
    url: `/financial/books/load/${id}`,
    method: 'get',
    data: {},
    withCredentials: true
  })
}

/**
 * @name:保存账套维护
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveBooks(data) {
  return request({
    url: '/financial/books/saveBooks',
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:删除账套维护
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/books/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:启用账套
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function enable(data) {
  return request({
    url: `/financial/books/enable`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:停用账套
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function disable(data) {
  return request({
    url: `/financial/books/disable`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:初始化账套
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function checkInit(data) {
  return request({
    url: `/financial/books/checkInit`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取初始化的模块
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function initType(data) {
  return request({
    url: `/financial/books/initType`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:设置初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function initBooks(data) {
  return request({
    url: `/financial/books/initBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取账套信息
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBooksInfo(data) {
  return request({
    url: `/financial/books/getBooksInfo`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:一键生成预设账套
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function initSaveBooks(params) {
  return request({
    timeout: 600000,
    url: `/financial/books/initSaveBooks`,
    method: 'get',
    params,
    withCredentials: true
  })
}



//出纳是否关联票据
export function updateIsRelevanceBill(data) {
  return request({
    url: `/financial/books/updateIsRelevanceBill`,
    method: 'post',
    data,
  })
}
// 通过账套id获取科目 booksTypeId
/* 基础会计科目查询 */
export function getBaseItemListReport(data) {
  return request({
    url: '/financial/booktypes/basedata/item/getBaseItemList',
    method: 'post',
    data: data,
    // payload: true,
    timeout: 180000
  })
}

export function execBaseDataYearEnded(data) {
  return request({
    url: '/financial/booksYearEnded/execBaseDataYearEnded',
    method: 'post',
    data: data,
    // payload: true,
    timeout: 180000
  })
}

/**
 * @name:年中停账检验接口
 * @param {*} data
 * @description: 检验是否可以进行年中停账
 * @return {*}
 * @test:
 * @msg:
 */
export function checkMidYearClosure(data) {
  return request({
    url: '/financial/books/checkMidYearClosure',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:年中停账确认接口
 * @param {*} data
 * @description: 执行年中停账操作
 * @return {*}
 * @test:
 * @msg:
 */
export function execMidYearClosure(data) {
  return request({
    url: '/financial/books/execMidYearClosure',
    method: 'post',
    data: data,
    withCredentials: true
  })
}