import request from '@/utils/request'

/**
 * 票据类型列表
 */
export function loadBillTypeList(data) {
  return request({
    url: '/bill/billType/page',
    data,
    method: 'post'
  })
}
/**
 * @name:票据详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/bill/billType/load/${id}`,
    method: 'get'
  })
}

queryParams = {
  page: 1,
  rows: 20
}
