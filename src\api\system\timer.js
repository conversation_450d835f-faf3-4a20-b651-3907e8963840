import request from '@/utils/request'

/**
 * @name: 定时任务列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function schedulePage(data) {
  return request({
    url: '/timer/schedule/page',
    method: 'get',
    params: data
  })
}

/**
 * @name: 是否有效
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function isvalid(data) {
  return request({
    url: '/json/isvalid.json',
    method: 'get',
    data: data
  })
}

/**
 * @name: 保存定时任务
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function scheduleSave(data) {
  return request({
    url: '/timer/schedule/save',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name: 查看定时任务
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function scheduleLoad(id) {
  return request({
    url: `/timer/schedule/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name: 删除定时任务
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function scheduleDelete(data) {
  return request({
    url: `/timer/schedule/delete`,
    method: 'POST',
    data: data,
    withCredentials: true
  })
}

/**
 * @name: 立即执行
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function scheduleRunOnce(id) {
  return request({
    url: `/timer/schedule/runOnce/${id}`,
    method: 'POST',
    withCredentials: true
  })
}

/**
 * @name: 执行日志列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function scheduleLogPage(data) {
  return request({
    url: '/timer/scheduleLog/page',
    method: 'post',
    params: data
  })
}

