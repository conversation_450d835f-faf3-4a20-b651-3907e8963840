import request from '@/utils/request'
// 票据发放列表数据
export function getBillInvalid(data) {
  return request({
    url: '/bill/billDist/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 查看
export function checkGrant(id) {
  return request({
    url: `/bill/billDist/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}
// 页面初始化
export function initGrant(id, type) {
  return request({
    url: `/bill/billDist/list/init?loginAreaOrgId=${id}&loginAreaOrgType=${type}
    `,
    method: 'get',
    withCredentials: true
  })
}

// 新增
export function issueAdd(data) {
  return request({
    url: '/bill/billDist/save',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 编辑
export function issueCompile(data) {
  return request({
    url: '/bill/billDist/save',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 删除
export function issueDelete(data) {
  return request({
    url: '/bill/billDist/delete',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
