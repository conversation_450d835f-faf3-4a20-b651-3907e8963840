import request from '@/utils/request'
/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadSettleListApi(data) {
  return request({
    url: '/qrpayment/settleDetail/page',
    method: 'post',
    data: data
  })
}

/**
 * 对账接口
 * @param {Object} params - 请求参数
 * @param {Array<string>} params.settleDetailIds - 对账单ID列表
 * @param {string} params.accountDetailId - 银行流水ID
 * @returns {Promise<Object>} - 返回操作结果对象
 */
export function reconcile(params) {
  return request({
    url: '/qrpayment/settleDetail/reconcile',
    method: 'post',
    params: {
      settleDetailIds: params.settleDetailIds.join(','),
      accountDetailId: params.accountDetailId
    }
  })
}

/**
 * 取消对账接口
 * @param {Object} params - 请求参数
 * @param {Array<string>} params.accountDetailIds - 关联银行流水ID列表
 * @returns {Promise<Object>} - 返回操作结果对象
 */
export function cancelReconcile(params) {
  return request({
    url: '/qrpayment/settleDetail/cancelReconcile',
    method: 'post',
    params: {
      accountDetailIds: params.accountDetailIds.join(',')
    }
  })
}
