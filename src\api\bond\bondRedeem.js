/*
 * @Description: 
 * @Version: 
 * @Author: luozc
 * @Date: 2023-10-10 08:30:22
 * @LastEditors: luozc
 * @LastEditTime: 2023-10-10 09:38:10
 */
import request from '@/utils/request'

/**
 * 查询赎回信息列表
 * @param {*} data 
 * @returns 
 */
export function getBondRedeemList(data) {
    return request({
        url: '/bond/bondRedeem/page',
        method: 'post',
        data
    })
}

/**
 * 查询赎回信息详情
 * @param {*} id 
 * @returns 
 */
export function getRedeemDetail(id) {
    return request({
        url: `/bond/bondRedeem/get/${id}`,
        method: 'get'
    })
}

/**
 * 保存
 * @param {*} data 
 * @returns 
 */
export function save(data) {
    return request({
        url: '/bond/bondRedeem/save',
        method: 'post',
        data,
        payload: true
    })
}

/**
 * 作废
 * @param {*} params 
 * @returns 
 */
export function cancel(params) {
    return request({
        url: '/bond/bondRedeem/cancel',
        method: 'post',
        params
    })
}

/**
 * 反作废
 * @param {*} id 
 * @returns 
 */
export function unCancel(id) {
    return request({
        url: `/bond/bondRedeem/unCancel?redeemId=${id}`,
        method: 'post'
    })
}

/**
 * 套打
 * @param {*} id 
 * @returns 
 */
export function print(id) {
    return request({
        url: `/bond/bondRedeem/print?redeemId=${id}`,
        method: 'post'
    })
}

/**
 * 补打
 * @param {*} id 
 * @returns 
 */
export function rePrint(id) {
    return request({
        url: `/bond/bondRedeem/rePrint?redeemId=${id}`,
        method: 'post'
    })
}