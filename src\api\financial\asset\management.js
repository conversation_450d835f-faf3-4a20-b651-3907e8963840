/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-11-19 10:21:49
 * @LastEditors: Peng Xiao
 * @LastEditTime: 2022-02-24 16:47:02
 */

import request from '@/utils/request'


/**
 * @name:获取折旧方法选项
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getDeprMethod () {
  return request({
    url: '/financial/asset/assetClass/depreciation?assetType=1',
    method: 'post',
    withCredentials: true
  })
}
/**
 * @name:批量设置资产分类
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function batchSaveClassChange (params, data) {
  return request({
    url: '/financial/asset/assetCard/batchSaveClassChange',
    method: 'post',
    params,
    data,
    payload: true
  })
}

/**
 * 批量设置资产科目、折旧科目、清理科目
 * @param {*} params 
 * @param {*} data 
 * @returns 
 */
export function batchAssetAccountingItemChange(params, data) {
  return request({
    url: '/financial/asset/assetCard/batchAssetAccountingItemChange',
    method: 'post',
    params,
    data,
    payload: true
  })
}

/**
 * @name:获取数据页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page (data) {
  return request({
    url: '/financial/asset/assetCard/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:资产录入保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save (data) {
  return request({
    url: `/financial/asset/assetCard/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:资产录入保存并入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveAndPosting ({ params, data }) {
  return request({
    url: `/financial/asset/assetCard/saveAndPosting`,
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:资产初始化录入保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function initSave (data) {
  return request({
    url: `/financial/asset/assetInit/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load ({ id }) {
  return request({
    url: `/financial/asset/assetCard/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:获取操作日志
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadChangeLog (params) {
  return request({
    url: `/financial/asset/assetCard/loadChangeLog`,
    method: 'post',
    params,
    withCredentials: true
  })
}
/**
 * @name:删除记录
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX (data) {
  return request({
    url: `/financial/asset/assetCard/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取资产使用状态
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function comboList (data) {
  return request({
    url: `/financial/asset/assetUseState/comboList`,
    method: 'post',
    data,
    withCredentials: true
  })
}

export function saveCostItem ({ params, data }) {
  return request({
    url: `/financial/asset/assetCard/saveCostItem`,
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}

export function loadCostItem (params) {
  return request({
    url: `/financial/asset/assetCard/loadCostItem`,
    method: 'post',
    params,
    withCredentials: true
  })
}

/**
 * @name:保存资产附件
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveFile (data) {
  return request({
    url: `/financial/asset/assetCard/saveFile`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存分类变更
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveClassChange ({ params, data }) {
  return request({
    url: `/financial/asset/assetCard/saveClassChange`,
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}
/**
 * @name:保存信息变更
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveInfoChange ({ params, data }) {
  return request({
    url: `/financial/asset/assetCard/saveInfoChange`,
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:保存拆分信息
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveSplitAsset ({ params, data }) {
  return request({
    url: `/financial/asset/assetSplit/saveSplitAsset`,
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:取消拆分
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unSplit (data) {
  return request({
    url: `/financial/asset/assetSplit/unSplit`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:加载打印的数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadPrintData (data) {
  return request({
    url: `/financial/asset/assetCard/loadPrintData`,
    method: 'post',
    data,
    withCredentials: true
  })
}
