<template>
  <div id="box" class="fold-box">
    <div id="left-container">
      <div class="title">
        <span>{{ $t(leftTitle) }}</span>
        <i class="el-icon-search" @click="callBack" />
      </div>
      <slot name="left" />
    </div>

    <div id="right-container" class="right-container">
      <div class="title">
        <span>{{ $t(rightTitle) }}</span>
      </div>
      <slot name="right" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'FoldBox',
  props: {
    callBack: { type: Function, default: () => {} },
    leftTitle: { type: String, default: '' },
    rightTitle: { type: String, default: '' }
  },
  data() {
    return {

    }
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.fold-box {
  height: 100%;
  display: flex;
  justify-content: space-between;
  #left-container,
  .right-container {
    .title {
      display: flex;
      justify-content: space-between;
      height: 34px;
      line-height: 30px;
      padding: 3px 10px;
      color: #575765;
      font-size: 14px;
      font-weight: bold;
      border-bottom: 1px solid #d7d7d7;
      background-color: #eceff4;
    }
  }
  #left-container {
    width: 250px;
    overflow-x: auto;
    .title > i {
        color: #409eff;
        cursor: pointer;
        line-height: 30px;
    }
  }
  .right-container {
    width: calc(100% - 255px);
    overflow-y: hidden;
  }
  #left-container,
  .right-container {
    border: 1px solid #d7d7d7;
    background-color: #fff;
  }
}
</style>
