// 租金收入明细表格列
export const rentTableColumns = [
  { prop: 'contractCode', label: '合同编号', minWidth: 120 },
  { prop: 'contractName', label: '合同名称', minWidth: 120 },
  { prop: 'secondName', label: '合同签订方', minWidth: 120 },
  { prop: 'startDate', label: '开始日期', minWidth: 120 },
  { prop: 'endDate', label: '结束日期', minWidth: 120 },
  { prop: 'totalMoney', label: '合同金额', minWidth: 120, align: 'right' },
  { prop: 'rentTotalMoney', label: '租金总金额', minWidth: 120, align: 'right' },
  { prop: 'rentYearMoney', label: '租金年金额', minWidth: 120, align: 'right' },
  { label: '操作', prop: 'operation', fixed: 'right', minWidth: 70 }
]
// 期满合同租金标准调整表格列
export const contractTableColumns = [
  {type: 'index', label: '序号', minWidth: 60},
  { prop: 'propertyName', label: '物业名称（地址）', minWidth: 150 },
  { prop: 'originalLessee', label: '乙方', minWidth: 80 },
  { prop: 'endDate', label: '到期日', minWidth: 90 },
  { prop: 'openingArea', label: '空地面积㎡', minWidth: 120 },
  { prop: 'originalPrice', label: '原单价（元/㎡）', minWidth: 120, align: 'right' },
  { prop: 'openingAreaPlanPrice', label: '空地面积计划底价（元/㎡）', minWidth: 120 },
  { prop: 'planMonthRent', label: '计划月租金（元/月）', minWidth: 120, align: 'right' },
  { prop: 'leaseTerm', label: '租赁期限（年）', minWidth: 120 },
  { prop: 'rentIncrease', label: '租金递增情况', minWidth: 120 },
  { prop: 'contractTotalObject', label: '合同总标的（元）', minWidth: 120 },
  { prop: 'purposeRequire', label: '租赁用途和相关要求', minWidth: 120 },
  { prop: 'bidBond', label: '竞投保证金', minWidth: 120, align: 'right' },
  { prop: 'disposalMethod', label: '流拍及中途退租处置方式', minWidth: 120 },
  { prop: 'remark', label: '备注', minWidth: 120 },
  { label: '操作', prop: 'operation', fixed: 'right', minWidth: 70 }
]
// 工资预算明细表格列
export const wagesTableColumns = [
  {type: 'index', label: '序号',width: 60},
  { prop: 'level', label: '级别', minWidth: 120 },
  { prop: 'postName', label: '岗位', minWidth: 120 },
  { prop: 'accountintItemCode', label: '支出预算科目', minWidth: 240 },
  { prop: 'totalSalary', label: '合计', minWidth: 100, align: 'right' },
  { prop: 'baseSalary', label: '基本工资总额', minWidth: 120, align: 'right' },
  { prop: 'accruedSalary', label: '计提工资总额', minWidth: 120, align: 'right' },
  { prop: 'performanceAward', label: '绩效奖励总额', minWidth: 120, align: 'right' },
  { prop: 'otherSalary', label: '其他工资', minWidth: 120, align: 'right' },
  { prop: 'tranCommSubsidy', label: '交通通讯补贴', minWidth: 120, align: 'right' },
  { prop: 'holidaySubsidy', label: '节日补贴', minWidth: 120, align: 'right' },
  { prop: 'overtimeDutySubsidy', label: '加班补贴', minWidth: 120, align: 'right' },
  { prop: 'postSubsidy', label: '岗位职务补贴', minWidth: 120, align: 'right' },
  { prop: 'annualLeaveSubsidy', label: '年假补贴', minWidth: 120, align: 'right' },
  { prop: 'priceSubsidy', label: '物价补贴', minWidth: 120, align: 'right' },
  { prop: 'highTemperatureSubsidy', label: '高温补贴', minWidth: 120, align: 'right' },
  { prop: 'senioritySubsidy', label: '工龄补贴', minWidth: 120, align: 'right' },
  { prop: 'retirementSubsidy', label: '退休补贴', minWidth: 120, align: 'right' },
  { prop: 'otherSubsidy', label: '其他补贴', minWidth: 120, align: 'right' },
  { prop: 'remark', label: '备注', minWidth: 200 },
  { label: '操作', prop: 'operation', fixed: 'right', minWidth: 70 }
] 
// 非损益类预算明细表格列
export const budgetTableColumns = [
  {type: 'index', label: '序号', width: 60},
  { label: '', prop: 'operation',minWidth: 70, align: 'center' },
  { prop: 'accountingItemCode', label: '科目代码', minWidth: 150 },
  { prop: 'accountingItemTitle', label: '科目名称', minWidth: 150 },
  { prop: 'accountingItemTitle', label: '项目名称', minWidth: 150 },
  { prop: 'projectBudget', label: '是否工程预算', minWidth: 100 },
  { prop: 'lastYearBudgetAmount', label: '上年计划', minWidth: 120, align: 'right' },
  { prop: 'lastYearBalAmount', label: '上年结余', minWidth: 120, align: 'right' },
  { prop: 'budgetAmount', label: '本年计划', minWidth: 120, align: 'right' },
  { prop: 'remark', label: '备注', minWidth: 100 },
]
// 年度预算收入表格列
export const incomeTableColmns = [
  {type: 'index', label: '序号', width: 60},
  { label: '', prop: 'operation',minWidth: 70, align: 'center' },
  { prop: 'accountingItemCode', label: '科目代码', minWidth: 150 },
  { prop: 'accountingItemTitle', label: '科目名称', minWidth: 150 },
  { prop: 'accountingItemTitle', label: '项目名称', minWidth: 150 },
  { prop: 'lastYearBudgetAmount', label: '上年计划', minWidth: 120, align: 'right' },
  { prop: 'lastYearBalAmount', label: '上年结余', minWidth: 120, align: 'right' },
  { prop: 'budgetAmount', label: '本年计划', minWidth: 120, align: 'right' },
  { prop: 'remark', label: '备注', minWidth: 150 },
]
// 年度预算支出表格列
export const payTableColmns = [
  {type: 'index', label: '序号', width: 60},
  { label: '', prop: 'operation',minWidth: 70, align: 'center' },
  { prop: 'accountingItemCode', label: '科目代码', minWidth: 150 },
  { prop: 'accountingItemTitle', label: '科目名称', minWidth: 150 },
  { prop: 'accountingItemTitle', label: '项目名称', minWidth: 150 },
  { prop: 'projectBudget', label: '是否工程预算', minWidth: 100 },
  { prop: 'lastYearBudgetAmount', label: '上年计划', minWidth: 120, align: 'right' },
  { prop: 'lastYearBalAmount', label: '上年结余', minWidth: 120, align: 'right' },
  { prop: 'budgetAmount', label: '本年计划', minWidth: 120, align: 'right' },
  { prop: 'remark', label: '备注', minWidth: 100 },
]