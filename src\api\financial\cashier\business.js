/*
 * @Descripttion:出纳账查询
 * @version:
 * @Author: 未知
 * @Date: 2021-11-08 10:56:09
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-15 20:00:40
 */
import request from '@/utils/request'

/**
 * @name:获取账套是否已经月结的标志
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getCashierFlag(params) {
  return request({
    url: '/financial/cashier/cashierSearch/getCashierFlag',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:页面
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/cashier/business/noPage',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:检查当前期间当前数据是否合法
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function checkBeforeGenerate(data) {
  return request({
    url: '/financial/cashier/business/checkBeforeGenerate',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:生成会计凭证
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function generateVoucher(data) {
  return request({
    url: '/financial/cashier/business/generateVoucher',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消生成会计凭证
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancelGenerateVoucher(data) {
  return request({
    url: '/financial/cashier/business/cancelGenerateVoucher',
    method: 'post',
    data,
    withCredentials: true
  })
}

