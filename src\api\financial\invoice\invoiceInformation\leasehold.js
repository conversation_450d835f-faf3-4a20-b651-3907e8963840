/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-10-09 16:59:46
 * @LastEditors: Andy
 * @LastEditTime: 2023-10-17 11:47:53
 */
import request from '@/utils/request'

/* 列表 */
export function page(data) {
  return request({
    url: '/invoice/invoiceLeasehold/page',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 保存/编辑 */
export function save(data) {
  return request({
    url: '/invoice/invoiceLeasehold/save',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 查看 */
export function load(id) {
  return request({
    url: `/invoice/invoiceLeasehold/load/${id}`,
    method: 'get'
  })
}
/* 删除 */
export function deleteData(data) {
  return request({
    url: `/invoice/invoiceLeasehold/delete`,
    method: 'post',
    data
  })
}
