<template>
  <div style="height:95%">
    <el-row type="flex" justify="space-between">
      <!-- <el-form ref="_formRef" :model="queryParams" inline> 
        <el-form-item :label="$t('合同编号')" prop="contractCode">
            <gever-input v-model="queryParams.contractCode"  style="width: 220px;"/>
        </el-form-item>
        <el-form-item :label="$t('合同标的物名称')" prop="protocolName">
            <gever-input v-model="queryParams.contractName"  style="width: 220px;"/>
        </el-form-item>
        <el-form-item :label="$t('乙方名称')" prop="partyB">
            <gever-input v-model="queryParams.partyB"  style="width: 220px;"/>
        </el-form-item>
        <el-form-item >
          <el-button
          type="primary"
          icon="el-icon-search"
          round
          plain
          @click="handleSearch"
        >
          搜索
        </el-button>
        </el-form-item>
      </el-form> -->
    </el-row>
    <gever-table
      ref="_geverTableRef"
      :loading="tableLoading"
      height="98%"
      :columns="table.columns"
      :data="table.tableList"
      :total="table.total"
      :pagi="queryParams"
      highlight-current-row
      @pagination-change="getList"
      @selection-change="handleSelectionChange"
      @current-change="selectRow"
    >

    
    <template #isSelected="{ row }">
      <input type="radio" :checked="selectedRow === row" :disabled="row.selectFlag==1">
    </template>
    <template #bankType="{ row }">
      {{ getTitle(row.bankType, optionsMap.bankType) }}
    </template>
    <template #accountType="{ row }"> 
      {{ getTitle(row.accountType, optionsMap.acountType) }}
    </template>
    </gever-table>
    <!-- </div>
      </template>
    </fold-box> -->
  </div>
</template>

<script>
import { loadBankAccountApi, queryExistsApi} from '@/api/paymentQr/qrPaymentAccount.js'
import { mapGetters, createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
// import {
//   YON, TradMethod, TradType
// } from '../../config'

export default {
  props: {
    projectStatus: {
      type: String,
      default: ''
    }
  },
  filters: {
  //   yonFilter(val) {
  //     const type = YON.find((item) => item.value == val)
  //     return type ? type.label : val
  //   },
  //   TradMethodFilter(val) {
  //     const type = TradMethod.find(item => item.value === val)
  //     return type ? type.label : val
  //   },
  //  TradTypeFilter(val) {
  //     const type = TradType.find(item => item.value === val)
  //     return type ? type.label : val
  //   }
  },
  data() {
    return {
      // YON,TradMethod, TradType,
      tableLoading: false,
      queryParams: {
        page: 1,
        rows: 20
      },
      optionsMap: {
        bankType: [],
        acountType: []
      },
      table: {
        columns: [
          { prop: 'isSelected', label: '选择', minWidth: '50' },
          // { label: '序号', prop: 'xh' , minWidth: '50'},
          { type: 'index', label: '序号', minWidth: '50' ,resizable: true},
          { label: '组织机构', prop: 'orgName' , minWidth: '300',resizable: true},
          { label: '户主名称', prop: 'ownerName' , minWidth: '300',resizable: true},
          { label: '账户类型', prop: 'accountType' , minWidth: '80',resizable: true},
          { label: '银行账号', prop: 'accountNumber' , minWidth: '200',resizable: true},
          { label: '银行类型', prop: 'bankType' , minWidth: '150',resizable: true},
          { label: '开户行', prop: 'bankName' , minWidth: '150',resizable: true}
        ],
        tableList: [
    
        ],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      selectedRow:{},
      type: '',
      currentId: ''
    }
  },
  computed: {
    ...mapState(['areaLogin'])
  },
  created() {
    this.handleSearch()
    this.getOptions()
  },
  methods: {
    async getList() {
      const orgCode = this.areaLogin.code
      this.tableLoading = true
      this.queryParams.state = 1
      this.queryParams.region = orgCode

      await loadBankAccountApi(this.queryParams)
        .then((res) => {
          this.table.tableList = res.data.rows
          this.table.total = res.data.total
        })
        .catch((err) => {
          this.tableLoading = false
        })

      const { data: existsAccounts = [] } = await queryExistsApi({orgCode: orgCode, accountNos: this.table.tableList.map(item => item.accountNumber)})
      this.table.tableList.forEach(item => {
        const selectFlag = existsAccounts.some(existsItem => existsItem === item.accountNumber) ? 1 : 0
        item.selectFlag = selectFlag
      })
      this.tableLoading = false

    },
    async getOptions () {
      const { data: bankType = [] } = await getDictionary('/payment/pay_bank_type/')
      this.optionsMap.bankType = bankType

      const { data: acountType = [] } = await getDictionary('/financial/bank_account_type/')
      this.optionsMap.acountType = acountType
    },
    handleSearch() {
      this.getList()
    },
    handleSelectionChange(selection) {
      // this.queryParams.page = 1
      this.tableSelection = selection
      this.tableIdSelection = selection.map((item) => item.id)
    },
    selectRow(row) {
      if(row.selectFlag!=1){
        this.selectedRow = row;
        this.tableSelection = [this.selectedRow]
        this.tableIdSelection = [this.selectedRow.id]
      }else{
        this.$refs._geverTableRef.setCurrentRow(this.selectedRow)
      }
    }
  }
}
</script>

<style scoped>
::v-deep el-table__expand-icon el-table__expand-icon--expanded {
  display: none !important;
}
::v-deep .el-table__expand-icon{
  display: none !important;
 }

.deduct-form {
  padding-left: 15px;
  padding-right: 15px;
}

</style>
