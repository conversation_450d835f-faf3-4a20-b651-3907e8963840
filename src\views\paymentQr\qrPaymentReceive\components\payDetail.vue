<template>
  <div v-loading="loading" class="gever-form detail" style="height: 100%">
    <el-table :data="details" row-key="id" border height="calc(95% - 100px)" 
    >
      <el-table-column type="index" label="序号" width="50" align="center" :row-class-name="handleRowClassName">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="payNo" label="订单明细号"  min-width="200" resizable="true" header-align="center" align="center">
        <template slot-scope="scope">
            <span >{{ scope.row.payNo }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="二维码生成时间" min-width="150" resizable="true" header-align="center" align="center">
        <template slot-scope="scope">
            <span >{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="voucherNo" label="交易凭证号" min-width="200" resizable="true" header-align="center" align="center">
        <template slot-scope="scope">
            <span >{{ scope.row.voucherNo }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="payTime" label="交易时间" min-width="150" resizable="true" header-align="center" align="center">
        <template slot-scope="scope">
            <span >{{ scope.row.payTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="二维码状态" min-width="100" resizable="true" header-align="center" align="center">
        <template slot-scope="scope">
          {{ getTitle(scope.row.status, optionsMap.payStatus) }}
        </template>
      </el-table-column>
      <el-table-column prop="failReason" label="备注" min-width="200" resizable="true" header-align="center" align="center">
        <template slot-scope="scope">
            <span >{{ scope.row.failReason }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getDictionary } from '@/api/gever/common.js' // 引入获取字典的方法
import {
  loadQrPaymentReceiveInfoApi,saveQrPaymentReceiveApi,updateQrPaymentReceiveApi, getQrcodesApi
} from '@/api/paymentQr/qrPaymentReceive.js'

export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      contentForm: {},
      details: [],
      rules: {},
      optionsMap: {
        payStatus: []
      }
    }
  },
  methods: {
    getTitle(status, options) {
      const item = options.find(option => option.id === status)
      return item ? item.text : ''
    }
  },
  async created() {
    // 获取支付状态字典数据
    const { data: payStatus = [] } = await getDictionary('/financial/qrPayment/qrcodeStatus/')
    this.optionsMap.payStatus = payStatus
    this.initPage()
  },
  methods: {
    initPage() {
      if (this.type === 'add') {
        // 新增页面初始化
      } else if (this.type === 'view' || this.type === 'edit') {
        // 查看、编辑页面初始化
        this.loading = true
        getQrcodesApi(this.id).then(res => {
          this.details = res.data
        }).finally(() => {
          this.loading = false
        })
      }
    },
    handleBatchIdChange(systemBatchId) {
      this.contentForm.systemBatchId = systemBatchId
    },
    save() {
      this.$refs['_geverFormRef'].validate(valid => {
        if (!valid) {
          return this.$message.warning(this.$t('请完善表单信息'))
        }
        this.loading = true
        if (this.contentForm.id) {
          updateQrPaymentReceiveApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        } else {
          saveQrPaymentReceiveApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>