<template>
  <div>
    <el-form inline :model="queryParams">
      <el-form-item :label="$t('申请编号')">
        <el-input
          v-model="queryParams.serialNumber"
          class="w150"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('支出类型')">
        <gever-select
          v-model="queryParams.expenditureType"
          clearable
          class="w150"
          :options="expendTypeComboOptions"
        />
      </el-form-item>
      <el-form-item :label="$t('资金用途')">
        <el-input v-model="queryParams.fundsUse" class="w150" clearable />
      </el-form-item>
      <el-form-item :label="$t('申请详情')">
        <el-input v-model="queryParams.details" class="w150" clearable />
      </el-form-item>
      <el-form-item :label="$t('申请时间')" prop="startCreationTime">
        <el-date-picker
          v-model="queryParams.startCreationTime"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="date"
          :placeholder="$t('选择日期')"
          class="w150"
          :picker-options="{ disabledDate: disabledCreationTimeStart }"
        />
        {{ $t('至') }}
        <el-date-picker
          v-model="queryParams.endCreationTime"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="date"
          :placeholder="$t('选择日期')"
          class="w150"
          :picker-options="{ disabledDate: disabledCreationTimeEnd }"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          round
          plain
          type="primary"
          icon="el-icon-search"
          @click="getList"
        >
          {{ $t('搜索') }}
        </el-button>
      </el-form-item>
    </el-form>
    <gever-table
      ref="_geverTableRef"
      v-loading="tableLoading"
      :columns="tableColumns"
      :data="tableList"
      pagination
      :total="total"
      :pagi="queryParams"
      :height="550"
      @p-current-change="getList"
      @size-change="getList"
      @selection-change="handleSelectionChange"
    >
      <template #expenditureType="{ row }">
        {{ getExpenditureTypeText(row.expenditureType, 'expendTypeComboOptions') }}
      </template>
      <template #approvalStatus="{ row }">
        {{ convertApprovalStatus(row.approvalStatus) }}
      </template>
      <template #fundSrc="{ row }">
        {{ convertFundSrc(row.fundSrc) }}
      </template>
      <template #settlementMethod="{ row }">
        {{ converSettlementMethod(row.settlementMethod) }}
      </template>
      <template #applicationType="{ row }">
        {{ convertApplicationType(row.applicationType) }}
      </template>
      <template #operation="{ row }">
        <el-button type="text" @click="handleViewApplicationForm(row)">
          {{ $t('查看') }}
        </el-button>
      </template>
    </gever-table>
    <public-drawer
      v-loading="loading"
      :visible.sync="applicationDrawerVisible"
      :title="drawerTitle"
      :size="70"
      @close="handleDrawerClose"
    >
      <application-info
        v-if="applicationDrawerVisible"
        :id="currentRow.id || ''"
        ref="_appRef"
        :view-state="viewState"
        :application="currentApplication"
        :receiver="receiverList"
        :load-type="'view'"
        :visible.sync="applicationDrawerVisible"
        @loading="handleLoading"
      />
    </public-drawer>
  </div>
</template>

<script>
import { selectForSpecialFundAdjust } from '@/api/payment/specialFund.js'
import { paymentExpenditureTypeTree } from '@/api/payment/expenseType'
import { page as billingMethodPage } from '@/api/payment/billingMethod'
import { comboJson } from '@/api/gever/common.js'
import moment from 'moment/moment'
import applicationInfo from '@/views/payment/approval/approvalApplication/applicationInfo.vue'
import { getApplication } from '@/api/payment/approval'

export default {
  name: 'SpecialApplicationSelect',
  components: {
    applicationInfo
  },
  props: {
    orgCode: {
      type: String,
      default: ''
    },
    receiverBankAccount: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      queryParams: {
        page: 1,
        rows: 20,
        serialNumber: '',
        fundsUse: '',
        expenditureType: '',
        details: '',
        startCreationTime: '',
        endCreationTime: '',
        payAccountNumber: '',
        receiverAccount: ''
      },
      settlementMethodOptins: [{ id: '', settleName: this.$t('全部') }],
      applicationTypeOptions: [
        { id: 0, text: '常规用款' },
        { id: 1, text: '收支相抵' },
        { id: 2, text: '冲减收入' },
        { id: 3, text: '非预算项目支出' },
        { id: 4, text: '银行代扣' },
        { id: 5, text: '扣减预算' },
        { id: 6, text: '非预算收支相抵' }
      ],
      tableList: [],
      total: 0,
      tableColumns: [
        { type: 'selection', width: '50', fixed: 'left' },
        { type: 'index', label: '序号', width: '80', fixed: 'left' },
        {
          label: '申请编号',
          prop: 'serialNumber',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '支出类型',
          prop: 'expenditureType',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '支付金额（元）',
          prop: 'amountPayable',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '已调账金额（元）',
          prop: 'adjustedAmount',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '未调账金额（元）',
          prop: 'unAdjustedAmount',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '申请类型',
          prop: 'applicationType',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '结算方式',
          prop: 'settlementMethod',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '申请日期',
          prop: 'creationTime',
          width: '150',
          filter: 'date',
          showOverflowTooltip: true
        },
        {
          label: '资金用途',
          prop: 'fundsUse',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '申请详情',
          prop: 'details',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '付款账号',
          prop: 'payAccountNumber',
          width: '180',
          showOverflowTooltip: true
        },
        {
          label: '收款方',
          prop: 'receiverAccount',
          width: '150',
          showOverflowTooltip: true
        },
/*        {
          label: '支付金额',
          prop: 'amountPayable',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money'
        },*/
        {
          label: '备注',
          prop: 'remark',
          width: '150',
          showOverflowTooltip: true
        }/*,
        {
          label: '操作',
          slotName: 'operation',
          width: '100',
          fixed: 'right'
        }*/
      ],
      selectedApplications: [],
      expendTypeComboOptions: [],
      approvalstatusOptions: [],
      fundSrcOptions: [],
      dateTime: [],
      payingOrgOptions: [],
      applicationDrawerVisible: false,
      drawerTitle: '',
      loading: false,
      currentRow: {},
      currentApplication: {},
      receiverList: [],
      viewState: 0
    }
  },
  created() {
    this.loadAllJson()
    this.getList()
  },
  methods: {
    async getList() {
      this.tableLoading = true
      this.queryParams.orgCode = this.orgCode
     // this.queryParams.payAccountNumber = this.receiverBankAccount
      try {
        const { data } = await selectForSpecialFundAdjust(this.queryParams)
        this.tableList = data.records
        this.total = data.total
      } finally {
        this.tableLoading = false
      }
    },
    disabledCreationTimeStart(time) {
      if (this.queryParams.endCreationTime) {
        const endDateStartOfDay = moment(this.queryParams.endCreationTime).startOf('day').toDate().getTime()
        const currentTimeStartOfDay = moment(time).startOf('day').toDate().getTime()
        return currentTimeStartOfDay > endDateStartOfDay
      }
      return false
    },
    disabledCreationTimeEnd(time) {
      if (this.queryParams.startCreationTime) {
        const startDateStartOfDay = moment(this.queryParams.startCreationTime).startOf('day').toDate().getTime()
        const currentTimeStartOfDay = moment(time).startOf('day').toDate().getTime()
        return currentTimeStartOfDay < startDateStartOfDay
      }
      return false
    },
    handleSelectionChange(selection) {
      this.selectedApplications = selection
    },
    async loadAllJson() {
      paymentExpenditureTypeTree({ page: 1, rows: 100 }).then((res) => {
        if (res.returnCode === '0') {
          this.expendTypeComboOptions.push(...res.data)
        }
      })
      comboJson({ path: '/payment/approvalstatus' }).then((res) => {
        this.approvalstatusOptions.push({ id: ' ', text: this.$t('全部') })
        this.approvalstatusOptions.push(...res.data)
      })
      comboJson({ path: '/payment/source_of_funds/' }).then((res) => {
        this.fundSrcOptions.push(...res.data)
      })
      // 结算方式
      billingMethodPage({ page: 1, rows: 100, parentId: '0' }).then((res) => {
        this.settlementMethodOptins = res.data.rows
      })
    },
    getExpenditureTypeText(val, options) {
      const valArr = val ? val.split(',') : []
      const textArr = []
      this[options].forEach((item) => {
        if (valArr.includes(item.id)) {
          textArr.push(item.text)
        }
      })
      return textArr.join(',')
    },
    convertApprovalStatus(val) {
      const status = this.approvalstatusOptions.find(
        (item) => item.id.toString() === val.toString()
      )
      return status ? status.text : ''
    },
    convertFundSrc(val) {
      const fundSrc = this.fundSrcOptions.find(
        (item) => item.id === val
      )
      return fundSrc ? fundSrc.text : ''
    },
    converSettlementMethod(val) {
      const settlementMethod = this.settlementMethodOptins.find(
        (item) => item.id == val
      )
      return settlementMethod ? settlementMethod.settleName : ''
    },
    convertApplicationType(val) {
      const applicationType = this.applicationTypeOptions.find(
        (item) => item.id == val
      )
      return applicationType ? applicationType.text : ''
    },
    confirmSelection() {
      this.$emit('confirm', this.selectedApplications)
    },
    dateChange() {
      this.queryParams.startCreationTime = this.dateTime ? this.dateTime[0] : ''
      this.queryParams.endCreationTime = this.dateTime ? this.dateTime[1] : ''
    },
    handleViewApplicationForm(row) {
      this.loading = true
      this.drawerTitle = row.returnFlag ? '查看申请退款' : '查看支出申请'
      this.currentRow = row
      this.viewState = row.approvalStatus
      this.applicationDrawerVisible = true
      getApplication(row.approvalApplicationId)
        .then((res) => {
          console.log(res)
          this.currentApplication = res.data[0]
          this.receiverList = res.data[1]
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleDrawerClose() {
      this.currentApplication = {}
    },
    handleLoading(flag) {
      this.loading = flag
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
