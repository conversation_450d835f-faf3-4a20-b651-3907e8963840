<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-14 10:41:56
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-19 10:00:39
-->
<template>
  <div>
    <fold-box right-title="专项资金类别明细" left-title="专项资金类别">
      <template #left>
        <div class="ogn-tree">
          <el-tree
            ref="areaTreeRef"
            lazy
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            :load="loadChildNode"
            @node-click="handleNodeChange"
          />
        </div>
      </template>
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('专项资金类别名称')">
              <el-input v-model="queryParams.categoryName" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金编码')">
              <el-input v-model="queryParams.categoryCode" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('数据状态')">
              <gever-select
                v-model="queryParams.status"
                class="w200"
                path="/financial/specialFund/category/status/"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                plain
                icon="el-icon-search"
                type="primary"
                round
                @click="handleSearch"
              >{{ $t('搜索') }}</el-button>
            </el-form-item>
            <div style="float: right;">
              <el-form-item>
                <el-button
                  v-hasPermi="'specialFund.category.save'"
                  type="primary"
                  round
                  icon="el-icon-plus"
                  @click="handleAdd"
                >{{ $t('新增') }}</el-button>
              </el-form-item>
              <!-- <el-form-item>
                <el-button
                  v-hasPermi="'specialFund.category.updateStatus'"
                  type="primary"
                  round
                  @click="updateStatus(1)"
                >{{ $t('生效') }}</el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'specialFund.category.updateStatus'"
                  type="primary"
                  round
                  @click="updateStatus(0)"
                >{{ $t('失效') }}</el-button>
              </el-form-item> -->
              <el-form-item>
                <el-button
                  v-hasPermi="'specialFund.category.delete'"
                  plain
                  type="primary"
                  round
                  icon="el-icon-delete"
                  @click="handleRemove"
                >{{ $t('删除') }}</el-button>
              </el-form-item>
            </div>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            :loading="tableLoading"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
          >
            <template #status="{ row }">
              <div>
                <!-- <span>{{ getTitle(row.status+'', optionsMap.status) }}</span> -->
                <el-switch
                  v-model="row.status"
                  v-hasPermi="'specialFund.category.updateStatus'"
                  :active-value="1"
                  :inactive-value="0"
                  @change="updateStatus(row)"
                >
                </el-switch>
              </div>
            </template>
            <template #createTime="{ row }">{{ row.createTime?row.createTime.substring(0,10):"" }}</template>
            <template #parentName="{ row }">{{ row.parentCode+' '+row.parentName }}</template>
            <template #operation="{ row }">
              <el-button
                v-hasPermi="'specialFund.category.view'"
                type="text"
                @click="handleView(row)"
              >{{ $t('查看') }}</el-button>
              <el-button
                v-hasPermi="'specialFund.category.update'"
                :disabled="row.status == 1"
                type="text"
                @click="handleEdit(row)"
              >{{ $t('编辑') }}</el-button>
              <el-button
                v-hasPermi="'specialFund.category.delete'"
                :disabled="row.status == 1"
                type="text"
                @click="handleRemove(row,true)"
              >{{ $t('删除') }}</el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <public-drawer
      :visible.sync="detailVisible"
      :title="detailTitle"
      :size="50"
      :buttons="detailButtons"
      @close="handleCloseDetail"
    >
      <detail
        v-if="detailVisible"
        :id="currentId"
        ref="_detailContentRef"
        :type="type"
        :cur-tree="curTree"
        :area="tradingLogin"
        :form-data="formData"
        @getAreaTree="getAreaTree"
        @findAndUpdateNode="findAndUpdateNode"
      />
    </public-drawer>
  </div>
</template>

<script>
import { loadCategoryListApi, deleteCategoryApi, selectByParentId, updateStatus } from '@/api/specialFund/category.js'
import detail from './components/detail'
// import { createNamespacedHelpers } from 'vuex'
// const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
export default {
  name: 'Category',
  components: {
    detail
  },
  filters: {
  },
  data () {
    return {
      selectedNode: null,
      queryParams: {
        page: 1,
        rows: 20,
        orgId: '',
        areaId: ''
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50' },
          { type: 'index', label: '序号', width: '50' },
          { label: '登记单位', prop: 'areaName', width: '150' },
          { label: '专项资金编号', prop: 'categoryCode', width: '120' },
          { label: '专项资金类别名称', prop: 'categoryName', width: '270' },
          { label: '父级类别', prop: 'parentName', width: '270' },
          { label: '状态', prop: 'status', width: '100' },
          { label: '创建人', prop: 'creatorName', width: '100' },
          { label: '创建日期', prop: 'createTime', minWidth: '100' },
          { label: '操作', slotName: 'operation', width: '130', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('确 定'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ],
      treeData: [
      ],
      tableLoading: false,
      treeParams: {
        parentId: '0'
      },
      curTree: {},
      selectionData: {},
      optionsMap: {
        status: []
      },
      formData: {},
      curTreeNode: {},
      defaultProps: {
        children: 'children',
        label: 'text',
        isLeaf: 'isLeaf'
      },
      timer: null,
      tradingLogin: {}
    }
  },
  computed: {
    // ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    // areaLogin: {
    //   deep: true,
    //   immediate: true,
    //   handler () {
    //     this.getList()
    //   }
    // }
  },
  created () {
    // this.getList()
    this.getOptions()
    this.timer = setInterval(() => {
      const storageVal = JSON.parse(sessionStorage.getItem('areaLoginCopy'))
      if (storageVal != null && storageVal.id && this.tradingLogin.id !== storageVal.id) {
        this.tradingLogin = storageVal
        this.getList(storageVal)
        this.selectByParentId(storageVal)
      }
    }, 100)
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  methods: {
    getStatus(status) {
      return status == '1'
    },
    async getOptions () {
      const { data: status = [] } = await getDictionary('/financial/specialFund/category/status/')
      this.optionsMap.status = status
    },
    getList () {
      if (this.queryParams.parentId) {
        this.tableLoading = true
        this.queryParams.areaCode = this.tradingLogin.code
        loadCategoryListApi(this.queryParams).then(res => {
          this.table.tableList = res.data.rows
          this.table.total = res.data.total
        }).finally(() => {
          this.tableLoading = false
        })
      }
    },
    selectByParentId (area) {
      this.treeParams.parentId = 'root'
      if (area) {
        this.treeParams.areaCode = area.code
      }
      this.curTree = {}
      selectByParentId(this.treeParams).then(res => {
        this.treeData = []
        res.data.map((cur) => {
          this.treeData.push({
            id: cur.id,
            state: 'closed',
            text: cur.categoryCode + ' ' + cur.categoryName,
            categoryName: cur.categoryName,
            parentCode: cur.categoryCode,
            name: cur.categoryName,
            parentId: cur.parentId,
            isLeaf: cur.isLeaf
          })
        })
      }).finally(() => {
      })
    },
    handleSearch () {
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleAdd () {
      if (this.tradingLogin.areaLevel != '3') {
        this.$message.warning(this.$t('请先选择区级地区！'))
        return
      }
      if (this.curTree.id == undefined) {
        this.$message.warning(this.$t('请先选择资产类别！'))
        return
      }
      this.detailTitle = '新增'
      this.type = 'add'
      this.currentId = ''
      this.detailButtons = [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('确 定'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
      this.detailVisible = true
    },
    handleView (row) {
      this.detailTitle = '查看'
      this.type = 'view'
      this.currentId = row.id
      this.formData = row
      this.detailButtons = [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        }
      ]
      this.detailVisible = true
    },
    handleEdit (row) {
      this.detailTitle = '编辑'
      this.type = 'edit'
      this.currentId = row.id
      this.formData = row
      this.detailButtons = [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('确 定'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
      this.detailVisible = true
    },
    handleRemove (row, batchFlag) {
      let ids
      if (batchFlag) {
        ids = row.id
      } else {
        if (this.tableIdSelection.length == 0) {
          return this.$message.warning(this.$t('请先选择要删除的数据！'))
        }
        ids = this.tableIdSelection.join(',')
      }
      const data = this.tableSelection.filter(item => item.status == 1)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择状态为“失效”的数据！'))
      }
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        deleteCategoryApi({ ids }).then(res => {
          if (res) {
            this.$message.success(res.message)
            this.getList()
            this.removeNode(ids)
          }
        })
      }).catch(() => { })
    },
    handleCloseDetail () {
      this.detailVisible = false
    },
    handleSave () {
      this.$refs._detailContentRef.save()
    },
    loadChildNode (node, resolve) {
      console.log('11', node)
      if (node.key != undefined) {
        selectByParentId({ parentId: node.data.id, areaCode: this.tradingLogin.code }).then((res) => {
          const curTree = []
          res.data.map((cur) => {
            curTree.push({
              id: cur.id,
              state: 'closed',
              text: cur.categoryCode + ' ' + cur.categoryName,
              categoryName: cur.categoryName,
              parentCode: cur.categoryCode,
              name: cur.categoryName,
              parentId: cur.parentId,
              isLeaf: cur.isLeaf
            })
          })
          resolve(curTree)
        })
      }
    },
    // 处理类型树切换
    handleNodeChange (data, node) {
      this.curTree = data
      this.curTreeNode = node
      console.log('this.curTreeNode', this.curTreeNode)

      this.queryParams.parentId = data.id
      // this.$nextTick(() => {
        this.getList()
      // })
    },
    selectionChange (data) {
      this.selectionData = data
    },
    getAreaTree () {
      this.getList()
      this.handleCloseDetail()
      // this.selectByParentId()
    },
    updateStatus (row) {
      // if (this.tableIdSelection.length == 0) {
      //   return this.$message.warning(this.$t('请先选择要操作的数据！'))
      // }
      // const data = this.tableSelection.filter(item => item.status == status)
      // if (data.length != 0) {
      //   if (status == 1) {
      //     return this.$message.warning(this.$t('请先选择状态为“失效”的数据！'))
      //   } else {
      //     return this.$message.warning(this.$t('请先选择状态为“生效”的数据！'))
      //   }
      // }
      // this.$confirm('是否将选中数据' + (status == 1 ? '生效' : '失效') + '？', status == 1 ? '生效' : '失效', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      console.log(row)
        updateStatus({ ids: row.id, status: row.status }).then(res => {
          this.$message.success(res.message)
          this.getList()
        })
      // }).catch(() => {
      // })
    },
    findAndUpdateNode (type, addData) {
      if (type === 'edit') {
        this.updateNode(addData)
      }
      if (type === 'add') {
        if (this.curTreeNode.expanded) {
          const data = Object.assign({}, this.curTreeNode.childNodes[0])
          data.id = Math.random().toString(36).substr(2, 8)
          data.data = {
            id: addData.id,
            state: 'closed',
            text: addData.categoryCode + ' ' + addData.categoryName,
            categoryName: addData.categoryName,
            parentCode: addData.categoryCode,
            name: addData.categoryName,
            parentId: addData.parentId,
            isLeaf: true
          }
          // data.parent.text = addData.categoryCode + ' ' + addData.categoryName
          data.key = addData.id
          data.label = addData.categoryCode + ' ' + addData.categoryName
          this.curTreeNode.data.isLeaf = false
          this.curTreeNode.data.hasChildren = true
          this.curTreeNode.isLeafByUser = false
          this.curTreeNode.isLeaf = false
          this.curTreeNode.childNodes.push(data)
        } else {
          console.log('新增2', this.curTreeNode)
          this.curTreeNode.data.isLeaf = false
          this.curTreeNode.data.hasChildren = true
          this.curTreeNode.isLeafByUser = false
          this.curTreeNode.isLeaf = false
          this.curTreeNode.loaded = false
        }
      }
    },
    // 修改节点
    updateNode (cur) {
      console.log('this.curTreeNode', this.curTreeNode)
      console.log('cur', cur)
      for (let i = 0; i < this.curTreeNode.childNodes.length; i++) {
        if (this.curTreeNode.childNodes[i].data.id == cur.id) {
          this.curTreeNode.childNodes[i].data = {
            id: this.curTreeNode.childNodes[i].data.id,
            state: 'closed',
            text: cur.categoryCode + ' ' + cur.categoryName,
            categoryName: this.curTreeNode.childNodes[i].data.categoryName,
            parentCode: this.curTreeNode.childNodes[i].data.categoryCode,
            name: cur.categoryName,
            parentId: this.curTreeNode.childNodes[i].data.parentId,
            isLeaf: this.curTreeNode.childNodes[i].data.isLeaf
          }
          this.curTreeNode.childNodes[i].parent.text = cur.categoryCode + ' ' + cur.categoryName
          this.curTreeNode.childNodes[i].label = cur.categoryCode + ' ' + cur.categoryName
        }
      }
    },
    removeNode (ids) {
      console.log('删除1', this.curTreeNode)
      if (this.curTreeNode.expanded) {
        console.log('删除2', this.curTreeNode)
        // 2. 获取父节点
        const parent = this.curTreeNode
        const idsArray = ids.split(',')
        idsArray.forEach(id => {
          // 3. 从父节点children中移除
          const index = parent.childNodes.findIndex(
            child => child.data.id === id
          )
          if (index !== -1) {
            parent.childNodes.splice(index, 1)
          }
        })
        // 如果删除后没有子节点，更新hasChildren状态
        if (parent.childNodes.length == 0) {
          this.$set(parent.data, 'hasChildren', false)
          this.$set(parent.data, 'isLeaf', true)
          this.$set(parent, 'isLeaf', true)
          this.$set(parent, 'expanded', false)
          this.$set(parent, 'loaded', false)
        }
      } else {
        this.$set(this.curTreeNode, 'loaded', false)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree-node__content {
  min-height: 60px !important;
  font-size: 11px !important;
  //border-bottom: 1px solid #d7d7d7;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 10px;
  cursor: pointer;
  white-space: normal !important; /* 允许内容换行 */
  line-height: normal !important; /* 根据需要调整行高 */
  height: auto !important; /* 如果设置了固定高度，则需要覆盖 */
  overflow: visible !important; /* 确保内容不会被隐藏 */
}
/* 可选：如果你希望控制每行的最大高度或是其他样式 */
::v-deep .el-tree-node__label {
  display: inline-block;
  max-width: 200px; /* 根据你的需求调整最大宽度 */
  word-break: break-all; /* 在单词内也进行换行 */
}
// ::v-deep .el-tree-node:focus>.el-tree-node__content {
//   background: #c8e4fe;
//   transition: transform 0.3s ease;
// }
::v-deep .el-tree-node.is-current > .el-tree-node__content {
  background-color: #c8e4fe !important;
}
// .active {
//   color: #000;
//   background: #c8e4fe;
//   border-left: 2px solid #1890ff;
// }
.arrow-icon {
  transition: transform 0.3s ease;
}

.rotate-90 {
  transform: rotate(90deg);
}
</style>
