import request from '@/utils/request'


/**
 * @name:获取列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
    return request({
        url: '/financial/accounting/accountingBatchMonthEnded/listAccountingBatchMonthEnded',
        method: 'post',
        data,
        withCredentials: true
    })
}



/**
 * @name:批量月结
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function monthEnded(data) {
    return request({
        url: '/financial/accounting/accountingBatchMonthEnded/monthEnded',
        method: 'post',
        data,
        withCredentials: true
    })
}


/**
 * @name:批量反月结
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unMonthEnded(data) {
    return request({
        url: '/financial/accounting/accountingBatchMonthEnded/unMonthEnded',
        method: 'post',
        data,
        withCredentials: true
    })
}

/**
 * 获取地区机构树
 */
 export function loadRegionTree(params) {
    return request({
      url: '/financial/report/collection/regionTree',
      method: 'post',
      params: params
    })
  }