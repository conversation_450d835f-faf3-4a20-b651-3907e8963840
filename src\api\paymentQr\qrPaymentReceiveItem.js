import request from '@/utils/request'
/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentReceiveItemListApi(params) {
  return request({
    url: '/qrpayment/item/page',
    method: 'get',
    params
  })
}

/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentReceiveItemOptionsApi(params) {
  return request({
    url: '/qrpayment/item/list',
    method: 'get',
    params
  })
}

/**
 * 生效/失效
 * @param {*} id 
 * @returns 
 */
export function setVoidApi(id) {
  return request({
    url: '/qrpayment/item/setVoid/'+id,
    method: 'post',
  })
}

/**
 * @name: 详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentReceiveItemInfoApi(id) {
  return request({
    url: `/qrpayment/item/get/${id}`,
    method: 'get'
  })
}

/**
 * @name: 删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteQrPaymentReceiveItemApi(data) {
  return request({
    url: '/qrpayment/item/deleteMul',
    method: 'post',
    data,
    payload:true
  })
}


/**
 * @name: 新增保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveQrPaymentReceiveItemApi(data) {
  return request({
    url: '/qrpayment/item/save',
    method: 'post',
    data
  })
}

/**
 * @name: 更新保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateQrPaymentReceiveItemApi(data) {
  return request({
    url: '/qrpayment/item/update',
    method: 'post',
    data
  })
}