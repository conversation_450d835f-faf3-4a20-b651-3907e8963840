/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-07 11:11:17
 * @LastEditors: Andy
 * @LastEditTime: 2023-09-09 16:30:54
 * @FilePath: \rural-financial\src\api\financial\custom-report\definedReport.js
 */
import request from '@/utils/request'

// 报表授权保存
export function saveX (data) {
  return request({
    url: '/financial/customReport/customReportAuth/saveX',
    method: 'post',
    data,
    payload: true
  })
}
// 查询已授权角色
export function getDefineAuth (params) {
  return request({
    url: '/financial/customReport/customReportAuth/getDefineAuth',
    method: 'get',
    params,
    withCredentials: true
  })
}
// 查询列表 {groupId}参数可以指定不同的分组
export function query (data) {
  return request({
    url: '/financial/customReport/customReportDefine/page',
    method: 'post',
    data,
    withCredentials: true
  })
}
// insert插入单个
export function insert (data) {
  return request({
    url: '/financial/customReport/customReportDefine/insert',
    method: 'post',
    data,
    withCredentials: true
  })
}

// post 或者 get 查询单个，id参数1，2，3。。。。。。。分别表示不同报表组
export function detail (id) {
  return request({
    url: `/financial/customReport/customReportDefine/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

// 报表更新
export function update (data) {
  return request({
    url: `/financial/customReport/customReportDefine/update`,
    method: 'post',
    data,
    withCredentials: true
  })
}

// 删除报表
export function del (data) {
  return request({
    url: `/financial/customReport/customReportDefine/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}

//复制
export function copy (data) {
  return request({
    url: '/financial/customReport/customReportDefine/copy',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

//获取账套类型
export function selectAllBooksType () {
  return request({
    url: `/financial/customReport/customReportDefine/selectAllBooksType`,
    method: 'post',
    withCredentials: true
  })
}