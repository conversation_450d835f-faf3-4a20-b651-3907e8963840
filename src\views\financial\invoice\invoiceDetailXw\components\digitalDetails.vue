
<template>
  <gever-dialog
    :title="$t(`发票详情`)"
    :visible="dialogVisible"
    width="90%" style="margin-top: 6vh; height: 80vh"
    :append-to-body="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="dialog-box">
      <el-form
        ref="_formRef"
        :inline="true"
        :model="form"
        label-position="right"
        label-width="120px"
        class="gever-form"
        disabled
      >
        <div class="form-title">
          <span>{{$t(rowData.typeName)}}</span>
          <span v-if="rowData.offsetState === 1" style="color: red;"><br>（红冲票） </span>
          <span v-else-if="rowData.offsetState === 2" style="color: red;"><br>（被红冲）</span>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('发票号码：')" prop="invoiceNo">
            <el-input
              v-model="rowData.invoiceNo"
              type="text"
              disabled
            />
          </el-form-item>
          <el-form-item :label="$t('开票日期：')" prop="drawDate">
            <el-date-picker
              v-model="rowData.drawDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              disabled
            />
          </el-form-item>
          <el-form-item :label="$t('原发票号：')" prop="offsetSourceNo">
            <el-input
              v-model="rowData.offsetSourceNo"
              type="text"
              disabled
            />
          </el-form-item>
          <el-form-item :label="$t('全电纸质发票号：')" prop="offsetInvoiceNo">
            <el-input
              v-model="rowData.offsetInvoiceNo"
              type="text"
              disabled
            />
          </el-form-item>
        </div>
        <!-- 付款方信息 -->
        <div class="partition-box">
          <div class="partition-left">
            <div class="partition-title">购方信息</div>
            <div class="form-sg">
              <el-form-item label="名称：">
                <el-input v-model="rowData.payerName" disabled />
              </el-form-item>
            </div>
            <div class="form-sg">
              <el-form-item label="税号：">
                <el-input v-model="rowData.payerTaxpayerNo" disabled />
              </el-form-item>
            </div>
            <div class="form-sg">
              <el-form-item label="地址、电话：">
                <el-input v-model="rowData.payerContactInfo" disabled />
              </el-form-item>
            </div>
            <div class="form-sg">
              <el-form-item label="开户行、账号：">
                <el-input v-model="rowData.payerAccountInfo" disabled />
              </el-form-item>
            </div>
          </div>

          <!-- 收款方信息 -->
          <div class="partition-right">
            <div class="partition-title">销方信息</div>
            <div class="form-sg">
              <el-form-item label="名称：">
                <el-input v-model="rowData.sellerName" disabled />
              </el-form-item>
            </div>
            <div class="form-sg">
              <el-form-item label="税号：">
                <el-input v-model="rowData.sellerTaxpayerNo" disabled />
              </el-form-item>
            </div>
            <div class="form-sg">
              <el-form-item label="地址、电话：">
                <el-input v-model="rowData.sellerContactInfo" disabled />
              </el-form-item>
            </div>
            <div class="form-sg">
              <el-form-item label="开户行、账号：">
                <el-input v-model="rowData.sellerAccountInfo" disabled />
              </el-form-item>
            </div>
          </div>
        </div>

        <!-- 开票信息表格 -->
        <div class="table-box">
          <div class="table-head">
            <div class="table-title">开票信息</div>
          </div>
          <gever-table
            ref="_geverTableRef"
            :columns="tableColumns"
            :data="table.rows"
            highlight-current-row
            height="170"
            :pagination="false"
            show-summary
            :summary-method="getSummaries"
            :class="rowData.offsetType !== 0 ? 'red-status' : ''"
          >
          </gever-table>
          <div class="table-footer">
            <div class="footer-box">
              <span>价税合计（大写）：</span>
              <span :class="rowData.offsetType !== 0 ? 'red' : ''">{{ smallToBig(rowData.totalAmount) }}</span>
            </div>
            <div class="footer-box">
              <span>价税合计：</span>
              <span :class="rowData.offsetType !== 0 ? 'red' : ''">{{ comma(rowData.totalAmount) }}</span>
            </div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="form-sg">
          <el-form-item label="备注：">
            <el-input v-model="rowData.remark" type="textarea" disabled />
          </el-form-item>
        </div>

        <!-- 摘要 -->
        <div class="form-sg">
          <el-form-item label="摘要：">
            <el-input v-model="rowData.summary" type="textarea" disabled />
          </el-form-item>
        </div>

        <!-- 收款人/复核人/开票人 -->
        <div class="form-sg">
          <el-form-item label="收款人：">
            <el-input v-model="rowData.payee" disabled />
          </el-form-item>
          <el-form-item label="复核人：">
            <el-input v-model="rowData.reviewer" disabled />
          </el-form-item>
          <el-form-item label="开票人：">
            <el-input v-model="rowData.drawer" disabled />
          </el-form-item>
        </div>
      </el-form>
    </div>
  </gever-dialog>
</template>

<script>
import { getInvoiceDetailXwItem } from '@/api/financial/invoice/invoiceDetailXw.js'
import { createNamespacedHelpers } from 'vuex'


const { mapState } = createNamespacedHelpers('area')

export default {
  name: 'InvoiceDetailView',
  props: {
    dialogVisible: { type: Boolean, default: false },
    rowData: {
      type: Object,
      default: () => ({})
    },
    orgId: { type: String, default: '' }
  },
  data() {
    return {
      loading: false,
      form: {
        totalAmount: 0
      },
      table: {
        rows: []
      },
      tableColumns: [
        { prop: 'taxKindCode', label: '分类编码', align: 'left' },
        { prop: 'chargeItem', label: '收费项目', align: 'left' },
        { prop: 'model', label: '型号', align: 'left' },
        { prop: 'unit', label: '计量单位', align: 'left' },
        { prop: 'qty', label: '数量', align: 'right', width: 150 },
        { prop: 'price', label: '单价', align: 'right' },
        { prop: 'saleAmount', label: '金额', align: 'right' },
        { prop: 'includeTax', label: '含税', align: 'center' },
        { prop: 'taxRate', label: '税率', align: 'center' },
        { prop: 'taxAmount', label: '税额', align: 'right' },
        { prop: 'taxableAmount', label: '应税金额', align: 'right' }
      ],
      taxKindCodeMap: {}
    }
  },
  computed: {
    ...mapState(['areaLogin']),
  },
  watch: {
    dialogVisible: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getList()
        }
      }
    }
  },
  methods: {
    async getList() {
      if (!this.rowData.id) return
      this.loading = true
      try {
        const res = await getInvoiceDetailXwItem({ id: this.rowData.id })
        // 提取发票明细作为表格数据
        this.table.rows = res.data || []
        // 可选：格式化金额字段（如果你需要）
        this.table.rows.forEach((item) => {
          item.qty = this.comma0(item.qty)
          item.price = this.comma(item.price)
          item.saleAmount = this.comma(item.saleAmount)
        })

      } catch (error) {
        console.error('获取发票详情失败:', error)
        this.$message.error('获取发票详情失败')
      } finally {
        this.loading = false
      }
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计:'
          return
        }
        if ([6, 7, 9, 10].includes(index)) {
          // 修改这里，先移除千位分隔符再转换
          const values = data.map(item => {
            const value = item[column.property] || ''
            return Number(String(value).replace(/,/g, ''))
          })
          const sumVal = values.reduce((prev, curr) => prev + curr, 0)
          sums[index] = this.comma(sumVal)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    smallToBig(money) {
      // 处理负数情况
      let isNegative = false;
      if (typeof money === 'string') {
        money = money.trim();
        if (money.startsWith('-')) {
          isNegative = true;
          money = money.substring(1);
        }
      } else if (typeof money === 'number' && money < 0) {
        isNegative = true;
        money = Math.abs(money);
      }

      // 原有的小写转大写逻辑
      const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
      const cnIntRadice = ['', '拾', '佰', '仟']
      const cnIntUnits = ['', '万', '亿']
      const cnIntLast = '元'
      const cnDecUnits = ['角', '分']

      money = parseFloat(money)
      if (isNaN(money) || money === 0) return '零元整'
      let integerNum = Math.floor(money).toString()
      let decimalNum = (money - Math.floor(money)).toFixed(2).substring(2)
      let chineseStr = ''
      let zeroCount = 0
      const IntLen = integerNum.length
      for (let i = 0; i < IntLen; i++) {
        const n = integerNum[i]
        const p = IntLen - i - 1
        const q = Math.floor(p / 4)
        const m = p % 4

        if (n === '0') {
          zeroCount++
        } else {
          if (zeroCount > 0) chineseStr += '零'
          zeroCount = 0
          chineseStr += cnNums[n] + cnIntRadice[m]
        }

        if (m === 0 && zeroCount < 4) {
          chineseStr += cnIntUnits[q]
        }
      }

      chineseStr += cnIntLast

      if (decimalNum && decimalNum !== '00') {
        for (let i = 0; i < 2; i++) {
          const n = decimalNum[i]
          if (n !== '0') {
            chineseStr += cnNums[n] + cnDecUnits[i]
          }
        }
      } else {
        chineseStr += '整'
      }

      // 添加负号前缀（如果是负数）
      return isNegative ? '负' + chineseStr : chineseStr
    },
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    comma(val, keep_dec = 2) {
      if (!val && val !== 0) return '0.00'
      val = Number(val).toFixed(keep_dec)
      const [intPart, decPart] = val.toString().split('.')
      const formattedInt = intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      return decPart ? `${formattedInt}.${decPart}` : formattedInt
    },
    comma0(val) {
      if (!val && val !== 0) return ''
      val = String(val)
      if (val.indexOf('.') === -1) {
        return val.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,')
      }
      return val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
        '.' + val.split('.')[1]
    },
    comma9(val) {
      if (!val && val !== 0) return '0.00'
      val = Number(val).toFixed(9)
      const [intPart, decPart] = val.toString().split('.')
      return intPart.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') + '.' + decPart
    },
    thousand(val, keep_dec = 2) {
      return Number(val).toFixed(keep_dec)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__row {
  .el-input--mini .el-input__inner {
    border: 0;
    padding: 0 0 0 3px;
  }
}
::v-deep .el-form-item {
  margin-bottom: 15px;
}
.dialog-box {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.form-title {
  text-align: center;
  span {
    font-size: 20px;
  }
}
.form-sg, .form-title, .partition-box, .table-box {
  margin: 5px 0;
}
.partition-box {
  display: flex;
  justify-content: space-between;
  margin: 5px 0;

  .partition-left,
  .partition-right {
    width: 49%;
    .partition-title {
      font-size: 18px;
      color: #409eff;
      margin-bottom: 10px;
    }
  }
}

.table-box {
  margin: 5px 0 5px 0;

  .table-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    .footer-box {
      display: flex;
      align-items: center;

      span:first-child {
        margin-right: 5px;
      }

      span:last-child {
        display: inline-block;
        line-height: 25px;
        min-width: 100px;
        height: 25px;
        border-bottom: 1px solid #0080ff;
      }
    }
  }
}

.red {
  color: red;
}

::v-deep .red-status .el-table__footer-wrapper tbody td {
  color: red;
}
</style>
