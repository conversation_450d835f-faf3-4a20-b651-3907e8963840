/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-11-15 14:17:04
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-15 14:17:30
 */

import request from '@/utils/request'
/**
 * @name:票据详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/bill/billType/load/${id}`,
    method: 'get'
  })
}
// 获取表格内容
export function billTypeList(data) {
  return request({
    url: '/bill/billType/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 票据新增
export function billTypeAdd(data) {
  return request({
    url: '/bill/billType/save',
    method: 'post',
    data: data
  })
}

// 票据新增
export function billPrintStyleAdd(data) {
  return request({
    url: '/bill/billPrintStyle/save',
    method: 'post',
    data: data
  })
}

// 票据删除
export function billTypeRemove(data) {
  return request({
    url: `/bill/billType/delete`,
    method: `post`,
    data: data
  })
}

// 套打模板
export function setToPlayATemplate(data) {
  return request({
    url: `/bill/billPrintStyle/loadPrintStyle?templateId=${data.templateId}&billTypeId=${data.id}`,
    method: `get`
  })
}

// 查看模块
export function billTypeCheck(id) {
  return request({
    url: `/bill/billType/load/${id}`,
    method: `get`
  })
}
// 编辑模块
export function billTypeRedact(id) {
  return request({
    url: `/bill/billType/load/${id}`,
    method: `get`
  })
}

// 确认编辑
export function confirmTheEditor(data) {
  return request({
    url: '/bill/billType/save',
    method: 'post',
    data: data
  })
}

// 套打模板
export function playTemplate() {
  return request({
    url: `/bill/billTemplate/loadTemplateList`,
    method: `get`
  })
}

export function templatePage(params) {
  return request({
    url: `/bill/billTemplate/page`,
    method: 'post',
    data:params
  })
}



export function templateLoad(id) {
  return request({
    url: `/bill/billTemplate/load/${id}`,
    method: 'get',
  })
}


export function templateSave(params) {
  return request({
    url: `/bill/billTemplate/save`,
    method: 'post',
    data:params
  })
}

export function doImportBgPic(params) {
  return request({
    url: `/bill/billTemplate/doImportBgPic`,
    method: 'post',
    data:params
  })
}


export function exportTemplate(params) {
  return request({
    url: `/bill/billTemplate/exportTemplate`,
    method: 'get',
    params
  })
}

export function templateDelete(params) {
  return request({
    url: `bill/billTemplate/delete`,
    method: 'post',
    data:params
  })
}