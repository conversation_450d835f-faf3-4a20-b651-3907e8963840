<template>
  <div style="height: 100%">
    <el-form ref="_formRef" :model="queryParams" :inline="true">
      <el-form-item :label="$t('资金用途')">
        <el-input
          v-model="queryParams.bussinessTitle"
          placeholder=""
          clearable
          class="w200"
          size="mini"
        />
      </el-form-item>
      <el-form-item :label="$t('审批事项名称')">
        <el-input
          v-model="queryParams.approvalSettingName"
          placeholder=""
          clearable
          class="w200"
          size="mini"
        />
      </el-form-item>
      <!-- <el-form-item :label="$t('交办单位')">
        <el-input
          v-model="queryParams.orgId"
          placeholder=""
          clearable
          class="w200"
          size="mini"
        />
      </el-form-item> -->
      <el-form-item :label="$t('申请类型')">
        <el-select v-model="queryParams.applicationType" clearable class="w150">
          <el-option
            v-for="item in applicationTypeOptions"
            :key="item.id"
            :label="item.text"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('支出类型')">
        <gever-select
          v-model="queryParams.expenditureType"
          clearable
          class="w150"
          :options="expenditureTypeOptions"
        />
      </el-form-item>
      <el-form-item :label="$t('金额(元)')">
        <el-input-number
          v-model="queryParams.startAmount"
          :precision="2"
          :controls="false"
          class="w100"
        />
        {{ $t('至') }}
        <el-input-number
          v-model="queryParams.endAmount"
          :precision="2"
          :controls="false"
          class="w100"
        />
      </el-form-item>
      <el-form-item label="交办单位" prop="orgCode">
        <area-select v-model="queryParams.orgCode" value-prop="code" :tree-node-data="treeNodeData" :clearable="false" @selectedNodeChange="handleOrgChange" />
      </el-form-item>
      <el-form-item>
        <el-button
          v-hasPermi="
            'financial.payment.approvalManager.approvalTask.getTodoList'
          "
          round
          plain
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
        >
          {{ $t('搜索') }}
        </el-button>
      </el-form-item>
      <el-form-item style="margin-left: auto">
        <el-button
          v-hasPermi="'financial.payment.approvalManager.approvalTask.verify'"
          round
          type="primary"
          icon="el-icon-plus"
          @click="handleDeal1"
        >
          {{ $t('办理') }}
        </el-button>
        <el-button round icon="el-customIcon-jiaoyin" @click="handleTrace1">
          {{ $t('跟踪') }}
        </el-button>
      </el-form-item>
    </el-form>
    <gever-table
      ref="_tableRef"
      v-hasPermi="'financial.payment.approvalManager.approvalTask.getTodoList'"
      v-loading="loading"
      :columns="tableColumns"
      :cell-style="{ 'font-size': '15px', color: 'black' }"
      :data="table.dataList"
      :total="table.total"
      :pagi="queryParams"
      @p-current-change="loadList"
      @size-change="loadList"
    >
      <template #operation="{ row }">
        <el-button
          v-hasPermi="'financial.payment.approvalManager.approvalTask.verify'"
          type="text"
          @click="handleDeal(row)"
        >
          {{ $t('办理') }}
        </el-button>
        <el-button type="text" @click="handleTrace(row)">
          {{ $t('跟踪') }}
        </el-button>
      </template>
      <template #footer-left>
        <p style="color: #46a6ff">
          {{ $t('本次支付合计：') }}
          <span style="color: #333; font-weight: bold">
            {{ $t(totalAmount) }}
          </span>
        </p>
      </template>
    </gever-table>

    <public-drawer
      ref="publicdrawer"
      :visible.sync="drawerVisible"
      :title="$t('审批')"
      :size="70"
      :buttons="
        isInSubmit === 1 || isInSubmit === 2
          ? drawerButtons1
          : isInSubmit === 3
          ? drawerButtons2
          : []
      "
      @close="applicationInfos.opinion = ''"
    >
      <div style="height: 100%; display: flex; flex-direction: column">
        <div class="pre-node-info">
          <span class="info">
            {{ $t('上一环节审核意见：') + perNodeOpinion }}
            <el-button type="primary" plain round @click="handleView">
              {{ $t('查看') }}
            </el-button>
          </span>
        </div>
        <application
          v-if="drawerVisible"
          ref="_appRef"
          class="application"
          :application="currentApplication"
          :application-books-id="applicationBooksId"
          :receiver="currentReceiver"
          :tree-data="treeData"
          :load-type="isInSubmit == 1 ? 'edit' : 'view'"
          :expend-type-options="expendTypeOptions"
          @closeDrawer="handleCloseDrawer"
        />
        <el-form
          v-if="isInSubmit == 3 && drawerVisible"
          ref="_applicationInfos"
          inline
          :model="applicationInfos"
          :rules="rules"
          class="el-options"
        >
          <el-row>
            <span>
              <el-form-item
                :label="$t('办理意见')"
                prop="opinion"
                style="color: #ff4949"
              >
                <!-- <el-input
                  v-model.trim="applicationInfos.opinion"
                  style="margin-top: 5px"
                >
                  <el-button
                    slot="append"
                    :loading="loadingInfoOpinion"
                    @click="handleSaveInfoOpinion"
                  >
                    保存
                  </el-button>
                </el-input> -->
                <el-autocomplete
                  v-model.trim="applicationInfos.opinion"
                  :title="applicationInfos.opinio"
                  :fetch-suggestions="
                    (queryString, callback) =>
                      handleGetInfoOpinion(queryString, callback)
                  "
                  style="width: 100%"
                >
                  <template #default="{ item }">
                    <el-row
                      type="flex"
                      justify="space-between"
                      :title="item.name"
                    >
                      <span class="overflow-hidden">{{ item.name }}</span>
                      <span>
                        <el-button
                          type="text"
                          @click.stop="handleRemoveInfo(item.id)"
                        >
                          {{ $t('删除') }}
                        </el-button>
                      </span>
                    </el-row>
                  </template>
                  <template #append>
                    <el-button @click="handleSaveInfoOpinion">
                      <span class="blue">
                        {{ $t('保存') }}
                      </span>
                    </el-button>
                  </template>
                </el-autocomplete>
              </el-form-item>
            </span>
          </el-row>
        </el-form>
      </div>
    </public-drawer>

    <public-drawer
      :visible.sync="processVisible"
      :title="$t('流程跟踪')"
      :size="70"
    >
      <process-trace
        v-if="processVisible"
        :url="processUrl"
        :show-process-log="true"
        :process-ins-id="processInsId"
        :bussiness-key="bussinessKey"
      />
    </public-drawer>

    <el-dialog
      :title="$t('驳回')"
      custom-class="reject-dialog"
      :visible.sync="backwardVisible"
    >
      <el-button-group class="backward">
        <el-button @click="handleBackward(false)">
          <i class="el-icon-document-checked" />
          <h2>{{ $t('驳回上一环节') }}</h2>
        </el-button>
        <el-button @click="handleBackward(true)">
          <i class="el-icon-document-delete" />
          <h2>{{ $t('驳回申请人') }}</h2>
        </el-button>
      </el-button-group>
    </el-dialog>

    <el-dialog
      :title="$t('审批')"
      custom-class="reject-dialog"
      close-on-click-modal
      :visible.sync="oneClickApproval"
    >
      <el-form
        ref="ruleForm"
        v-loading="approvalLoading"
        :model="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item
          label="办理意见"
          prop="opinion"
          :rules="[
            { required: true, message: '请输入办理意见', trigger: 'blur' }
          ]"
        >
          <el-input v-model="ruleForm.opinion" type="textarea" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="approvalLoading" @click="handleDisagrees">
          不同意
        </el-button>
        <el-button
          :loading="approvalLoading"
          type="primary"
          @click="handleApproval"
        >
          同意
        </el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="$t('驳回')"
      custom-class="reject-dialog"
      :visible.sync="disagreeVisible"
    >
      <el-button-group class="backward">
        <el-button @click="handleDisagree(false)">
          <i class="el-icon-document-checked" />
          <h2>{{ $t('驳回上一环节') }}</h2>
        </el-button>
        <el-button @click="handleDisagree(true)">
          <i class="el-icon-document-delete" />
          <h2>{{ $t('驳回申请人') }}</h2>
        </el-button>
      </el-button-group>
    </el-dialog>
  </div>
</template>

<script>
import { paymentExpenditureTypeTree as expenseTypePage } from '@/api/payment/expenseType'
import { formatMoney } from '@/utils/index.js'
import {
  getTodoList,
  getApplication,
  dealApproval,
  infoOpinionSave,
  infoOpinionPage,
  infoOpinionDelete,
  authOrgDataAll
} from '@/api/payment/approval.js'
import Application from '../approvalApplication/applicationInfo.vue'
import {
  comboJson,
  authTreeDataAll,
  getProcessTraceList
} from '@/api/gever/common.js'
export default {
  name: 'ToDoList',
  components: { Application },
  data() {
    return {
      loading: false,
      loadingInfoOpinion: false,
      applicationBooksId: '',
      oneClickApproval: false,
      disagreeVisible: false,
      selectedRows: [],
      ruleForm: {
        opinion: ''
      },
      currentWorkflow: null,
      applicationInfos: {
        opinion: ''
      },
      expenditureTypeOptions: [],
      applicationTypeOptions: [
        { id: 0, text: '常规用款' },
        { id: 1, text: '收支相抵' },
        { id: 2, text: '冲减收入' },
        { id: 3, text: '非预算项目支出' },
        { id: 4, text: '银行代扣' },
        { id: 5, text: '扣减预算' },
        { id: 6, text: '非预算收支相抵' }
      ],
      perNodeOpinion: '',
      orgOptions: [],
      tableColumns: [
        { type: 'selection', width: '50' },
        { label: '序号', type: 'index', index: this.indexMethod, width: '50' },
        { label: '申请编号', prop: 'bak7', minWidth: '150' },
        { label: '金额', prop: 'bak6', minWidth: '120', filter: 'money' },
        { label: '申请类型', prop: 'bak9', minWidth: '120' },
        { label: '支出类型', prop: 'bak10', minWidth: '150', resizable: true, showOverflowTooltip: false },
      /* { label: '任务类型', prop: 'processName', minWidth: '120' }, */
        { label: '审批事项名称', prop: 'bak8', minWidth: '150', resizable: true, showOverflowTooltip: false },
        {
          label: '资金用途',
          prop: 'bussinessTitle',
          minWidth: '250',
          resizable: true,
          showOverflowTooltip: false
        },
        { label: '当前节点', prop: 'action', minWidth: '100' },
        {
          label: '交办单位',
          prop: 'actionOrgName',
          minWidth: '150',
          resizable: true,
          showOverflowTooltip: false
        },
        { label: '交办人', prop: 'bak5', minWidth: '100' },
        { label: '交办时间', prop: 'createTime', minWidth: '180' },
        { label: '操作', slotName: 'operation', width: '120', fixed: 'right' }
      ],
      table: {
        total: 0,
        dataList: []
      },
      queryParams: {
        searchKey: '',
        bussinessTitle: '',
        startAmount: undefined,
        endAmount: undefined,
        orgId: '',
        page: 1,
        rows: 20
      },
      drawerVisible: false,
      currentApplication: {},
      treeData: [],
      expendTypeOptions: [],
      drawerButtons1: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: () => {
            this.drawerVisible = false
          }
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: () => {
            this.$refs['_appRef'].handleSaveApplication()
          }
        },
        {
          type: 'primary',
          text: this.$t('提 交'),
          buttonStatus: true,
          callback: () => {
            this.$refs['_appRef'].handleSaveAndSubmit()
          }
        }
      ],
      drawerButtons2: [
        {
          type: '',
          text: this.$t('不同意'),
          buttonStatus: true,
          callback: this.handleReject
        },
        {
          type: 'primary',
          text: this.$t('同 意'),
          buttonStatus: true,
          callback: this.handleApprove
        }
      ],
      processUrl: '',
      processInsId: '',
      bussinessKey: '',
      processVisible: false,
      backwardVisible: false,
      currentReceiver: [],
      rules: {
        opinion: [
          {
            required: true,
            message: this.$t('办理意见不能为空'),
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              if (value.length > 180) {
                return callback(new Error(this.$t('长度在1~180个字符之间')))
              } else {
                return callback()
              }
            }
          }
        ]
      },
      approvalLoading: false,
      treeNodeData: {}
    }
  },
  computed: {
    totalAmount() {
      const total = this.table.dataList.reduce((t, cur) => {
        return (t += +(cur.bak6 || 0))
      }, 0)

      return formatMoney(total)
    },
    isInSubmit() {
      // 判断当前待办是否在提交节点
      if (
        this.currentApplication.creator === localStorage.getItem('userId') &&
        this.currentWorkflow &&
        this.currentWorkflow.action === '提交'
      ) {
        return 1
      } else if (
        this.currentApplication.creator !== localStorage.getItem('userId') &&
        this.currentWorkflow &&
        this.currentWorkflow.action === '提交'
      ) {
        return 2
      } else {
        return 3
      }
    }
  },
  created() {
    this.loadList()
    this.loadComboJson()
    authTreeDataAll().then((res) => {
      this.treeData = res.data
    })
    this.loadAllOptions()
  },
  methods: {
    async handleGetInfoOpinion(queryString, callback) {
      const { data } = await infoOpinionPage()
      const result = queryString
        ? data.filter(({ name = '' }) => name.indexOf(queryString) > -1)
        : data
      result.forEach((data) => {
        data.value = data.name || ''
      })
      callback(result)
    },
    handleOrgChange(data) {
      if (!data) {
        this.queryParams.orgId = ''
        this.queryParams.areaCode = ''
      } else {
        this.treeNodeData = data
        if (data.type == 'Area') {
          this.queryParams.areaCode = data.code ? data.code : ''
        } else {
          this.queryParams.orgId = data.id ? data.id : ''
        }
      }
    },
    handleRemoveInfo(id) {
      this.$confirm('确认删除该条审批意见吗', this.$t('提示'), {
        confirmButtonText: '确认',
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(async () => {
          try {
            await infoOpinionDelete({ id })
          } catch (error) {
            console.log(error)
          }
        })
        .catch(() => {})
    },
    handleSaveInfoOpinion() {
      this.loadingInfoOpinion = true
      infoOpinionSave({
        orgCode: this.currentWorkflow.areaCode,
        name: this.applicationInfos.opinion
      })
        .then((res) => {
          this.$message.success(res.message)
        })
        .finally(() => {
          this.loadingInfoOpinion = false
        })
      console.log(this.applicationInfos.opinion, 999)
    },
    handleSearch() {
      this.loadList()
    },
    indexMethod(index) {
      return (this.queryParams.page - 1) * this.queryParams.rows + index + 1
    },
    loadList() {
      this.loading = true
      getTodoList(this.queryParams)
        .then((res) => {
          this.table.total = res.data.total
          this.table.dataList = res.data.rows
        })
        .finally(() => {
          this.loading = false
        })
    },
    loadComboJson() {
      comboJson({ path: '/payment/expenditure_type' }).then((res) => {
        this.expendTypeOptions = res.data
      })
    },
    handleDisagrees() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.disagreeVisible = true
        }
      })
    },
    // 一键办理不同意
    handleDisagree(val) {
      const keys = []
      const taskIds = []
      for (let i = 0; i < this.selectedRows.length; i++) {
        keys.push(this.selectedRows[i].bussinessKey)
        taskIds.push(this.selectedRows[i].taskId)
      }
      const params = {
        id: keys.join(','),
        taskId: taskIds.join(','),
        opinion: this.ruleForm.opinion,
        agree: false,
        backward: val
      }
      dealApproval(params).then((res) => {
        if (res.returnCode == '0') {
          this.$message.success(
            `本次审批${this.selectedRows.length}条,操作成功`
          )
          this.ruleForm.opinion = ''
          this.disagreeVisible = false
          this.oneClickApproval = false
          this.loadList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 一键办理同意
    handleApproval() {
      this.approvalLoading = true
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          const keys = []
          const taskIds = []
          for (let i = 0; i < this.selectedRows.length; i++) {
            keys.push(this.selectedRows[i].bussinessKey)
            taskIds.push(this.selectedRows[i].taskId)
          }
          const params = {
            id: keys.join(','),
            taskId: taskIds.join(','),
            opinion: this.ruleForm.opinion,
            agree: true,
            backward: ''
          }
          dealApproval(params)
            .then((res) => {
              if (res.returnCode == '0') {
                this.$message.success(
                  `本次审批${this.selectedRows.length}条,操作成功`
                )
                this.ruleForm.opinion = ''
                this.oneClickApproval = false
                this.loadList()
              } else {
                this.$message.error(res.message)
              }
            })
            .finally(() => {
              this.approvalLoading = false
            })
        } else {
          this.approvalLoading = false
        }
      })
    },
    handleDeal1() {
      this.selectedRows = this.$refs['_tableRef'].$refs['elTable'].selection
      if (this.selectedRows.length < 1) {
        return this.$message.warning(this.$t('请先选择一条需要办理的任务数据'))
      } else if (this.selectedRows.length > 10) {
        return this.$message.warning(this.$t('最多选择十条数据'))
      }
      this.oneClickApproval = true
      // this.handleDeal()
    },
    handleDeal(row) {
      this.currentWorkflow = row
      getApplication(row.bussinessKey).then((res) => {
        this.drawerVisible = true
        this.applicationBooksId = res.data[0].booksId
        this.currentApplication = res.data[0]
        this.currentReceiver = res.data[1]
        this.getPreNodeInfo()
      })
    },
    handleReject() {
      if (
        this.applicationInfos.opinion === '' ||
        this.applicationInfos.opinion === null
      ) {
        return this.$message.warning(this.$t('请输入办理意见'))
      }
      this.$refs['_applicationInfos'].validate((valid) => {
        if (valid) {
          this.backwardVisible = true
        }
      })
    },
    handleApprove() {
      if (
        this.applicationInfos.opinion === '' ||
        this.applicationInfos.opinion === null
      ) {
        // return this.$message.warning(this.$t('请输入办理意见'))
        this.applicationInfos.opinion = '同意'
      }
      this.$refs['_applicationInfos'].validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('是否确定同意办理此事项?'), {
            confirmButtonText: this.$t('确定'),
            cancelButtonText: this.$t('取消'),
            type: 'warning'
          })
            .then(() => {
              const params = {
                bussinessKey: this.currentWorkflow.bussinessKey,
                id: this.currentApplication.id,
                taskId: this.currentWorkflow.taskId,
                opinion: this.applicationInfos.opinion,
                agree: true,
                backward: ''
              }
              this.$refs.publicdrawer.btnLoading = true
              dealApproval(params)
                .then((res) => {
                  if (res.returnCode == '0') {
                    this.$message.success(res.message)
                    this.drawerVisible = false
                    this.loadList()
                  } else {
                    this.$message.error(res.message)
                  }
                })
                .finally(() => {
                  this.$refs.publicdrawer.btnLoading = false
                })
            })
            .catch(() => {})
        }
      })
    },
    handleCloseDrawer() {
      this.drawerVisible = false
      this.applicationInfos.opinion = ''
      this.$refs['_applicationInfos']?.clearValidate()
      this.loadList()
    },
    handleTrace1() {
      const selectedRows = this.$refs['_tableRef'].$refs['elTable'].selection
      if (selectedRows.length !== 1) {
        return this.$message.warning(this.$t('请先选择一条需要跟踪的任务数据'))
      }
      this.handleTrace(selectedRows[0])
    },
    handleTrace(row) {
      this.currentWorkflow = row
      getApplication(row.bussinessKey).then((res) => {
        this.currentApplication = res.data[0]
        this.handleView()
      })
    },
    handleView() {
      if (!this.currentApplication.procInstId) {
        return this.$message.warning(this.$t('数据异常'))
      }
      this.processUrl =
        '/' +
        process.env.VUE_APP_REAL +
        '/workflow/genProcessDiagram?processInsId=' +
        this.currentApplication.procInstId +
        '&bussinessKey=' +
        this.currentApplication.id
      this.processInsId = this.currentApplication.procInstId
      this.bussinessKey = this.currentApplication.id
      this.processVisible = true
    },
    handleBackward(flag) {
      this.backwardVisible = false
      const msg = flag
        ? this.$t('是否确定驳回到申请人？')
        : this.$t('是否确定驳回到上一环节？')
      this.$confirm(msg, {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          const params = {
            bussinessKey: this.currentWorkflow.bussinessKey,
            id: this.currentApplication.id,
            taskId: this.currentWorkflow.taskId,
            opinion: this.applicationInfos.opinion,
            agree: false,
            backward: flag
          }
          this.$refs.publicdrawer.btnLoading = true
          dealApproval(params)
            .then((res) => {
              if (res.returnCode === '0') {
                this.$message.success(res.message)
                this.drawerVisible = false
                this.loadList()
              } else {
                this.$message.error(res.message)
                this.drawerVisible = false
                this.loadList()
              }
            })
            .finally(() => {
              this.$refs.publicdrawer.btnLoading = false
            })
        })
        .catch(() => {})
    },
    getPreNodeInfo() {
      const queryParams = {
        page: 1,
        rows: 20
      }
      getProcessTraceList(
        Object.assign(queryParams, {
          traceType: 0,
          procInstId: this.currentApplication.procInstId,
          bussinessKey: this.currentApplication.id
        })
      ).then((res) => {
        this.perNodeOpinion = res.data.rows[res.data.rows.length - 1].opinion
      })
    },
    async loadAllOptions() {
      this.optionLoading = true
/*      const data3 = await authOrgDataAll()
      this.orgOptions = data3.data*/
      this.optionLoading = false
      expenseTypePage({ page: 1, rows: 100 }).then((res) => {
        this.expenditureTypeOptions = res.data.map((cur) => {
          if (cur.id == '0') {
            cur.id = ''
          }
          return cur
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-options .el-form-item {
  width: 100%;
  .el-form-item__content {
    width: 100%;
  }
}
.pre-node-info {
  // height: 40px;
  border: 1px #ffdb47 solid;
  background-color: #fff7d3;
  padding: 5px 15px;
  margin-bottom: 10px;
  .info {
    width: 100%;
  }
  span {
    display: inline-block;
    // height: 28px;
    line-height: 28px;
  }
  .el-button {
    float: right;
  }
}
.application {
  overflow-y: auto;
}
.backward {
  display: flex;
  justify-content: space-around;
  & > .el-button {
    padding: 10px 0;
    width: 200px;
    display: flex;
    border: 1px solid #e1e1e1;
    border-radius: 10px;
    flex-direction: column;
    align-items: center;
    i {
      color: #e1e1e1;
      font-size: 50px;
    }
    h2 {
      font-size: 16px;
    }
  }
}
::v-deep .reject-dialog {
  .el-dialog__body {
    // padding: 100px 20px;
  }
}
</style>
