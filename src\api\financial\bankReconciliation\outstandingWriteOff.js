import request from '@/utils/request'

/**
 * @name:列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/listOutstandingWriteOff',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:自动核销
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function autoOutstandingWriteOff(data) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/autoOutstandingWriteOff',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:核销
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function outstandingWriteOff(data) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/outstandingWriteOff',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:取消核销
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancelOutstandingWriteOff(params, data) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/cancelOutstandingWriteOff',
    method: 'post',
    data,
    params,
    payload: true
  })
}
/**
 * @name:获取未达账前置条件控制标识
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getOutstandingFlag(params) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/getOutstandingFlag',
    method: 'get',
    params,
  })
}