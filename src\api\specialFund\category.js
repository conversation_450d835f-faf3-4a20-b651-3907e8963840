import request from '@/utils/request'
/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadCategoryListApi(params) {
  return request({
    url: '/specialFund/category/page',
    method: 'get',
    params
  })
}

/**
 * @name: 详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadCategoryInfoApi(id) {
  return request({
    url: `/specialFund/category/get/${id}`,
    method: 'get'
  })
}

/**
 * @name: 删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteCategoryApi(data) {
  return request({
    url: '/specialFund/category/deleteMul',
    method: 'post',
    data
  })
}

/**
 * @name: 新增保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveCategoryApi(data) {
  return request({
    url: '/specialFund/category/save',
    method: 'post',
    data
  })
}

/**
 * @name: 更新保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateCategoryApi(data) {
  return request({
    url: '/specialFund/category/update',
    method: 'post',
    data
  })
}

/**
 * @name: 获取下级
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function selectByParentId(data) {
  return request({
    url: '/specialFund/category/selectByParentId',
    method: 'get',
    params:data
  })
}

/**
 * @name: 生效失效
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateStatus(data) {
  return request({
    url: '/specialFund/category/updateStatus',
    method: 'post',
    data
  })
}