<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-06-19 11:43:04
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-07 17:02:02
-->
<template>
  <div>
    <el-form inline @submit.native.prevent>
      <el-form-item :label="$t('年份')">
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          class="w150"
          value-format="yyyy"
          format="yyyy"
          style="width: 100%;"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('期间')">
        <el-select v-model="queryParams.period" placeholder="请选择">
          <el-option
            v-for="month in 12"
            :key="month"
            class="w200"
            :label="`${month}月`"
            :value="month"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('摘要')">
        <el-input v-model="queryParams.summary" type="text" class="w200" clearable />
      </el-form-item>
      <el-form-item>
        <el-button
          plain
          icon="el-icon-search"
          type="primary"
          round
          @click="handleSearch"
        >{{ $t('搜索') }}</el-button>
      </el-form-item>
    </el-form>
    <gever-table
      ref="_geverTableRef"
      :height="500"
      :loading="tableLoading"
      :columns="table.columns"
      :data="table.tableList"
      :total="table.total"
      :pagi="queryParams"
      :pagination="false"
      @pagination-change="getList"
      @selection-change="handleSelectionChange"
    >
      <template #type="{ }">
        <span>银收</span>
      </template>
    </gever-table>
  </div>
</template>

<script>
import { cashierVoucherListByBankAccount } from '@/api/specialFund/receipt.js'
import { getDictionary } from '@/api/gever/common.js'
import { getToken } from '@/utils/auth'
export default {
  name: '',
  components: {
  },
  props: {
    orgId: {
      type: String,
      default: ''
    },
    area: {
      type: Object,
      default: () => { }
    },
    bankAccountNumber: {
      type: String,
      default: ''
    },
    totalAmount: {
      type: Number,
      default: 0.00
    }
  },
  data () {
    return {
      tableLoading: false,
      queryParams: {
        page: 1,
        rows: 20,
        year: new Date().getFullYear().toString(),
        period: ''
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50', fixed: 'left' },
          { label: '日期', prop: 'businessDate', width: '120' },
          { label: '凭证字', prop: 'type', width: '120' },
          { label: '凭证号', prop: 'typeNumber', width: '120' },
          { label: '流水号', prop: 'voucherNumber', width: '120' },
          { label: '摘要', prop: 'summary', minWidth: '200' },
          { label: '金额', prop: 'debit', width: '120', filter: 'money', align: 'right' },
          { label: '已入账金额', prop: 'postedAmount', filter: 'money', width: '150', align: 'right' },
          { label: '未入账金额', prop: 'unpostedAmount', filter: 'money', width: '150', align: 'right' }
        ],
        tableList: [],
        total: 0
      },
      optionsMap: {
        status: [],
        specialSource: [],
        specialType: [],
        approvalstatus: [],
        payStatus: [],
        noPayFail: [
          {
            id: '1',
            text: '是'
          },
          {
            id: '0',
            text: '否'
          }
        ]
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      contentForm: {},
      detailVisible: false,
      tableSelection: [],
      tableIdSelection: []
    }
  },
  computed: {
  },
  created () {
    console.log('this.totalAmount', this.totalAmount)

    this.getDate()
  },
  methods: {
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    getDate () {
      // 获取当前日期对象
      const now = new Date()
      // 获取当前年份（4位数）
      const currentYear = now.getFullYear()
      // 获取当前月份（0-11，0表示一月）
      const currentMonth = now.getMonth() + 1 // 通常我们会加1得到1-12
      this.queryParams.year = new Date().getFullYear().toString()
      this.queryParams.period = currentMonth
      console.log('this.queryParams.year', this.queryParams.year)

      this.getList()
    },
    handleCloseDetail () {
      this.cashierVisible = false
    },
    // 失去焦点时格式化显示两位小数
    formatDecimal (field) {
      if (this.queryParams[field]) {
        const num = parseFloat(this.queryParams[field])
        if (!isNaN(num)) {
          this.queryParams[field] = num.toFixed(2)
        }
      }
    },
    handleSearch () {
      this.getList()
    },
    getList () {
      this.queryParams.orgId = this.orgId
      this.queryParams.bankAccount = this.bankAccountNumber
      this.tableLoading = true
      cashierVoucherListByBankAccount(this.queryParams).then(res => {
        this.table.tableList = res.data
        // this.table.total = res.data.total
        // if (res.data.total != 0) {
        //   this.table.tableList.push({
        //     orgName: '总合计',
        //     payAmount: res.data.totalAmount,
        //     actualPayAmount: res.data.totalActualPayAmount
        //   })
        // }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    save () {
      if (this.tableSelection.length == 0) {
        this.$message.warning(this.$t('请先选择一条出纳数据！'))
      }
      if (this.tableSelection.length > 1) {
        this.$message.warning(this.$t('只能选择一条出纳数据！'))
      }
      if (this.tableSelection[0].unpostedAmount < this.totalAmount) {
        this.$message.warning(this.$t('入账金额超出未入账金额'))
      }
      this.$emit('cashierSave', this.tableSelection)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .row-bold {
  font-weight: bold !important;
  background-color: #f8f8f9;
}
</style>
