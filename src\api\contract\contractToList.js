/*
 * @Descripttion:
 * @version:
 * @Author: Tanronglong
 * @Date: 2021-10-27 14:39:08
 * @LastEditors: Tanronglong
 * @LastEditTime: 2021-11-09 13:41:07
 */
import request from '@/utils/request'
// 待办事项   /workflow/getTodoList?path=/contract/workflow/contract&subArea=true
export function getTodoList(data) {
  return request({
    url: `/workflow/getTodoList?path=/contract/workflow/contract&subArea=true`,
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 不同意 /contract/workflow/verify
export function verify(data) {
  return request({
    url: '/contract/workflow/verify',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 已办事项  /workflow/getHasDoneTaskList?path=/contract/workflow/contract&subArea=true
export function getHasDoneTaskList(data) {
  return request({
    url: '/workflow/getHasDoneTaskList?path=/contract/workflow/contract&subArea=true',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同变更保存接口 /contract/contractChanges/saveChanges
export function saveChanges(data) {
  return request({
    url: '/contract/contractChanges/saveChanges',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 作废流程 /contract/workflow/revocation
export function revocation(data) {
  return request({
    url: '/contract/workflow/revocation',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 办理流程调取接口 /contract/workflow/verify/init
export function init(data) {
  return request({
    url: '/contract/workflow/verify/init?bussinessTypeKey=' + data.bussinessTypeKey + '&id=' + data.id
    + '&bussinessKey=' + data.bussinessKey
    + '&procInstId=' + data.procInstId
    + '&taskId=' + data.taskId,
    method: 'get'
  })
}
// 驳回流程调取接口 /contract/workflow/resubmit/init?bussinessTypeKey=registration_workflow&id=d9d7edbec6004b69aac39649418f9fda
// &bussinessKey=d9d7edbec6004b69aac39649418f9fda&procInstId=68cc5dc9675b496090d3b0f69c106e21&taskId=720b498ca4cf439faef18ed4a3ded97b
export function resubmitInit(data) {
  return request({
    url: '/contract/workflow/resubmit/init',
    method: 'get',
    params: data
  })
}
// 付款合同 init接口 /contract/paymentContractChange/view/init/*
export function paymentInit(data) {
  return request({
    url: `/contract/paymentContractChange/view/init/${data}`,
    method: 'get'
  })
}
