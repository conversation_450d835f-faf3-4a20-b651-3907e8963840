<!-- eslint-disable complexity -->
<template>
  <div>
    <fold-box right-title="支出申请">
      <template v-if="paymentLogin.type === 'Organization'" #right>
        <div v-loading="boxLoading" class="right-box">
          <el-form inline :model="queryParams">
            <el-form-item :label="$t('审批事项名称')">
              <el-input v-model="queryParams.matter" class="w100" />
            </el-form-item>
            <el-form-item :label="$t('资金用途')">
              <el-input v-model="queryParams.fundsUse" class="w100" />
            </el-form-item>
            <el-form-item :label="$t('申请详情')">
              <el-input v-model="queryParams.details" class="w100" />
            </el-form-item>
            <el-form-item :label="$t('审批状态')">
              <el-select
                v-model="queryParams.approvalStatus"
                class="w150"
              >
                <el-option
                  v-for="item in approvalstatusOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('支付状态')">
              <el-select
                v-model="queryParams.paymentState"
                clearable
                class="w150"
              >
                <el-option
                  v-for="item in paymentStateOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('支出类型')">
              <gever-select
                v-model="queryParams.expenditureType"
                clearable
                class="w150"
                :options="expenditureTypeOptions"
              />
            </el-form-item>
            <el-form-item :label="$t('结算方式')" prop="expenditureType">
              <el-select
                v-model="queryParams.settlementMethod"
                clearable
                class="w100"
              >
                <el-option
                  v-for="item in settlementMethodOptions"
                  :key="item.id"
                  :label="item.settleName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('金额(元)')">
              <el-input-number
                v-model="queryParams.startAmount"
                :precision="2"
                :controls="false"
                class="w100"
              />
              {{ $t('至') }}
              <el-input-number
                v-model="queryParams.endAmount"
                :precision="2"
                :controls="false"
                class="w100"
              />
            </el-form-item>
            <el-form-item :label="$t('选择区间')">
              <el-date-picker
                v-model="dateTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                :default-time="['0:00:00', '23:59:59']"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="dateChange"
              />
            </el-form-item>
<!--            <el-form-item :label="$t('乡村振兴类别')" prop="revitalizationType">
              <gever-select
                v-model="queryParams.revitalizationType"
                :options="revitalizationTypeOptions"
                clearable
                class="w150"
              />
            </el-form-item>-->
            <el-form-item>
              <el-button
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplication.list'
                "
                round
                plain
                type="primary"
                icon="el-icon-search"
                @click="loadList"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
          </el-form>
          <div class="btn-container">
            <el-button
              v-hasPermi="
                'financial.payment.approvalManager.approvalApplication.add'
              "
              round
              type="primary"
              icon="el-icon-plus"
              :disabled="paymentLogin.type === 'Area'"
              @click="handleAdd"
            >
              <!-- !current.id || !!current.state ||  -->
              {{ $t('新增') }}
            </el-button>
            <el-button
              v-hasPermi="
                'financial.payment.approvalManager.approvalApplication.delete'
              "
              round
              plain
              type="primary"
              icon="el-icon-delete"
              :disabled="paymentLogin.type === 'Area'"
              @click="handleBatchRemove"
            >
              {{ $t('删除') }}
            </el-button>
            <el-button
              v-hasPermi="
                'financial.payment.approvalManager.approvalApplication.submit'
              "
              round
              plain
              type="primary"
              :disabled="paymentLogin.type === 'Area'"
              @click="handleSubmit"
            >
              {{ $t('提交') }}
            </el-button>
<!--            <el-button-->
<!--              v-hasPermi="-->
<!--                'financial.payment.approvalManager.approvalApplication.setRevitalizationType'-->
<!--              "-->
<!--              round-->
<!--              plain-->
<!--              type="primary"-->
<!--              @click="revitalizationTypeOpen"-->
<!--            >-->
<!--              {{ $t('设置乡村振兴') }}-->
<!--            </el-button>-->
          </div>
          <gever-table
            ref="_geverTableRef"
            v-loading="tableLoading"
            :columns="tableColumns"
            :data="tableList"
            pagination
            :total="total"
            :pagi="queryParams"
            @p-current-change="loadList"
            @size-change="loadList"
          >
            <!-- <template #expenditureType="{ row }">
              {{ convertExpenditureType(row.expenditureType) }}
            </template> -->
            <template #amount="{ row }">
              {{ row.amount | formatMoney }}
            </template>
            <template #approvalStatus="{ row }">
              {{ convertApprovalStatus(row.approvalStatus) }}
            </template>
            <template #creationTime="{ row }">
              {{ convertCreationTime(row.creationTime) }}
            </template>
            <template #paymentState="{ row }">
              {{ convertPaymentState(row.paymentState) }}
            </template>
            <template #revitalizationType="{ row }">
              {{ convertRevitalizationType(row.revitalizationType) }}
            </template>
            <template #settlementMethod="{ row }">
              {{ convertSettlementMethod(row.settlementMethod) }}
            </template>
            <template #operation="{ row }">
              <el-button
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplication.view'
                "
                type="text"
                @click="handleView(row)"
              >
                {{ $t('查看') }}
              </el-button>
              <el-button
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplication.edit'
                "
                type="text"
                :disabled="paymentLogin.type === 'Area'"
                @click="handleEdit(row)"
              >
                {{ $t('编辑') }}
              </el-button>
              <el-button
                v-if="
                  [3, 4, 5].includes(row.approvalStatus) &&
                    row.paymentState != 2
                "
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplication.toVoid'
                "
                type="text"
                @click="handleInvalidated(row)"
              >
                {{ $t('作废') }}
              </el-button>
              <el-button
                v-if="
                  row.paymentState == 2 && ![1, 5].includes(row.applicationType)
                "
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplication.add'
                "
                type="text"
                @click="handleRefund(row)"
              >
                {{ $t('退款') }}
              </el-button>
              <el-button
                v-if="row.approvalStatus === 1"
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplication.delete'
                "
                type="text"
                :disabled="paymentLogin.type === 'Area'"
                @click="handleRemove(row, false)"
              >
                {{ $t('删除') }}
              </el-button>
              <!-- <div style="position: relative; display: inline-block"> -->
              <el-dropdown trigger="click" placement="bottom-end">
                <span class="el-dropdown-link">
                  <i class="el-icon-more" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="handleTrace(row)">
                    {{ $t('跟踪') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-hasPermi="
                      'financial.payment.approvalManager.approvalApplication.add'
                    "
                    @click.native="handleReApprove(row)"
                  >
                    {{ $t('再次申请') }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-hasPermi="
                      'financial.payment.approvalManager.approvalApplication.revocation'
                    "
                    @click.native="handleRevocation(row)"
                  >
                    {{ $t('撤销提交') }}
                  </el-dropdown-item>
                  <!-- <el-dropdown-item @click.native="handleTovoid(row)">{{
                    $t("作废")
                  }}</el-dropdown-item> -->
                  <!-- <el-dropdown-item v-hasPermi="'financial.payment.approvalManager.approvalApplication.pdfView'" @click.native="handlePreview(row)">{{ $t('PDF预览') }}</el-dropdown-item> -->
                  <el-dropdown-item @click.native="handlePreview(row)">
                    {{ $t('PDF预览') }}
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="handlePrint(row)">
                    {{ $t('打印') }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <!-- </div> -->
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <public-drawer
      ref="_appDrawerRef"
      v-loading="loading"
      :visible.sync="applicationDrawerVisible"
      :title="drawerTitle"
      :size="70"
      @close="handleDrawerClose"
    >
      <application
        v-if="applicationDrawerVisible"
        :id="currentRow.id || ''"
        ref="_appRef"
        :view-state="viewState"
        :application="currentApplication"
        :receiver="receiverList"
        :load-type="loadType"
        :info-type="infoType"
        :show-process-btn="showProcessBtn"
        :accounting-item-id="current.id || ''"
        :visible.sync="applicationDrawerVisible"
        @loading="handleLoading"
        @Invalid="Invalid"
        @closeDrawer="handleCloseDrawer"
      />
      <div slot-name="footer" class="drawer-footer">
        <template v-if="loadType === 'add' || loadType === 'edit'">
          <div class="footer-btn">
            <el-button type="default" @click="applicationDrawerVisible = false">
              关闭
            </el-button>
            <el-button
              :loading="loading"
              type="default"
              @click="handleSaveApplication(false)"
            >
              保存
            </el-button>
            <el-button
              :loading="loading"
              type="primary"
              @click="handleSaveApplication(true)"
            >
              提交
            </el-button>
          </div>
        </template>
        <template v-else-if="loadType === 'invalid'">
          <el-button type="default" @click="applicationDrawerVisible = false">
            关闭
          </el-button>
          <el-button
            type="primary"
            :loading="invalidLoading"
            @click="handleInvalid"
          >
            作废
          </el-button>
        </template>
        <template v-else-if="loadType === 'refund'">
          <div class="footer-btn">
            <el-button type="default" @click="applicationDrawerVisible = false">
              关闭
            </el-button>
            <el-button
              :loading="loading"
              type="primary"
              @click="handleSaveRefund"
            >
              保存
            </el-button>
            <!-- <el-button
              :loading="loading"
              type="primary"
              @click="handleSaveApplication(true)"
            >
              提交
            </el-button> -->
          </div>
        </template>
      </div>
    </public-drawer>

    <el-dialog
      :title="$t('流程跟踪')"
      append-to-body
      :visible.sync="processVisible"
      width="1000px"
      top="5vh"
      custom-class="process-dialog"
    >
      <process-trace
        v-if="processVisible"
        :url="processUrl"
        :show-process-log="true"
        :process-ins-id="processInsId"
        :bussiness-key="bussinessKey"
      />
    </el-dialog>
    <!-- 申请退款 -->
    <RefundDetails
      v-if="refundVis"
      :visible.sync="refundVis"
      :cur-row="refundForm"
      @success="loadList"
    />
    <!-- 设置乡村振兴 -->
    <gever-dialog
      v-loading="loading"
      :visible.sync="revitalizationTypeVisible"
      title="设置乡村振兴"
      :perch-height="300"
      width="26%"
      :buttons="[
        {
          type: '',
          text: '关 闭',
          buttonStatus: true,
          callback: () => {
            revitalizationTypeVisible = false
          }
        },
        {
          text: '确定',
          type: 'primary',
          buttonStatus: true,
          disabled: loading === true,
          callback: saveRevitalizationType
        }
      ]"
      @close="revitalizationTypeClose"
    >
      <el-form ref="_revitalizationTypeFormRef" inline class="gever-form" :model="revitalizationTypeFrom" label-position="top">
        <el-form-item style="width: 100%" prop="revitalizationType" label="乡村振兴类别">
            <gever-select
              v-model="revitalizationTypeFrom.revitalizationType"
              path="/payment/revitalization_type/"
              number-key
              :clearable="true"
            />
        </el-form-item>
      </el-form>
    </gever-dialog>
  </div>
</template>

<script>
// import CashierItemTree from '@/views/financial/cashier/voucher/components/CashierItemTree.vue'
import Application from './applicationInfo.vue'
import RefundDetails from './refundDetails.vue'
import { tableColumns } from './config'
import { comboJson } from '@/api/gever/common.js'
import { printReport } from './assistDetailLedger.js'
import { uuid } from '@/utils/gever.js'
import {
  loadApprovalApplicationList,
  getApplication,
  removeApprovalApplication,
  submitApprovalApplication,
  revocationApprovalApplication,
  tovoidApprovalApplication,
  getBudgetSettingOneBy,
  copyFileByBusinessId,
  setRevitalizationType
  // selectReturnFlagApplication
} from '@/api/payment/approval.js'
import { getToken } from '@/utils/auth'
import { mapState } from 'vuex'
import moment from 'moment'
import { paymentExpenditureTypeTree as expenseTypePage } from '@/api/payment/expenseType'
import { page as billingMethodPage } from '@/api/payment/billingMethod'

export default {
  name: 'ApprovalApplication',
  components: {
    Application,
    RefundDetails
    // CashierItemTree
  },
  data() {
    return {
      showProcessBtn: true,
      infoType: false,
      tableLoading: false,
      loading: false,
      invalidLoading: false,
      treeData: [],
      expenditureTypeOptions: [],
      approvalstatusOptions: [{ id: '100', text: this.$t('全部') }],
      paymentStateOptions: [
        { id: '100', text: this.$t('全部') },
        { id: '0', text: this.$t('未支付') },
        { id: '1', text: this.$t('支付中') },
        { id: '2', text: this.$t('支付成功') },
        { id: '3', text: this.$t('部分成功') },
        { id: '5', text: this.$t('退款成功') },
        { id: '-1', text: this.$t('支付失败') },
        { id: '-9', text: this.$t('已退回') },
        { id: '-10', text: this.$t('交易终止') },
        { id: '99', text: this.$t('状态未知') }
      ],
      revitalizationTypeOptions: [{ id: '', text: this.$t('全部'), selected: true }],
      settlementMethodOptions: [{ id: '', settleName: this.$t('全部') }],
      tableList: [],
      tableColumns: tableColumns,
      dateTime: [],
      queryParams: {
        page: 1,
        rows: 20,
        region: '',
        matter: '',
        fundsUse: '',
        approvalStatus: '100',
        paymentState: ' ',
        expenditureType: '',
        accountingItemId: '',
        revitalizationType: '',
        startAmount: undefined,
        endAmount: undefined,
        startDate: '',
        endDate: '',
        settlementMethod: ''
      },
      total: 0,
      currentApplication: {},
      receiverList: [],
      applicationDrawerVisible: false,
      viewState: 0,
      drawerTitle: '',
      processUrl: '',
      processInsId: '',
      bussinessKey: '',
      processVisible: false,
      current: {}, // 当前选择的科目数据
      currentRow: {}, // 当前点击的行
      loadType: '',
      // queryId: '',
      queryData: {},
      boxLoading: false,
      flag: true,
      refundForm: {},
      refundVis: false,
      revitalizationTypeVisible: false,
      revitalizationTypeFrom: {}
    }
  },
  computed: {
    ...mapState('financial', ['booksId']),
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  watch: {
    paymentLogin: {
      /** 监听到变化后就触发一次handler 相当与created */
      handler(val) {
        if (val.type !== 'Organization') {
          return this.$message.warning('请先选择机构')
        }
        if (val.id) {
          this.queryParams.region = val.code
          this.loadList()
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.loadAllComboJson()
  },
  activated() {
    if (this.$route.params.accountingItemId) {
      // this.queryId = this.$route.params?.accountingItemId
      // this.current.id = this.queryId
      this.queryData = JSON.parse(this.$route.params.form)
      this.flag = true
      this.handleAdd()
    }
  },
  methods: {
    getToken,
    dateChange() {
      this.queryParams.startDate = this.dateTime ? this.dateTime[0] : ''
      this.queryParams.endDate = this.dateTime ? this.dateTime[1] : ''
      // const [startDate = '', endDate = ''] = this.dateTime || []
      // this.queryParams = { ...this.queryParams, startDate, endDate }
    },
    handleInvalid() {
      this.invalidLoading = true
      this.$refs['_appRef'].handleInvalid()
    },
    Invalid(val) {
      console.log(val, '作废===================')
      this.invalidLoading = false
    },
    // handleCurrentChange(current) {
    //   this.current = current
    //   this.queryParams.accountingItemId = current.id
    //   this.handleSearch()
    // },
    handleSearch() {
      this.queryParams.page = 1
      this.loadList()
    },
    loadAllComboJson() {
      comboJson({ path: '/payment/approvalstatus' }).then((res) => {
        this.approvalstatusOptions.push(...res.data)
      })
      comboJson({ path: '/payment/revitalization_type' }).then((res) => {
        this.revitalizationTypeOptions.push(...res.data)
      })
      expenseTypePage({ page: 1, rows: 100 }).then((res) => {
        this.expenditureTypeOptions = res.data.map((cur) => {
          if (cur.id == '0') {
            cur.id = ''
          }
          return cur
        })
      })
      // 结算方式
      billingMethodPage({ page: 1, rows: 100 }).then((res) => {
        this.settlementMethodOptions.push(...res.data.rows)
      })
    },
    // eslint-disable-next-line complexity
    loadList() {
      const params = JSON.parse(JSON.stringify(this.queryParams))
      for (const key in params) {
        if (params[key] && typeof params[key] === 'string') {
          params[key] = params[key].trim()
        }
        if ((key === 'startAmount' || key === 'endAmount') && !params[key]) {
          params[key] = ''
        }
        if (['startDate', 'endDate'].includes(key) && !params[key]) {
          delete params[key]
        }
      }
      this.tableLoading = true
      // params.booksId = this.booksId || ''
      loadApprovalApplicationList(params)
        .then((res) => {
          if (res && res.data) {
            this.tableList = res.data.rows
            this.total = res.data.total
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    async handleAdd() {
      const res = await getBudgetSettingOneBy({
        orgId: this.paymentLogin.id,
        year: moment().format('YYYY')
      })
      if (res?.returnCode === '0') {
        this.$set(
          this.currentApplication,
          'useBudgetFlag',
          res.data.budgetControl
        )
        this.$set(this.currentApplication, 'businessYear', res.data.budgetYear)
      }
      this.loadType = 'add'
      this.drawerTitle = '新增支出申请'
      this.currentRow = {}
      this.infoType = true
      this.$set(this.currentApplication, 'orgId', this.paymentLogin.id)
      this.$set(
        this.currentApplication,
        'creationTime',
        moment().format('YYYY-MM-DD')
      )
      this.$set(this.currentApplication, 'orgCode', this.paymentLogin.code)
      this.$set(this.currentApplication, 'orgName', this.paymentLogin.fname)
      this.$set(
        this.currentApplication,
        'submitUserName',
        this.$store.state.user.name
      )

      this.receiverList = []
      if (this.flag && this.queryData !== {}) {
        // this.$set(this.currentApplication, 'settlementMethod', this.queryData.cashierExpenseTypeId)
        this.$set(this.currentApplication, 'amount', this.queryData.credit)
        this.$set(
          this.currentApplication,
          'docNumber',
          this.queryData.receiptSheets
        )
        this.$set(
          this.currentApplication,
          'budgetItemName',
          this.queryData.accountingItemTitle
        )
        this.$set(
          this.currentApplication,
          'budgetItemId',
          this.queryData.budgetItemId
        )
        // this.$set(this.currentApplication, 'expenditureType', this.queryData.cashierExpenseTypeId)
      }
      this.applicationDrawerVisible = true
    },
    handleView(row) {
      this.loadType = 'view'
      this.loading = true
      this.drawerTitle = row.returnFlag ? '查看申请退款' : '查看支出申请'
      this.currentRow = row
      this.viewState = row.approvalStatus
      this.applicationDrawerVisible = true
      getApplication(row.id)
        .then((res) => {
          this.currentApplication = res.data[0]
          this.receiverList = res.data[1]
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 作废申请记录
    async handleInvalidated(row) {
      if (row.returnFlag) {
        await this.$confirm(
          this.$t('作废后无法再重新发起退款申请，是否继续？'),
          {
            confirmButtonText: this.$t('确定'),
            cancelButtonText: this.$t('取消'),
            type: 'warning'
          }
        )
      }
      if (
        row.approvalStatus == 5 ||
        row.approvalStatus == 4 ||
        row.approvalStatus == 3
      ) {
        this.loadType = 'invalid'
        this.loading = true
        this.drawerTitle = '作废支出申请'
        this.infoType = true
        this.currentRow = row
        this.applicationDrawerVisible = true
        getApplication(row.id)
          .then((res) => {
            this.currentApplication = res.data[0]
            this.receiverList = res.data[1]
          })
          .finally(() => {
            this.loading = false
          })
      } else {
        return this.$message.warning(
          this.$t('请选择审批中或已通过申请或驳回状态的支出申请进行作废!')
        )
      }
    },

    handleEdit(row) {
      if (row.approvalStatus !== 1 && row.approvalStatus !== 5) {
        return this.$message.warning(
          this.$t('只有未提交或已驳回的记录才能编辑')
        )
      }
      this.loadType = row.returnFlag ? 'refund' : 'edit'
      this.loading = true
      this.viewState = 0
      this.drawerTitle = row.returnFlag ? '编辑申请退款' : '编辑支出申请'
      this.infoType = true
      this.currentRow = row
      this.applicationDrawerVisible = true
      getApplication(row.id)
        .then((res) => {
          const data = res.data[0]
          if (row.returnFlag) {
            data.sourceAmount = data.sourceAmount ? data.sourceAmount : Math.abs(res.data[0].amount)
          }
          this.currentApplication = data
          this.receiverList = res.data[1]
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleLoading(flag) {
      this.loading = flag
    },
    handleRemove(row, flag) {
      let removeFlag = true
      if (!flag && row.approvalStatus !== 1) {
        removeFlag = false
      } else if (flag) {
        row.forEach((item) => {
          removeFlag = removeFlag && item.approvalStatus == 1
        })
      }
      if (!removeFlag) {
        return this.$message.warning(this.$t('只有未提交的记录才能删除！'))
      }
      let params = null
      if (flag) {
        params = {
          id: row.reduce((sum, value) => {
            return sum + value.id + ','
          }, '')
        }
      } else {
        params = { id: row.id }
      }
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          removeApprovalApplication(params).then((res) => {
            res.returnCode === '0'
              ? this.$message.success(res.message)
              : this.$message.warning(res.message)
            this.loadList()
          })
        })
        .catch(() => {})
    },
    handleBatchRemove() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (selectedRows.length < 1) {
        return this.$message.warning(this.$t('请选择要删除的数据'))
      }
      this.handleRemove(selectedRows, true)
    },
    handleSubmit() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (selectedRows.length !== 1) {
        return this.$message.warning(this.$t('请选择一条数据!'))
      }
      if (
        selectedRows[0].approvalStatus != '1' &&
        selectedRows[0].approvalStatus != '5'
      ) {
        return this.$message.warning(this.$t('不能重复提交审批!'))
      }
      const params = { id: selectedRows[0].id }
      this.$confirm(this.$t('确定要提交吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          this.boxLoading = true
          submitApprovalApplication(params)
            .then((res) => {
              res.returnCode === '0'
                ? this.$message.success(res.message)
                : this.$message.warning(res.message)
              this.boxLoading = false
              this.loadList()
            })
            .finally(() => {
              this.boxLoading = false
            })
        })
        .catch(() => {
          this.boxLoading = false
        })
    },
    async handleRefund(row) {
      this.loadType = 'refund'
      this.drawerTitle = '申请退款'
      this.currentRow = row
      this.showProcessBtn = false
      this.applicationDrawerVisible = true
      getApplication(row.id)
        .then((res) => {
          const { creationTime } = res.data[0]
          const year =
            moment().year() === moment(creationTime).year()
              ? creationTime
              : `${moment(creationTime).year()}-12-31`
          this.currentApplication = {
            ...res.data[0],
            creationTime: year,
            fundsUse: `【退】${res.data[0].fundsUse}`,
            sourceAmount: res.data[0].amount,
            amount: -res.data[0].amount,
            submitUserName: this.$store.state.user.name
          }
          this.receiverList = res.data[1].map((cur) => {
            cur.sourceAmount = cur.amountPayable
            cur.amountPayable = -cur.amountPayable
            if (cur.specialFundList) {
              for (const fund of cur.specialFundList) {
                fund.payAmount = -fund.payAmount

                if (fund.adjustmentList) {
                  for (const adjustment of fund.adjustmentList){
                    adjustment.payAmount = -adjustment.payAmount
                  }
                }
              }
            }
            if (cur.invoiceList) {
              for (const invoice of cur.invoiceList){
                invoice.payAmount = -invoice.payAmount
              }
            }
            return cur
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleDownloadAttachment() {
      const url =
        process.env.VUE_APP_BASE_API +
        '/payment/approval/approvalApplication/export_zip?access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', '') // 添加downLoad属性
      document.body.appendChild(link)
      link.click()
      link.remove()
    },
    handleSaveApplication(isCommit) {
      this.$refs['_appRef'].handleSaveApplication(isCommit)
      this.flag = false
    },
    handleSaveRefund(isCommit) {
      this.$refs['_appRef'].handleSaveApplication()
      this.flag = false
    },
    handleSaveAndSubmit() {
      // this.loading = true
      this.$refs['_appRef'].handleSaveAndSubmit()
      this.flag = false
    },
    handleTrace(row) {
      if (!row.procInstId) {
        return this.$message.warning(this.$t('只有已提交的申请单才能进行跟踪'))
      }
      this.processUrl =
        '/' +
        process.env.VUE_APP_REAL +
        '/workflow/genProcessDiagram?processInsId=' +
        row.procInstId +
        '&bussinessKey=' +
        row.id +
        '&access_token=' +
        this.getToken()
      this.processInsId = row.procInstId
      this.bussinessKey = row.id
      this.processVisible = true
    },

    handleReApprove(row) {
      if (
        row.approvalStatus != 1 &&
        row.approvalStatus != 5 &&
        row.approvalStatus != 4 &&
        row.approvalStatus != 6
      ) {
        return this.$message.warning(
          this.$t('未提交、已驳回或审核通过的数据，才允许再次申请！')
        )
      }
      this.$confirm('是否重新发起申请审批?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          reFn()
        })
        .catch(() => {})
      const reFn = () => {
        this.loadType = 'add'
        this.drawerTitle = '新增支出申请'
        this.applicationDrawerVisible = true
        this.loading = true
        this.showProcessBtn = false
        getApplication(row.id)
          .then((res) => {
            this.currentApplication = res.data[0]
            // this.currentApplication.amount = 0
            // this.currentApplication.approvalSettingsId = ''
            this.currentApplication.id = ''
            this.receiverList = res.data[1]
            this.receiverList.forEach(cur => {
              cur.deductRelList = []
              cur.specialFundList = []
              cur.invoiceList = []
            })
            if (res.data[0]?.approvalFileSettingInsts?.length) {
              res.data[0]?.approvalFileSettingInsts.forEach((cur, i) => {
                this.getCopyFileByBusiness(row.id, i)
              })
            }
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    getCopyFileByBusiness(id, fileClassification) {
      copyFileByBusinessId({
        srcBusinessId: id,
        srcFileClassification: fileClassification,
        tarFileClassification: fileClassification,
        batchId: uuid()
      }).then((res) => {
        this.currentApplication.approvalFileSettingInsts[
          fileClassification
        ].fileList = res.data
      })
    },
    handleRevocation(row) {
      if (row.approvalStatus != 2) {
        return this.$message.warning(this.$t('只有已提交的数据才能撤回'))
      }
      this.$confirm(this.$t('确定要撤回审批吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          const params = { id: row.id }
          this.boxLoading = true
          revocationApprovalApplication(params)
            .then((res) => {
              res.returnCode === '0'
                ? this.$message.success(res.message)
                : this.$message.warning(res.message)
              this.loadList()
            })
            .finally(() => {
              this.boxLoading = false
            })
        })
        .catch(() => {
          this.boxLoading = false
        })
    },
    handleTovoid(row) {
      if (row.approvalStatus != 4 || row.paymentState != 0) {
        return this.$message.warning(
          this.$t('只有审批通过且未支付的数据才能作废')
        )
      }
      this.$confirm(this.$t('确定要作废审批吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(() => {
          const params = { id: row.id }
          tovoidApprovalApplication(params).then((res) => {
            res.returnCode === '0'
              ? this.$message.success(res.message)
              : this.$message.warning(res.message)
            this.loadList()
          })
        })
        .catch(() => {})
    },
    handlePrint(row) {
      printReport(row)
    },
    handlePreview(row) {
      if (row.approvalStatus == 1) {
        return this.$message.warning(
          this.$t('审批状态为未提交的记录不能预览pdf')
        )
      }
      const url =
        process.env.VUE_APP_BASE_API +
        '/payment/approval/approvalApplication/export_pdf?id=' +
        row.id +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.open(url)
    },
    handleCloseDrawer() {
      this.applicationDrawerVisible = false
      this.loadList()
    },
    handleDrawerClose() {
      this.currentApplication = {}
      this.showProcessBtn = true
    },
    // convertExpenditureType(val) {
    //   const option = this.
    // },
    convertAmount(val) {
      return val.toFixed(2)
    },
    convertApprovalStatus(val) {
      const status = this.approvalstatusOptions.find(
        (item) => item.id.toString() === val.toString()
      )
      return status ? status.text : ''
    },
    convertCreationTime(val) {
      return val ? val.split(' ')[0] : ''
    },
    convertPaymentState(val) {
      const status = this.paymentStateOptions.find(
        (item) => item.id === val + ''
      )
      return status ? status.text : ''
    },
    convertRevitalizationType(val) {
      const revitalizationType = this.revitalizationTypeOptions.find(
        (item) => item.id === val + ''
      )
      return revitalizationType ? revitalizationType.text : '/'
    },
    convertSettlementMethod(val) {
      const settlementMethod = this.settlementMethodOptions.find(
        (item) => item.id === val + ''
      )
      return settlementMethod ? settlementMethod.settleName : ''
    },
    revitalizationTypeClose() {
      this.revitalizationTypeVisible = false
    },
    revitalizationTypeOpen() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (!selectedRows || selectedRows.length < 1) {
        return this.$message.warning(this.$t('请先选择数据!'))
      }
      this.$set(this.revitalizationTypeFrom, 'revitalizationType', '')
     // this.$set(this.revitalizationTypeFrom, 'revitalizationType', selectedRows[0].revitalizationType ? selectedRows[0].revitalizationType : '')
      this.revitalizationTypeVisible = true
    },
    saveRevitalizationType() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (!selectedRows || selectedRows.length < 1) {
        return this.$message.warning(this.$t('请选择一条数据!'))
      }
      const ids = selectedRows.map(item => item.id)
      this.loading = true
      setRevitalizationType({ ids: ids.join(','), revitalizationType: this.revitalizationTypeFrom.revitalizationType }).then(res => {
        if (res.returnCode === '0') {
          this.$message.success(res.message)
          this.$emit('refreshTable')
          this.$emit('update:revitalizationTypeVisible', false)
          this.revitalizationTypeVisible = false
          this.revitalizationTypeFrom = {}
          this.loadList()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    formatRevitalizationType(row) {
      return row.revitalizationType ? row.revitalizationType : '/'
    }
  }
}
</script>

<style lang="scss" scoped>
// .el-main {
//   overflow: hidden;
// }
// .footer-btn {
//   display: flex;
//   justify-content: center;
//   align-items: center;
// }
.gever-form {
  padding-bottom: 50px;
  // height: 100%;
  // overflow-y: auto;
}
.drawer-footer {
  padding-bottom: 10px;
  position: fixed;
  right: 28%;
  bottom: 0;
  // height: 40px;
  left: 30%;
  right: 0;
  z-index: 9;
  background-color: #fff;
  display: flex;
  justify-content: center;
}
.el-main {
  max-height: calc(100vh - 100px);
}
.btn-container {
  width: 100%;
  margin-bottom: 5px;
  display: flex;
  justify-content: left;
}
</style>
