
import request from '@/utils/request'

/**
 * 结转损益列表
 * @param {*} data
 * @returns
 */
export function carryoverPage(data) {
  return request({
    url: '/financial/accounting/accountingCarryover/page',
    method: 'post',
    data: data
  })
}

/**
 * YesAndNo
 * @param {*} data
 * @returns
 */
export function YesAndNo(data) {
  return request({
    url: '/json/financial/public/YesAndNo.json',
    method: 'get',
    data: data
  })
}

/**
 * 方向下拉
 * @param {*} data
 * @returns
 */
export function Direction(data) {
  return request({
    url: '/json/financial/public/Direction.json',
    method: 'get',
    data: data
  })
}

/**
 * 新增结转损益
 * @param {*} data
 * @returns
 */
export function saveCarryover(data) {
  return request({
    url: '/financial/accounting/accountingCarryover/saveCarryover',
    method: 'POST',
    payload: true,
    data: data
  })
}

/**
 * 删除结转损益
 * @param {*} data
 * @returns
 */
export function deleteCarryover(data) {
  return request({
    url: '/financial/accounting/accountingCarryover/deleteCarryover',
    method: 'POST',
    data: data
  })
}

/**
 * 查看结转损益
 * @param {*} data
 * @returns
 */
export function carryoverLoad(id) {
  return request({
    url: `/financial/accounting/accountingCarryover/load/${id}`,
    method: 'GET'
  })
}

/**
 * 查看结转损益科目列表
 * @param {*} data
 * @returns
 */
export function carryoverAccoitempage(data) {
  return request({
    url: `/financial/accounting/accountingCarryoverAccoitem/page`,
    method: 'POST',
    data: data
  })
}

/**
 * 生成凭证
 * @param {*} data
 * @returns
 */
export function checkBeforeGenerate(data) {
  return request({
    url: `/financial/accounting/accountingCarryoverAccoitem/checkBeforeGenerate`,
    method: 'POST',
    data: data
  })
}

/**
 * 结转损益生成凭证前检查
 */
export function checkBeforeGenerateInCarryover(data) {
  return request({
    url: `/financial/accounting/accountingCarryover/checkBeforeGenerate`,
    method: 'POST',
    data: data
  })
}

/**
 * 结转损益生成凭证前提示
 */
export function tipsBeforeGenerate(data) {
  return request({
    url: `/financial/accounting/accountingCarryover/tipsBeforeGenerate`,
    method: 'POST',
    data: data
  })
}

/**
 * 结转损益生成凭证
 */
 export function generateVoucherX(data) {
  return request({
    url: `/financial/accounting/accountingCarryover/generateVoucherX`,
    method: 'POST',
    data: data
  })
}