import request from '@/utils/request'
export function getBillRecoverList(data) {
  return request({
    url: '/bill/billRecover/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
export function getUsersData(data) {
  return request({
    url: '/bill/billRecover/getUserData',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
export function insert(data) {
  return request({
    url:  '/bill/billRecover/save',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
export function getDetail(id) {
  return request({
    url:  `/bill/billRecover/load/${id}`,
    method: 'get',
    // data: data,
    withCredentials: true
  })
}
export function getAreaLevel(data) {
  return request({
    url:  `/bill/billType/getAreaOrg`,
    method: 'get',
    params: data,
    withCredentials: true
  })
}