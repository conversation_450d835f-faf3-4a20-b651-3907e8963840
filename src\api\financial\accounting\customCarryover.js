
import request from '@/utils/request'

/**
 * 自定义结转列表
 * @param {*} data
 * @returns
 */
export function customCarryoverPage(data) {
  return request({
    url: '/financial/accounting/accountingCustomCarryover/page',
    method: 'post',
    data: data
  })
}

/**
 * YesAndNo
 * @param {*} data
 * @returns
 */
export function YesAndNo(data) {
  return request({
    url: '/json/financial/public/YesAndNo.json',
    method: 'get',
    data: data
  })
}

/**
 * 方向下拉
 * @param {*} data
 * @returns
 */
export function Direction(data) {
  return request({
    url: '/json/financial/public/Direction.json',
    method: 'get',
    data: data
  })
}

/**
 * 新增结转损益
 * @param {*} data
 * @returns
 */
export function saveCarryover(data) {
  return request({
    url: '/financial/accounting/accountingCustomCarryover/saveCarryover',
    method: 'POST',
    payload: true,
    data: data
  })
}

/**
 * 删除结转损益
 * @param {*} data
 * @returns
 */
export function deleteCarryover(data) {
  return request({
    url: '/financial/accounting/accountingCustomCarryover/deleteCarryover',
    method: 'POST',
    data: data
  })
}

/**
 * 查看结转损益
 * @param {*} data
 * @returns
 */
export function customCarryoverLoad(id) {
  return request({
    url: `/financial/accounting/accountingCustomCarryover/load/${id}`,
    method: 'GET'
  })
}

/**
 * 查看结转损益科目列表
 * @param {*} data
 * @returns
 */
export function customCarryoverAccoitem(data) {
  return request({
    url: `/financial/accounting/accountingCustomCarryoverAccoitem/page`,
    method: 'POST',
    data: data
  })
}

/**
 * 自定义结转损益生成凭证前检查
 */
export function checkBeforeGenerateInCustomCarryover(data) {
  return request({
    url: `/financial/accounting/accountingCustomCarryover/checkBeforeGenerate`,
    method: 'POST',
    data: data
  })
}

/**
 *  自定义结转损益生成凭证前提示
 */
export function tipsBeforeGenerate(data) {
  return request({
    url: `/financial/accounting/accountingCustomCarryover/tipsBeforeGenerate`,
    method: 'POST',
    data: data
  })
}

/**
 * 结转损益生成凭证
 */
 export function generateVoucherX(data) {
  return request({
    url: `/financial/accounting/accountingCustomCarryover/generateVoucherX`,
    method: 'POST',
    data: data
  })
}
