import request from '@/utils/request'

/* 获取合同类型 */
export function getAllContractType(data) {
  return request({
    url: `/bill/billDetail/getAllContractType`,
    method: 'get',
    params: data
  })
}

/* 查询可用于开票的每期应收款记录  */
export function getInvoicableReceivableAccounts(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/getInvoicableReceivableAccounts`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 查询合同已收款但未开票的实际收款记录 */
export function getReceivabledNoBillAccounts(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/getReceivabledNoBillAccounts`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 删除票据 */
export function deleteBill(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/deleteBill`,
    method: 'post',
    data: data
    // payload: true
  })
}
/* 票据收款列表 */
export function page(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/page`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 出纳关联票据 */
export function relationBillCashier(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/relationBillCashier`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 取消出纳关联票据 */
export function cancelCashierRelation(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/cancelCashierRelation`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 票据列表取消关联出纳 */
export function cancelRelateCashier(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/cancelRelateCashier`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 票据列表查询已关联的会计 */
export function getRelatedAccounting(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/getRelateAccounting`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 票据列表取消关联会计 */
export function cancelRelateAccounting(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/cancelRelateAccounting`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 校验出纳是否已关联票据 */
export function billVerifyCashierRelation(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/verifyCashierRelation`,
    method: 'get',
    params: data
    // payload: true
  })
}

/* 全部收款 */
export function allCollection(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/allCollection`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 部分收款（非合同）列表 */
export function getPartNonContractList(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/getPartNonContractList`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 部分收款（非合同）保存 */
export function partNonContractCollection(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/partNonContractCollection`,
    method: 'post',
    data: data,
    payload: true
  })
}

/* 部分收款（合同）列表 */
export function getPartContractList(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/getPartContractList`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 部分收款（合同）保存 */
export function partContractCollection(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/partContractCollection`,
    method: 'post',
    data: data,
    payload: true
  })
}

/* 获取票据收款记录 */
export function getCollectionRecord(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/getCollectionRecord`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 取消收款   */
export function cancelCollection(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/cancelCollection`,
    method: 'post',
    data: data
    // payload: true
  })
}

/* 出纳关联票据列表记录 */
export function getCashierRelationList(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/getCashierRelationList`,
    method: 'post',
    data: data
    // payload: true
  })
}
/* 获取合同类型 */
export function getIncomeType(data, type = 'billDetail') {
  return request({
    url: `/bill/${type}/getIncomeType`,
    method: 'get'
    // payload: true
  })
}
