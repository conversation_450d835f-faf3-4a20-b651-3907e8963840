import request from '@/utils/request'

/**
 * 获取账套月结列表
 */
export function loadAccountingMonthEndedList(data) {
  return request({
    url: '/financial/accounting/accountingMonthEnded/listAccountingMonthEnded',
    method: 'post',
    data: data
  })
}
/**
 * 月结
 */
export function monthEnded(data) {
  return request({
    url: '/financial/accounting/accountingMonthEnded/monthEnded',
    method: 'post',
    data: data
  })
}

/**
 * 反月结
 */
export function unMonthEnded(data) {
  return request({
    url: '/financial/accounting/accountingMonthEnded/unMonthEnded',
    method: 'post',
    data: data
  })
}

/**
 * 出纳对账列表
 */
export function cashierList(data) {
  return request({
    url: '/financial/accounting/accountingMonthEnded/cashierReconciliation',
    method: 'post',
    data: data
  })
}

/**
 * 资产对账列表
 */
export function assetsList(data) {
  return request({
    url: '/financial/accounting/accountingMonthEnded/assetReconciliation',
    method: 'post',
    data: data
  })
}
