<template>
  <div>
    <el-form
      ref="_formRef"
      inline
      class="gever-form"
      :rules="rules"
      :model="currentBankAccount"
      label-width="130px"
      :disabled="disabled"
    >
      <div class="form-sg">
        <el-form-item :label="$t('组织机构代码')" required>
          <el-input v-model="currentBankAccount.orgCode" disabled />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('组织机构')" prop="orgCode">
          <el-select ref="_orgSelectRef" v-model="currentBankAccount.orgName">
            <el-option
              :value="treeNodeData.code"
              :label="treeNodeData.text"
              style="height: auto"
            >
              <area-tree
                ref="_treeRef"
                :tree-data="treeData"
                @selectedNodeChange="handleOrgChange"
              />
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('户主名称')" required>
          <el-input v-model="currentBankAccount.ownerName" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('账户类型')" prop="accountType" required>
          <gever-select
            v-model="currentBankAccount.accountType"
            path="/financial/bank_account_type/"
            clearable
          />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('银行账号')" prop="accountNumber">
          <el-input v-model.trim="currentBankAccount.accountNumber" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('所属银行')" prop="bankType">
          <gever-select
            v-model="currentBankAccount.bankType"
            path="/payment/pay_bank_type"
            clearable
          />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('开户银行')" prop="bankName">
          <el-input v-model.trim="currentBankAccount.bankName" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('存款类型')" prop="depositType">
          <gever-select
            v-model="currentBankAccount.depositType"
            path="/payment/depositType/"
            number-key
            clearable
          />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('银农直连渠道')" prop="channelId">
          <el-select v-model="currentBankAccount.channelId" @input="handleChannelChange" clearable>
            <el-option
              v-for="item in channelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('银农直连客户号')" prop="customerNumber" :rules="{required: certType == '1', message: '客户号必填', trigger: 'change'}">
          <el-input v-model.trim="currentBankAccount.customerNumber" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('银农直连证书密码')" prop="certificatePassword" :rules="{required: certType == '1', message: '签约证书密码必填', trigger: 'change'}">
          <el-input v-model.trim="currentBankAccount.certificatePassword" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('是否签订监管协议')" prop="hasRegulatoryAgreement">
          <el-select v-model="currentBankAccount.hasRegulatoryAgreement" clearable>
            <el-option
              v-for="item in whetherOptions"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('是否禁用')" prop="state">
          <el-select v-model="currentBankAccount.state" clearable>
            <el-option
              v-for="item in stateOptions"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('扩展信息1')" prop="extendField1">
          <el-input v-model="currentBankAccount.extendField1" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('扩展信息2')" prop="extendField2">
          <el-input v-model="currentBankAccount.extendField2" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('扩展信息3')" prop="extendField3">
          <el-input v-model="currentBankAccount.extendField3" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('扩展信息4')" prop="extendFiel4">
          <el-input v-model="currentBankAccount.extendField4" />
        </el-form-item>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('扩展信息5')" prop="extendField5">
          <el-input v-model="currentBankAccount.extendField5" />
        </el-form-item>
      </div>

      <div class="gever-title">
        <span v-if="currentBankAccount.hasRegulatoryAgreement == 1" style="color: red">*</span>
        {{ $t('监管协议附件信息') }}
        <span class="red" style="font-weight: 400;">
            (附件类型仅支持jpg, jpeg, png, doc, xls, txt, pdf, docx, xlsx, zip)
          </span>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('')" prop="fileBatchId">
          <gever-upload
            ref = "_evidenceIdRef"
            :photographic="true"
            :accept="'.jpg,.jpeg,.png,.doc,.xls,.docx,.xlsx,.pdf,.txt,.zip'"
            :default-batch-id="currentBankAccount.fileBatchId"
            :business-id="currentBankAccount.id"
            file-classification="bankAccountFile"
            :disabled= disabled
            :list-type="'form-list'"
            :limit="10"
            @batch-change="changeFileBatch"
          />
        </el-form-item>
      </div>
      <div class="gever-title">
        <span v-if="certType == 1" style="color: red">*</span>
        {{ $t('签约银农直连证书') }}
        <span class="red" style="font-weight: 400;">
            (附件类型仅支持 pfx，cer，zip，rar)
          </span>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('')" prop="cerFileBatchId">
          <gever-upload
            ref = "_cerFileRef"
            :photographic="false"
            :accept="'.pfx,.cer,.zip,.rar'"
            :default-batch-id="currentBankAccount.fileBatchId"
            :business-id="currentBankAccount.id"
            file-classification="bankAccountCert"
            :disabled= disabled
            :list-type="'form-list'"
            :limit="10"
            @batch-change="changeFileBatch"
          />
        </el-form-item>
      </div>
      <div class="gever-title">
        <span v-if="certType == 1" style="color: red">*</span>
        {{ $t('其他附件') }}
        <span class="red" style="font-weight: 400;">
            (附件类型仅支持jpg, jpeg, png, doc, xls, txt, pdf, docx, xlsx, zip)
          </span>
      </div>
      <div class="form-sg">
        <el-form-item :label="$t('')" prop="otherFileBatchId">
          <gever-upload
            ref = "_otherFileRef"
            :photographic="true"
            :accept="'.jpg,.jpeg,.png,.doc,.xls,.docx,.xlsx,.pdf,.txt,.zip'"
            :default-batch-id="currentBankAccount.fileBatchId"
            :business-id="currentBankAccount.id"
            file-classification="bankAccountOtherFile"
            :disabled= disabled
            :list-type="'form-list'"
            :limit="10"
            @batch-change="changeFileBatch"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import { loadChannelList } from '@/api/payment/capital.js'
import { authTreeDataAll } from '@/api/gever/common.js'

export default {
  props: {
    bankAccount: { type: Object, default: () => {} },
    disabled: { type: Boolean, default: true },
    orgInfo: { type: Object, default: () => {} },
    type: { type: String, default: 'add' }
  },
  data() {
    var fileValidator = (rule, value, callback) => {
      if (this.currentBankAccount.hasRegulatoryAgreement === 1
        && this.$refs._evidenceIdRef.fileList.length === 0) {
        this.$message.error(this.$t('“是否签订监管协议”选择是时，必须上传附件'))
      } else {
        callback()
      }
    }
    var cerFileValidator = (rule, value, callback) => {
      if (this.certType == 1
        && this.$refs._cerFileRef.fileList.length === 0) {
        this.$message.error(this.$t('当前选择的支付渠道的证书类型为机构独占证书，必须上传签约证书文件'))
      } else {
        callback()
      }
    }
    var bank = (rule, value, callback) => {
      var regx = /^\d{14,20}/
      if (regx.test(value)) {
        callback()
      } else {
        callback(new Error(this.$t('请输入正确的银行账号')))
      }
    }
    return {
      rootAreaCode: '',
      treeData: [],
      treeNodeData: {},
      currentBankAccount: this.bankAccount,
      certType: 0,
      channelList: [],
      stateOptions: [
        {
          id: 0,
          text: '禁用'
        },
        {
          id: 1,
          text: '启用'
        }
      ],
      whetherOptions: [
        {
          id: 1,
          text: '是'
        },
        {
          id: 0,
          text: '否'
        }
      ],
      rules: {
        orgCode: [
          { required: true, message: '请选择组织机构', trigger: 'change' }
        ],
        accountType: [
          {
            required: true,
            message: '请选择账户类型',
            trigger: ['blur', 'change']
          }
        ],
        accountNumber: [
          { required: true, message: '请输入银行账号', trigger: 'blur' }
          // {
          //   validator: bank,
          //   trigger: 'blur'
          // },
          // { min: 1, max: 35, message: this.$t('长度在 1 到 35 个字符'), trigger: 'blur' }
        ],
        bankType: [
          { required: true, message: '请选择支付类型', trigger: 'change' }
        ],
        hasRegulatoryAgreement: [
          { required: true, message: '请选择是否签订监管协议', trigger: 'change' }
        ],
        // channelId: [{ required: true, message: '请选择支付渠道', trigger: 'blur' }],
        state: [
          { required: true, message: '请选择是否禁用', trigger: 'change' }
        ],
        customerNumber: [
          { required: true, message: '请填写客户号', trigger: 'change' }
        ],
        certificatePassword: [
          { required: true, message: '请填写签约证书密码', trigger: 'change' }
        ],
        bankName: [
          {
            required: true,
            min: 0,
            max: 100,
            message: this.$t('请输入开户银行, 长度在 1 到 100 个字符'),
            trigger: 'blur'
          }
        ],
        extendField1: [
          {
            min: 0,
            max: 100,
            message: this.$t('长度在 1 到 100 个字符'),
            trigger: 'blur'
          }
        ],
        extendField2: [
          {
            min: 0,
            max: 100,
            message: this.$t('长度在 1 到 100 个字符'),
            trigger: 'blur'
          }
        ],
        extendField3: [
          {
            min: 0,
            max: 100,
            message: this.$t('长度在 1 到 100 个字符'),
            trigger: 'blur'
          }
        ],
        extendField4: [
          {
            min: 0,
            max: 100,
            message: this.$t('长度在 1 到 100 个字符'),
            trigger: 'blur'
          }
        ],
        extendField5: [
          {
            min: 0,
            max: 100,
            message: this.$t('长度在 1 到 100 个字符'),
            trigger: 'blur'
          }
        ],
        fileBatchId: [
          {
            validator: fileValidator,
            trigger: 'blur'
          }
        ],
        cerFileBatchId: [
          {
            validator: cerFileValidator,
            trigger: 'blur'
          }
        ]
      }
    }
  },

  watch: {
    'currentBankAccount.bankType': {
      handler: function (val) {
        if (val) {
          if (this.currentBankAccount.channelId !== undefined) {
            this.currentBankAccount.channelId = ''
          }
          this.loadBankList()
        }
      }
    }
  },
  created() {
    if (!this.currentBankAccount.hasOwnProperty('state')) {
      // 默认启动
      this.$set(this.currentBankAccount, 'state', 1)
    }
    this.rootAreaCode = JSON.parse(localStorage.getItem('areaLogin'))
      ? JSON.parse(localStorage.getItem('areaLogin')).code
      : ''
    authTreeDataAll({ rootAreaCode: this.rootAreaCode }).then((res) => {
      this.treeData = res.data
      if (this.type == 'add') {
        this.$nextTick(() => {
          const node = this.$refs._treeRef.$refs._treeRef.getNode(
            this.orgInfo.id
          )
          this.handleOrgChange(node.data)
        })
      }
    })
    this.loadBankList()
  },
  methods: {
    changeFileBatch(batchId) {
      this.currentBankAccount.fileBatchId = batchId
    },
    loadBankList() {
      if (this.currentBankAccount.bankType) {
        const data = {
          orgCode: this.currentBankAccount.orgCode,
          bankType: this.currentBankAccount.bankType
        }
        loadChannelList(data).then((res) => {
          this.channelList = res.data || []
          this.handleChannelChange()
        })
      }
    },
    handleOrgChange(val) {
      // if (val.type !== 'Organization') {
      //   return this.$message.warning(this.$t('请选择机构'))
      // } else {
      this.treeNodeData = val
      console.log(
        '🚀 ~ file: bankAccount.vue:271 ~ handleOrgChange ~ this.type:',
        this.type
      )
      if (this.type !== 'view') {
        this.$set(this.currentBankAccount, 'orgCode', val.code)
        this.$set(this.currentBankAccount, 'orgName', val.text)
        this.$set(this.currentBankAccount, 'orgId', val.id)
        this.$set(this.currentBankAccount, 'ownerName', val.text)
      }
      this.$nextTick(() => {
        this.$refs['_orgSelectRef'].blur()
      })
      // }
    },
    handleChannelChange() {
      if (!this.currentBankAccount.channelId) {
        this.certType = 0
        this.currentBankAccount.certType = 0
        return
      }
      const item = this.channelList.find(option => option.id === this.currentBankAccount.channelId)
      this.certType = item.certType
      this.currentBankAccount.certType = item.certType
    }
  }
}
</script>

<style></style>
