<template>
  <div>
    <fold-box right-title="支付结果">
      <template #right>
        <div class="right-box">
          <el-form ref="_formRef" :model="queryParams" inline>
            <el-form-item :label="$t('支付单号')" prop="paymentCode">
              <el-input v-model="queryParams.paymentCode" />
            </el-form-item>
            <el-form-item :label="$t('资金用途')" prop="fundsUsed">
              <el-input v-model="queryParams.fundsUsed" />
            </el-form-item>
            <el-form-item :label="$t('交易状态')" prop="fundsUsed">
              <el-select v-model="queryParams.payStatus">
                <el-option
                  v-for="item in paymentStateOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('金额(元)')">
              <gever-input-number
                v-model="queryParams.amountStart"
                :controls="false"
                :value-on-clear="null"
                :precision="2"
                class="w100"
              />
              {{ $t('至') }}
              <gever-input-number
                v-model="queryParams.amountEnd"
                :controls="false"
                :value-on-clear="null"
                :precision="2"
                class="w100"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                v-hasPermi="'financial.payment.manager.payOrderPayee.list'"
                type="primary"
                round
                icon="el-icon-search"
                @click="handleSearch"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-popover
                v-model="advanceVisible"
                trigger="manual"
                popper-class="advance-search"
                width="450"
              >
                <h3>{{ $t('高级搜索') }}</h3>
                <el-form
                  ref="_advanceFormRef"
                  inline
                  label-width="180px"
                  :model="queryParams"
                >
                  <el-form-item
                    :label="$t('收款方银行账户名称')"
                    prop="accountOwner"
                  >
                    <el-input v-model="queryParams.accountOwner" />
                  </el-form-item>
                  <el-form-item
                    :label="$t('收款方银行账户号码')"
                    prop="account"
                  >
                    <el-input v-model="queryParams.account" />
                  </el-form-item>
                </el-form>
                <div style="float: right">
                  <el-button type="primary" plain round @click="handleReset">
                    {{ $t('重置') }}
                  </el-button>
                  <el-button
                    v-hasPermi="'financial.payment.manager.payOrderPayee.list'"
                    type="primary"
                    round
                    icon="el-icon-search"
                    @click="handleAdvanceSearch"
                  >
                    {{ $t('搜索') }}
                  </el-button>
                  <el-link
                    type="primary"
                    :underline="false"
                    icon="el-icon-arrow-up"
                    style="margin-left: 10px"
                    @click="advanceVisible = false"
                  >
                    收起
                  </el-link>
                </div>
                <!-- <i
                  slot="reference"
                  class="el-customIcon-filter"
                  style="font-size: 16px; color: #409eff; cursor: pointer"
                  @click="advanceVisible = true"
                /> -->
                <span
                  slot="reference"
                  style="font-size: 14px; color: #409eff; cursor: pointer"
                  @click="advanceVisible = true"
                >
                  高级搜索
                </span>
              </el-popover>
            </el-form-item>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            v-loading="loading"
            :columns="tableColumns"
            :data="table.dataList"
            :total="table.total"
            :pagi="queryParams"
            @p-current-change="loadList"
            @size-change="loadList"
          >
            <template #operation="{ row }">
              <el-button
                v-hasPermi="'financial.payment.manager.payOrderPayee.view'"
                type="text"
                @click="handleView(row)"
              >
                {{ $t('查看') }}
              </el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <public-drawer
      :title="$t('查看支付结果')"
      :visible.sync="drawerVisible"
      destroy-on-close
    >
      <payment-result-detail :id="currentId" />
    </public-drawer>
  </div>
</template>

<script>
import { loadPayResultList } from '@/api/payment/capital.js'
import PaymentResultDetail from './paymentResult.vue'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'PaymentResult',
  components: { PaymentResultDetail },
  data() {
    return {
      advanceVisible: false,
      queryParams: {
        nodeId: '',
        paymentCode: '',
        fundsUsed: '',
        payStatus: '',
        accountOwner: '',
        account: '',
        page: 1,
        rows: 20,
        region: ''
      },
      table: {
        total: 0,
        dataList: []
      },
      tableColumns: [
        { type: 'selection', width: '50' },
        { label: '序号', type: 'index', index: this.indexMethod, width: '50' },
        { label: '支付申请单流水号', prop: 'orderSerialNumber', width: '160' },
        { label: '明细流水号', prop: 'serialNumber', width: '160' },
        { label: '交易状态', prop: 'payStatus', width: '80' },
        { label: '申请单位', prop: 'orgName', width: '120' },
        { label: '支付单号', prop: 'paymentCode', minWidth: '120' },
        { label: '支付发起人/终止人', prop: 'payUserName', minWidth: '140' },
        {
          label: '发起支付时间/终止时间',
          prop: 'actualPayTime',
          minWidth: '160'
        },
        { label: '资金用途', prop: 'fundsUsed', minWidth: '180' },
        {
          label: '收款方银行账户名称',
          prop: 'accountOwner',
          minWidth: '150'
        },
        {
          label: '收款方银行账户号码',
          prop: 'account',
          minWidth: '160'
        },
        { label: '金额（元）', prop: 'payAmount', width: '120', filter: 'money' },
        { label: '操作', slotName: 'operation', width: '100', fixed: 'right' }
      ],
      paymentStateOptions: [
        { id: '', text: this.$t('全部') },
        // { id: '0', text: this.$t('未支付') },
        { id: '1', text: this.$t('支付中') },
        { id: '2', text: this.$t('支付成功') },
        { id: '5', text: this.$t('退款成功') },
        { id: '-1', text: this.$t('支付失败') },
        { id: '-9', text: this.$t('已退回') },
        { id: '-10', text: this.$t('交易终止') },
        { id: '99', text: this.$t('状态未知') }
      ],
      drawerVisible: false,
      currentId: '',
      loading: false
    }
  },
  computed: {
    ...mapState(['areaLogin'])
  },
  watch: {
    areaLogin: {
      /** 监听到变化后就触发一次handler 相当与created */
      immediate: true,
      handler(val) {
        if (val) {
          this.queryParams.region = val.code || ''
          this.loadList()
        }
      },
      deep: true
    }
  },
  created() {
    this.loadList()
  },
  deactivated() {
    if (this.advanceVisible) this.advanceVisible = false
  },
  methods: {
    indexMethod(index) {
      return (this.queryParams.page - 1) * this.queryParams.rows + index + 1
    },
    loadList() {
      this.loading = true
      loadPayResultList(this.queryParams).then((res) => {
        this.table.dataList = res.data.rows
        this.table.total = res.data.total
      })
      .finally(() => {
        this.loading = false
      })
    },
    handleSearch() {
      this.queryParams.page = 1
      this.advanceVisible = false
      this.$refs['_advanceFormRef'].resetFields()
      this.loadList()
    },
    handleAdvanceSearch() {
      this.queryParams.page = 1
      this.loadList()
    },
    handleReset() {
      this.$refs['_formRef'].resetFields()
      this.$refs['_advanceFormRef'].resetFields()
    },
    handleView(row) {
      this.currentId = row.id
      this.drawerVisible = true
    }
  }
}
</script>

<style lang="scss" scoped></style>
