/*
 * @Descripttion:会计科目类型接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-26 10:43:17
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-10-26 11:42:50
 */
import request from '@/utils/request'

/**
 * @name:获取基础设置的科目类型
 * @param {Object} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/books/basedata/accountingItemType/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取会计科目类型
 * @param {String} booksId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listByBooksId({ booksId = '' }) {
  return request({
    url: '/financial/books/basedata/accountingItemType/listByBooksId',
    method: 'post',
    data: { booksId },
    withCredentials: true
  })
}
