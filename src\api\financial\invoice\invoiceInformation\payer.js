/*
 * @Descripttion: 
 * @version: 
 * @Author: 未知
 * @Date: 2023-10-09 16:59:46
 * @LastEditors: Andy
 * @LastEditTime: 2023-10-17 11:47:53
 */
import request from '@/utils/request'

/*
export function demo(data) {
  return request({
    url: ``,
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}
*/

/*
export function demo(params) {
  return request({
    url: ``,
    method: 'get',
    params,
    // payload: true,
  })
}
*/

/* 列表 */
export function page(data) {
  return request({
    url: '/invoice/invoicePayer/page',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 保存/编辑 */
export function save(data) {
  return request({
    url: '/invoice/invoicePayer/save',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 查看 */
export function load(id) {
  return request({
    url: `/invoice/invoicePayer/load/${id}`,
    method: 'get'
  })
}

/* 根据机构ID查询付款方信息 */
export function getPayerByOrgId(params) {
  return request({
    url: `/invoice/invoicePayer/getPayerByOrgId`,
    method: 'get',
    params
  })
}

/* 删除 */
export function deleteData(data) {
  return request({
    url: `/invoice/invoicePayer/delete`,
    method: 'post',
    data
  })
}
