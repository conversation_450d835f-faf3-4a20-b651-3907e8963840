<template>
  <div>
    <public-drawer
      :title="title"
      :visible.sync="dialogVisible"
      :size="70"
      :destroy-on-close="true"
      custom-class="budget-planning-dialog"
      :close-on-click-modal="false"
      :append-to-body="false"
      :buttons="
        buttons.length
          ? buttons
          : [
            {
              type: 'default',
              text: '关闭',
              buttonStatus: type !== '查看',
              callback: () => {
                dialogVisible = false
              }
            },
            {
              type: 'primary',
              text: '保存',
              loading: saveLoading,
              buttonStatus: type !== '查看',
              callback: handleSure
            },
            {
              type: 'primary',
              text: '暂存',
              loading: saveTimeLoading,
              buttonStatus: type !== '查看',
              callback: handleSure2
            }
          ]
      "
      @closed="closed"
    >
      <el-form
        ref="_formSubmitRef"
        :model="form"
        label-width="140px"
        :disabled="activeTab === 'file' ? false : type === '查看'"
      >
        <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
          <el-col :span="8">
            <el-form-item prop="budgetType" label="预算类型">
              <gever-input v-model="form.budgetType" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="budgetYear" label="年份">
              <gever-select
                v-model="form.budgetYear"
                :options="optionsMap.listYearList"
                label-prop="createYear"
                value-prop="createYear"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="budgetCode" label="预算编码">
              <gever-input
                v-model="form.budgetCode"
                disabled
                placeholder="自动生成"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="projectName" label="项目名称">
              <gever-input v-model="form.projectName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="unitName" label="单位名称">
              <gever-input v-model="form.unitName" disabled />
            </el-form-item>
          </el-col>
          <el-col v-if="type !== '创建'" :span="8">
            <el-form-item prop="approvalStatus" label="审核状态">
              <gever-select
                v-model="form.approvalStatus"
                :options="optionsMap.approvalStatus"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="budgetTotalIncome" label="预算收入合计">
              <el-input-number
                v-model="form.budgetTotalIncome"
                disabled
                :min="0"
                :precision="2"
                :controls="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="budgetTotalExpend" label="预算支出合计">
              <el-input-number
                v-model="form.budgetTotalExpend"
                disabled
                :min="0"
                :precision="2"
                :controls="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              prop="budgetDifference"
              label="预算差额"
              :rules="[
                {
                  validator: (rule, value, callback) =>
                    ruleBudgetDifference(rule, value, callback),
                  trigger: 'change'
                }
              ]"
            >
              <el-input-number
                v-model="form.budgetDifference"
                disabled
                :precision="2"
                :controls="false"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item prop="budgetTotalExtExp" label="特大额开支合计">
              <el-input-number
                v-model="form.budgetTotalExtExp"
                disabled
                :precision="2"
                :controls="false"
              />
            </el-form-item>
          </el-col> -->
          <el-col v-if="form.gzysmx" :span="8">
            <el-form-item prop="budgetTotalSalary" label="工资预算合计">
              <el-input-number
                v-model="form.budgetTotalSalary"
                disabled
                :precision="2"
                :controls="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="budgetControl" label="是否开启预算控制">
              <el-switch
                v-model="form.budgetControl"
                :active-value="1"
                :inactive-value="0"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col v-if="form.budgetControl == '1'" :span="8">
            <el-form-item
              prop="budgetThreshold"
              label="预算提醒阈值"
              :rules="[
                {
                  required: true,
                  message: '请输入预算提醒阈值',
                  trigger: 'blur'
                },
                {
                  validator: (rule, value, callback) =>
                    ruleBudgetControl(rule, value, callback),
                  trigger: 'blur'
                }
              ]"
            >
              <el-input-number
                v-model="form.budgetThreshold"
                disabled
                :controls="false"
                :precision="2"
                :max="100"
                :min="0"
              />
              <span class="append-ratio">%</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="isDeficit" label="是否允许赤字">
              <el-switch
                v-model="form.isDeficit"
                :active-value="1"
                :inactive-value="0"
                disabled
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item prop="isAnnualBalance" label="应用年度结余">
              <el-switch
                v-model="form.isAnnualBalance"
                :active-value="1"
                :inactive-value="0"
                disabled
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item prop="annualBalance" label="往年结余">
              <el-input-number
                v-model="form.annualBalance"
                :disabled="form.isAnnualBalance == '0'"
                :precision="2"
                :controls="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          style="min-height: 580px"
          :class="
            $route.path === '/financial/budget/budgetApproval' && istodolist
              ? 'approval'
              : ''
          "
        >
          <el-col :span="24" class="gever-title">
            <span>{{ $t('高级设置') }}</span>
            <!-- <div>
              <el-button
                  type="primary"
                  plain
                  icon="el-icon-upload2"
                  @click="handleExport"
                >{{ $t('导出预算') }}</el-button>
              <el-button
                type="primary"
                plain
                icon="el-icon-download"
                @click="handleImport"
              >
                {{ $t('导入预算编制') }}
              </el-button>
            </div> -->
          </el-col>
          <el-col :span="24">
            <SeniorTabSetting
              ref="_seniorTabRef"
              :is-plan="true"
              :type="type"
              :tab-map="tabMap"
              :tab-table="tabTable"
              :accounting-item-type="optionsMap.accountingItemType"
              :loading="tabTable.loading"
              :subject-list="subjectList"
              :contracts-visible.sync="contractsVisible"
              :non-profit-and-loss-items="tabTable.data.nonProfitAndLossItems"
              @fillSubjectData="fillSubjectData"
              @handleActiveTab="handleActiveTab"
              @handleAddRow="handleAddRow"
              @handleDelFn="handleDelFn"
              @handleDelRow="handleDelRow"
              @handleSubmitSave="handleSubmitSave"
              @handleFileChange="handleFileChange"
              @handleReadySalary="handleReadySalary"
              @handleAmountCount="handleAmountCount"
              @handleSalaryAddRow="handleSalaryAddRow"
              @handleNonProfitAndLossAddRow="handleNonProfitAndLossAddRow"
              @initNonProfit="initNonProfit"
              @disableOne="disableOne"
              @handleBudgetTotalSalary="handleBudgetTotalSalary"
            />
          </el-col>
          <el-col v-show="activeTab === 'file'" :span="24">
            <!-- 附件 -->
            <FileDetail
              :disabled="type === '查看'"
              :business-id="form.id"
              @handleFileChange="handleFileChange"
            />
          </el-col>
          <el-col
            v-if="
              ['incomeItems', 'expendItems', 'nonProfitAndLossItems'].includes(
                activeTab
              )
            "
            :span="24"
          >
            <h3>
              {{ $t('本年计划数合计金额：') }}
              <span class="text-danger font16">
                {{ $t(formatMoney(amountCount)) }}
              </span>
              {{ $t(' 元') }}
            </h3>
          </el-col>
        </el-row>
      </el-form>
      <slot name="footer" />
    </public-drawer>
    <!-- 选择合同-->
    <budget-choose :visible.sync="contractsVisible" @confirm="saveContracts" />
    <!-- 导入预算编制弹窗 -->
    <el-dialog
      title="批量导入"
      :visible.sync="batchImportVisible"
      width="800px"
      destroy-on-close
    >
      <div>
        <el-row type="flex" justify="center" align="center">
          <gever-upload
            drag
            accept=".xlsx"
            :limit="1"
            @batch-change="handleFileBudget"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              <em>点击上传</em>
              或者将文件拖放到这里(xlsx)
            </div>
          </gever-upload>
        </el-row>

        <div class="gever-title">说明</div>
        <div class="bdr-10 import-explain">
          <div>
            请根据导入模板填写预算编制数据。导入模板：
            <span class="pointer" style="color: #00e">
              <a
                target="_blank"
                download
                :href="`/excel/financial/${excleType}.xlsx`"
              >
                {{ $t('预算编制导入模板.xlsx') }}
              </a>
            </span>
          </div>
        </div>
        <!-- <div class="gever-title">导入检测结果</div>
          <div class="gever-import-result">
            <ul>
              <li v-for="(result, index) in importResults" :key="index">
                {{ result }}
              </li>
            </ul>
          </div> -->
        <el-row type="flex" justify="end" align="center">
          <el-button
            type="primary"
            icon="el-icon-upload"
            :loading="loading"
            @click="handleUpload"
          >
            {{ $t('导入') }}
          </el-button>
          <el-button @click="handleCloseImport">{{ $t('取消') }}</el-button>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import mixins from './mixins'
import SeniorTabSetting from './seniorTabSetting.vue'
import { formatMoney } from '@/utils/index.js'
import { tabMap } from '../budgetPlanning/config'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('financial')
import _ from 'lodash'
import {
  save,
  details,
  getItemData,
  checkCanAdd,
  getImportContent
} from '@/api/budget/budgetPlanning'
import {
  details as settingDetails,
  getRentalIncomeItemList,
  getByBooksTypeId
} from '@/api/budget/budgetItemSetting'
export default {
  name: 'PlanningForm',
  components: {
    SeniorTabSetting,
    BudgetChoose: () => import('./BudgetChoose.vue'),
    FileDetail: () => import('./FileDetail.vue')
  },
  mixins: [mixins],
  props: {
    istodolist: {
      type: Boolean,
      default: false
    },
    optionsMap: {
      type: Object,
      default: () => ({})
    },
    buttons: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: '查看'
    },
    propsTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // formRules,
      tabMap: _.cloneDeep(tabMap),
      form: {},
      isTemp: 0,
      booksLevel: {},
      activeTab: 'incomeItems',
      saveLoading: false,
      saveTimeLoading: false,
      tableLoading: false,
      drawerLoading: false,
      dialogVisible: false,
      contractsVisible: false,
      tabTable: {
        data: {
          incomeItems: [], // 年度预算收入
          expendItems: [], // 年度预算支出
          rentalIncomes: [], // 租金明细
          adjExpContracts: [], // 期满合同租金标准调整
          salaryBudgets: [], // 工资预算
          nonProfitAndLossItems: [] // 非损益类明细科目
        },
        loading: false
      },
      batchImportVisible: false,
      budgetBatchId: '',
      loading: false,
      importResults: [],
      isExamineMessageView: false // 判断是否是从待办事项进来的审核信息查看
    }
  },
  computed: {
    ...mapState(['books']),
    excleType() {
      if (this.books?.newBooksType === 2) {
        return 'budgetPlanningTemplateYey'
      }
      if (this.books?.newBooksType === 3) {
        return 'budgetPlanningTemplateSc'
      }
      return 'budgetPlanningTemplate'
    },
    title() {
      if (!this.propsTitle) {
        return this.$t(`${this.type}预算编制`)
      }
      return this.$t(this.propsTitle)
    },
    // incomeItems
    incomeCount() {
      const allData = this.tabTable.data.incomeItems.filter(
        (cur) => cur.accountingItemLevel === 1
      )
      return allData.reduce(
        (total, item) => total + (item.budgetAmount ?? 0),
        0
      )
    },
    // 年度预算支出
    expendCount() {
      const allData = this.tabTable.data.expendItems.filter(
        (cur) => cur.accountingItemLevel === 1
      )
      return allData.reduce(
        (total, item) => total + (item.budgetAmount ?? 0),
        0
      )
    },
    // 非损益类预算明细
    nonProfitAndLossCount() {
      const allData = this.tabTable.data.nonProfitAndLossItems.filter(
        (cur) => cur.accountingItemLevel === 1
      )
      return allData.reduce(
        (total, item) => total + (item?.budgetAmount ?? 0),
        0
      )
    },
    amountCount() {
      if (this.activeTab === 'incomeItems') {
        return this.incomeCount
      }
      if (this.activeTab === 'expendItems') {
        return this.expendCount
      }
      // 2023-08-25  有非损益类预算明细的时候，预算支出和特大额支出的合计金额不加上非损益类的金额
      if (this.activeTab === 'nonProfitAndLossItems') {
        return this.nonProfitAndLossCount
      }
      return 0
    }
  },
  watch: {
    // 开关开的时候，预算差额的计算=预算收入+金额编辑框的金额-预算支出
    'form.annualBalance': {
      handler: function (newValue) {
        // 往年结余的数据加到预算收入里面
        const valueIncome = this.incomeCount + newValue
        // 计算预算差额
        const value = valueIncome - this.expendCount
        if (!this.isExamineMessageView) {
          this.$set(this.form, 'budgetTotalIncome', valueIncome)
          this.$set(this.form, 'budgetDifference', value)
        }
      },
      immediate: true
    },
    'form.isAnnualBalance': {
      handler: function (newValue) {
        if (newValue == '0') {
          this.form.annualBalance = 0
        }
      },
      immediate: true
    }
  },
  methods: {
    formatMoney,
    // 填充用户导入的数据
    handleFillData(data) {
      for (const codeKey in data) {
        ['incomeItems', 'expendItems', 'nonProfitAndLossItems'].forEach(
          (name) => {
            const tableData = this.tabTable.data[name]
            if (!tableData?.length) return
            tableData.forEach((item, index) => {
              if (item.accountingItemCode === codeKey) {
                this.$set(
                  this.tabTable.data[name][index],
                  'budgetAmount',
                  data[codeKey]
                )
              }
            })
          }
        )
      }
    },
    // 保存上传的预算编制附件id
    handleFileBudget(batchId) {
      this.budgetBatchId = batchId
    },
    // 导入预算编制
    async handleUpload() {
      try {
        this.loading = true
        const {
          message = '',
          returnCode,
          data
        } = await getImportContent({
          batchId: this.budgetBatchId
        })
        if (returnCode === '0') {
          this.handleFillData(data)
          this.$message.success(message)
        }
        this.loading = false
        this.handleCloseImport()
      } catch (error) {
        const { returnCode, message, data } = error
        if (returnCode === '1') {
          this.importResults = JSON.parse(message)
        } else {
          this.importResults = [message].concat(data)
        }
        this.loading = false
      }
    },
    // 关闭导出弹窗
    handleCloseImport() {
      this.batchImportVisible = false
    },
    // 导入预算编制
    handleImport() {
      this.batchImportVisible = true
    },
    handleExport() {},
    // 校验预算阈值提醒
    ruleBudgetControl(rule, value, callback) {
      if (value <= 0) {
        callback(new Error(this.$t('预算提醒阈值不能小于等于0')))
      }
      callback()
    },
    // 处理读取工资明细
    handleReadySalary({ index, row }) {
      const expendItems = this.tabTable.data?.expendItems
      for (let index = 0; index < expendItems.length; index++) {
        const cur = expendItems[index]
        if (
          row.accountingItemCode === cur.accountingItemCode &&
          row.budgetPrjType !== 3
        ) {
          this.$message.error(
            `读取工资预算明细失败: 科目【${row.accountingItemCode}-${row.accountingItemTitle}】不是科目项目`
          )
          return
        }
        if (
          row.accountingItemCode === cur.accountingItemCode &&
          row.leafItem !== 1
        ) {
          this.$message.error(
            `读取工资预算明细失败: 科目【${row.accountingItemCode}-${row.accountingItemTitle}】不是明细科目`
          )
          return
        }
      }
      this.tabTable.data?.expendItems.for
      this.tabTable.data?.expendItems.splice(index, 1, row)
      this.$nextTick(() => {
        this.handleAmountCount({
          row,
          index,
          budgetType: 'expendItems',
          prop: 'budgetAmount'
        })
      })
    },
    // 修该工资预算合计
    handleBudgetTotalSalary(total) {
      this.form.budgetTotalSalary = total
    },
    // 删除
    handleDelFn({ index, budgetType }) {
      this.$confirm('确认要删除该条数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tabTable.data[budgetType].splice(index, 1)
      })
    },
    // 校验预算差额
    ruleBudgetDifference(rule, value, callback) {
      if (value < 0 && !this.form.isDeficit) {
        callback(new Error(this.$t('预算差额不能小于0')))
      }
      callback()
    },
    // 计算form里的预算支出合计
    budgetTotalExpendCount() {
      // 2023-08-25  有非损益类预算明细的时候，预算支出和特大额支出的合计金额不加上非损益类的金额
      //   this.form.budgetTotalExpend = this.expendCount + this.nonProfitAndLossCount
      this.form.budgetTotalExpend = this.expendCount
    },
    // 新增一行工资预算明细
    handleSalaryAddRow() {
      this.tabTable.data[this.activeTab].push({
        level: '',
        postName: '',
        accountintItemCode: '',
        totalSalary: 0,
        baseSalary: 0,
        accruedSalary: 0,
        performanceAward: 0,
        otherSalary: 0,
        tranCommSubsidy: 0,
        holidaySubsidy: 0,
        overtimeDutySubsidy: 0,
        postSubsidy: 0,
        annualLeaveSubsidy: 0,
        priceSubsidy: 0,
        highTemperatureSubsidy: 0,
        senioritySubsidy: 0,
        retirementSubsidy: 0,
        otherSubsidy: 0,
        remark: ''
      })
    },
    // 保存选择合同
    saveContracts(row) {
      // 当前row已在已选数据里
      row.map((item, index) => {
        if (
          this.tabTable.data?.[this.activeTab]?.some(
            (cur) => cur.id === item.id
          )
        ) {
          return
        }
        const {
          id,
          contractCode,
          contractName,
          secondName,
          beginDate,
          endDate
        } = item
        let data = {}
        // 租金明细
        if (this.activeTab === 'rentalIncomes') {
          data = {
            id,
            contractCode,
            contractName,
            secondName,
            startDate: beginDate,
            endDate
          }
        }
        // 期满合同
        if (this.activeTab === 'adjExpContracts') {
          data = {
            id,
            endDate,
            propertyName: contractName,
            originalLessee: secondName,
            openingArea: 0,
            originalPrice: 0,
            openingAreaPlanPrice: 0,
            planMonthRent: 0,
            leaseTerm: 0,
            rentIncrease: 0,
            contractTotalObject: 0,
            purposeRequire: '',
            bidBond: 0,
            disposalMethod: '',
            remark: ''
          }
        }
        // 租金收入 / 期满合同
        this.tabTable.data[this.activeTab].push(data)
      })
    },
    // 查询租金收入科目
    handleRentalIncomeItemList(id) {
      getRentalIncomeItemList(id).then((res) => {
        if (res?.returnCode === '0') {
          if (!res.data?.length) return
          const data =
            res.data?.map((cur) => {
              const { accountingItemCode, accountingItemTitle } = cur
              const prop =
                'accountingItemCode_' +
                accountingItemCode +
                '_' +
                accountingItemTitle
              return {
                prop,
                label: cur.accountingItemTitle,
                minWidth: 100
              }
            }) ?? []
          const index = this.tabMap.findIndex((cur) => cur.key === 'zjsrmx')
          if (index > -1) {
            this.tabMap[index].columns = [
              ...this.tabMap[index].columns,
              ...data
            ]
          }
        } else {
          this.$message.warning(this.$t(res.message))
        }
      })
    },
    // 关闭
    handleClose() {
      this.dialogVisible = false
    },
    // 打开drawer
    showDrawer(data) {
      if (!data) {
        this.handleCheckCanAdd()
      } else {
        this.form = { ...data }
        if (Object.keys(data).length == '1') {
          this.isExamineMessageView = true
        } else {
          this.isExamineMessageView = false
        }
        this.dialogVisible = true
        this.getDetails()
      }
    },
    // 获取单条数据详情
    getDetails() {
      this.drawerLoading = true
      details(this.form.id)
        .then((res) => {
          if (res.returnCode === '0') {
            this.form = {
              batchId: this.form.batchId,
              budgetTotalIncome: 0,
              budgetTotalExpend: 0,
              budgetDifference: 0,
              budgetType: res?.data?.budgetSetting?.budgetType,
              ...res.data
            }
            this.$emit('getUpload', this.form.uploadFile)
            const budgetSetting = res?.data?.budgetSetting
            getByBooksTypeId({ booksTypeId: budgetSetting?.booksTypeId }).then(
              (res) => {
                if (res.returnCode === '0') {
                  this.booksLevel = res.data
                }
              }
            )
            if (budgetSetting.zjsrmx) {
              this.handleRentalIncomeItemList(budgetSetting.id)
            }
            this.tabMap = this.tabMap.filter((cur) => budgetSetting[cur.key])
            this.getChildrenItemData(budgetSetting.id)
          } else {
            this.form = {
              id: '',
              budgetTotalIncome: 0,
              budgetTotalExpend: 0,
              budgetDifference: 0
            }
            this.$message.warning(this.$t(res.message))
          }
        })
        .finally(() => {
          this.$emit('changebId', this.form.procInstId)
          this.drawerLoading = false
        })
    },
    // 获取子表数据
    getChildrenItemData(budgetSettingId) {
      this.tabTable.loading = true
      getItemData({
        budgetSettingId,
        budgetPlaitId: this.form.id,
        booksId: this.books.id
      })
        .then((res) => {
          if (res.returnCode === '0') {
            const {
              incomeItems,
              expendItems,
              nonProfitAndLossItems,
              rentalIncomes,
              adjExpContracts,
              salaryBudgets
            } = res.data
            this.tabTable.data = {
              nonProfitAndLossItems:
                nonProfitAndLossItems?.map((cur) => {
                  const budgetAmount = cur?.budgetAmount ?? 0
                  const lastYearAmount = cur?.lastYearAmount ?? 0
                  return {
                    ...cur,
                    lastYearAmount: cur?.lastYearAmount ?? 0,
                    difference: budgetAmount - lastYearAmount,
                    ratio: isNaN(lastYearAmount / budgetAmount)
                      ? 0
                      : (lastYearAmount / budgetAmount) * 100,
                    rentalIncome: cur.rentalIncome ?? 0,
                    disabled: cur.accountingItemCode == '001',
                    projectBudget: cur.projectBudget ?? 0
                  }
                }) ?? [],
              rentalIncomes:
                rentalIncomes?.map((cur) => {
                  if (!cur?.items?.length) return cur
                  const item = cur
                  cur.items.forEach((row) => {
                    const key =
                      'accountingItemCode_' +
                      row.accountingItemCode +
                      '_' +
                      row.accountingItemTitle
                    cur[key] = row.value
                  })
                  delete cur.items
                  return item
                }) ?? [],
              adjExpContracts: adjExpContracts ?? [],
              salaryBudgets:
                salaryBudgets?.map((cur) => {
                  const code = []
                  if (
                    cur.accountintItemCode?.length > this.booksLevel.levelItem1
                  ) {
                    let bookLen = 0
                    for (const key in this.booksLevel) {
                      if (
                        key.includes('levelItem') &&
                        this.booksLevel[key] &&
                        bookLen < cur.accountintItemCode.length
                      ) {
                        bookLen += this.booksLevel[key]

                        code.push(cur.accountintItemCode.slice(0, bookLen))
                      }
                    }
                  } else {
                    code.push(cur.accountintItemCode)
                  }
                  return {
                    ...cur,
                    accountintItemCode: code
                  }
                }) ?? [],
              incomeItems: incomeItems.map((cur) => {
                const budgetAmount = cur?.budgetAmount ?? 0
                const lastYearAmount = cur?.lastYearAmount ?? 0
                return {
                  ...cur,
                  lastYearAmount: cur?.lastYearAmount ?? 0,
                  difference: budgetAmount - lastYearAmount,
                  ratio: isNaN(lastYearAmount / budgetAmount)
                    ? 0
                    : (lastYearAmount / budgetAmount) * 100,
                  rentalIncome: cur.rentalIncome ?? 0
                }
              }),
              expendItems: expendItems.map((cur) => {
                const budgetAmount = cur?.budgetAmount ?? 0
                const lastYearAmount = cur?.lastYearAmount ?? 0
                return {
                  ...cur,
                  lastYearAmount: cur?.lastYearAmount ?? 0,
                  difference: budgetAmount - lastYearAmount,
                  ratio: isNaN(lastYearAmount / budgetAmount)
                    ? 0
                    : (lastYearAmount / budgetAmount) * 100,
                  rentalIncome: cur.rentalIncome ?? 0,
                  projectBudget: cur.projectBudget ?? 0
                }
              })
            }
          } else {
            this.tabTable.data = {
              nonProfitAndLossItems: [], // 非损益类明细科目
              incomeItems: [], // 年度预算收入
              expendItems: [] // 年度预算支出
            }
            this.$message.warning(this.$t(res.message))
          }
        })
        .finally(() => {
          this.tabTable.loading = false
        })
    },
    // 校验是否能新增
    handleCheckCanAdd() {
      checkCanAdd({ booksId: this.books.id }).then((res) => {
        if (res?.returnCode === '0') {
          this.dialogVisible = true
          this.handleSettingDetails(res.data.budgetSettingId)
        } else {
          this.$message.warning(this.$t(res.message))
        }
      })
    },

    // 获取预算设置数据
    handleSettingDetails(id) {
      this.drawerLoading = true
      settingDetails(id)
        .then((res) => {
          if (res?.returnCode === '0') {
            const {
              budgetType,
              budgetYear,
              budgetCod,
              id,
              budgetThreshold,
              budgetControl
            } = res?.data ?? {}

            getByBooksTypeId({ booksTypeId: res.data.booksTypeId }).then(
              (res) => {
                if (res.returnCode === '0') {
                  this.booksLevel = res.data
                }
              }
            )
            if (this.type === '创建') {
              this.form = {
                ...res?.data,
                batchId: this.form.batchId,
                budgetType: budgetType ?? '',
                budgetYear: budgetYear ?? '',
                budgetCode: budgetCod ?? '',
                id: '',
                budgetSettingId: id,
                budgetTotalIncome: 0,
                budgetTotalExpend: 0,
                budgetDifference: 0,
                budgetTotalExtExp: 0,
                budgetTotalSalary: 0,
                budgetThreshold: budgetThreshold ?? 0,
                votePass: 1,
                annualBalance: 0,
                budgetControl: budgetControl ?? 0,
                unitName: this.books.booksName,
                projectName: `${this.books.createYear}年度收支预算`
              }
            } else {
              this.form = {
                ...this.form,
                budgetTotalIncome: 0,
                budgetTotalExpend: 0,
                budgetDifference: 0,
                budgetSettingId: id,
                votePass: 1,
                budgetControl: 1
              }
            }
            if (res.data.zjsrmx) {
              this.handleRentalIncomeItemList(id)
            }
            this.tabMap = this.tabMap.filter((cur) => res.data[cur.key])
            this.getChildrenItemData(id)
          }
        })
        .finally(() => {
          this.drawerLoading = false
        })
    },
    handleParentNode(code, level, budgetType, parentAccountingItemCode, prop) {
      // 当前行父节点index
      const parentNodeInex =
        parentAccountingItemCode === '0'
          ? 0
          : this.tabTable.data[budgetType].findIndex(
              (cur) => cur?.accountingItemCode === parentAccountingItemCode
            )
      if (parentNodeInex < 0) return

      // 当前行兄弟节点(含当前节点)
      const brotherNode = this.tabTable.data[budgetType].filter(
        (cur) =>
          cur?.accountingItemCode.includes(parentAccountingItemCode) &&
          cur?.accountingItemCode?.length === code.length
      )
      // 兄弟节点总额
      const brotherNodeTotal = brotherNode.reduce(
        (total, cur) => total + Number(cur?.[prop] ?? 0),
        0
      )
      // 更新父节点的和
      this.$set(
        this.tabTable.data[budgetType][parentNodeInex],
        prop,
        brotherNodeTotal.toFixed(2) * 1
      )
      // 更新父节点其他数据
      // this.$nextTick(() => {
      //   const row = this.tabTable?.data?.[budgetType]?.[parentNodeInex] ?? {}
      //   this.handleAmountCount({
      //     row,
      //     index: parentNodeInex,
      //     budgetType,
      //     prop
      //   })
      // })

      // 非一级科目继续递归计算父节点和
      if (level - 1 > 1) {
        // 当前行父节点code
        const parentCode =
          this.tabTable.data?.[budgetType]?.[parentNodeInex]
            .parentAccountingItemCode

        this.handleParentNode(
          parentAccountingItemCode,
          level - 1,
          budgetType,
          parentCode,
          prop
        )
      }
    },
    // 计算上年计划/上年结余/本年计划
    handleAmountCount({ row, index, budgetType, prop }) {
      if (!this.tabTable?.data[budgetType][index][prop]) {
        this.$set(this.tabTable?.data[budgetType][index], prop, 0)
      }
      // 当前节点是二级科目则计算一级科目总额
      if (row.accountingItemLevel > 1 && row.leafItem !== 2) {
        this.handleParentNode(
          row.accountingItemCode,
          row.accountingItemLevel,
          budgetType,
          row.parentAccountingItemCode,
          prop
        )
      }
      // 2023-08-25  有非损益类预算明细的时候，预算支出和特大额支出的合计金额不加上非损益类的金额
      const budgetTotalExpend = this.expendCount
      // 开关开的时候，预算差额的计算=预算收入+金额编辑框的金额-预算支出
      const annualBalance = this.form.annualBalance || 0
      // 往年结余加入到预算收入
      const incomeCount = this.incomeCount + annualBalance
      const budgetDifference = incomeCount - budgetTotalExpend
      this.$set(this.form, 'budgetTotalIncome', incomeCount)
      this.$set(this.form, 'budgetTotalExpend', budgetTotalExpend)
      this.$set(this.form, 'budgetDifference', budgetDifference)
      // 计算特大额支出
      if (['expendItems', 'nonProfitAndLossItems'].includes(budgetType)) {
        const extExpData = this.tabTable?.data?.expendItems?.filter(
          (cur) => cur.extExp
        )
        const count = extExpData.reduce((total, cur) => {
          return total + (cur.budgetAmount ?? 0)
        }, 0)
        this.$set(this.form, 'budgetTotalExtExp', count)
      }
    },
    // 计算比率 （本年预算数 budgetAmount - 去年实际数 lastYearAmount）/去年实际数*100% lastYearAmount 去年为0时， 应该是0，保留4位小数后*100
    countRatio(budgetAmount, lastYearAmount) {
      if (!lastYearAmount) return 0
      const res = (budgetAmount - lastYearAmount) / lastYearAmount
      return res.toFixed(4) * 100
    },
    // 附件
    handleFileChange(batchId) {
      this.$set(this.form, 'batchId', batchId)
    },
    // 新增行
    handleAddRow({ sourceName, row, index }) {
      // 科目编码
      // 添加的索引
      let addIndex = index

      const curBrother = this.tabTable.data[sourceName].filter(
        (cur) =>
          cur.accountingItemCode.startsWith(row.accountingItemCode) &&
          cur.accountingItemCode.length > row.accountingItemCode.length
      )
      let code = `${row.accountingItemCode}001`
      if (curBrother?.length) {
        const item = curBrother[curBrother.length - 1]
        addIndex = this.tabTable.data[sourceName].findIndex(
          (cur) => cur.accountingItemCode === item.accountingItemCode
        )
        code = `${+item.accountingItemCode + 1}`
      }
      const item = {
        accountingItemCode: code,
        accountingItemType: row.accountingItemType,
        budgetAmount: !curBrother?.length ? row.budgetAmount : 0,
        lastYearBudgetAmount: !curBrother?.length
          ? row.lastYearBudgetAmount
          : 0,
        lastYearBalAmount: !curBrother?.length ? row.lastYearBalAmount : 0,
        accountingItemTitle: '',
        leafItem: 1,
        budgetPrjType: 2,
        parentAccountingItemCode: row.accountingItemCode,
        accountingItemParentId: row.accountingItemId,
        accountingItemId: row.accountingItemId,
        accountingItemLevel: row.accountingItemLevel + 1,
        remark: !curBrother?.length ? row.remark : '',
        projectBudget: row.projectBudget ?? 0
      }
      // leafItem 0有子节点 1没有子节点 2项目
      if (sourceName === 'expendItems') {
        item.extExp = 0
      }
      if (row.budgetPrjType === 3) {
        this.$set(this.tabTable.data[sourceName][index], 'budgetPrjType', 1)
        this.$set(this.tabTable.data[sourceName][index], 'projectBudget', 0)
      }
      this.tabTable.data[sourceName].splice(addIndex + 1, 0, item)
    },
    // 修改选中tab
    handleActiveTab(val) {
      this.activeTab = val
    },
    // 关闭抽屉清空数据
    closed() {
      this.tabTable.data = {
        nonProfitAndLossItems: [], // 非损益类明细科目
        incomeItems: [], // 年度预算收入
        expendItems: [] // 年度预算支出
      }
      this.form = {
        id: '',
        budgetTotalIncome: 0,
        budgetTotalExpend: 0,
        budgetDifference: 0
      }
      this.tabMap = _.cloneDeep(tabMap)
      this.$refs?._seniorTabRef?.resetActiveTab()
      this.activeTab = 'incomeItems'
    },
    // 保存
    handleSure() {
      this.isTemp = 1
      // const list = this.tabTable.data?.nonProfitAndLossItems
      // if (list?.length) {
      //   for (let i = 0; i < list.length; i++) {
      //     const row = list[i]
      //     if (!row.accountingItemTitle) {
      //       return this.$message.error(
      //         `【非损益类预算明细】第${i + 1}行 会计科目 不能为空`
      //       )
      //     }
      //   }
      // }
      this.$refs._formSubmitRef?.validate((valid) => {
        if (!valid) return
        this.$refs._seniorTabRef.rulePass()
      })
    },
    // 暂存
    handleSure2() {
      this.isTemp = 0
      // const list = this.tabTable.data?.nonProfitAndLossItems
      // if (list?.length) {
      //   for (let i = 0; i < list.length; i++) {
      //     const row = list[i]
      //     if (!row.accountingItemCode) {
      //       return this.$message.error(
      //         `【非损益类预算明细】第${i + 1}行 会计科目 不能为空`
      //       )
      //     }
      //   }
      // }
      this.$refs._formSubmitRef?.validate((valid) => {
        if (!valid) return
        this.$refs._seniorTabRef.rulePass()
      })
    },
    // 保存提交
    handleSubmitSave() {
      const {
        incomeItems,
        expendItems,
        rentalIncomes,
        adjExpContracts,
        salaryBudgets,
        nonProfitAndLossItems
      } = this.tabTable.data
      const {
        id,
        batchId,
        budgetSettingId,
        projectName,
        budgetCode,
        budgetYear,
        unitName,
        budgetTotalIncome,
        budgetTotalExpend,
        budgetDifference,
        budgetTotalExtExp,
        budgetTotalSalary,
        budgetControl,
        budgetThreshold,
        isDeficit,
        votePass,
        annualBalance,
        isAnnualBalance
      } = this.form
      const params = {
        id,
        votePass,
        budgetSettingId,
        projectName,
        budgetCode,
        budgetYear,
        unitName,
        budgetTotalIncome,
        budgetTotalExpend,
        budgetDifference,
        budgetTotalExtExp,
        budgetTotalSalary,
        budgetControl,
        budgetThreshold,
        isDeficit,
        annualBalance,
        isAnnualBalance,
        rentalIncomes: rentalIncomes.map((cur) => {
          const {
            contractId,
            contractCode,
            contractName,
            secondName,
            startDate,
            endDate,
            totalMoney,
            rentTotalMoney,
            rentYearMoney
          } = cur
          const items = []
          for (const key in cur) {
            if (key.startsWith('accountingItemCode_')) {
              const rowArr = key.split('_')
              items.push({
                accountingItemCode: +rowArr[1],
                accountingItemTitle: rowArr[2],
                value: cur[key]
              })
            }
          }
          return {
            id: cur.id,
            budgetPlaitId: id,
            contractId,
            contractCode,
            contractName,
            secondName,
            startDate,
            endDate,
            totalMoney,
            rentTotalMoney,
            rentYearMoney,
            items
          }
        }),
        adjExpContracts,
        salaryBudgets: salaryBudgets.map((cur) => {
          const {
            level,
            postName,
            accountintItemCode,
            totalSalary,
            baseSalary,
            accruedSalary,
            performanceAward,
            otherSalary,
            tranCommSubsidy,
            holidaySubsidy,
            overtimeDutySubsidy,
            postSubsidy,
            annualLeaveSubsidy,
            priceSubsidy,
            highTemperatureSubsidy,
            senioritySubsidy,
            retirementSubsidy,
            otherSubsidy,
            accountingItemId,
            remark
          } = cur
          return {
            level,
            postName,
            totalSalary,
            baseSalary,
            accruedSalary,
            performanceAward,
            otherSalary,
            tranCommSubsidy,
            holidaySubsidy,
            overtimeDutySubsidy,
            postSubsidy,
            annualLeaveSubsidy,
            priceSubsidy,
            highTemperatureSubsidy,
            senioritySubsidy,
            retirementSubsidy,
            otherSubsidy,
            budgetPlaitId: id,
            accountingItemId,
            accountintItemCode:
              accountintItemCode[accountintItemCode.length - 1],
            accountintItemTitle: accountintItemCode
              ? expendItems.find(
                  (cur) => cur.accountingItemCode === accountintItemCode
                )?.accountingItemPathTitle
              : '',
            remark
          }
        }),
        nonProfitAndLossItems: nonProfitAndLossItems?.map((cur) => {
          delete cur.isTemp
          return { ...cur, extExp: 0 }
        }),
        incomeItems: incomeItems?.map((cur) => {
          delete cur.isTemp
          return {
            ...cur,
            extExp: cur.extExp ?? 0,
            budgetAmount: cur.budgetAmount ?? 0
          }
        }),
        expendItems: expendItems.map((cur) => {
          delete cur.isTemp
          return {
            ...cur,
            extExp: cur.extExp ?? 0,
            budgetAmount: cur.budgetAmount ?? 0
          }
        }),
        orgId: this.books.orgId,
        booksId: this.books.id,
        batchId,
        isTemp: this.isTemp
      }
      this.isTemp ? (this.saveLoading = true) : (this.saveTimeLoading = true)
      save(params)
        .then((res) => {
          if (res.returnCode === '0') {
            this.$message.success(this.$t(res.message))
            this.dialogVisible = false
            this.$parent.getList()
          } else {
            this.$message.warning(this.$t(res.message))
          }
        })
        .finally(() => {
          this.isTemp
            ? (this.saveLoading = false)
            : (this.saveTimeLoading = false)
        })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .import-upload .el-upload {
  width: 100%;
  margin-bottom: 20px;
  .el-upload-dragger {
    width: 100%;
  }
}
::v-deep .el-input-number,
.el-input-number--mini {
  width: 100% !important;
}
::v-deep.append-ratio {
  position: absolute;
  right: 5px;
}
::v-deep .gever-title {
  display: flex;
  justify-content: space-between;
}
.approval {
  margin-bottom: 280px;
}
</style>
