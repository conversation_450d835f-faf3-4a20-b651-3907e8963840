<template>
  <div>
    <fold-box right-title="乡村振兴设置">
      <template #right>
        <div class="right-box">
          <el-form inline :model="queryParams">
            <el-form-item :label="$t('申请编号')">
              <el-input
                v-model="queryParams.serialNumber"
                class="w150"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('资金用途')">
              <el-input v-model="queryParams.fundsUse" class="w150" clearable />
            </el-form-item>
            <el-form-item :label="$t('申请详情')">
              <el-input v-model="queryParams.details" class="w150" clearable />
            </el-form-item>
            <el-form-item :label="$t('支出类型')">
              <gever-select
                v-model="queryParams.expenditureType"
                clearable
                class="w150"
                :options="expenditureTypeOptions"
              />
            </el-form-item>
            <el-form-item :label="$t('收款方')">
              <el-input
                v-model="queryParams.receiverAccount"
                class="w150"
                clearable
              />
            </el-form-item>
            <!--            <el-form-item :label="$t('审批状态')">
              <el-select
                v-model="queryParams.approvalStatus"
                clearable
                class="w150"
              >
                <el-option
                  v-for="item in approvalstatusOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>-->
            <el-form-item :label="$t('支付状态')">
              <el-select
                v-model="queryParams.paymentState"
                clearable
                class="w150"
              >
                <el-option
                  v-for="item in paymentStateOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('乡村振兴类别')" prop="revitalizationType">
              <gever-select
                v-model="queryParams.revitalizationType"
                :options="revitalizationTypeOptions"
                clearable
                class="w150"
              />
            </el-form-item>
            <el-form-item :label="$t('资金来源')">
              <gever-select
                v-model="queryParams.fundSrc"
                clearable
                class="w150"
                :options="fundSrcOptions"
              />
            </el-form-item>
<!--            <el-form-item :label="$t('资金归属单位')">
              <el-select v-model="queryParams.revitalizationOrgCode" class="w150w" clearable filterable >
                <el-option
                  v-for="d in searchRevitalizationOrgOptions"
                  :key="d.code"
                  :label="d.fname"
                  :value="d.code"
                />
              </el-select>
            </el-form-item>-->
            <el-form-item :label="$t('付款账号')">
              <el-input v-model="queryParams.payAccountNumber" class="w150"/>
            </el-form-item>
            <el-form-item :label="$t('锁定状态')">
              <gever-select
                v-model="queryParams.revitalizationLock"
                clearable
                class="w150"
                :options="lockOptions"
              />
            </el-form-item>
            <el-form-item :label="$t('申请时间')" prop="startCreationTime">
              <el-date-picker
                v-model="queryParams.startCreationTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
                class="w150"
                :picker-options="{ disabledDate: disabledCreationTimeStart }"
              />
              {{ $t('至') }}
              <el-date-picker
                v-model="queryParams.endCreationTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
                class="w150"
                :picker-options="{ disabledDate: disabledCreationTimeEnd }"
              />
            </el-form-item>
            <el-form-item :label="$t('支付时间')" prop="startActualPayTime">
              <el-date-picker
                v-model="queryParams.startActualPayTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
                class="w150"
                :picker-options="{ disabledDate: disabledActualPayTimeStart }"
              />
              {{ $t('至') }}
              <el-date-picker
                v-model="queryParams.endActualPayTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
                class="w150"
                :picker-options="{ disabledDate: disabledActualPayTimeEnd }"
              />
            </el-form-item>
            <el-form-item :label="$t('出纳时间')" prop="startCashierDate">
              <el-date-picker
                v-model="queryParams.startCashierDate"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
                class="w150"
                :picker-options="{ disabledDate: disabledCashierDateStart }"
              />
              {{ $t('至') }}
              <el-date-picker
                v-model="queryParams.endCashierDate"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="date"
                :placeholder="$t('选择日期')"
                class="w150"
                :picker-options="{ disabledDate: disabledCashierDateEnd }"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                round
                plain
                type="primary"
                icon="el-icon-search"
                @click="getList"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
          </el-form>
          <div style="float: right;padding-bottom: 5px;">
            <el-button
              v-hasPermi="
                'financial.operationsMaintenanceFund.revitalizationSet.set'
              "
              round
              type="primary"
              icon="el-icon-edit"
              @click="handleOpenRevitalizationSet(1)"
            >
              {{ $t('设置乡村振兴') }}
            </el-button>
            <el-button
              v-hasPermi="
                'financial.operationsMaintenanceFund.revitalizationSet.orgSet'
              "
              round
              type="primary"
              icon="el-icon-edit"
              @click="handleOpenRevitalizationSet(3)"
            >
              {{ $t('设置资金归属单位') }}
            </el-button>
            <el-button
              v-hasPermi="
                'financial.operationsMaintenanceFund.revitalizationSet.amountSet'
              "
              round
              type="primary"
              icon="el-icon-edit"
              @click="handleOpenRevitalizationSet(2)"
            >
              {{ $t('录入乡村振兴金额') }}
            </el-button>
            <el-button
              v-hasPermi="
                'financial.operationsMaintenanceFund.revitalizationSet.lock'
              "
              round
              type="primary"
              icon="el-icon-lock"
              @click="handleLock"
            >
              {{ $t('锁定') }}
            </el-button>
            <el-button
              v-hasPermi="
                'financial.operationsMaintenanceFund.revitalizationSet.unlock'
              "
              round
              type="primary"
              icon="el-icon-unlock"
              @click="handleUnlock"
            >
              {{ $t('解锁') }}
            </el-button>
            <el-button
              v-hasPermi="
                'financial.operationsMaintenanceFund.revitalizationSet.lockAll'
              "
              round
              type="primary"
              icon="el-icon-lock"
              @click="openLockView(1)"
            >
              {{ $t('锁定所有') }}
            </el-button>
            <el-button
              v-hasPermi="
                'financial.operationsMaintenanceFund.revitalizationSet.unlockAll'
              "
              round
              type="primary"
              icon="el-icon-unlock"
              @click="openLockView(0)"
            >
              {{ $t('解锁所有') }}
            </el-button>
            <el-button
              plain
              round
              icon="el-icon-download"
              @click="handleExport"
            >
              {{ $t('导出乡村振兴') }}
            </el-button>
          </div>
          <gever-table
            ref="_geverTableRef"
            v-loading="tableLoading"
            :columns="tableColumns"
            :data="tableList"
            pagination
            :total="total"
            :pagi="queryParams"
            @p-current-change="getList"
            @size-change="getList"
          >
            <template #performance="{ row }">
              {{ row.projectPerformance ? '是' : '否' }}
            </template>
            <template #expenditureType="{ row }">
              {{
                getExpenditureTypeText(
                  row.expenditureType,
                  'expenditureTypeOptions'
                )
              }}
            </template>
            <template #approvalStatus="{ row }">
              {{ convertApprovalStatus(row.approvalStatus) }}
            </template>
            <template #fundSrc="{ row }">
              {{ convertFundSrc(row.fundSrc) }}
            </template>
            <template #revitalizationType="{ row }">
              {{ convertRevitalizationType(row.revitalizationType) }}
            </template>
            <template #revitalizationLock="{ row }">
              {{ convertLockState(row.revitalizationLock) }}
            </template>
            <template #payStatus="{ row }">
              {{ convertPaymentState(row.payStatus) }}
            </template>
            <template #operation="{ row }">
              <el-button v-has-permi="'financial.operationsMaintenanceFund.revitalizationSet.view'" type="text" @click="handleViewApplicationForm(row)">
                {{ $t('查看申请单') }}
              </el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <!-- 设置乡村振兴 -->
    <gever-dialog
      v-loading="loading"
      :visible.sync="revitalizationTypeVisible"
      :title="isSetAmount ? '录入乡村振兴金额' : '设置乡村振兴'"
      :perch-height="300"
      width="35%"
      :buttons="[
        {
          type: '',
          text: '关 闭',
          buttonStatus: true,
          callback: () => {
            revitalizationTypeVisible = false
          }
        },
        {
          text: '确定',
          type: 'primary',
          buttonStatus: true,
          disabled: loading === true,
          callback: handlerSave
        }
      ]"
      @close="revitalizationTypeClose"
    >
      <el-form ref="_revitalizationTypeFormRef" inline class="gever-form" label-width="120px" :model="revitalizationTypeFrom">
        <div class="form-sg">
          <el-form-item v-if="isSetAmount" prop="orgName" label="申请单位：  ">
            <el-input v-model="revitalizationTypeFrom.orgName" disabled />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item v-if="isSetAmount" prop="serialNumber" label="申请编号：  ">
            <el-input v-model="revitalizationTypeFrom.serialNumber" disabled />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item v-if="isSetAmount" style="width: 100%" prop="fundsUse" label="资金用途：  ">
            <el-input v-model="revitalizationTypeFrom.fundsUse" disabled type="textarea" />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item v-if="isSetAmount" style="width: 100%" prop="amountPayable" label="收款方金额： ">
            <gever-input-number v-model="revitalizationTypeFrom.amountPayable" :controls="false" :precision="2" disabled />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item v-if="isSetAmount" style="width: 100%" prop="receiverAccount" label="收款方名称： ">
            <el-input v-model="revitalizationTypeFrom.receiverAccount" disabled />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item v-if="!isSetOrg" style="width: 100%" prop="revitalizationType" label="乡村振兴类别：">
            <gever-select
              v-model="revitalizationTypeFrom.revitalizationType"
              path="/payment/revitalization_type/"
              number-key
              :clearable="true"
            />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item v-if="isSetAmount" style="width: 100%" prop="revitalizationAmount" label="乡村振兴金额：">
            <gever-input-number v-model="revitalizationTypeFrom.revitalizationAmount" :controls="false" :precision="2" :min="0.01" :max="revitalizationTypeFrom.amountPayable" />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item v-if="isSetAmount || isSetOrg" style="width: 100%" prop="revitalizationOrgCode" label="资金归属单位：">
            <el-select v-model="revitalizationTypeFrom.revitalizationOrgCode" :disabled="isSetAmount" class="w100w" clearable filterable >
              <el-option
                v-for="d in curSetRevitalizationOrgOptions"
                :key="d.code"
                :label="d.fname"
                :value="d.code"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </gever-dialog>

    <!-- 锁定乡村振兴 -->
    <gever-dialog
      v-loading="loading"
      :visible.sync="lockVisible"
      :title="lockType == 1 ? '锁定乡村振兴' : '解锁乡村振兴'"
      :perch-height="300"
      width="35%"
      :buttons="[
        {
          type: '',
          text: '关 闭',
          buttonStatus: true,
          callback: () => {
            lockVisible = false
          }
        },
        {
          text: '确定',
          type: 'primary',
          buttonStatus: true,
          disabled: loading === true,
          callback: handleBatchLock
        }
      ]"
    >
      <el-form ref="_lockFormRef" inline class="gever-form" label-width="120px" :model="lockForm">
        <div class="form-sg">
          <el-form-item prop="areaCode" label="所属镇街：  ">
            <el-select v-model="lockForm.areaCode" class="w100w" clearable filterable @change="handleChangeTown">
              <el-option
                v-for="d in townOptions"
                :key="d.id"
                :label="d.text"
                :value="d.id"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item prop="orgName" label="机构：  ">
            <gever-input v-model="lockForm.dataOrgName" :readonly="true" placeholder="请选择">
              <template slot="append">
                <el-button type="primary" @click="dataOrgClick">{{ $t('选择') }}</el-button>
              </template>
            </gever-input>
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item style="width: 100%" prop="startDate" label="支出申请时间：  ">
            <el-date-picker
              v-model="lockForm.startDate"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="date"
              :placeholder="$t('选择日期')"
              class="w150"
              :picker-options="{ disabledDate: disabledLockDateStart }"
            />
            {{ $t('至') }}
            <el-date-picker
              v-model="lockForm.endDate"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="date"
              :placeholder="$t('选择日期')"
              class="w150"
              :picker-options="{ disabledDate: disabledLockDateEnd }"
            />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item style="width: 100%" prop="accountType" label="付款账户类型： ">
            <gever-select
              v-model="lockForm.accountType"
              multiple
              :options="accountTypeOptions"
            />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item style="width: 100%" label="支付状态： ">
            <el-select
              v-model="lockForm.paymentState"
              clearable
              disabled
            >
              <el-option
                v-for="item in paymentStateOptions"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </gever-dialog>

    <el-dialog title="机构选择" :visible.sync="orgVisible" width="40%">
      <div style="height:400px">
        <el-tree
          ref="_tree"
          :data="treeData"
          show-checkbox
          node-key="code"
          :default-checked-keys="dataOrg"
          :props="{
            children: 'children',
            label: 'text'
          }"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="orgVisible = false">取 消</el-button>
        <el-button type="primary" @click="orgDataComit">确 定</el-button>
      </span>
    </el-dialog>

    <public-drawer
      ref="_appDrawerRef"
      v-loading="loading"
      :visible.sync="applicationDrawerVisible"
      :title="drawerTitle"
      :size="70"
      @close="handleDrawerClose"
    >
      <application
        v-if="applicationDrawerVisible"
        :id="currentRow.id || ''"
        ref="_appRef"
        :project-performance="projectPerformance"
        :view-state="viewState"
        :application="currentApplication"
        :receiver="receiverList"
        :load-type="loadType"
        :info-type="infoType"
        :show-process-btn="showProcessBtn"
        :accounting-item-id="current.id || ''"
        :visible.sync="applicationDrawerVisible"
        @loading="handleLoading"
        @Invalid="Invalid"
      />
    </public-drawer>
  </div>
</template>

<script>
import Application from '@/views/payment/approval/approvalApplication/applicationInfo.vue'
import { getApplication, setRevitalizationType, setRevitalizationAmount, pageRevitalization, getOrgByAreaCode,
  setRevitalizationOrg, lockRevitalization, unlockRevitalization, lockAllRevitalization, unlockAllRevitalization, getAuthTown } from '@/api/payment/approval.js'
import { paymentExpenditureTypeTree } from '@/api/payment/expenseType'
import { comboJson, loginAreaOrg } from '@/api/gever/common.js'
import { getToken } from '@/utils/auth'
import moment from 'moment'
import {
  authTreeData
} from '@/api/system/organization.js'

export default {
  components: {
    Application
  },
  props: {},
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      projectPerformance: '',
      rowId: '',
      rowData: {},
      infoType: false,
      current: {}, // 当前选择的科目数据
      showProcessBtn: true,
      performanceVisible: false,
      tableLoading: false,
      rowIdArr: [],
      approvalstatusOptions: [{ id: '', text: this.$t('全部') }],
      fundSrcOptions: [{ id: '', text: this.$t('全部') }],
      queryParams: {
        page: 1,
        rows: 20,
        orgCode: '',
        serialNumber: '',
        fundsUse: '',
        receiverAccount: '',
        revitalizationType: '',
        approvalStatus: '',
        paymentState: '2',
        startCreationTime: moment(new Date()).format('yyyy') + '-01-01',
        endCreationTime: moment(new Date()).format('yyyy') + '-12-31',
        fundSrc: '',
        startActualPayTime: '',
        endActualPayTime: '',
        startCashierDate: '',
        endCashierDate: '',
        revitalizationLock: ''
      },
      performanceOptions: [
        { id: '0', text: '否' },
        { id: '1', text: '是' }
      ],
      tableList: [],
      total: 0,
      tableColumns: [
        { type: 'selection', align: 'center', width: '50', fixed: 'left' },
        { type: 'index', label: '序号', width: '80', fixed: 'left' },
        {
          label: '资金用途',
          prop: 'fundsUse',
          width: '150',
          showOverflowTooltip: true,
          fixed: 'left'
        },
        {
          label: '申请详情',
          prop: 'details',
          width: '150',
          showOverflowTooltip: true,
          fixed: 'left'
        },
        {
          label: '申请金额（元）',
          prop: 'amount',
          width: '150',
          showOverflowTooltip: true,
          filter: 'money',
          fixed: 'left'
        },
        {
          label: '收款方',
          prop: 'receiverAccount',
          width: '150',
          showOverflowTooltip: true,
          fixed: 'left'
        },
        {
          label: '收款方金额',
          prop: 'amountPayable',
          width: '120',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '乡村振兴类别',
          prop: 'revitalizationType',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '乡村振兴金额',
          prop: 'revitalizationAmount',
          width: '120',
          showOverflowTooltip: true,
          filter: 'money'
        },
        {
          label: '锁定状态',
          prop: 'revitalizationLock',
          width: '120',
          showOverflowTooltip: true
        },
        {
          label: '申请单位',
          prop: 'orgName',
          minWidth: '200',
          showOverflowTooltip: true
        },
        {
          label: '资金归属单位',
          prop: 'revitalizationOrgName',
          minWidth: '200',
          showOverflowTooltip: true
        },
        {
          label: '申请编号',
          prop: 'serialNumber',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '申请时间',
          prop: 'creationTime',
          width: '150',
          filter: 'date',
          showOverflowTooltip: true
        },
        {
          label: '支付时间',
          prop: 'actualPayTime',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '出纳日期',
          prop: 'cashierDate',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '资金来源',
          prop: 'fundSrc',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '支出类型',
          prop: 'expenditureType',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '付款账号',
          prop: 'payAccountNumber',
          width: '250',
          showOverflowTooltip: true
        },
        {
          label: '收款方备注',
          prop: 'remark',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '审批状态',
          prop: 'approvalStatus',
          width: '150',
          showOverflowTooltip: true
        },
        {
          label: '支付状态',
          prop: 'payStatus',
          width: '150',
          showOverflowTooltip: true
        },
        { label: '操作', slotName: 'operation', width: '150', fixed: 'right' }
      ],
      loadType: '',
      drawerTitle: '',
      loading: false,
      currentRow: {},
      viewState: 0,
      applicationDrawerVisible: false,
      currentApplication: {},
      receiverList: [],
      revitalizationTypeOptions: [{ id: '', text: this.$t('全部') }, { id: '-1', text: this.$t('未设置') }, { id: '0', text: this.$t('已设置') }],
      expenditureTypeOptions: [{ id: '', text: this.$t('全部') }],
      revitalizationTypeVisible: false,
      revitalizationTypeFrom: {},
      paymentStateOptions: [
        { id: '', text: this.$t('全部') },
        { id: '0', text: this.$t('未支付') },
        { id: '1', text: this.$t('支付中') },
        { id: '2', text: this.$t('支付成功') },
        { id: '5', text: this.$t('退款成功') },
        { id: '-1', text: this.$t('支付失败') },
        { id: '-9', text: this.$t('已退回') },
        { id: '-10', text: this.$t('交易终止') },
        { id: '99', text: this.$t('状态未知') }
      ],
      isSetAmount: false,
      curSetRevitalizationOrgOptions: [],
      searchRevitalizationOrgOptions: [],
      isSetOrg: false,
      lockVisible: false,
      lockType: 1,
      lockForm: {},
      accountTypeOptions: [{ id: '0', text: this.$t('现金') }],
      townOptions: [],
      orgVisible: false,
      dataOrg: [],
      treeData: [],
      lockOptions: [
        { id: '', text: this.$t('全部') },
        { id: '0', text: this.$t('未锁定') },
        { id: '1', text: this.$t('已锁定') }
      ]
    }
  },
  computed: {
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  watch: {
    paymentLogin: {
      /** 监听到变化后就触发一次handler 相当与created */
      handler(val) {
        this.getOrgOptionsForSearch()
        if (val.type !== 'Organization') {
          if (val.id) {
            this.queryParams.areaCode = val.code
            this.queryParams.orgCode = ''
            this.getList()
          }
        } else {
          if (val.id) {
            this.queryParams.areaCode = ''
            this.queryParams.orgCode = val.code
            this.getList()
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.loadAllJson()
  },
  methods: {
    getToken,
    async getList() {
      if (this.paymentLogin.type == 'Area' && this.paymentLogin.areaLevel <= 3) {
        return this.$message.warning(this.$t('只能选择镇、村或机构进行查询!'))
      }
      this.tableLoading = true
      const { data } = await pageRevitalization(this.queryParams)
      this.tableLoading = false
      this.tableList = data.rows
      this.total = data.total
    },
    // 查看申请单
    handleViewApplicationForm(row) {
      this.loadType = 'view'
      this.loading = true
      this.drawerTitle = row.returnFlag ? '查看申请退款' : '查看支出申请'
      this.currentRow = row
      this.viewState = row.approvalStatus
      this.applicationDrawerVisible = true
      getApplication(row.approvalApplicationId)
        .then((res) => {
          this.currentApplication = res.data[0]
          this.receiverList = res.data[1]
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleLoading(flag) {
      this.loading = flag
    },
    Invalid(val) {
      this.invalidLoading = false
    },
    handleDrawerClose() {
      this.currentApplication = {}
      this.showProcessBtn = true
    },
    getExpenditureTypeText(val, options) {
      const valArr = val.split(',')
      const textArr = []
      this[options].forEach((item) => {
        if (valArr.includes(item.id)) {
          textArr.push(item.text)
        }
      })
      return textArr.join(',')
    },
    convertApprovalStatus(val) {
      const status = this.approvalstatusOptions.find(
        (item) => item.id.toString() === val.toString()
      )
      return status ? status.text : ''
    },
    convertFundSrc(val) {
      const fundSrc = this.fundSrcOptions.find(
        (item) => item.id === val
      )
      return fundSrc ? fundSrc.text : ''
    },
    convertPaymentState(val) {
      const status = this.paymentStateOptions.find(
        (item) => item.id === val + ''
      )
      return status ? status.text : ''
    },
    convertRevitalizationType(val) {
      const revitalizationType = this.revitalizationTypeOptions.find(
        (item) => item.id === val + ''
      )
      return revitalizationType ? revitalizationType.text : '/'
    },
    convertLockState(val) {
      const status = this.lockOptions.find(
        (item) => item.id === val + ''
      )
      return status ? status.text : ''
    },
    revitalizationTypeClose() {
      this.revitalizationTypeVisible = false
    },
    handleOpenRevitalizationSet(setType) {
      // 1 设置  2 设置金额  3设置单位
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (!selectedRows || selectedRows.length < 1) {
        return this.$message.warning(this.$t('请先选择数据!'))
      }
      this.isSetAmount = false
      this.isSetOrg = false
      if (setType == 2) {
        if (selectedRows.length != 1) {
          return this.$message.warning(this.$t('只能选一条数据进行录入!'))
        }
        this.isSetAmount = true
      }
      if (setType == 3) {
        this.isSetOrg = true
      }
      let hasSpecialFunds = false
      let isSpecialPay = false
      for (let i = 0; i < selectedRows.length; i++) {
        const item = selectedRows[i]
        if (item.revitalizationLock == 1) {
          return this.$message.warning(this.$t('支出已被锁定，不能进行乡村振兴相关设置!'))
        }
        if (item.revitalizationType) {
          continue
        }
        if (item.fundSrc == 2) {
          hasSpecialFunds = true
        }
        if (item.expenditureType == '844340E63C6D11B28DE7438D255A3F76') {
          isSpecialPay = true
        }
      }
      if (hasSpecialFunds || isSpecialPay) {
         this.$confirm(this.$t(`所选数据含有支出类型为"专项支出"或资金来源为"专项资金"的支出，是否继续设置?`), {
          type: 'warning'
        }).then(async () => {
          if (this.isSetAmount) {
            this.revitalizationAmountOpen(selectedRows[0])
          } else if (this.isSetOrg) {
            this.revitalizationOrgOpen()
          } else {
            this.revitalizationTypeOpen()
          }
        })
      } else {
        if (this.isSetAmount) {
          this.revitalizationAmountOpen(selectedRows[0])
        } else if (this.isSetOrg) {
          this.revitalizationOrgOpen()
        } else {
          this.revitalizationTypeOpen()
        }
      }
    },
    revitalizationTypeOpen() {
      this.revitalizationTypeFrom = {}
      this.$set(this.revitalizationTypeFrom, 'revitalizationType', '')
      // this.$set(this.revitalizationTypeFrom, 'revitalizationType', selectedRows[0].revitalizationType ? selectedRows[0].revitalizationType : '')
      this.revitalizationTypeVisible = true
    },
    revitalizationOrgOpen() {
      if (this.paymentLogin.type == 'Area' && this.paymentLogin.areaLevel <= 4) {
        return this.$message.warning(this.$t('只能选择村或机构进行操作!'))
      }
      this.revitalizationTypeFrom = {}
      this.$set(this.revitalizationTypeFrom, 'revitalizationOrgCode', '')
      this.isSetOrg = true
      this.getOrgOptionsForBatchSet()
      this.revitalizationTypeVisible = true
    },
    revitalizationAmountOpen(rowData) {
      this.revitalizationTypeFrom = JSON.parse(JSON.stringify(rowData))
      this.$set(this.revitalizationTypeFrom, 'revitalizationType', rowData.revitalizationType)
      this.$set(this.revitalizationTypeFrom, 'revitalizationAmount', !rowData.revitalizationAmount || rowData.revitalizationAmount == 0 ? rowData.amountPayable : rowData.revitalizationAmount)
      this.$set(this.revitalizationTypeFrom, 'revitalizationOrgCode', rowData.revitalizationOrgCode ? rowData.revitalizationOrgCode : rowData.orgCode)
      this.getOrgOptionsForSet(rowData.areaCode)
      this.revitalizationTypeVisible = true
    },
    handlerSave() {
      if (this.isSetAmount) {
        this.saveRevitalizationAmount()
      } else if (this.isSetOrg) {
        this.saveRevitalizationOrg()
      } else {
        this.saveRevitalizationType()
      }
    },
    saveRevitalizationType() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (!selectedRows || selectedRows.length < 1) {
        return this.$message.warning(this.$t('请选择一条数据!'))
      }
      const ids = selectedRows.map(item => item.id)
      this.loading = true
      setRevitalizationType({ ids: ids.join(','), revitalizationType: this.revitalizationTypeFrom.revitalizationType }).then(res => {
        if (res.returnCode === '0') {
          this.$message.success(res.message)
          this.$emit('refreshTable')
          this.$emit('update:revitalizationTypeVisible', false)
          this.revitalizationTypeVisible = false
          this.revitalizationTypeFrom = {}
          this.getList()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    saveRevitalizationOrg() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (!selectedRows || selectedRows.length < 1) {
        return this.$message.warning(this.$t('请选择至少一条数据!'))
      }
      const ids = selectedRows.map(item => item.id)
      this.loading = true
      setRevitalizationOrg({ ids: ids.join(','), revitalizationOrgCode: this.revitalizationTypeFrom.revitalizationOrgCode }).then(res => {
        if (res.returnCode === '0') {
          this.$message.success(res.message)
          this.$emit('refreshTable')
          this.$emit('update:revitalizationTypeVisible', false)
          this.revitalizationTypeVisible = false
          this.revitalizationTypeFrom = {}
          this.getList()
        }
      }).finally(() => {
        this.isSetOrg = false
        this.loading = false
      })
    },
    saveRevitalizationAmount() {
      const selectedRows =
        this.$refs['_geverTableRef'].$refs['elTable'].selection
      if (!selectedRows || selectedRows.length < 1) {
        return this.$message.warning(this.$t('请选择一条数据!'))
      }
      if (selectedRows.length > 1) {
        return this.$message.warning(this.$t('只能选一条数据进行录入!'))
      }
      if (!this.revitalizationTypeFrom.revitalizationType) {
        return this.$message.warning(this.$t('请选择乡村振兴类型!'))
      }
      if (!this.revitalizationTypeFrom.revitalizationAmount || this.revitalizationTypeFrom.revitalizationAmount == 0) {
        return this.$message.warning(this.$t('乡村振兴金额不能为空或等于0!'))
      }
      if (this.revitalizationTypeFrom.revitalizationAmount > selectedRows[0].amountPayable) {
        return this.$message.warning(this.$t('乡村振兴金额不能大于收款方金额!'))
      }
      if (!this.revitalizationTypeFrom.revitalizationOrgCode) {
        return this.$message.warning(this.$t('请选择资金归属单位!'))
      }
      this.loading = true
      setRevitalizationAmount({
        id: selectedRows[0].id,
        revitalizationType: this.revitalizationTypeFrom.revitalizationType,
        revitalizationAmount: this.revitalizationTypeFrom.revitalizationAmount,
        revitalizationOrgCode: this.revitalizationTypeFrom.revitalizationOrgCode
      }).then(res => {
        if (res.returnCode === '0') {
          this.$message.success(res.message)
          this.$emit('refreshTable')
          this.$emit('update:revitalizationTypeVisible', false)
          this.revitalizationTypeVisible = false
          this.revitalizationTypeFrom = {}
          this.getList()
        }
      }).finally(() => {
        this.isSetAmount = false
        this.loading = false
      })
    },
    formatRevitalizationType(row) {
      return row.revitalizationType ? row.revitalizationType : '/'
    },
    openLockView(lockType) {
      this.lockForm = {
        startDate: moment().startOf('month').format('YYYY-MM-DD'),
        endDate: moment().endOf('month').format('YYYY-MM-DD'),
        accountType: ['0', '1', '2', '4', '7', '8', '9'],
        paymentState: '2'
      }
      this.$set(this.lockForm, 'lockType', lockType)
      this.$set(this.lockForm, 'dataOrgName', '')
      this.dataOrg = []
      this.treeData = []
      this.getTownList()
      this.lockType = lockType
      this.lockVisible = true
    },
    handleBatchLock() {
      if (!this.lockForm.startDate) {
        return this.$message.warning(this.$t('请选择开始时间!'))
      }
      if (!this.lockForm.endDate) {
        return this.$message.warning(this.$t('请选择结束时间!'))
      }
      if (this.lockForm.startDate > this.lockForm.endDate) {
        return this.$message.warning(this.$t('开始时间不能大于结束时间!'))
      }
      if (!this.lockForm.accountType) {
        return this.$message.warning(this.$t('请选择账户类型!'))
      }
      if (!this.lockForm.paymentState) {
        return this.$message.warning(this.$t('请选择付款状态!'))
      }
      if (this.lockType == '1') {
        lockAllRevitalization({
          lockType: this.lockType,
          startDate: this.lockForm.startDate,
          endDate: this.lockForm.endDate,
          orgCode: this.dataOrg.join(','),
          accountType: this.lockForm.accountType,
          paymentState: this.lockForm.paymentState
        }).then(res => {
          if (res.returnCode === '0') {
            this.$message.success(res.message)
            this.lockVisible = false
            this.getList()
          }
        })
      } else {
        unlockAllRevitalization({
          lockType: this.lockType,
          startDate: this.lockForm.startDate,
          endDate: this.lockForm.endDate,
          orgCode: this.dataOrg.join(','),
          accountType: this.lockForm.accountType,
          paymentState: this.lockForm.paymentState
        }).then(res => {
          if (res.returnCode === '0') {
            this.$message.success(res.message)
            this.lockVisible = false
            this.getList()
          }
        })
      }
    },
    handleLock(row) {
      const checkData = this.$refs['_geverTableRef'].selection
      if (!row.id) {
        if (!checkData.length) {
          return this.$message.warning('请选择数据！')
        }
      }
      this.$confirm(this.$t(`确定锁定吗?`), {
        type: 'warning'
      }).then(async () => {
        let ids = ''
        if (checkData.length) {
          ids = checkData.map((l) => l.id).join(',')
        }
        const { returnCode } = await lockRevitalization({ id: row.id ? row.id : ids })
        if (returnCode == '0') {
          this.$message.success('操作成功')
          this.getList()
        }
      })
    },
    handleUnlock(row) {
      const checkData = this.$refs['_geverTableRef'].selection
      if (!row.id) {
        if (!checkData.length) {
          return this.$message.warning('请选择数据！')
        }
      }
      this.$confirm(this.$t(`确定解锁吗?`), {
        type: 'warning'
      }).then(async () => {
        let ids = ''
        if (checkData.length) {
          ids = checkData.map((l) => l.id).join(',')
        }
        const { returnCode } = await unlockRevitalization({ id: row.id ? row.id : ids })
        if (returnCode == '0') {
          this.$message.success('操作成功')
          this.getList()
        }
      })
    },
    orgDataComit() {
      const getCheckedNodes = this.$refs._tree.getCheckedNodes()
      this.dataOrg = getCheckedNodes.map(item => item.code)
      this.$set(this.lockForm, 'dataOrgName', '已选择' + this.dataOrg.length + '个机构')
      this.orgVisible = false
    },
    handleExport() {
      if (this.paymentLogin.areaLevel < 4) {
        return this.$message.warning(
          this.$t('地区只能选择镇、村或机构进行导出，不能选择市或区')
        )
      }
      let str = ''
      const obj = { ...this.queryParams }
      delete obj.page
      delete obj.rows
      for (var i = 0; i < Object.keys(obj).length; i++) {
        if (str) {
          str += '&'
        }
        str += Object.keys(obj)[i] + '=' + Object.values(obj)[i]
      }
      const url =
        this.baseUrl +
        `/payment/manager/payOrderPayee/exportRevitalization?` +
        str +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.location.href = url
    },
    getOrgOptionsForBatchSet() {
      this.curSetRevitalizationOrgOptions = []
      if (this.paymentLogin.type == 'Area') {
        this.getOrgOptionsForSet(this.paymentLogin.code)
      } else {
        this.getOrgOptionsForSet(this.paymentLogin.areaCode)
      }
    },
    getOrgOptionsForSet(areaCode) {
      this.curSetRevitalizationOrgOptions = []
      getOrgByAreaCode(areaCode)
        .then((res) => {
          this.curSetRevitalizationOrgOptions = res.data
        })
    },
    getOrgOptionsForSearch() {
      this.searchRevitalizationOrgOptions = []
      if (this.paymentLogin.type == 'Area') {
        getOrgByAreaCode(this.paymentLogin.code)
          .then((res) => {
            this.searchRevitalizationOrgOptions = res.data
          })
      } else {
        const org = { code: this.paymentLogin.code, fname: this.paymentLogin.fname }
        this.searchRevitalizationOrgOptions.push(org)
      }
    },
    getTownList() {
      getAuthTown().then((res) => {
          this.townOptions = res.data
          this.$set(this.lockForm, 'areaCode', this.townOptions[0].id)
          this.treeData = []
          this.treeData.push(this.townOptions[0])
        })
    },
    handleChangeTown(val) {
      this.treeData = []
      this.townOptions.forEach(t => {
        if (val == t.id) {
          this.treeData.push(t)
        }
      })
    },
    dialogClose () {
      this.orgVisible = false
    },
    dataOrgClick () {
      this.dataOrg = []
      this.orgVisible = true
    },
    loadAllJson() {
      comboJson({ path: '/payment/approvalstatus' }).then((res) => {
        this.approvalstatusOptions.push(...res.data)
      })
      comboJson({ path: '/payment/source_of_funds/' }).then((res) => {
        this.fundSrcOptions.push(...res.data)
      })
      comboJson({ path: '/payment/revitalization_type' }).then((res) => {
        this.revitalizationTypeOptions.push(...res.data)
      })
      comboJson({ path: '/financial/bank_account_type/' }).then((res) => {
        this.accountTypeOptions.push(...res.data)
      })
      paymentExpenditureTypeTree({ page: 1, rows: 100 }).then((res) => {
        this.expenditureTypeOptions.push(...res.data.map((cur) => {
          if (cur.id == '0') {
            cur.id = ''
          }
          return cur
        })
        )
      })
    },
    disabledCreationTimeStart(time) {
      if (this.queryParams.endCreationTime) {
        const endDateStartOfDay = moment(this.queryParams.endCreationTime).startOf('day').toDate().getTime()
        const currentTimeStartOfDay = moment(time).startOf('day').toDate().getTime()
        return currentTimeStartOfDay > endDateStartOfDay
      }
      return false
    },
    disabledCreationTimeEnd(time) {
      if (this.queryParams.startCreationTime) {
        const startDateStartOfDay = moment(this.queryParams.startCreationTime).startOf('day').toDate().getTime()
        const currentTimeStartOfDay = moment(time).startOf('day').toDate().getTime()
        return currentTimeStartOfDay < startDateStartOfDay
      }
      return false
    },
    disabledActualPayTimeStart(time) {
      if (this.queryParams.endActualPayTime) {
        const endDateStartOfDay = moment(this.queryParams.endActualPayTime).startOf('day').toDate().getTime()
        const currentTimeStartOfDay = moment(time).startOf('day').toDate().getTime()
        return currentTimeStartOfDay > endDateStartOfDay
      }
      return false
    },
    disabledActualPayTimeEnd(time) {
      if (this.queryParams.startActualPayTime) {
        const startDateStartOfDay = moment(this.queryParams.startActualPayTime).startOf('day').toDate().getTime()
        const currentTimeStartOfDay = moment(time).startOf('day').toDate().getTime()
        return currentTimeStartOfDay < startDateStartOfDay
      }
      return false
    },
    disabledCashierDateStart(time) {
      if (this.queryParams.endCashierDate) {
        const endDateStartOfDay = moment(this.queryParams.endCashierDate).startOf('day').toDate().getTime()
        const currentTimeStartOfDay = moment(time).startOf('day').toDate().getTime()
        return currentTimeStartOfDay > endDateStartOfDay
      }
      return false
    },
    disabledCashierDateEnd(time) {
      if (this.queryParams.startCashierDate) {
        const startDateStartOfDay = moment(this.queryParams.startCashierDate).startOf('day').toDate().getTime()
        const currentTimeStartOfDay = moment(time).startOf('day').toDate().getTime()
        return currentTimeStartOfDay < startDateStartOfDay
      }
      return false
    },
    disabledLockDateStart(time) {
      if (this.lockForm.endDate) {
        const endDate = moment(this.lockForm.endDate)
        const maxStartDate = endDate.subtract(3, 'months').add(1, 'days')
        const currentTime = moment(time)
        return currentTime.isBefore(maxStartDate, 'day') || currentTime.isAfter(moment(this.lockForm.endDate), 'day')
      }
      return false
    },
    disabledLockDateEnd(time) {
      if (this.lockForm.startDate) {
        const startDate = moment(this.lockForm.startDate)
        const maxEndDate = startDate.add(3, 'months').subtract(1, 'days')
        const currentTime = moment(time)
        return currentTime.isAfter(maxEndDate, 'day') || currentTime.isBefore(moment(this.lockForm.startDate), 'day')
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped></style>
