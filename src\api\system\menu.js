import request from '@/utils/request'

// 查询菜单列表
export function listMenu(query) {
  return request({
    url: '/system/menu/list',
    method: 'get',
    params: query,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 查询菜单详细
export function getMenu(menuId) {
  return request({
    url: '/system/menu/' + menuId,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 查询菜单下拉树结构
export function treeselect() {
  return request({
    url: '/system/menu/treeselect',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId) {
  return request({
    url: '/system/menu/roleMenuTreeselect/' + roleId,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/system/menu',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/system/menu',
    method: 'put',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}

// 删除菜单
export function delMenu(menuId) {
  return request({
    url: '/system/menu/' + menuId,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  })
}
