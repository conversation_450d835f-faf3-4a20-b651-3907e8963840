/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-07 11:11:17
 * @LastEditors: Andy
 * @LastEditTime: 2023-09-13 17:01:38
 * @FilePath: \rural-financial\src\api\financial\custom-report\definedReport.js
 */
import request from '@/utils/request'

// 查询报表数据
export function getReportData(id) {
  return request({
    url:`/financial/customReport/customReportRecord/get/${id}`,
    method: 'get',
    withCredentials: true
  })
}

export function queryList(data) {
  return request({
    url:`/financial/customReport/customReportRecord/page`,
    method: 'post',
    data,
    withCredentials: true
  })
}

// 新增
export function insert(data) {
  return request({
    url: '/financial/customReport/customReportRecord/insert',
    method: 'post',
    data,
    withCredentials: true
  })
}

// 详情
export function detail(id) {
  return request({
    url: `/financial/customReport/customReportRecord/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

// 编辑
export function update(data) {
  return request({
    url: `/financial/customReport/customReportRecord/update`,
    method: 'post',
    data,
    withCredentials: true
  })
}

// 删除报表组
export function del(data) {
  return request({
    url: `/financial/customReport/customReportRecord/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}

export function rendering(data) {
  return request({
    url: `/financial/customReport/customReportRecord/rendering`,
    method: 'post',
    data,
    responseType: 'blob',
    withCredentials: true,
    payload: true
  })
}

