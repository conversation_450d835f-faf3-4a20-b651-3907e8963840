<template>
  <div>
    <fold-box
      left-title="银行账户"
      right-title="银行交易账单"
      :call-back="toggleSearch"
    >
      <template #left>
        <el-form v-show="showFilter" inline>
          <el-form-item :label="$t('账号：')">
            <el-input v-model="filterParams.accountNumber" class="w100" />
          </el-form-item>
          <el-form-item :label="$t('名称：')">
            <el-input v-model="filterParams.ownerName" class="w100" />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              plain
              round
              icon="el-icon-search"
              @click="handleSearchList"
            >
              {{ $t('搜索') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="left-subNav" :loading="accountLoading">
          <ul class="gever-subNav">
            <li
              v-for="item in currentList"
              :key="item.id"
              :class="{ active: activeIndex === item.id }"
              @click="handleClick(item)"
            >
              <span>
                <span>{{ item.bankAccountNo }}</span>
                <span>{{ item.accountName }}</span>
              </span>
            </li>
          </ul>
        </div>
      </template>
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('订单类型')" prop="orderType">
              <el-select v-model="queryParams.orderType" clearable placeholder="请选择">
                <el-option
                  v-for="item in orderTypeOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            
            <!-- <el-form-item :label="$t('商户号')" prop="merchantNo">
              <gever-input v-model="queryParams.merchantNo" />
            </el-form-item> -->
            <el-form-item :label="$t('客户账号')" prop="customerAccount">
              <!-- 添加输入事件处理，限制字母和数字 -->
              <gever-input v-model="queryParams.customerAccount" />
            </el-form-item>
            <el-form-item :label="$t('交易时间')" prop="transactionTimeStart">
              <el-date-picker
                v-model="queryParams.transactionTimeStart"
                class="w45%"
                type="date"
                value-format="yyyy-MM-dd"
              /> -
              <el-date-picker
                v-model="queryParams.transactionTimeEnd"
                class="w45%"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            <el-form-item :label="$t('交易金额')" prop="transactionAmountMin">
              <gever-input 
                v-model="queryParams.transactionAmountMin" 
                style="width:45%" 
                @input="handleAmountInput('transactionAmountMin', $event)" 
              /> -
              <gever-input 
                v-model="queryParams.transactionAmountMax" 
                style="width:45%" 
                @input="handleAmountInput('transactionAmountMax', $event)" 
              />
            </el-form-item>
            <el-form-item :label="$t('已对账')" prop="reconciliation">
              <el-select v-model="queryParams.reconciliation" clearable placeholder="请选择">
                <el-option
                  v-for="item in reconciliationStatusOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button plain icon="el-icon-search" type="primary" round @click="handleSearch">
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
          </el-form>
          <gever-table
            :loading="tableLoading"
            ref="_geverTableRef"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            style="clear: both;"
          >
            <!-- 添加自定义列 -->
            <template #orderNo="{ row }">
              <el-button v-if="row.isEs==1" type="text" @click="handleView(row)">{{ row.orderNo }}</el-button>
              <span v-else>{{ row.orderNo }}</span>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <public-drawer 
        :visible.sync="showDetail" 
        title="查看收款订单" 
        :size="70" 
        @close="handleCloseDetail"
    >
      <detail
        v-if="showDetail"
        :detail-visible.sync="showDetail"
        ref="_detailContentRef"
        type="view"
        :order-no="getOrderNoPrefix(currentRow)"
      />
    </public-drawer>
  </div>
</template>

<script>
import { loadQrPaymentAccountListApi } from '@/api/paymentQr/qrPaymentAccount.js'
import { loadSettleListApi } from '@//api/paymentQr/qrPaymentSettle.js'
import { loadAccountList, loadAccountDetail } from '@/api/payment/capital.js'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import FoldBox from '../foldBox/index.vue'
// 引入 detail 组件
import detail from '../qrPaymentReceive/components/detail'

export default {
  name: 'SettleDetail',
  components: { 
    FoldBox,
    detail
  },
  data() {
    return {
      tableLoading: false,
      accountLoading: false,
      showFilter: false,
      filterParams: {
        accountNumber: '',
        ownerName: ''
      },
      accountList: [],
      currentList: [],
      activeIndex: null,
      activeAccountNumber: null,
      orderTypeOptions: [
        { text: '平台订单', id: '01' },
        { text: '非平台订单', id: '02'}
      ],
      reconciliationStatusOptions: [
        { text: '是', id: '1' },
        { text: '否', id: '0' }
      ],
      showDetail: false,
      currentRow: null,
      queryParams: {
        page: 1,
        rows: 20,
        transactionTimeStart: this.getFirstDayOfMonth(), 
        transactionTimeEnd: this.getToday(), 
        accountId: '',
        reconciliation: ''
      },
      accountsQueryParams: {
        page: 1,
        rows: 99999,
        status: 1
      },
      table: {
        columns: [
          { type: 'index', label: '序号', width: '50' },
          { label: '账户明细时间', prop: 'accountDetailTime', minWidth: '150', resizable: true },
          { label: '商户号', prop: 'merchantNo', minWidth: '150', resizable: true },
          { label: '交易号', prop: 'orderNo', minWidth: '150', resizable: true, slotName: 'orderNo' },
          { label: '交易时间', prop: 'transactionTime', minWidth: '150', resizable: true },
          { label: '交易金额(元)', prop: 'transactionAmount', minWidth: '100', resizable: true },
          { label: '客户账号', prop: 'customerAccount', minWidth: '150', resizable: true },
          { label: '说明', prop: 'description', minWidth: '200', resizable: true }
        ],
        tableList: [],
        total: 0
      }
    }
  },
  computed: {
    ...mapState(['areaLogin'])
  },
  watch: {
    activeIndex(newVal) {
      this.queryParams.accountId = this.activeAccountId
      this.queryParams.merchantNo = this.activeMerchantNo
      this.getList()
    },
    areaLogin: {
      handler: function (val) {
        // loadAccountList().then((res) => {
        //   this.accountList = res.data.rows
        //   this.currentList = res.data.rows
        // })
        this.loadAccounts()
        this.getList()
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    window.addEventListener('resize', () => {
      this.$nextTick(() => {
        this.$refs._geverTableRef.setHeight()
      })
    })
    // loadAccountList().then((res) => {
    //   this.accountList = res.data.rows
    //   this.currentList = res.data.rows
    // })
    // this.getList()
  },
  methods: {
    toggleSearch() {
      this.showFilter = !this.showFilter
    },
    loadAccounts(){
      const areaCode = this.areaLogin.areaCode || this.areaLogin.code
      const orgCode = this.areaLogin.code
      if(this.areaLogin.type == 'Organization'){
        this.accountsQueryParams.orgCode = orgCode
        this.accountsQueryParams.areaCode = ''
      }else{
        this.accountsQueryParams.areaCode = areaCode
        this.accountsQueryParams.orgCode = ''
      }
      if(!areaCode){
        return
      }

      this.accountLoading = true
      
      loadQrPaymentAccountListApi(this.accountsQueryParams).then(res => {
        res.data.rows.forEach(v=>{
          if(v.status){
            v.status = true
          }else{
            v.status = false
          }
        })

        const seenIds = new Set();
        this.accountList = res.data.rows.filter(item => {
          if (!seenIds.has(item.bankAccountId)) {
            seenIds.add(item.bankAccountId);
            return true;
          }
          return false;
        });
        this.currentList = this.accountList
      })
      .catch(err => {
          this.table.tableList = []
          this.table.total = 0
        })
      .finally(() => {
        this.accountLoading = false
      })
    },
    handleSearchList() {
      this.currentList = this.accountList.filter((item) => {
        if (
          this.filterParams.accountNumber.length &&
          this.filterParams.ownerName.length
        ) {
          return (
            item.bankAccountNo.includes(this.filterParams.accountNumber) &&
            item.accountName.includes(this.filterParams.ownerName)
          )
        } else if (this.filterParams.accountNumber.length) {
          return item.bankAccountNo.includes(this.filterParams.accountNumber)
        } else if (this.filterParams.ownerName.length) {
          return item.accountName.includes(this.filterParams.ownerName)
        } else {
          return true
        }
      })
    },
    handleClick(item) {
      this.activeIndex = item.id
      this.activeAccountId = item.bankAccountId
      this.activeAccountNumber = item.bankAccountNo
      this.accountOwner = item.accountName
      this.activeMerchantNo = item.merchantNo
    },
    getList() {
      if(!this.activeIndex){
        this.table.tableList = []
        this.table.total = 0
        this.tableLoading = false
        return
      }
      const areaCode = this.areaLogin.areaCode || this.areaLogin.code

      if (!this.queryParams.transactionTimeStart) {
        this.$set(this.queryParams, 'transactionTimeStart', '')
      }
      if (!this.queryParams.transactionTimeEnd) {
        this.$set(this.queryParams, 'transactionTimeEnd', '')
      }

      if (areaCode) {
        this.tableLoading = true
        loadSettleListApi(this.queryParams).then(res => {
          this.table.tableList = res.data.rows
          this.table.total = res.data.total
        })
        .catch(err => {
          this.table.tableList = []
          this.table.total = 0
        })
        .finally(() => {
          this.tableLoading = false
        })
      }
    },
    handleSearch() {
      this.getList()
    },
    handleAccountInput(value) {
    // 过滤非字母数字字符
    const filteredValue = value.replace(/[^a-zA-Z0-9]/g, '');
    this.queryParams.customerAccount = filteredValue;
    },
    
    // 修改：交易金额输入处理（限制两位小数）
    handleAmountInput(field, value) {
    // 1. 移除非数字和非小数点字符
    let sanitized = value.replace(/[^0-9.]/g, '');
    
    // 2. 限制只能有一个小数点（移除第一个小数点后的所有其他小数点）
    const firstDotIndex = sanitized.indexOf('.');
    if (firstDotIndex !== -1) {
      sanitized = sanitized.slice(0, firstDotIndex + 1) + 
                  sanitized.slice(firstDotIndex + 1).replace(/\./g, '');
    }
    
    // 3. 限制小数点后最多两位
    sanitized = sanitized.replace(/\.(\d{2})\d*/g, '.$1');
    
    // 4. 处理开头的小数点（如：".12" 转为 "0.12"）
    if (sanitized.startsWith('.')) {
      sanitized = '0' + sanitized;
    }
    
    // 更新绑定值
    this.queryParams[field] = sanitized;
    },
    getYesterday() {
      const date = new Date();
      date.setDate(date.getDate() - 1);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    getFirstDayOfMonth() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = '01';
      return `${year}-${month}-${day}`;
    },
    // 新增获取当天日期的方法
    getToday() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    // 点击交易号时触发的方法
    handleView(row) {
      this.currentRow = row
      this.showDetail = true
    },
    handleCloseDetail() {
      this.showDetail = false
    },
    getOrderNoPrefix(row) {
      if (row && row.orderNo) {
        return row.orderNo.split('-')[0] || row.orderNo;
      }
      return '';
    }
  }
}
</script>

<style scoped>

</style>

<style lang="scss" scoped>
#left-container {
  .el-form {
    margin: 0 5px;
    .el-form-item {
      margin-bottom: 0 !important;
      ::v-deep input {
        border: none;
        border-bottom: 1px solid #dcdfe6;
      }
    }
  }
  .left-subNav {
    .gever-subNav li {
      display: flex;
      align-items: center;
      & > span {
        display: inline-block;
        & > span {
          display: block;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.advance-search {
  * {
    font-size: 12px;
  }
  h3 {
    font-size: 14px;
    font-weight: bold;
    margin: 0 0 5px 0;
  }
}
.flex-w {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>