/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-10-25 17:06:24
 * @LastEditors: Peng Xiao
 * @LastEditTime: 2021-12-02 19:31:05
 */
import request from '@/utils/request'

/**
 * @name:账套期间
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function combotree (booksId) {
  return request({
    url: `/financial/books/basedata/accountingperiod/combotree?booksId=${booksId}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:获取有账务数据的机构
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function authOrgTree (data) {
  return request({
    url: '/financial/books/authOrgTree',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
/**
 * @name:获取账套登录的年份
 * @param {Object} data { orgId:'机构id' }
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function orgBooksYear (data) {
  return request({
    url: '/financial/books/orgBooksYear',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取账户登陆的期间
 * @param {*} data {orgId:'机构id',year:'年份' }
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function orgBooksPeriod (data) {
  return request({
    url: '/financial/books/orgBooksPeriod',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

export function login (data) {
  return request({
    url: '/financial/books/login',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * 获取摘要
 * @param {*} data { booksId: '' }
 * @returns
 */
export function getModelSummaryList (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/getModelSummaryList',
    method: 'post',
    params: data
  })
}

/**
 * 获取摘要列表数据
 * @param {*} data { booksId: '' }
 * @returns
 */
export function loadSummaryList (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/page',
    method: 'post',
    data: data
  })
}

/**
 * 获取单条摘要信息
 * @param {*} data 摘要id
 * @returns
 */
export function loadSummaryInfo (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/load/' + data,
    method: 'get'
  })
}

/**
 * 保存单条摘要信息
 * @param {*} data 摘要信息
 * @returns
 */
export function saveSummaryInfo (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/save',
    method: 'post',
    params: data
  })
}

/**
 * 删除单条摘要信息
 * @param {*} data 摘要id
 * @returns
 */
export function removeSummaryInfo (data) {
  return request({
    url: '/financial/accounting/accountingCommomInfoSummary/delete',
    method: 'post',
    params: data
  })
}

/**
 * 获取会计科目
 * @param {*} data { booksId: '' }
 * @returns
 */
export function getModelItemList (data) {
  return request({
    url: '/financial/books/basedata/accountingItem/getModelItemList',
    method: 'get',
    params: data
  })
}

/**
 * 获取结算方式
 * @param {*} data { booksId: '' }
 * @returns
 */
export function cashiersettlementtype (data) {
  return request({
    url: '/financial/books/basedata/cashiersettlementtype/getModelList',
    method: 'post',
    params: data
  })
}

/**
 * 获取凭证信息
 * @param {*} data { booksId: '', noPager:  }
 * @returns
 */
export function loadVoucher (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/loadVoucher',
    method: 'post',
    params: data
  })
}

/**
 * 创建、编辑账套时获取初始化信息
 * @param {*} data { booksId: '', noPager:  }
 * @returns
 */
export function loadInitInfo (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/add/init',
    method: 'get',
    params: data
  })
}

/**
 * 出纳科目
 * @param {*} data { booksId: '', noPager:  }
 * @returns
 */
export function cashierTree (params) {
  return request({
    url: '/financial/books/basedata/accountingItem/cashierTree',
    method: 'get',
    params
  })
}

/**
 * 获取会计科目的辅助核算类型列表
 * @param {*} data { booksId: '', accountingItemId:  }
 * @returns
 */
export function getAssistForVoucher (params) {
  return request({
    url: '/financial/books/basedata/assistType/getAssistForVoucher',
    method: 'get',
    params: params
  })
}

/**
 * 获取常用凭证列表
 * @param {*} data
 * @returns
 */
export function mAccountingVoucher (data) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/page',
    method: 'post',
    data: data
  })
}

/**
 * 导入时根据id获取常用凭证信息
 * @param {*} data
 * @returns
 */
export function getMAccountingVoucher (data) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/getVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 根据id获取常用凭证信息(编辑、查看)
 * @param {*} data
 * @returns
 */
export function loadMAccountingVoucher (id) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/load/' + id,
    method: 'get'
  })
}

/**
 * 保存常用凭证信息
 * @param {*} params { booksId: '' }
 * @param {*} data { id: '', sortNumber: '', voucherName: '' }
 * @returns
 */
export function saveMAccountingVoucher (params, data) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/saveVoucher',
    method: 'post',
    params: params,
    data: data,
    payload: true
  })
}

/**
 * 删除常用凭证信息
 * @param {*} data { id: '', booksId: '' }
 * @returns
 */
export function deleteMAccountingVoucher (data) {
  return request({
    url: '/financial/accounting/mAccountingVoucher/deleteVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 保存凭证
 * @param {*} params { booksId: '', period: '' }
 * @param {*} data {}
 * @returns
 */
export function saveVoucher (params, data) {
  return request({
    url: '/financial/accounting/accountingVoucher/saveVoucher',
    method: 'post',
    params: params,
    data: data,
    payload: true
  })
}

/**
 * 获取会计科目的期间余额
 * @param {*} params { booksId: '', period: '' }
 * @returns
 */
export function getPeriodBalance (params) {
  return request({
    url: '/financial/accounting/accountingItemBalance/getPeriodBalance',
    method: 'get',
    params: params
  })
}
/**
 * 获取各模块期间
 * @param {*} data 摘要id
 * @returns
 */
export function getBooksInfoByOrgIdAndYear (params) {
  return request({
    url: '/financial/books/getBooksInfoByOrgIdAndYear',
    method: 'get',
    params
  })
}

