import request from '@/utils/request'
/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentReceiveListApi(data) {
  return request({
    url: '/qrpayment/receive/page',
    method: 'post',
    data: data
  })
}

/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentReceiveDetailOptionsApi(params) {
  return request({
    url: '/qrpayment/detail/list',
    method: 'get',
    params
  })
}

// export function loadAppQrPaymentReceiveListApi(params) {
//   return request({
//     url: '/qrpayment/appProxy/receive/page',
//     method: 'get',
//     params
//   })
// }


/**
 * @name: 详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentReceiveInfoApi(id) {
  return request({
    url: `/qrpayment/receive/get/${id}`,
    method: 'get'
  })
}

/**
 * @name: 详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentReceiveInfoByNoApi(orderNo) {
  return request({
    url: `/qrpayment/receive/getByNo/${orderNo}`,
    method: 'get'
  })
}

/**
 * @name: 收款二维码信息详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getQrcodeApi(receiveId) {
  return request({
    url: `/qrpayment/receive/getQrcode/${receiveId}`,
    method: 'get'
  })
}

/**
 * @name: 收款二维码信息列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getQrcodesApi(receiveId) {
  return request({
    url: `/qrpayment/receive/getQrcodes/${receiveId}`,
    method: 'get'
  })
}




/**
 * @name: 删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteQrPaymentReceiveApi(data) {
  return request({
    url: '/qrpayment/receive/delete',
    method: 'post',
    data
  })
}

/**
 * @name: 新增保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveQrPaymentReceiveApi(data) {
  return request({
    url: '/qrpayment/receive/save',
    method: 'post',
    data
  })
}

/**
 * @name: 创建收款二维码
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveQrcodeApi(data) {
  return request({
    url: '/qrpayment/receive/saveQrcode',
    method: 'post',
    data
  })
}

/**
 * @name: 更新保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateQrPaymentReceiveApi(data) {
  return request({
    url: '/qrpayment/receive/update',
    method: 'post',
    data
  })
}



/**
 * @name: 更新保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function terminateQrPaymentReceiveApi(data) {
  return request({
    url: '/qrpayment/receive/terminateOrders',
    method: 'post',
    data
  })
}