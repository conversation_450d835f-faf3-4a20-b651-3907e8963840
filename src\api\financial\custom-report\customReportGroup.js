/*
 * @Descripttion: 
 * @version: 
 * @Author: 未知
 * @Date: 2023-09-05 12:43:51
 * @LastEditors: Andy
 * @LastEditTime: 2023-09-05 16:51:58
 */
import request from '@/utils/request'

// 查询报表组列表·
export function query(data) {
  return request({
    url: '/financial/customReport/customReportGroup/page',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 新增报表组
export function insert(data) {
  return request({
    url: '/financial/customReport/customReportGroup/insert',
    method: 'post',
    data,
    withCredentials: true
  })
}

// 报表组详情
export function detail(id) {
  return request({
    url: `/financial/customReport/customReportGroup/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

// 报表组详情
export function update(data) {
  return request({
    url: `/financial/customReport/customReportGroup/update`,
    method: 'post',
    data,
    withCredentials: true
  })
}

// 删除报表组
export function del(data) {
  return request({
    url: `/financial/customReport/customReportGroup/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}

// 查询所有报表组
export function selectAll(data) {
  return request({
    url: `/financial/customReport/customReportGroup/selectAll`,
    method: 'post',
    data,
    withCredentials: true
  })
}