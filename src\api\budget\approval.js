import request from '@/utils/request'
/**
 * 获取预算待办事项
 */
 export function getTodoList(data) {
  return request({
    url: '/workflow/getTodoList?path=/standard/financial/budget&subArea=true',
    method: 'post',
    data
  })
}
/**
 * 预算审批
 */
 export function budgetPlanningVerify(data) {
  return request({
    url: '/financial/budget/budgetPlait/verify',
    method: 'post',
    data
  })
}
/**
 * 预算调整
 */
 export function budgetAdjustmentVerify(data) {
  return request({
    url: '/financial/budget/budgetAdjustment/verify',
    method: 'post',
    data
  })
}