<template>
  <div>
    <fold-box
      left-title="选择"
      right-title="银行账户明细"
      :call-back="toggleSearch"
    >
      <template #left>
        <el-form v-show="showFilter" inline>
          <el-form-item :label="$t('账号：')">
            <el-input v-model="filterParams.accountNumber" class="w100" />
          </el-form-item>
          <el-form-item :label="$t('名称：')">
            <el-input v-model="filterParams.ownerName" class="w100" />
          </el-form-item>
          <el-form-item>
            <el-button
              v-hasPermi="'financial.payment.manager.bankStatement.list'"
              type="primary"
              plain
              round
              icon="el-icon-search"
              @click="handleSearchList"
            >
              {{ $t('搜索') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="left-subNav">
          <ul class="gever-subNav">
            <li
              v-for="item in currentList"
              :key="item.id"
              :class="{ active: activeIndex === item.id }"
              @click="handleClick(item)"
            >
              <span>
                <span>{{ item.accountNumber }}</span>
                <span>{{ item.ownerName }}</span>
              </span>
            </li>
          </ul>
        </div>
      </template>
      <template #right>
        <div class="right-box">
          <el-form ref="_formRef" :model="queryParams" inline class="flex-w">
            <div>
              <el-form-item :label="$t('交易日期：')" prop="startDay">
                <el-date-picker
                  v-model="queryParams.startDay"
                  type="date"
                  value-format="yyyy-MM-dd"
                  class="w150"
                />
              </el-form-item>
              <el-form-item>{{ $t('至') }}</el-form-item>
              <el-form-item prop="endDay">
                <el-date-picker
                  v-model="queryParams.endDay"
                  type="date"
                  value-format="yyyy-MM-dd"
                  class="w150"
                />
              </el-form-item>
              <el-form-item :label="$t('交易类别：')" prop="direction">
                <el-select v-model="queryParams.direction" class="w100">
                  <el-option
                    v-for="item in directionOptions"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('排序方式：')">
                <gever-select v-model.trim="queryParams.orderBy" class="w150" :clearable="false" :options="orderByOptions" />
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.payment.manager.bankStatement.list'"
                  type="primary"
                  round
                  icon="el-icon-search"
                  @click="handleSearch"
                >
                  {{ $t('搜索') }}
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-popover
                  v-model="advanceVisible"
                  trigger="manual"
                  popper-class="advance-search"
                  width="350"
                >
                  <h3>{{ $t('高级搜索') }}</h3>
                  <el-form
                    ref="_advanceFormRef"
                    inline
                    label-width="100px"
                    :model="queryParams"
                  >
                    <el-form-item
                      :label="$t('对方账号')"
                      prop="otherAccountNumber"
                    >
                      <el-input v-model="queryParams.otherAccountNumber" />
                    </el-form-item>
                    <el-form-item
                      :label="$t('对方户名')"
                      prop="otherAccountOwner"
                    >
                      <el-input v-model="queryParams.otherAccountOwner" />
                    </el-form-item>
                    <el-form-item :label="$t('附言')" prop="postScript">
                      <el-input v-model="queryParams.postScript" />
                    </el-form-item>
                  </el-form>
                  <div style="float: right">
                    <el-button type="primary" plain round @click="handleReset">
                      {{ $t('重置') }}
                    </el-button>
                    <el-button
                      v-hasPermi="
                        'financial.payment.manager.bankStatement.list'
                      "
                      type="primary"
                      icon="el-icon-search"
                      round
                      @click="handleAdvanceSearch"
                    >
                      {{ $t('搜索') }}
                    </el-button>
                    <el-link
                      type="primary"
                      :underline="false"
                      icon="el-icon-arrow-up"
                      style="margin-left: 10px"
                      @click="advanceVisible = false"
                    >
                      收起
                    </el-link>
                  </div>
                  <!-- <i
                    slot="reference"
                    class="el-customIcon-filter"
                    style="font-size: 16px; color: #409eff; cursor: pointer"
                    @click="advanceVisible = true"
                  /> -->
                  <span
                    slot="reference"
                    style="font-size: 14px; color: #409eff; cursor: pointer"
                    @click="advanceVisible = true"
                  >
                    高级搜索
                  </span>
                </el-popover>
              </el-form-item>
            </div>
            <div>
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.payment.manager.bankStatement.sync'"
                  type="primary"
                  icon="el-icon-refresh"
                  plain
                  round
                  @click="handleSync"
                >
                  {{ $t('同步') }}
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.payment.manager.bankStatement.export'"
                  type="primary"
                  icon="el-icon-download"
                  plain
                  round
                  @click="handleExport"
                >
                  {{ $t('导出') }}
                </el-button>
              </el-form-item>
            </div>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            v-loading="loading"
            :columns="tableColumns"
            :data="table.dataList"
            :total="table.total"
            :pagi="queryParams"
            @p-current-change="loadList"
            @size-change="loadList"
          >
            <template #orderCode="{ row }">
              <el-button type="text">{{ row.orderCode }}</el-button>
            </template>
            <template #operation="{ row }">
              <el-button
                v-hasPermi="'financial.payment.manager.bankStatement.view'"
                type="text"
                @click="handleView(row)"
              >
                {{ $t('查看') }}
              </el-button>
              <el-button
                v-hasPermi="
                  'financial.payment.manager.bankStatement.downloadReceipt'
                "
                type="text"
                @click="handleDownload(row)"
              >
                {{ $t('下载回单') }}
              </el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>

    <el-dialog :title="$t('查看账户明细')" :visible.sync="visible">
      <payment-bank-statement
        v-if="visible"
        :payment-bank-id="paymentBankId"
        :account-owner="accountOwner"
      />
    </el-dialog>
  </div>
</template>

<script>
import { loadAccountList, loadAccountDetail } from '@/api/payment/capital.js'
import FoldBox from '../foldBox/index.vue'
import paymentBankStatement from './paymentBankStatement.vue'
import { getToken } from '@/utils/auth'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'AccountDetail',
  components: { FoldBox, paymentBankStatement },
  data() {
    return {
      loading: false,
      advanceVisible: false,
      showFilter: false,
      filterParams: {
        // 左侧查询参数
        accountNumber: '',
        ownerName: ''
      },
      activeIndex: '',
      accountOwner: '',
      queryParams: {
        // 右侧表格筛选参数
        accountId: '',
        reload: false,
        startDay: '',
        endDay: '',
        direction: ' ',
        otherAccountNumber: '',
        otherAccountOwner: '',
        postScript: '',
        orderBy: 'timeDesc',
        page: 1,
        rows: 20
      },
      table: {
        dataList: [],
        total: 0
      },
      tableColumns: [
        { label: '序号', type: 'index', index: this.indexMethod, width: '80' },
        { label: '交易时间', prop: 'tradeTime', width: '180' },
        { label: '支出金额（元）', prop: 'outAmt', width: '150' },
        { label: '收入金额（元）', prop: 'inAmt', width: '150' },
        { label: '账户余额（元）', prop: 'balance', width: '150' },
        { label: '上笔余额（元）', prop: 'preBalance', width: '150' },
        { label: '对方账号', prop: 'otherAccountNumber', width: '250' },
        { label: '对方户名', prop: 'otherAccountOwner', width: '300' },
        { label: '对方行名', prop: 'bankName', width: '400' },
        { label: '交易摘要', prop: 'absInfo', width: '100' },
        { label: '附言', prop: 'postScript', width: '400' },
        { label: '支付申请单编号', slotName: 'orderCode', width: '250' },
        { label: '操作', slotName: 'operation', fixed: 'right', width: '150' }
      ],
      accountList: [],
      currentList: [],
      directionOptions: [
        { id: ' ', text: this.$t('全部') },
        { id: 'C', text: this.$t('收入') },
        { id: 'D', text: this.$t('支出') }
      ],
      orderByOptions: [
        { id: 'timeAsc', text: this.$t('按时间升序') },
        { id: 'timeDesc', text: this.$t('按时间降序') }
      ],
      visible: false,
      paymentBankId: ''
    }
  },
  computed: {
    // ...mapState(['paymentLogin'])
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    }
  },
  watch: {
    activeIndex(val) {
      this.queryParams.accountId = val
    },
    paymentLogin: {
      handler(val) {
        if (val) {
          loadAccountList().then((res) => {
            this.accountList = res.data.rows
            this.currentList = res.data.rows
          })
          this.loadList()
        }
      },
      deep: true
    }
  },
  created() {
    window.addEventListener('resize', () => {
      this.$nextTick(() => {
        this.$refs._geverTableRef.setHeight()
      })
    })
    loadAccountList().then((res) => {
      this.accountList = res.data.rows
      this.currentList = res.data.rows
    })
  },
  deactivated() {
    if (this.advanceVisible) this.advanceVisible = false
  },
  methods: {
    indexMethod(index) {
      return (this.queryParams.page - 1) * this.queryParams.rows + index + 1
    },
    getToken,
    toggleSearch() {
      this.showFilter = !this.showFilter
    },
    handleSearchList() {
      this.currentList = this.accountList.filter((item) => {
        if (
          this.filterParams.accountNumber.length &&
          this.filterParams.ownerName.length
        ) {
          return (
            item.accountNumber.includes(this.filterParams.accountNumber) &&
            item.ownerName.includes(this.filterParams.ownerName)
          )
        } else if (this.filterParams.accountNumber.length) {
          return item.accountNumber.includes(this.filterParams.accountNumber)
        } else if (this.filterParams.ownerName.length) {
          return item.ownerName.includes(this.filterParams.ownerName)
        } else {
          return true
        }
      })
    },
    handleClick(item) {
      // if (this.activeIndex === item.id) {
      //   this.activeIndex = ''
      // } else {
      this.activeIndex = item.id

      this.accountOwner = item.ownerName
      console.log(item)
      // }
    },
    loadList() {
      this.loading = true
      const params = JSON.parse(JSON.stringify(this.queryParams))
      params.direction = params.direction.trim()
      loadAccountDetail(params)
        .then((res) => {
          this.table.dataList = res.data.rows
          this.table.total = res.data.total
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSearch() {
      this.advanceVisible = false
      this.$refs['_advanceFormRef'].resetFields()
      this.loadList()
    },
    handleAdvanceSearch() {
      this.loadList()
    },
    handleReset() {
      this.$refs['_formRef'].resetFields()
      this.$refs['_advanceFormRef'].resetFields()
    },
    handleSync() {
      if (!this.queryParams.startDay || !this.queryParams.endDay) {
        return this.$message.warning(this.$t('同步必须明确指定日期区间'))
      }
      if (!this.activeIndex) {
        return this.$message.warning(this.$t('请选择账户'))
      }
      this.queryParams.reload = true
      this.loadList()
    },
    handleExport() {
      if (!this.activeIndex) {
        return this.$message.warning(this.$t('请选择账户'))
      }
      const params = JSON.parse(JSON.stringify(this.queryParams))
      params.reload = false
      params.page = 1
      params.rows = 9999
      params.direction = params.direction.trim()
      let url = ''
      let index = 0
      for (const key in params) {
        url += key + '=' + params[key]
        if (Object.keys(params).length - 1 !== index) {
          url += '&'
        }
        index++
      }
      window.open(
        process.env.VUE_APP_BASE_API +
          '/payment/manager/bankStatement/export?' +
          url +
          '&access_token=' +
          this.getToken() +
          '&tenant_id=' +
          this.$Cookies.get('X-tenant-id-header')
      )
    },
    handleView(row) {
      this.paymentBankId = row.id
      this.visible = true
    },
    handleDownload(row) {
      const curPayAccoun = this.currentList.find(
        (cur) => cur.id === row.accountId
      )
      if (curPayAccoun.bankType === '102') {
        return this.$message.warning(this.$t('工商银行无电子回单下载！'))
      }
      const url =
        process.env.VUE_APP_BASE_API +
        '/payment/manager/bankStatement/downloadReceipt/' +
        row.id +
        '?access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.open(url)
    }
  }
}
</script>

<style lang="scss" scoped>
#left-container {
  .el-form {
    margin: 0 5px;
    .el-form-item {
      margin-bottom: 0 !important;
      ::v-deep input {
        border: none;
        border-bottom: 1px solid #dcdfe6;
      }
    }
  }
  .left-subNav {
    .gever-subNav li {
      display: flex;
      align-items: center;
      & > span {
        display: inline-block;
        & > span {
          display: block;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.advance-search {
  * {
    font-size: 12px;
  }
  h3 {
    font-size: 14px;
    font-weight: bold;
    margin: 0 0 5px 0;
  }
}
.flex-w {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>
