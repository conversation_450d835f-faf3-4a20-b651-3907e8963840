<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-23 18:50:28
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-14 11:43:56
-->
<template>
  <div>
    <fold-box right-title="专项资金账查询">
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('专项资金名称')">
              <el-input v-model="queryParams.receiptName" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金编号')">
              <el-input v-model="queryParams.receiptCode" type="text" class="w200" clearable />
            </el-form-item>

            <el-form-item :label="$t('专项资金类别')">
              <!-- :show-all-levels="false" @getValue="handleCategoryValue" -->
              <categoryTreeSelect v-model="queryParams.categoryId" placeholder="请选择"></categoryTreeSelect>
            </el-form-item>
            <el-form-item :label="$t('专项资金用途')">
              <el-input v-model="queryParams.purpose" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('编制方')">
              <gever-select
                v-model="queryParams.specialType"
                class="w200"
                path="/financial/specialFund/receipt/specialType/"
                placeholder="请选择"
              />
            </el-form-item>

            <el-form-item :label="$t('专项资金来源')">
              <gever-select
                v-model="queryParams.specialSourceList"
                class="w200"
                multiple
                collapse-tags
                path="/financial/specialFund/receipt/specialSource/"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item :label="$t('是否历史数据')">
              <gever-select
                v-model="queryParams.isHistory"
                class="w200"
                :options="optionsMap.isHistory"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item :label="$t('')">
              <el-checkbox v-model="queryParams.isBalance">显示余额为零</el-checkbox>
            </el-form-item>
            <!-- <el-form-item :label="$t('')">
              <el-checkbox v-model="queryParams.isAccountingItem">显示未关联科目资金</el-checkbox>
            </el-form-item>-->
            <el-form-item>
              <el-button
                plain
                icon="el-icon-search"
                type="primary"
                round
                @click="handleSearch"
              >{{ $t('搜索') }}</el-button>
              <el-button
                v-hasPermi="'specialFund:receipt:export'"
                icon="el-customIcon-baocun"
                type="primary"
                round
                :loading="exportloading"
                @click="handleExport"
              >{{ $t('导出') }}</el-button>
            </el-form-item>
            <!-- <div style="float: right;">
              <el-form-item>
                <el-button
                  v-hasPermi="'specialFund:receipt:updateAccountingItem'"
                  type="primary"
                  round
                  @click="handleUpdateAccountingItem"
                >{{ $t('关联会计科目') }}</el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  v-hasPermi="'specialFund:receipt:cancelAccountingItem'"
                  type="primary"
                  round
                  @click="handleCancelAccountingItem"
                >{{ $t('取消关联') }}</el-button>
              </el-form-item>
            </div>-->
          </el-form>
          <div style="height: 87%;">
            <gever-table
              ref="_geverTableRef"
              :loading="tableLoading"
              :columns="table.columns"
              :data="table.tableList"
              :total="table.total"
              :pagi="queryParams"
              :row-class-name="getRowClassName"
              :pagination="false"
              @pagination-change="getList"
              @selection-change="handleSelectionChange"
            >
              <template #isHistory="{ row }">{{ getTitle(row.isHistory+'', optionsMap.isHistory) }}</template>
              <template
                #specialType="{ row }"
              >{{ getTitle(row.specialType+'', optionsMap.specialType) }}</template>
              <template
                #specialSource="{ row }"
              >{{ getTitle(row.specialSource+'', optionsMap.specialSource) }}</template>
              <template #status="{ row }">{{ getTitle(row.status+'', optionsMap.status) }}</template>
              <template #categoryId="{ row }">
                <span
                  v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
                >{{ row.categoryCode+' '+row.categoryName }}</span>
              </template>
              <template #createTime="{ row }">{{ row.createTime?row.createTime.substring(0,10):"" }}</template>

              <template #rowIndex="{row, index }">
                <span
                  v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
                >{{ (queryParams.page - 1) * queryParams.rows + index + 1 }}</span>
              </template>
              <template #operation="{ row }">
                <span v-if="row.orgName != '本页合计' && row.orgName != '总合计'">
                  <!-- v-hasPermi="'receipt.receipt.receipt.view'" -->
                  <el-button
                    v-hasPermi="'specialFund:receiptDetaile:receipt:get:id'"
                    type="text"
                    @click="handleView(row)"
                  >{{ $t('查看专项资金') }}</el-button>
                  <!-- v-hasPermi="'receipt.receipt.receipt.edit'" -->
                  <el-button
                    v-hasPermi="'payment:approval:approvalReceiver:pageSpecialFundPay'"
                    type="text"
                    @click="handleExpenditureDetail(row)"
                  >{{ $t('支出明细') }}</el-button>
                </span>
              </template>
            </gever-table>
            <div style="display: flex;justify-content:space-between">
              <div>
                说明：
                <div>1.支出申请金额：审批中或已审批的支出申请金额的合计；</div>
                <div>2.可用金额：入账金额 - 支出申请金额;</div>
                <div>3.实际支出金额：已办理支付，并支付成功的金额的合计;</div>
                <div>4.余额：入账金额 - 实际支出金额 ;</div>
              </div>
              <div>
                <el-pagination
                  :current-page="queryParams.page"
                  :page-sizes="[5, 10, 20, 30, 40, 50, 100]"
                  :page-size="queryParams.rows"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="table.total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>
      </template>
    </fold-box>
    <public-drawer
      :visible.sync="detailVisible"
      :title="detailTitle"
      :size="60"
      :buttons="detailButtons"
      @close="handleCloseDetail"
    >
      <detail
        v-if="detailVisible"
        :id="currentId"
        ref="_detailContentRef"
        :detail-visible.sync="detailVisible"
        :area="areaLogin"
        :type="type"
        :special-type="2"
        @refreshTable="getList"
      />
    </public-drawer>
    <book-type-tree ref="bookTypeTreeRef" @refreshTable="getList"></book-type-tree>
    <public-drawer
      :visible.sync="payTableVisible"
      title="支出明细"
      :size="75"
      @close="handleCloseDetail"
    >
      <payTable
        v-if="payTableVisible"
        ref="_payTableVisibleRef"
        :special-fund-id="specialFundId"
        :area="areaLogin"
        :receipt-code="receiptCode"
        :detail-visible.sync="payTableVisible"
      />
    </public-drawer>
    <!-- <accountingItem ref="accountingItemRef" :ids="ids" @refreshTable="getList"></accountingItem> -->
  </div>
</template>

<script>
import { selectAccountPage, cancelAccountingItem } from '@/api/specialFund/receipt.js'
import detail from '../receipt/components/detail'
import categoryTreeSelect from '../receipt/components/categoryTreeSelect'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
import { getToken } from '@/utils/auth'
import bookTypeTree from './components/bookTypeTree'
import payTable from './components/payTable.vue'
export default {
  name: 'Receipt',
  components: {
    detail,
    categoryTreeSelect,
    bookTypeTree,
    payTable
  },
  data () {
    return {
      exportloading: false,
      accountingItemVisible: false,
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      queryParams: {
        page: 1,
        rows: 20,
        specialType: '',
        isBalance: false
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50', selectable: this.selectEnable },
          { label: '序号', prop: 'rowIndex', width: '50' },
          { label: '镇（街）名称', prop: 'streetAreaName', width: '150' },
          { label: '村名称', prop: 'villageAreaName', width: '150' },
          { label: '单位名称', prop: 'orgName', width: '300' },
          { label: '编制方', prop: 'specialType', width: '100' },
          { label: '专项资金编号', prop: 'receiptCode', width: '150' },
          { label: '专项资金名称', prop: 'receiptName', width: '150' },
          { label: '专项资金类别', prop: 'categoryId', width: '200' },
          { label: '专项资金来源', prop: 'specialSource', width: '150' },
          { label: '入账金额', prop: 'amountStr', width: '150', align: 'right' },
          { label: '支出申请金额', prop: 'expenditureApplicationAmountStr', width: '150', align: 'right' },
          { label: '可用金额', prop: 'availableAmountStr', width: '150', align: 'right' },
          { label: '实际支出金额', prop: 'actualExpenditureAmountStr', width: '150', align: 'right' },
          { label: '余额', prop: 'balanceStr', width: '150', align: 'right' },
          { label: '专项资金用途', prop: 'purpose', width: '150' },
          { label: '入账日期', prop: 'arrivalDate', width: '150' },
          { label: '期限', prop: 'term', width: '100' },

          { label: '是否历史数据', prop: 'isHistory', width: '120' },
          { label: '操作', slotName: 'operation', width: '180', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
      ],
      treeData: [],
      props: {
        value: 'id', // ID字段名
        label: 'text', // 显示名称
        children: 'children', // 子级字段名
        isLeaf: 'isLeaf'
      },
      optionsMap: {
        status: [],
        specialSource: [],
        specialType: [],
        isHistory: [
          {
            text: '否',
            id: '0'
          },
          {
            text: '是',
            id: '1'
          }
        ]
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      ids: '',
      specialFundId: '',
      payTableVisible: false,
      receiptCode: ''
    }
  },
  computed: {
    ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler () {
        this.getList()
      }
    }
  },
  created () {
    // this.getList()
    this.getOptions()
  },
  methods: {
    handleSizeChange (val) {
      this.queryParams.rows = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.queryParams.page = val
      this.getList()
    },
    getRowClassName ({ row, rowIndex }) {
      if (row.orgName == '总合计') { // 例如，让第一行的字体加粗
        return 'row-bold'
      }
      return ''
    },
    handleUpdateAccountingItem () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要关联的数据！'))
      }
      if (this.tableIdSelection.length > 1) {
        return this.$message.warning(this.$t('请选择单条数据进行关联会计科目！'))
      }
      const data = this.tableSelection.filter(item => item.accountingItemId != undefined)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择未关联会计科目的数据！'))
      }
      this.ids = this.tableIdSelection.join(',')
      this.accountingItemVisible = true
      this.$refs.bookTypeTreeRef.open(this.ids, this.areaLogin.id, this.tableSelection[0].arrivalDate)
    },
    handleCancelAccountingItem () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要取消关联的数据！'))
      }
      const data = this.tableSelection.filter(item => item.accountingItemId == undefined)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择已关联会计科目的数据！'))
      }
      this.$confirm(this.$t('是否确认取消关联会计科目？'), '取消关联', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.tableIdSelection.join(',')
        cancelAccountingItem({ ids }).then(res => {
          this.$message.success(this.$t('操作成功'))
          this.getList()
        })
      }).catch(() => {
      })
    },
    async getOptions () {
      const { data: status = [] } = await getDictionary('/financial/specialFund/receipt/status/')
      this.optionsMap.status = status
      const { data: specialSource = [] } = await getDictionary('/financial/specialFund/receipt/specialSource/')
      this.optionsMap.specialSource = specialSource
      const { data: specialType = [] } = await getDictionary('/financial/specialFund/receipt/specialType/')
      this.optionsMap.specialType = specialType
    },
    handleCategoryValue (value) {
      this.queryParams.categoryId = value
    },
    getList () {
      if (this.areaLogin.type == undefined) {
        // this.$message.warning(this.$t('请选择地区/机构！'))
        return
      }
      if (this.areaLogin.type == 'Organization') {
        this.queryParams.orgId = this.areaLogin.id
        this.queryParams.areaCode = ''
      } else {
        this.queryParams.areaCode = this.areaLogin.code
        this.queryParams.orgId = ''
      }
      this.queryParams.status = '3'
      this.tableLoading = true
      selectAccountPage(this.queryParams).then(res => {
        this.table.tableList = res.data.rows
        this.table.total = res.data.total
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleSearch () {
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleView (row) {
      this.detailTitle = '查看'
      this.type = 'view'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleCloseDetail () {
      this.detailVisible = false
      this.batchFileVisible = false
    },
    handleSave () {
      this.$refs._detailContentRef.save()
    },
    handleSaveDraft () {
      this.$refs._detailContentRef.saveDraft()
    },
    selectEnable (row) {
      return row.orgName != '本页合计' && row.orgName != '总合计'
    },
    handleBatchFileSave () {
      this.$refs._batchFileRef.save()
    },
    handleExport () {
      this.exportloading = true
      let error = false
      try {
        // 拼接参数
        const params = new URLSearchParams()
        Object.keys(this.queryParams).forEach(key => {
          const value = this.queryParams[key]
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value)
          }
        })

        // 获取 token 和 tenant_id
        const accessToken = getToken()
        const tenantId = this.$Cookies.get('X-tenant-id-header')

        // 构建完整 URL
        const api = '/specialFund/receipt/export'
        const url = `${process.env.VUE_APP_BASE_API}${api}?${params.toString()}&access_token=${accessToken}&tenant_id=${tenantId}`

        // 创建 <a> 标签并触发下载
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', '') // 强制浏览器下载（而不是预览）
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.exportloading = false
      } catch (err) {
        error = true // 捕获到异常，标记为错误
        this.$message.error(this.$t('导出失败')) // 显示错误消息
      } finally {
        this.exportloading = false
        if (!error) {
          // this.$message.success(this.$t('导出成功')) // 没有错误才显示成功消息
        }
      }
    },
    handleExpenditureDetail (row) {
      this.specialFundId = row.id
      this.receiptCode = row.receiptCode
      this.payTableVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .row-bold {
  font-weight: bold !important;
  background-color: #f8f8f9;
}
</style>
