
import request from '@/utils/request'
/* 查询合同已收款但未开票的实际收款记录 */
export function getReceivabledNoBillAccounts(data) {
  return request({
    url: '/bill/sundryBillDetail/getReceivabledNoBillAccounts',
    method: 'post',
    data: data
    // payload: true
  })
}
/* 获取合同类型 */
export function getAllContractType(data) {
  return request({
    url: `/bill/sundryBillDetail/getAllContractType`,
    method: 'get',
    params: data
  })
}

/* 查询可用于开票的每期应收款记录  */
export function getInvoicableReceivableAccounts(data) {
  return request({
    url: '/bill/sundryBillDetail/getInvoicableReceivableAccounts',
    method: 'post',
    data: data
    // payload: true
  })
}

// 删除
export function deleteBill(data) {
  return request({
    url: '/bill/sundryBillDetail/deleteBill',
    method: 'post',
    data,
  })
}
// 库存数据-不分页
export function seleteList(data) {
  return request({
    url: '/bill/sundryBillStock/selectStockList',
    method: 'post',
    data,
  })
}
// 缴款人/单位信息列表
export function billCommonInfoOtherSideList(data) {
  return request({
    url: '/bill/sundryBillCommonInfoOtherSide/getInfoList',
    method: 'post',
    data
  })
}
// 保存缴款人/单位信息
export function billCommonInfoOtherSideSave(data) {
  return request({
    url: '/bill/sundryBillCommonInfoOtherSide/save',
    method: 'post',
    data
  })
}
// 删除缴款人/单位信息
export function billCommonInfoOtherSideDelete(data) {
  return request({
    url: '/bill/sundryBillCommonInfoOtherSide/delete',
    method: 'post',
    data
  })
}
// 常用收款人信息列表
export function billCommonInfoPayeeList(data) {
  return request({
    url: '/bill/sundryBillCommonInfoPayee/getInfoList',
    method: 'post',
    data
  })
}
// 摘要信息列表
export function getHistorySummary(params) {
  return request({
    url: '/bill/sundryBillDetail/getHistorySummary',
    method: 'get',
    params
  })
}
// 保存常用收款人信息
export function billCommonInfoPayeeSave(data) {
  return request({
    url: '/bill/sundryBillCommonInfoPayee/save',
    method: 'post',
    data
  })
}
// 删除常用收款人信息
export function billCommonInfoPayeeDelete(data) {
  return request({
    url: '/bill/sundryBillCommonInfoPayee/delete',
    method: 'post',
    data
  })
}
// 删除收款项目信息
export function billProjectDelete(data) {
  return request({
    url: '/bill/sundryBillCommonInfoChargeItem/delete',
    method: 'post',
    data
  })
}
// 获取常用项目信息
export function billCommonInfoChargeItemList(data) {
  return request({
    url: '/bill/sundryBillCommonInfoChargeItem/getInfoList',
    method: 'post',
    data
  })
}
// 保存常用项目信息列表
export function billCommonInfoChargeItemSave(data) {
  return request({
    url: '/bill/sundryBillCommonInfoChargeItem/save',
    method: 'post',
    data
  })
}
// initPage
export function billDetailInit(data) {
  return request({
    url: '/bill/sundryBillDetail/list/init',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
export function billDetailpage(data) {
  return request({
    url: '/bill/sundryBillDetail/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 旧系统红冲保存
export function formerBillRefund(data) {
  return request({
    url: '/bill/sundryBillDetail/formerBillRefund',
    method: 'post',
    data: data
  })
}
// 开出收款收据
export function billDetailAddReceipt(data) {
  return request({
    url: '/bill/sundryBillDetail/saveWithItems',
    method: 'post',
    data: data
    // withCredentials: true
  })
}
// 批量开出收款收据
export function billDetailBatchReceipt(data) {
  return request({
    url: '/bill/sundryBillDetail/batchInsertWithItems',
    method: 'post',
    data: data
    // withCredentials: true
  })
}
export function billDetailbillTypeLoad(id) {
  return request({
    url: `/bill/billType/load/${id}`,
    method: 'get'
    // withCredentials: true
  })
}
// 开出定额收据
export function billDetailAddQuota(data) {
  return request({
    url: `/bill/sundryBillDetail/save`,
    method: 'post',
    data
    // withCredentials: true
  })
}
export function getbillUserDetail(data) {
  return request({
    url: `/bill/sundryBillDetail/loadDetail?detailId=${data.id}`,
    method: 'get',
    data,
    withCredentials: true
  })
}
// 票据详情
export function billDetailLoad(id) {
  return request({
    url: `/bill/sundryBillDetail/load/${id}`,
    //  url: `/bill/sundryBillDetail/loadDetail/detailId=${id}`,
    method: 'get'
  })
}
// 获取打印样式
export function getbillPrintStyle(data) {
  return request({
    url: `/bill/billPrintStyle/loadPrintStyle?templateId=&billTypeId=${data.billTypeId}`,
    method: 'get',
    data
  })
}
// 更新打印状态
export function updatePrint(data) {
  return request({
    url: `/bill/sundryBillDetail/updatePrintState`,
    method: 'post',
    data
  })
}
// 更新打印状态
export function execInvalid(data) {
  return request({
    url: `/bill/sundryBillDetail/execInvalid`,
    method: 'post',
    data
  })
}
// 反作废
export function execUnInvalid(id) {
  return request({
    url: `/bill/sundryBillDetail/execUnInvalid/${id}`,
    method: 'post'
  })
}
// 获取明细表格
export function getItemList(id) {
  return request({
    url: `/bill/sundryBillDetail/getItemList/${id}`,
    method: 'get'
  })
}
// 结算
export function billSettle(data) {
  return request({
    url: `/bill/sundryBillDetail/settle`,
    method: 'post',
    data
  })
}
// 取消结算 cancelSettle
export function cancelBillSettle(data) {
  return request({
    url: `/bill/sundryBillDetail/cancelSettle`,
    method: 'post',
    data
  })
}
// 获取作废原因枚举
export function billCombotree(prarms) {
  return request({
    url: `/system/dictionary/combotree?path=${prarms}`,
    method: 'get',
    data: prarms,
    withCredentials: true
  })
}

/**
 * @name:收款票据那里获取预算列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getListPlaitItem(params) {
  return request({
    url: `/bill/sundryBillDetail/getListPlaitItem`,
    method: 'get',
    params
  })
}

export function getContractList(data) {
  return request({
    url: `/bill/sundryBillDetail/getContractList`,
    method: 'post',
    data
  })
}
/**
 * @name:获取当前登录的机构
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getLoginAreaOrg(params) {
  return request({
    url: `/bill/sundryBillDetail/getLoginAreaOrg`,
    method: 'get',
    params
  })
}
/**
 * @name:获取预算项目控制
 * @param {String} orgId 机构id
 * @param {String} year 年份
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBudgetInfo(params) {
  return request({
    url: `/bill/sundryBillDetail/getBudgetInfo`,
    method: 'get',
    params
  })
}

/**
 * @name:获取合同
 * @param {String} orgId 机构id
 * @param {String} year 年份
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getWXContractList(data) {
  return request({
    url: `/bill/sundryBillDetail/getWXContractList`,
    method: 'post',
    data
  })
}

/**
 * @name:获取合同收款详情
 * @param {String} orgId 机构id
 * @param {String} year 年份
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getWXContractCollectList(data) {
  return request({
    url: `/bill/sundryBillDetail/getWXContractCollectList`,
    method: 'post',
    data
  })
}

/**
 * @name:获取合同收款项目列表
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getWXCollectionItem(params) {
  return request({
    url: `/bill/sundryBillDetail/getWXCollectionItem`,
    method: 'get',
    params
  })
}

/**
 * @name:获取已收金额列表
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getActualAmountList(params) {
  return request({
    url: `/bill/sundryBillDetail/getActualAmountList`,
    method: 'get',
    params
  })
}

/**
 * @name:反作废
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancelInvalid(data) {
  return request({
    url: `/bill/sundryBillDetail/cancelInvalid`,
    method: 'post',
    data
  })
}


//金额作废

export function invalidAmount(data) {
  return request({
    url: `/bill/sundryBillDetail/invalidAmount`,
    method: 'post',
    data
  })
}


//金额反作废

export function invalidAmountCancel(data) {
  return request({
    url: `/bill/sundryBillDetail/invalidAmountCancel`,
    method: 'post',
    data
  })
}

//红冲

export function fullRefund(data) {
  return request({
    url: '/bill/sundryBillDetail/fullRefund',
    method: 'post',
    data
  })
}
//红冲未收款

export function unpaidRefund(data) {
  return request({
    url: '/bill/sundryBillDetail/unpaidRefund',
    method: 'post',
    data
  })
}

//修改开票方式
export function updateInvoicingStage (data) {
  return request({
    url: `/bill/sundryBillDetail/updateInvoicingStage`,
    method: 'post',
    data
  })
}

//修改开票方式
export function updateInvoicingStage2 (data) {
  return request({
    url: `/bill/sundryBillDetail/updateInvoicingStage2`,
    method: 'post',
    data
  })
}
