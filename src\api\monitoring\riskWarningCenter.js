import request from '@/utils/request'

/**
 * 获取地区树
 */
export function authTreeData(params) {
  return request({
    url: '/system/organization/authTreeData',
    method: 'get',
    params
  })
}

/**
 * 数据字典获取
 */
export function dictionaryCombotree(params) {
  return request({
    url: '/system/dictionary/combotree',
    method: 'get',
    params: params
  })
}

/**
 * 获取监控计划列表
 */
export function loadMoniPlanList(data) {
  return request({
    url: '/leaderview/moniPlan/page',
    method: 'post',
    data: data
  })
}

/**
 * 获取监控计划详情
 */
export function loadMoniPlan(id) {
  return request({
    url: '/leaderview/moniPlan/loadData/' + id,
    method: 'get'
  })
}

/**
 * 获取全部的角色列表
 */
export function loadAllRoleList() {
  return request({
    url: '/system/role/roles',
    method: 'get'
  })
}

/**
 * 获取已选的角色列表
 */
export function loadRoleList(data) {
  return request({
    url: '/leaderview/moniPlan/selectRoleList',
    method: 'post',
    data: data
  })
}

/**
 * 保存监控计划
 */
export function saveMoniPlan(data) {
  return request({
    url: '/leaderview/moniPlan/save',
    method: 'post',
    data: data
  })
}

/**
 * 删除监控计划
 */
export function removeMoniPlan(data) {
  return request({
    url: '/leaderview/moniPlan/delete',
    method: 'post',
    data: data
  })
}

/**
 * 获取预警数据列表
 */
export function loadRiskWarmingDataList(data) {
  return request({
    url: '/leaderview/riskWarming/page',
    method: 'post',
    data: data
  })
}

/**
 * 获取预警记录列表
 */
export function loadDetailPage(data) {
  return request({
    url: '/leaderview/riskWarming/detailPage',
    method: 'post',
    data: data
  })
}

/**
 * 预警处理
 */
export function handleRiskWarming(data) {
  return request({
    url: '/leaderview/riskWarming/handle',
    method: 'post',
    data: data
  })
}

/**
* 获取会计科目
*/
export function getAccountingItemType(data) {
 return request({
    url:'/financial/booktypes/basedata/itemType/listByBooksTypeId',
   method: 'post',
   data: data
 })
}
//获取所有资产
export function accountingItem(data) {
  return request({
    url:'/financial/booktypes/basedata/item/tree',
    method: 'post',
    data: data
  })
}
//资产类别分类
export function baseAssetClass(data) {
  return request({
    url:'/financial/booktypes/basedata/baseAssetClass/tree',
    method: 'post',
    data: data
  })
}
export function baseAssetClassPage(data) {
  return request({
    url:'/financial/booktypes/basedata/baseAssetClass/page',
    method: 'post',
    data: data
  })
}




