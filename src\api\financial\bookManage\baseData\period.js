/*
 * @Descripttion:基础设置-期间设置
 * @version:
 * @Author: 未知
 * @Date: 2021-10-25 16:01:30
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-02 11:24:13
 */
import request from '@/utils/request'

/**
 * @name:根据账套id获取基础设置的期间设置
 * @param {Object} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/books/basedata/accountingperiod/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

