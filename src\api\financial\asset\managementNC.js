/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2021-11-19 10:21:49
 * @LastEditors: Peng Xiao
 * @LastEditTime: 2022-02-24 16:47:02
 */

import request from '@/utils/request'


/**
 * @name:获取资产类型树NC
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function tree({ data, params }) {
  return request({
    url: `/financial/asset/assetCardNC/tree`,
    method: 'post',
    params,
    data,
    withCredentials: true
  })
}


/**
 * @name:获取数据页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/asset/assetCardNC/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/asset/assetCardNC/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}


/**
 * @name:获取资产使用状态
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function comboList(data) {
  return request({
    url: `/financial/asset/assetCardNC/comboList`,
    method: 'post',
    data,
    withCredentials: true
  })
}