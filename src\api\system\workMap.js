import request from '@/utils/request'

// 查询菜单列表
export function getListData(data) {
  return request({
    url: '/general/mapConfig/listData',
    method: 'post',
    data
  })
}

// 删除
export function removeList(data) {
  return request({
    url: '/general/mapConfig/deleteX',
    method: 'post',
    data
  })
}

// 查看
export function loadList(id) {
    return request({
      url: `/general/mapConfig/load/${id}`,
      method: 'get'
    })
  }


// 保存
export function saveOrUpdate(data) {
    return request({
      url: '/general/mapConfig/saveOrUpdate',
      method: 'post',
      data
    })
  }