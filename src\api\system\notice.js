import request from '@/utils/request'

// 查询菜单列表
export function getNoticeList(data) {
  return request({
    url: '/system/message/list',
    method: 'post',
    data: data
  })
}

// 查询消息类型
export function getMessageType() {
  return request({
    url: '/system/message/getMessageTypeForCombo',
    method: 'get'
  })
}

// 新增消息
export function addMessage(data) {
  return request({
    url: '/system/message/add',
    method: 'post',
    data,
    payload: true
  })
}

// 更新消息
export function updateMessage(data) {
  return request({
    url: '/system/message/updateNotic',
    method: 'post',
    data,
    payload: true
  })
}

// 发布消息
export function releaseMessage(data) {
  return request({
    url: '/system/message/release',
    method: 'post',
    data,
    payload: true
  })
}

// 新增并发布消息
export function addAndReleaseMessage(data) {
  return request({
    url: '/system/message/addAndRelease',
    method: 'post',
    data,
    payload: true
  })
}

// 获取消息基本信息
export function getBaseInfo(params) {
  return request({
    url: '/system/message/getBaseInfo',
    method: 'get',
    params
  })
}

// 获取消息发布的地区机构信息
export function getSelectedTreeData(params) {
  return request({
    url: '/system/message/getSelectedTreeData',
    method: 'get',
    params
  })
}

// 获取消息发布的角色列表
export function getRoleList(params) {
  return request({
    url: '/system/message/getRoleList',
    method: 'get',
    params
  })
}

// 获取消息撤销记录
export function getRevokeList(data) {
  return request({
    url: '/system/message/getRevokeList',
    method: 'post',
    data
  })
}

// 删除消息
export function removeList(data) {
  return request({
    url: '/system/message/delete',
    method: 'post',
    data
  })
}
