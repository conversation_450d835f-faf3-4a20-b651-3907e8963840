import request from '@/utils/request'
// 票据发放列表数据
export function getBillSignFor(data) {
  return request({
    url: '/bill/billReceive/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 查看票据签收
export function checkReceipt(id) {
  return request({
    url: `/bill/billReceive/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

// 页面初始化
export function initGrant(id, type) {
  return request({
    url: `/bill/billDist/list/init?loginAreaOrgId=${id}&loginAreaOrgType=${type}
    `,
    method: 'get',
    withCredentials: true
  })
}
