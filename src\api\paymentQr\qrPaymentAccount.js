import request from '@/utils/request'
/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentAccountListApi(params) {
  return request({
    url: '/qrpayment/account/page',
    method: 'get',
    params
  })
}

/**
 * @name: 列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentAccountOptionsApi(params) {
  return request({
    url: '/qrpayment/account/list',
    method: 'get',
    params
  })
}

/**
 * @name: 当前所属地区
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getAreaApi() {
  return request({
    url: '/qrpayment/account/getArea',
    method: 'get'
  })
}

/**
 * @name: 银行账户列表查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadBankAccount<PERSON><PERSON>(params) {
  return request({
    url: '/payment/manager/bankAccount/page',
    method: 'post',
    params
  })
}


/**
 * @name: 详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadQrPaymentAccountInfoApi(id) {
  return request({
    url: `/qrpayment/account/get/${id}`,
    method: 'get'
  })
}

/**
 * @name: 删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteQrPaymentAccountApi(data) {
  return request({
    url: '/qrpayment/account/deleteMul',
    method: 'post',
    data,
    payload:true
  })
}


/**
 * @name: 新增保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveQrPaymentAccountApi(data) {
  return request({
    url: '/qrpayment/account/save',
    method: 'post',
    data
  })
}

/**
 * @name: 更新保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateQrPaymentAccountApi(data) {
  return request({
    url: '/qrpayment/account/update',
    method: 'post',
    data
  })
}

/**
 * 生效/失效
 * @param {*} id 
 * @returns 
 */
export function setVoidApi(id) {
  return request({
    url: '/qrpayment/account/setVoid/'+id,
    method: 'post',
  })
}


/**
 * @name: 查询已存在数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function queryExistsApi(data) {
  return request({
    url: '/qrpayment/account/queryExists',
    method: 'post',
    data,
    payload:true
  })
}
