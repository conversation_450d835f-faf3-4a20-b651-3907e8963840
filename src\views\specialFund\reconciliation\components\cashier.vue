<!--
 * @Description: 专户余额与专项资金余额对账表
 * @Author: liuwf
 * @Date: 2025-05-28 15:17:03
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-08 10:06:36
-->
<template>
  <div style="height: 100% ;">
    <el-form inline @submit.native.prevent>
      <el-form-item :label="$t('年份')">
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          value-format="yyyy"
          format="yyyy"
          placeholder="选择年"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('期间')">
        <el-col :span="11">
          <el-select v-model="queryParams.monthStart" placeholder="请选择" @change="validateRange">
            <el-option v-for="item in 12" :key="item+''" :label="item+'月'" :value="item+''"></el-option>
          </el-select>
        </el-col>
        <el-col class="line" :span="2" style="text-align: center;">-</el-col>
        <el-col :span="11">
          <el-select v-model="queryParams.monthEnd" placeholder="请选择" @change="validateRange">
            <el-option v-for="item in 12" :key="item" :label="item+'月'" :value="item+''"></el-option>
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item :label="$t('银行账号')">
        <el-input v-model="queryParams.bankAccountNumber" type="text" class="w200" clearable />
      </el-form-item>
      <el-form-item :label="$t('账户类型')">
        <!-- <el-input v-model="queryParams.receiptName" type="text" class="w200" clearable /> -->
        <gever-select
          v-model="queryParams.bankAccountType"
          class="w200"
          :options="optionsMap.bankAccountType"
        />
      </el-form-item>
      <el-form-item :label="$t('')">
        <el-checkbox v-model="queryParams.isBalance">显示全零行</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button
          plain
          icon="el-icon-search"
          type="primary"
          round
          @click="handleSearch"
        >{{ $t('搜索') }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-customIcon-baocun"
          type="primary"
          round
          :loading="exportloading"
          @click="handleExport"
        >{{ $t('导出') }}</el-button>
      </el-form-item>
    </el-form>
    <div style="text-align: center;font-size: 24px;font-weight: bold;margin-bottom: 10px;">
      <span>专项资金出纳对账表</span>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: 10px;">
      <div>
        <span>单位名称：{{ orgName }}</span>
      </div>
      <div>
        <span>统计时间：{{ statisticalTime }}</span>
      </div>
    </div>
    <div style="height: 95%;">
      <gever-table
        ref="_geverTableRef"
        :loading="tableLoading"
        :columns="table.columns"
        :data="table.tableList"
        :total="table.total"
        :pagi="queryParams"
        :pagination="false"
        :row-class-name="getRowClassName"
        @pagination-change="getList"
      >
        <template #differenceInitBalStr="{ row }">
          <div
            class="table-text"
            :style="{ color: row.differenceInitBal != 0 ? 'red' : '' }"
          >{{ row.differenceInitBalStr }}</div>
        </template>
        <template #differenceIncomeStr="{ row }">
          <div
            class="table-text"
            :style="{ color: row.differenceIncome != 0 ? 'red' : '' }"
          >{{ row.differenceIncomeStr }}</div>
        </template>
        <template #differenceExpensesStr="{ row }">
          <div
            class="table-text"
            :style="{ color: row.differenceExpenses != 0 ? 'red' : '' }"
          >{{ row.differenceExpensesStr }}</div>
        </template>
        <template #differenceBalanceStr="{ row }">
          <div
            class="table-text"
            :style="{ color: row.differenceBalance != 0 ? 'red' : '' }"
          >{{ row.differenceBalanceStr }}</div>
        </template>
        <template #specialFundInitBalStr="{ row }">
          <div class="table-text">{{ row.specialFundInitBalStr }}</div>
        </template>
        <template #specialFundIncomeStr="{ row }">
          <div class="table-text">{{ row.specialFundIncomeStr }}</div>
        </template>
        <template #specialFundExpensesStr="{ row }">
          <div class="table-text">{{ row.specialFundExpensesStr }}</div>
        </template>
        <template #specialFundBalanceStr="{ row }">
          <div class="table-text">{{ row.specialFundBalanceStr }}</div>
        </template>
        <template #cashierInitBalStr="{ row }">
          <div class="table-text">{{ row.cashierInitBalStr }}</div>
        </template>
        <template #cashierIncomeStr="{ row }">
          <div class="table-text">{{ row.cashierIncomeStr }}</div>
        </template>
        <template #cashierExpensesStr="{ row }">
          <div class="table-text">{{ row.cashierExpensesStr }}</div>
        </template>
        <template #cashierBalanceStr="{ row }">
          <div class="table-text">{{ row.cashierBalanceStr }}</div>
        </template>
      </gever-table>
      <div style="text-align: right; width: 100%;margin-top: 10px; margin-bottom: 10px;">
        <!-- <span>打印日期: {{ currentDate }}</span> -->
      </div>
    </div>
  </div>
</template>

<script>
import { selectCashierReport } from '@/api/specialFund/receipt.js'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
import { getToken } from '@/utils/auth'
export default {
  name: 'Receipt',
  components: {
  },
  data () {
    return {
      exportloading: false,
      tableLoading: false,
      queryParams: {
        page: 1,
        rows: 20,
        isZero: '0',
        year: '',
        monthStart: '1',
        monthEnd: '',
        isBalance: false
      },
      table: {
        columns: [
          { type: 'index', label: '序号', width: '50', fixed: 'left' },
          { label: '单位名称', prop: 'orgName', width: '300', fixed: 'left' },
          { label: '银行账号', prop: 'bankAccountNumber', width: '180', fixed: 'left' },
          { label: '账户类型', prop: 'bankAccountTypeText', width: '150', fixed: 'left' },
          {
            label: '专项资金', children: [
              { label: '期初余额', prop: 'specialFundInitBalStr', width: '150' },
              { label: '收入', prop: 'specialFundIncomeStr', width: '150' },
              { label: '支出', prop: 'specialFundExpensesStr', width: '150' },
              { label: '余额', prop: 'specialFundBalanceStr', width: '150' }
            ]
          },
          {
            label: '出纳', children: [
              { label: '期初余额', prop: 'cashierInitBalStr', width: '150' },
              { label: '收入', prop: 'cashierIncomeStr', width: '150' },
              { label: '支出', prop: 'cashierExpensesStr', width: '150' },
              { label: '余额', prop: 'cashierBalanceStr', width: '150' }
            ]
          },
          {
            label: '差额', children: [
              { label: '期初余额', prop: 'differenceInitBalStr', width: '150' },
              { label: '收入', prop: 'differenceIncomeStr', width: '150' },
              { label: '支出', prop: 'differenceExpensesStr', width: '150' },
              { label: '余额', prop: 'differenceBalanceStr', width: '150' }
            ]
          }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
      ],
      optionsMap: {
        zero: [
          {
            text: '是',
            id: '1'
          },
          {
            text: '否',
            id: '0'
          }
        ],
        bankAccountType: []
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      dateStart: '',
      dateEnd: '',
      orgName: '',
      statisticalTime: '',
      currentDate: ''
    }
  },
  computed: {
    ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler () {
        if (this.queryParams.year == null || this.queryParams.year == '' || this.queryParams.year == undefined) {
          return
        }
        if (this.queryParams.monthStart == null || this.queryParams.monthStart == '' || this.queryParams.monthStart == undefined) {
          return
        }
        if (this.queryParams.monthEnd == null || this.queryParams.monthEnd == '' || this.queryParams.monthEnd == undefined) {
          return
        }
        this.getList()
      }
    }
  },
  created () {
    this.getOptions()
    const now = new Date()
    this.queryParams.year = String(now.getFullYear())
    this.queryParams.monthEnd = String(now.getMonth() + 1)
  },
  methods: {
    validateRange () {
      if (this.queryParams.monthStart && this.queryParams.monthEnd) {
        if (Number(this.queryParams.monthStart) > Number(this.queryParams.monthEnd)) {
          this.$message.warning('开始月份不能大于结束月份')
          // 可以在这里重置其中一个值，或者根据业务需求处理
          this.queryParams.monthStart = ''
        }
      }
    },
    getCurrentDate () {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    handleSizeChange (val) {
      this.queryParams.rows = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.queryParams.page = val
      this.getList()
    },
    async getOptions () {
      const { data: bankAccountType = [] } = await getDictionary('/financial/bank_account_type/')
      this.optionsMap.bankAccountType = bankAccountType.filter(item => item.id == 3 || item.id == 5 || item.id == 6)
    },
    getList () {
      if (this.areaLogin.type == 'Organization') {
        this.queryParams.orgId = this.areaLogin.id
        this.queryParams.orgName = this.areaLogin.fname
        this.queryParams.areaCode = ''
      } else {
        this.$message.warning('请选择机构')
        return
      }
      if (this.queryParams.year == null || this.queryParams.year == '' || this.queryParams.year == undefined) {
        this.$message.warning('请选择年份')
        return
      }
      if (this.queryParams.monthStart == null || this.queryParams.monthStart == '' || this.queryParams.monthStart == undefined) {
        this.$message.warning('请选择开始月份')
        return
      }
      if (this.queryParams.monthEnd == null || this.queryParams.monthEnd == '' || this.queryParams.monthEnd == undefined) {
        this.$message.warning('请选择截止月份')
        return
      }
      let monthStart = this.queryParams.monthStart
      if (this.queryParams.monthStart < 10) {
        monthStart = '0' + this.queryParams.monthStart
      }
      let monthEnd = this.queryParams.monthEnd
      if (this.queryParams.monthEnd < 10) {
        monthEnd = '0' + this.queryParams.monthEnd
      }
      this.tableLoading = true
      this.currentDate = this.getCurrentDate()
      this.statisticalTime = this.queryParams.year + '-' + monthStart + ' 至 ' + this.queryParams.year + '-' + monthEnd
      this.orgName = this.areaLogin.fname
      selectCashierReport(this.queryParams).then(res => {
        this.table.tableList = res.data
        this.table.total = res.data.total
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleSearch () {
      this.getList()
    },
    getRowClassName ({ row, rowIndex }) {
      if (row.bankAccountNumber == '总合计') { // 例如，让第一行的字体加粗
        return 'row-bold'
      }
      return ''
    },
    handleExport () {
      if (this.areaLogin.type == 'Organization') {
        this.queryParams.orgId = this.areaLogin.id
        this.queryParams.orgName = this.areaLogin.fname
        this.queryParams.areaCode = ''
      } else {
        this.$message.warning('请选择机构')
        return
      }
      if (this.queryParams.year == null || this.queryParams.year == '' || this.queryParams.year == undefined) {
        this.$message.warning('请选择年份')
        return
      }
      if (this.queryParams.monthStart == null || this.queryParams.monthStart == '' || this.queryParams.monthStart == undefined) {
        this.$message.warning('请选择开始月份')
        return
      }
      if (this.queryParams.monthEnd == null || this.queryParams.monthEnd == '' || this.queryParams.monthEnd == undefined) {
        this.$message.warning('请选择截止月份')
        return
      }
      let error = false
      try {
        // 拼接参数
        const params = new URLSearchParams()
        Object.keys(this.queryParams).forEach(key => {
          const value = this.queryParams[key]
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value)
          }
        })

        // 获取 token 和 tenant_id
        const accessToken = getToken()
        const tenantId = this.$Cookies.get('X-tenant-id-header')

        // 构建完整 URL
        const api = '/specialFund/reconciliation/report/cashierReportExport'
        const url = `${process.env.VUE_APP_BASE_API}${api}?${params.toString()}&access_token=${accessToken}&tenant_id=${tenantId}`
        this.exportloading = true
        // 创建 <a> 标签并触发下载
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', '') // 强制浏览器下载（而不是预览）
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // this.$message.success(this.$t('导出成功'))
      } catch (err) {
        error = true // 捕获到异常，标记为错误
        this.$message.error(this.$t('导出失败')) // 显示错误消息
      } finally {
        this.exportloading = false
        if (!error) {
          // this.$message.success(this.$t('导出成功')) // 没有错误才显示成功消息
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .table-text {
  width: 100%;
  text-align: right;
}
::v-deep .row-bold {
  font-weight: bold !important;
  background-color: #f8f8f9;
}
</style>
