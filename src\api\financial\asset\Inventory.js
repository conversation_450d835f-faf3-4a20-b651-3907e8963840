
import request from '@/utils/request'

/**
 * @name:获取资产盘点
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
    return request({
        url: '/financial/asset/assetInventory/page',
        method: 'post',
        data: data,
        withCredentials: true
    })
}

/**
 * @name:盘点初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function initInventoryData(data) {
    return request({
        url: '/financial/asset/assetInventory/initInventoryData',
        method: 'get',
        params: data,
        withCredentials: true
    })
}

/**
 * @name:保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function saveX(data) {
    return request({
        url: '/financial/asset/assetInventory/saveX',
        method: 'post',
        payload: true,
        data: data,
        withCredentials: true
    })
}

/**
 * @name:导出
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function exportExcel(data) {
    return request({
        url: '/financial/asset/assetInventory/exportExcel',
        method: 'get',
        params: data,
        withCredentials: true
    })
}
