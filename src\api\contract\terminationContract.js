import request from '@/utils/request'

//合同终止列表
export function selectPage (data) {
  return request({
    url: '/contract/contractTermination/selectPage',
    method: 'post',
    data
  })
}

//可终止的合同分页列表
export function pageContractForTermination (data) {
  return request({
    url: '/contract/contractTermination/pageContractForTermination',
    method: 'post',
    data
  })
}

//合同终止删除
export function terminationDelete (data) {
  return request({
    url: '/contract/contractTermination/delete',
    method: 'post',
    data
  })
}

//合同终止查看
export function getView (params) {
  return request({
    url: '/contract/contractTermination/get',
    method: 'get',
    params: params
  })
}
export function getByContractId (params) {
  return request({
    url: '/contract/contractTermination/getByContractId',
    method: 'get',
    params: params
  })
}

// 合同终止取消终止
export function cancel (data) {
  return request({
    url: '/contract/contractTermination/cancel',
    method: 'post',
    data
  })
}

//合同终止提交
export function submit (data) {
  return request({
    url: `/contract/contractTermination/submit?businessTypeKey=${data.businessTypeKey}&orgId=${data.orgId}`,
    method: 'post',
    data: data.ids,
    payload: true
  })
}

// 合同终止转坏账
export function toBad (data) {
  return request({
    url: '/contract/contractTermination/toBad',
    method: 'post',
    data,
    payload: true
  })
}

// 合同终止保存并提交
export function saveAndSubmit (data) {
  return request({
    url: '/contract/contractTermination/saveAndSubmit',
    method: 'post',
    data,
    payload: true
  })
}
