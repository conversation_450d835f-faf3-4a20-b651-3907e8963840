<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-20 08:51:25
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-19 14:41:56
-->

<template>
  <div>
    <!-- 外部 Tooltip 手动控制 -->
    <!-- <el-tooltip
      :content="valueTitle"
      placement="top"
      :disabled="valueTitle.length == 1 || valueTitle.length == 0"
      :manual="true"
      :value="showExternalTooltip"
      @mouseenter.native="handleExternalTooltipEnter"
      @mouseleave.native="handleExternalTooltipLeave"
    >-->
    <el-select
      ref="select"
      v-model="valueTitle"
      :clearable="clearable"
      :popper-append-to-body="false"
      :placeholder="placeholder"
      popper-class="popper"
      :title="valueTitle || ''"
      :visible="dropdownVisible"
      v-bind="$attrs"
      @clear="clearHandle"
      @visible-change="handleSelectVisibleChange"
      v-on="$listeners"
    >
      <el-option :value="valueTitle" :label="valueTitle">
        <div class="tree-scroll-container">
          <div class="tree-inner-container">
            <el-tree
              id="tree-option"
              ref="selectTree"
              :accordion="accordion"
              :expand-on-click-node="false"
              :data="options"
              :props="props"
              lazy
              :node-key="props.value"
              :load="loadChildNode"
              :default-expanded-keys="defaultExpandedKey"
              v-bind="$attrs"
              @node-click="handleNodeClick"
              v-on="$listeners"
            >
              <template slot-scope="{ node }">
                <span>{{ node.label }}</span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-option>
    </el-select>
    <!-- </el-tooltip> -->
  </div>
</template>

<script>
import { selectByParentId } from '@/api/specialFund/category.js'
import { authTreeData } from '@/api/gever/common.js'
export default {
  name: 'CategortTreeSelect',
  props: {
    /* 配置项 */
    props: {
      type: Object,
      default: () => {
        return {
          value: 'id', // ID字段名
          label: 'text', // 显示名称
          children: 'children', // 子级字段名
          isLeaf: 'isLeaf'
        }
      }
    },
    value: {
      type: String,
      default: () => { return null }
    },
    /* 可清空选项 */
    clearable: {
      type: Boolean,
      default: () => { return true }
    },
    /* 自动收起 */
    accordion: {
      type: Boolean,
      default: () => { return false }
    },
    title: {
      type: String,
      default: () => { return '' }
    },
    areaCode: {
      type: String,
      default: () => { return '' }
    },
    categoryId: {
      type: String,
      default: () => { return '' }
    },
    placeholder: {
      type: String,
      default: () => { return '' }
    },
    /* 是否查询失效 */
    isStatus: {
      type: Boolean,
      default: () => { return false }
    }
  },
  data () {
    return {
      dropdownVisible: false, // 控制下拉显示
      valueId: this.categoryId, // 初始值
      valueTitle: this.title == ' ' ? '' : this.title,
      defaultExpandedKey: [],
      showOuterTooltip: false,
      code: this.areaCode,
      options: [],
      showExternalTooltip: false, // 控制外部 tooltip 显示状态
      isMouseInTreeArea: false // 是否鼠标在树区域内
    }
  },
  watch: {
    categoryId () {
      this.valueId = this.categoryId
      this.valueTitle = this.title == ' ' ? '' : this.title

      this.initHandle()
    }
  },
  mounted () {
    if (this.areaCode == '') {
      this.authTreeData()
    } else {
      this.selectByParentId(this.areaCode)
    }
    this.initHandle()
  },
  methods: {
    setOptionWidth (event) {
      const empty = document.getElementsByClassName('el-select-dropdown__empty')
      const select = this.$refs._selectRef
      const selectDropdown = select.$el.querySelectorAll('.el-select-dropdown')
      selectDropdown[0].style.minWidth = event.srcElement.clientWidth + 'px'
      empty[0] && (empty[0].style.width = event.srcElement.clientWidth + 'px')
      this.elSelectWidth = this.multiple
        ? parseFloat(event.srcElement.style.maxWidth)
        : event.srcElement.clientWidth
    },
    authTreeData () {
      authTreeData().then(res => {
        this.code = res.data[0].code
        this.selectByParentId(res.data[0].code)
      })
    },
    selectByParentId (areaCode) {
      const params = {
        parentId: 'root',
        areaCode: areaCode,
        status: this.isStatus ? 1 : null
      }
      selectByParentId(params).then(res => {
        this.options = []
        res.data.map((cur) => {
          this.options.push({
            id: cur.id,
            state: 'closed',
            text: cur.categoryCode + ' ' + cur.categoryName,
            categoryName: cur.categoryName,
            parentCode: cur.categoryCode,
            name: cur.categoryName,
            parentId: cur.parentId,
            isLeaf: cur.isLeaf
          })
        })
      }).finally(() => {
      })
    },
    // 鼠标离开树区域
    handleTreeAreaLeave () {
      this.isMouseInTreeArea = false
      this.showExternalTooltip = !!this.valueTitle
    },

    // 鼠标进入 select 输入框区域
    handleExternalTooltipEnter () {
      if (!this.isMouseInTreeArea) {
        this.showExternalTooltip = !!this.valueTitle
      }
    },

    // 鼠标离开 select 输入框区域
    handleExternalTooltipLeave () {
      this.showExternalTooltip = false
    },
    // 鼠标进入树区域
    handleTreeAreaEnter () {
      this.isMouseInTreeArea = true
      this.showExternalTooltip = false // 进入树区域不显示外部 tooltip
    },
    // 下拉菜单打开/关闭时触发
    handleSelectVisibleChange (open) {
      if (!open) {
        // 下拉关闭时判断是否要显示外部 tooltip
        this.showExternalTooltip = this.isMouseInTreeArea
      }

      this.dropdownVisible = open
    },
    // 初始化值
    initHandle () {
      // if (this.valueId) {
      //   this.valueTitle = this.$refs.selectTree.getNode(this.valueId).data[this.props.label] // 初始化显示
      //   this.$refs.selectTree.setCurrentKey(this.valueId) // 设置默认选中
      //   this.defaultExpandedKey = [this.valueId] // 设置默认展开
      // }
      this.$nextTick(() => {
        const scrollWrap = document.querySelectorAll('.el-scrollbar .el-select-dropdown__wrap')[0]
        const scrollBar = document.querySelectorAll('.el-scrollbar .el-scrollbar__bar')
        scrollWrap.style.cssText = 'margin: 0px; max-height: none; overflow: hidden;'
        scrollBar.forEach(ele => ele.style.width = 0)
      })
    },
    // 切换选项
    handleNodeClick (node) {
      if (node.isLeaf) {
        this.valueTitle = node[this.props.label]
        this.valueId = node[this.props.value]
        this.$emit('getValue', this.valueId, node.parentCode, node.categoryName)
        this.$emit('input', this.valueId)
        this.defaultExpandedKey = []
        console.log('关闭')
        this.$set(this, 'dropdownVisible', false)
        this.$refs.select.handleClose() // 手动关闭
      }
    },
    // 清除选中
    clearHandle () {
      this.valueTitle = ''
      this.valueId = null
      this.defaultExpandedKey = []
      this.clearSelected()
      this.$emit('getValue', '')
      this.$emit('input', '')
    },
    /* 清空选中样式 */
    clearSelected () {
      const allNode = document.querySelectorAll('#tree-option .el-tree-node')
      allNode.forEach((element) => element.classList.remove('is-current'))
    },
    loadChildNode (node, resolve) {
      selectByParentId({ parentId: node.data.id, areaCode: this.code, status: this.isStatus ? 1 : null }).then((res) => {
        const curTree = []
        res.data.map((cur) => {
          curTree.push({
            id: cur.id,
            state: 'closed',
            text: cur.categoryCode + ' ' + cur.categoryName,
            categoryName: cur.categoryName,
            parentCode: cur.categoryCode,
            name: cur.categoryName,
            parentId: cur.parentId,
            isLeaf: cur.isLeaf
          })
        })
        resolve(curTree)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* 控制选择器宽度 */
::v-deep .el-select {
  width: 100%;
}

/* 下拉菜单宽度控制 */
::v-deep .tree-select-popper {
  width: var(--el-select-width) !important;
}

/* 选项容器样式 */
::v-deep .el-select-dropdown__item {
  padding: 0 !important;
  height: auto !important;
}

/* 滚动容器样式 */
.tree-scroll-container {
  max-height: 300px;
  width: var(--el-select-width);
  overflow: auto;
}

/* 内部滚动区域 */
.tree-inner-container {
  width: 100%;
  // overflow-x: auto;
  // overflow-y: auto;
  max-width: 500px;
}

/* 树组件样式 */
::v-deep .el-tree {
  min-width: 100%;
  width: max-content !important;
}

/* 树节点样式 */
::v-deep .el-tree-node__content {
  white-space: nowrap;
  font-weight: normal !important;
}

/* 移除默认滚动行为 */
::v-deep .el-select-dropdown__wrap {
  max-height: none !important;
}

::v-deep .el-scrollbar__wrap {
  //overflow: hidden !important;
}

/* 确保树节点文本使用正常字重 */
::v-deep .el-tree-node__label {
  font-weight: normal;
}

/* 选中状态下保持正常字重 */
::v-deep .el-tree-node.is-current > .el-tree-node__content {
  font-weight: normal !important;
}

.el-tooltip__popper {
  z-index: 9999 !important;
}
/* 如果需要进一步调整 el-tree 的样式 */
::v-deep .el-tree-node__content {
  padding-right: 10px; /* 增加一些内边距避免内容过于贴近滚动条 */
}

/* 确保 tree 的节点文本不换行，并支持横向滚动 */
::v-deep .tree-select-popper .el-tree-node__content {
  white-space: nowrap;
}

/* 设置树的基础样式 */
::v-deep .el-tree {
  width: fit-content;
  min-width: 100%;
}
</style>
