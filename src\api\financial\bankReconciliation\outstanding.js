import request from '@/utils/request'

/**
 * @name:分页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/page',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load(params) {
  return request({
    url: `/financial/cashier/cashierOutstandingAccount/loadOutstanding`,
    method: 'get',
    params,
    withCredentials: true
  })
}
/**
 * @name:新增页面初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function addInit(params) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/add/init',
    params,
    method: 'get',
    withCredentials: true
  })
}
/**
 * @name:编辑页面初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function editInit(params) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/edit/init',
    params,
    method: 'get',
    withCredentials: true
  })
}
/**
 * @name:删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteOutstanding(data) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/deleteOutstanding',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(params, data) {
  return request({
    url: '/financial/cashier/cashierOutstandingAccount/saveX',
    method: 'post',
    data,
    params,
    payload: true
  })
}