
const components = require.context('./', true, /index.js$/)
// 定义 install 方法

const install = function(Vue) {
  components.keys().forEach(fileName => {
    const config = components(fileName)
    const { name } = config.default
    if (name) {
      Vue.component(name, config.default || config)
    }
  })
}

if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

export default {
  // 导出的对象必须具备一个 install 方法
  install,
  // 组件列表
  ...components
}

