
import request from '@/utils/request'

export function listByUserDiv() {
  return request({
    url: `/financial/bookTypes/listByUserDiv`,
    method: 'get',
    withCredentials: true
  })
}

export function getBatchAuthOrgList(data) {
  return request({
    url: '/financial/books/getBatchAuthOrgList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

export function batchSaveBooks(params,data) {
  return request({
    url: '/financial/books/batchSaveBooks',
    method: 'post',
    params,
    data: data,
    payload: true,
    timeout: 180000
  })
}