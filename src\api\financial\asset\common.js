/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-12-02 19:31:59
 * @LastEditTime: 2024-07-27 14:30:52
 * @LastEditors: jzh-8975 <EMAIL>
 * @Description:
 * @FilePath: \gever-zhxc-ui\src\api\financial\asset\common.js
 * ^-^
 */
import request from '@/utils/request'

export function addInit(params, module) {
  return request({
    url: `/financial/asset/${module}/add/init`,
    method: 'get',
    params
  })
}

export function editInit(params, module) {
  return request({
    url: `/financial/asset/${module}/edit/init`,
    method: 'get',
    params
  })
}

export function checkBeforeGenerate(data, module) {
  return request({
    url: `/financial/asset/${module}/checkBeforeGenerate`,
    method: 'post',
    data: data
  })
}

export function generateVoucher(data, module) {
  return request({
    url: `/financial/asset/${module}/generateVoucher`,
    method: 'post',
    data: data
  })
}
