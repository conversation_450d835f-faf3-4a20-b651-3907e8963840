/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-22 11:17:09
 * @LastEditTime: 2022-05-16 09:33:59
 * @LastEditors: Peng Xiao
 * @Description:工作流接口
 * @FilePath: \gever-zhxc-ui\src\api\workflow.js
 * ^-^
 */

import request from '@/utils/request'

const baseUrl = '/workflow'
const mock = false
/**
 * @name:获取待办事项
 * @description:
 * @param {*} params
 * @param {*} data
 * @return {*}
 * @msg:
 */
export function getTodoList({ params, data }) {
  return request({
    url: `${baseUrl}/getTodoList`,
    method: 'post',
    params,
    data,
    withCredentials: true
  })
}

/**
 * @name:获取已办事项
 * @description:
 * @param {*} params
 * @param {*} data
 * @return {*}
 * @msg:
 */
export function getHasDoneTaskList({ params, data }) {
  return request({
    url: `${baseUrl}/getHasDoneTaskList`,
    method: 'post',
    params,
    data,
    withCredentials: true
  })
}

/**
 * @name:获取跟踪日志列表
 * @description:
 * @param {*} data
 * @return {*}
 * @msg:
 */
export function getProcessTraceList(data) {
  return request({
    url: `${baseUrl}/getProcessTraceList`,
    method: 'post',
    data,
    withCredentials: true
  })
}


