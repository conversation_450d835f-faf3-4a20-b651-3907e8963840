import request from '@/utils/request'
/**
 * @name: 系统配置列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadConfigList(query) {
  return request({
    url: '/system/config/page',
    method: 'get',
    params: query
  })
}
/**
 * @name: 系统配置保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveConfig(query) {
  return request({
    url: '/system/config/save',
    method: 'post',
    data: query
  })
}
/**
 * @name: 系统配置编辑
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function updateConfig(query) {
  return request({
    url: '/system/config/update',
    method: 'post',
    data: query
  })
}
/**
 * @name: 系统配置删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteConfig(query) {
  return request({
    url: '/system/config/delete',
    method: 'post',
    data: query
  })
}
/**
 * @name: 禁用接口
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function setNotValid(query) {
  return request({
    url: '/system/config/loseConfig',
    method: 'post',
    data: query
  })
}
/**
 * @name: 启用接口
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function setValid(query) {
  return request({
    url: '/system/config/takeConfig',
    method: 'post',
    data: query
  })
}
/**
 * @name: 编辑查看数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function settingLoad(query, data) {
  return request({
    url: `/system/config/load/${query}?_ood_=${data._ood_}`,
    method: 'get',
    query: data
  })
}
/**
 * @name: 租户管理列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */

/**
 * @name: 根据code获取系统配置
 * @param {*} code
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function loadSettingByCode(code) {
  return request({
    url: `/system/config/getByCode?code=${code}`,
    method: 'get'
  })
}

export function loadTenantList(query) {
  return request({
    url: '/tenant/page',
    method: 'post',
    params: query
  })
}
/**
 * @name: 租户保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveTenant(query) {
  return request({
    url: '/tenant/add',
    method: 'post',
    data: query
  })
}
/**
 * @name: 租户删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteTenant(query) {
  return request({
    url: `/tenant/delete/${query}`,
    method: 'post'
  })
}
/**
 * @name: 禁用接口
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function setEnabled(query) {
  return request({
    url: `/tenant/disable/${query}`,
    method: 'post'
  })
}
/**
 * @name: 启用接口
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function openEnablad(query) {
  return request({
    url: `/tenant/enable/${query}`,
    method: 'post'
  })
}
/**
 * @name: 修改用户
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function setTenant(query) {
  return request({
    url: '/tenant/modify',
    method: 'post',
    params: query
  })
}
/**
 * @name: 查看用户
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function viewTenant(query) {
  return request({
    url: `/tenant/view/${query}`,
    method: 'get'
  })
}
/**
 * @name: 系统日志管理树
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function logMgtTree(params) {
  return request({
    url: '/system/log/tree',
    method: 'get',
    params: params
  })
}

/**
 * @name: 系统日志查询树
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function logSearchTree(params) {
  return request({
    url: '/system/logSearch/tree',
    method: 'get',
    params: params
  })
}
/**
 * @name: 日志管理列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadLogList(query) {
  return request({
    url: '/system/log/page',
    method: 'get',
    params: query
  })
}

/**
 * @name: 日志查询列表显示
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function loadLogSearchList(query) {
  return request({
    url: '/system/logSearch/page',
    method: 'get',
    params: query
  })
}
/**
 * @name: 日志管理查看
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function logLoad(data, query) {
  return request({
    url: '/system/log/load/' + data + '?_ood_=' + query._ood_,
    method: 'get',
    query: query
  })
}
/**
 * @name: 日志管理删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteLog(query) {
  return request({
    url: '/system/log/delete',
    method: 'post',
    data: query
  })
}
/**
 * @name: 获取版本列表数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadVersionList(query) {
  return request({
    url: '/system/version/page',
    params: query,
    method: 'get'
  })
}
/**
 * @name: 新增版本保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveVersion(query) {
  return request({
    url: '/system/version/save',
    data: query,
    method: 'post'
  })
}
/**
 * @name: 版本编辑查看数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function versionLoad(query, data) {
  return request({
    url: `/system/version/load/${query}?_ood_=${data._ood_}`,
    method: 'get',
    query: data
  })
}
/**
 * @name: 版本删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteVersion(query) {
  return request({
    url: '/system/version/delete',
    data: query,
    method: 'post'
  })
}
/**
 * @name: 流程定义文件列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadProcessTreeData(query) {
  return request({
    url: '/workflow/processDefinition/tree',
    params: query,
    method: 'get'
  })
}
/**
 * @name: 流程定义编辑查看
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadProcess(query) {
  return request({
    url: '/workflow/processDefinition/load/' + query,
    method: 'get'
  })
}
/**
 * @name: 流程定义列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadProcessList(query) {
  console.info(query)
  return request({
    url: '/workflow/processDefinition/page',
    params: query,
    method: 'get'
  })
}
/**
 * @name: 新增编辑流程保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveProcess(query) {
  return request({
    url: '/workflow/processDefinition/save',
    data: query,
    method: 'post'
  })
}
/**
 * @name: 删除流程
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteProcess(query) {
  return request({
    url: '/workflow/processDefinition/delete',
    data: query,
    method: 'post'
  })
}
/**
 * @name: 复制流程保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function copyProcess(query) {
  return request({
    url: '/workflow/processDefinition/copyProcess',
    data: query,
    method: 'post'
  })
}
/**
 * @name: 节点配置接口
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getNodeList(query) {
  return request({
    url: '/workflow/processDefinition/getNodeList',
    params: query,
    method: 'get'
  })
}

/**
 * @name: 节点配置刷新保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveNodeList(query) {
  return request({
    url: '/workflow/processDefinition/node',
    data: query,
    method: 'post',
    payload: true
  })
}
/**
 * @name: 节点配置刷新缓存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function refreshNode(processKey) {
  return request({
    url: '/workflow/processDefinition/node/refresh/' + processKey,
    method: 'get'
  })
}

export function loadBankList(query) {
  return request({
    url: '/system/bankInfo/page',
    method: 'get',
    params: query
  })
}

export function loadBank(id) {
  return request({
    url: `/system/bankInfo/load/${id}`,
    method: 'GET'
  })
}

export function saveBank(data) {
  return request({
    url: '/system/bankInfo/save',
    method: 'post',
    data: data
  })
}

export function deleteBank(query) {
  return request({
    url: '/system/bankInfo/delete',
    method: 'post',
    data: query
  })
}

export function loadValidFlag() {
  return request({
    url: '/json/isvalid.json',
    method: 'get'
  })
}

/**
 * @name: 自动部署流程
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function autoDeploy(query) {
  return request({
    url: '/workflow/processDefinition/autoDeploy',
    method: 'get',
    params: query
  })
}

/**
 * @name: 地区配置列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function configSpecialPage(query) {
  return request({
    url: '/system/configSpecial/page',
    method: 'get',
    params: query
  })
}

/**
 * @name: 系统配置选项
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getConfigList(query) {
  return request({
    url: '/system/config/getConfigList',
    method: 'get',
    params: query
  })
}

/**
 * @name: 保存地区配置
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function configSpecialSave(query) {
  return request({
    url: '/system/configSpecial/save',
    method: 'post',
    data: query
  })
}

/**
 * @name: 查看地区配置
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function configSpecialLoad(id) {
  return request({
    url: `/system/configSpecial/load/${id}`,
    method: 'get'
  })
}

/**
 * @name: 删除地区配置
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function configSpecialDel(query) {
  return request({
    url: '/system/configSpecial/delete',
    method: 'post',
    data: query
  })
}

/**
 * @name: 禁用地区配置接口
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function configSpecialNotValid(query) {
  return request({
    url: '/system/configSpecial/loseConfig',
    method: 'post',
    data: query
  })
}
/**
 * @name: 启用地区配置接口
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function configSpecialValid(query) {
  return request({
    url: '/system/configSpecial/takeConfig',
    method: 'post',
    data: query
  })
}
