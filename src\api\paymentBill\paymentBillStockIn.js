/*
 * @Description: 
 * @Version: 
 * @Author: luozc
 * @Date: 2023-09-20 10:18:33
 * @LastEditors: luozc
 * @LastEditTime: 2023-10-10 10:41:58
 */
import request from '@/utils/request'

/**
 * 分页查询支付凭证入库信息列表
 * @param {*} data 
 * @returns 
 */
export function getPaymentBillStockList(data) {
    return request({
        url: '/paymentbill/paymentBillStockIn/page',
        method: 'post',
        data
    })
}

/**
 * 分页查询支付票据入库记录信息列表
 * @param {*} data 
 * @returns 
 */
 export function getBillStockRecordList(data) {
    return request({
        url: '/paymentbill/paymentBillStockIn/pageList',
        method: 'post',
        data
    })
}

/**
 * 获取支付凭证入库详情信息
 * @param {*} id 
 * @returns 
 */
export function getStorkInDetail(id) {
    return request({
        url: `/paymentbill/paymentBillStockIn/get/${id}`,
        method: 'get'
    })
}

/**
 * 删除支付凭证入库信息
 * @param {*} id 
 * @returns 
 */
export function removeStockIn(id) {
    return request({
        url: `/paymentbill/paymentBillStockIn/delete?id=${id}`,
        method: 'post'
    })
}

/**
 * 保存
 * @param {*} data 
 * @returns 
 */
export function saveBillStockIn(data){
    return request({
        url: '/paymentbill/paymentBillStockIn/save',
        method: 'post',
        data,
        payload: true
    })
}