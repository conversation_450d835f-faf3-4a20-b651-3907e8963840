/*
 * @Descripttion:会计科目接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-26 11:31:32
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2024-07-03 18:24:51
 */

import request from '@/utils/request'

// 封存 取消封存
export function sealOrUnseal (data, type) {
  return request({
    url: `/financial/books/basedata/accountingItem/${type}`,
    method: 'post',
    data,
    withCredentials: true
  })
}


/**
 * @name:获取基础设置的科目类型
 * @param {Object} data
 * {
    itemCode: '编号'
    itemTitle: '名称'
    booksId: '登陆的账套id'
    accountingItemTypeId: '账套类型id'
    parentId: '父级id'
    page: 1
    rows: 20
  }
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page (data) {
  return request({
    url: '/financial/books/basedata/accountingItem/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:根据bookId和itemTypeId获取会计科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getAccountingItemList (data) {
  return request({
    url: '/financial/books/basedata/accountingItem/getAccountingItemList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:根据bookId和itemTypeId获取会计科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBaseItemList (data) {
  return request({
    url: '/financial/booktypes/basedata/item/getBaseItemList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
/**
 * @name:根据bookId和leafItem获取出纳科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getCashierItemList (data) {
  return request({
    url: '/financial/books/basedata/accountingItem/getCashierItemList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取会计科目树
 * @param {String} booksId 登录的账套id
 * @param {String} nodeType
 * @param {String} itemTypeId 账套类型id
 * @param {String} id 父节点id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function tree ({ booksId, nodeType, itemTypeId, id = '0' }) {
  return request({
    url: '/financial/books/basedata/accountingItem/tree',
    method: 'get',
    params: { booksId, nodeType, itemTypeId, id },
    withCredentials: true
  })
}

/**
 * @name:获取会计科目新增时时的初始化信息
 * @param {*} id 详情的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function init (params) {
  return request({
    url: `/financial/books/basedata/accountingItem/add/init`,
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:获取会计科目详情
 * @param {*} id 详情的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load ({ id }) {
  return request({
    url: `/financial/books/basedata/accountingItem/load/${id}`,
    method: 'get',
    params: {},
    withCredentials: true
  })
}

/**
 * @name:保存会计科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save (data) {
  return request({
    url: `/financial/books/basedata/accountingItem/save`,
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:删除会计科目
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX (data) {
  return request({
    url: `/financial/books/basedata/accountingItem/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:批量设置辅助核算
 * @param {*} params
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveAssist ({ params, data }) {
  return request({
    url: `/financial/books/basedata/accountingItem/saveAssist`,
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:批量设置辅助核算
 * @param {*} params
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveImportTemp (data) {
  return request({
    url: `/financial/books/basedata/accountingItem/saveImportTemp`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:出纳科目树
 * @param {*} params
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cashierTree (params) {
  return request({
    url: `/financial/books/basedata/accountingItem/cashierTree`,
    method: 'get',
    params,
    withCredentials: true
  })
}
/**
 * @name:出纳科目树
 * @param {*} params
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cashierTreeOnce (params) {
  return request({
    url: '/financial/books/basedata/accountingItem/cashierTreeOnce',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * 检验子级科目是否被引用
 * @param {*} data 
 * @returns 
 */
export function checkItemUseForFirstSonItem (data) {
  return request({
    url: `/financial/books/basedata/accountingItem/checkItemUseForFirstSonItem`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * 校验本级科目是否被引用
 * @param {*} data 
 * @returns 
 */
export function checkItemUseForCurrentItem (data) {
  return request({
    url: `/financial/books/basedata/accountingItem/checkItemUseForCurrentItem`,
    method: 'post',
    data,
    withCredentials: true
  })
}