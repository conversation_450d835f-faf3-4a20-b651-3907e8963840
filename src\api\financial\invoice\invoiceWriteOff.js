/*
 * @Descripttion: 
 * @version: 
 * @Author: 未知
 * @Date: 2023-10-09 16:59:46
 * @LastEditors: Andy
 * @LastEditTime: 2023-11-01 09:21:36
 */
import request from '@/utils/request'

/*
export function demo(data) {
  return request({
    url: ``,
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}
*/

/*
export function demo(params) {
  return request({
    url: ``,
    method: 'get',
    params,
    // payload: true,
  })
}
*/

/* 列表 */
export function pageWriteOff(data) {
  return request({
    url: '/invoice/invoiceDetail/page',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 核销 */
export function verify(data) {
  return request({
    url: '/invoice/invoiceDetail/verify',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 取消核销 */
export function cancelVerify(data) {
  return request({
    url: `/invoice/invoiceDetail/cancelVerify`,
    method: 'post',
    data
  })
}

/* 复核 */
export function review(data) {
  return request({
    url: `/invoice/invoiceDetail/review`,
    method: 'post',
    data
  })
}

/* 取消复核 */
export function cancelReview(data) {
  return request({
    url: `/invoice/invoiceDetail/cancelReview`,
    method: 'post',
    data
  })
}
