/*
 * @Descripttion:
 * @version:
 * @Author: Tanronglong
 * @Date: 2021-10-23 11:16:27
 * @LastEditors: Tanronglong
 * @LastEditTime: 2021-11-08 17:15:41
 */
import request from '@/utils/request'
// 选择需要变更的合同数据
export function pageContractForChanges(data) {
  return request({
    url: '/contract/contractChanges/pageContractForChanges',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 选择需要变更的合同确定保存    /contract/contractChanges/load
export function contractChanges(data) {
  return request({
    url: '/contract/contractChanges/load',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 选择修改合同菜单提交
export function submitChange(data) {
  return request({
    url: '/contract/workflow/submit',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同变更数据 /contract/contractRegistration/changeRecordData
export function changeRecordData(data) {
  return request({
    url: '/contract/contractRegistration/changeRecordData',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 删除 /contract/contractChanges/deleteChanges
export function deleteChanges(data) {
  return request({
    url: '/contract/contractChanges/deleteChanges',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
