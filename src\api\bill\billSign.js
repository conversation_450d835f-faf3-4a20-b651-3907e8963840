import request from '@/utils/request'
// 票据领用列表数据
export function recipientList(data) {
  return request({
    url: '/bill/billSign/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 票据领用修改和新增
export function recipientRedact(data) {
  return request({
    url: '/bill/billSign/save',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 票据领用删除
export function recipientDelete(data) {
  return request({
    url: '/bill/billSign/delete',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 票据领用查看
export function recipientView(id) {
  return request({
    url: `/bill/billSign/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}
