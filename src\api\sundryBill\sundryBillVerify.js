import request from '@/utils/request'
// 票据回收  首页表格数据
export function getBillVerifyList(data) {
  return request({
    url: '/bill/sundryBillVerify/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 获取年份
export function getBillVerifyYearList(data) {
  return request({
    url: '/bill/sundryBillVerify/getYearList',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 提交
export function BillVerifySubmit(data) {
  return request({
    url: '/bill/sundryBillVerify/submit',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//取消提交
export function BillVerifyCancelSubmit(data) {
  return request({
    url: '/bill/sundryBillVerify/cancelSubmit',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//核销
export function BillVerify(data) {
  return request({
    url: '/bill/sundryBillVerify/verify',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//取消核销
export function BillCancelVerify(data) {
  return request({
    url: '/bill/sundryBillVerify/cancelVerify',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
//详情
export function BillVerifyDetail(data) {
  return request({
    url: '/bill/sundryBillVerify/pageDetail',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
/**
 * @name:获取票据核销列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function pageBillDetail(data) {
  return request({
    url: '/bill/sundryBillVerify/pageBillDetail',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
