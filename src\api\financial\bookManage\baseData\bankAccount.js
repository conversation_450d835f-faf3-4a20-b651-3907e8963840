/*
 * @Descripttion:银行账户接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-29 16:40:25
 * @LastEditors: Pengxiao
 * @LastEditTime: 2022-09-07 15:08:14
 */

import request from '@/utils/request'

/**
 * @name:银行账户列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/books/basedata/bankaccount/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取关联账户详情
 * @param {String} id 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/books/basedata/bankaccount/load/${id}`,
    method: 'get',
    params: {},
    withCredentials: true
  })
}

/**
 * @name:保存关联账户类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/books/basedata/bankaccount/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:删除关联账户类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/books/basedata/bankaccount/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:查询账套银行数据
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBankAccount(params) {
  return request({
    url: `/financial/books/basedata/bankaccount/getBankAccount`,
    method: 'get',
    params,
    withCredentials: true
  })
}
