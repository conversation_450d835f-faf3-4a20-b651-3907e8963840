/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-11-24 09:38:03
 * @LastEditTime: 2022-09-28 15:52:09
 * @LastEditors: Pengxiao
 * @Description:
 * @FilePath: \b-ui\src\api\financial\asset\adjust.js
 * ^-^
 */

import request from '@/utils/request'

/**
 * @name:获取资产入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/asset/assetAdjust/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/asset/assetAdjust/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:保存编辑结果
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/asset/assetAdjust/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:保存并入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveAndPosting({ data, params }) {
  return request({
    url: `/financial/asset/assetAdjust/saveAndPosting`,
    method: 'post',
    data,
    params,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteWithBooks(data) {
  return request({
    url: `/financial/asset/assetAdjust/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function posting(data) {
  return request({
    url: `/financial/asset/assetAdjust/posting`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消资产入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unPosting(data) {
  return request({
    url: `/financial/asset/assetAdjust/unPosting`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:审核
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function approval(data) {
  return request({
    url: `/financial/asset/assetAdjust/submit`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消审核
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unApproval(data) {
  return request({
    url: `/financial/asset/assetAdjust/unSubmit`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消凭证
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancelGenerateVoucher(data) {
  return request({
    url: `/financial/asset/assetAdjust/cancelGenerateVoucher`,
    method: 'post',
    data,
    withCredentials: true
  })
}
export function addInit(data) {
  return request({
    url: `/financial/asset/assetAdjust/addInit`,
    method: 'post',
    data,
    withCredentials: true
  })
}
