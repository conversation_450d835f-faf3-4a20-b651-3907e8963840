import request from '@/utils/request'

/**
 * @name:获取当前用户有权限地区机构树(懒加载)
 * @param {String}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function authTreeData(params) {
  return request({
    url: '/system/area/authTreeData',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:获取地区机构树(懒加载)
 * @param {String}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function treeData(params) {
  return request({
    url: '/system/area/treeData',
    method: 'get',
    params,
    withCredentials: true
  })
}

// 根据code获取地区详情
export function loadArea(code) {
  return request({
    url: '/system/area/load/code/' + code,
    method: 'get',
    withCredentials: true
  })
}