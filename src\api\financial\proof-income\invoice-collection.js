/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-09-23 08:41:34
 * @LastEditTime: 2022-09-26 10:20:17
 * @LastEditors: Peng Xiao
 * @Description:发票领用
 * @FilePath: \b-ui\src\api\financial\proof-income\invoice-collection.js
 * ^-^
 */
import request from "@/utils/request";

export const baseUrl = "/financial/invoiceCollection";

/**
 * @name:列表
 * @param {String} orgId 组织机构
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: `${baseUrl}/page`,
    method: "post",
    data,
    withCredentials: true,
  });
}
export function load({id}) {
  return request({
    url: `${baseUrl}/load/${id}`,
    method: "get",
    withCredentials: true,
  });
}
/**
 * @name:保存
 * @param {String} orgId 机构id（和booksId不能同时为空）
 * @param {String} invoiceCode 发票代码
 * @param {String} booksId 账套id（和orgId不能同时为空）
 * @param {String} collectDate 领用时间
 * @param {String} numberLength 号码长度
 * @param {String} beginSeq 开始序号
 * @param {String} endSeq 结束序号
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `${baseUrl}/save`,
    method: "post",
    data,
    withCredentials: true,
  });
}
/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `${baseUrl}/delete`,
    method: "post",
    data,
    withCredentials: true,
  });
}

/**
 * @name:取消领用
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function receiveCancel(data) {
  return request({
    url: `${baseUrl}/receiveCancel`,
    method: "post",
    data,
    withCredentials: true,
  });
}

/**
 * @name:发票作废-作废
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function invoiceSave(data) {
  return request({
    url: '/financial/invoiceInvalid/save',
    method: "post",
    data,
    withCredentials: true,
  });
}

/**
 * @name:发票作废-列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function invoicePage(data) {
  return request({
    url: '/financial/invoiceInvalid/page',
    method: "post",
    data,
    withCredentials: true,
  });
}

/**
 * @name:发票作废-查看
 * @param {*} params
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function invoiceLoad(id) {
  return request({
    url: `/financial/invoiceInvalid/load/${id}`,
    method: "get",
    withCredentials: true,
  });
}

