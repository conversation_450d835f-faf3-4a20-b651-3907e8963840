<template>
  <div v-loading="loading" class="gever-form">
    <div class="module-title">
      <span class="label-title">
        {{ $t(`申请信息`) }}
      </span>
      <el-button
        v-show="applicationInfo.procInstId && showProcessBtn"
        type="primary"
        plain
        round
        icon="el-icon-plus"
        style="float: right"
        @click="handlePreviewProcess"
      >
        {{ $t('预览审批流程') }}
      </el-button>
    </div>
    <el-form ref="_formRef" :model="applicationInfo" label-width="120px">
      <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
        <el-col :span="12">
          <el-form-item
            :label="$t('日期')"
            prop="creationTime"
            :rules="{
              required: true,
              message: '日期必填',
              trigger: ['blur', 'change']
            }"
          >
            <el-date-picker
              v-model="applicationInfo.creationTime"
              :disabled="isDisabled"
              value-format="yyyy-MM-dd"
              type="date"
              style="width: 100%"
              placeholder="选择日期时间"
              align="right"
              :picker-options="{
                disabledDate: (time) => {
                  // 获取当前年份
                  const currentYear = moment(
                    applicationInfo.creationTime
                  ).year()

                  // 获取时间的年份
                  const year = new Date(time).getFullYear()

                  const isHasBudget = this.receiverList.some((cur) => cur.parentKey && cur.budgetItemId)

                  // 如果年份在当前年份之前或之后，则禁用
                  return ((isHasBudget || isRefund) && (year < currentYear || year > currentYear ))
                },
                shortcuts: [
                  {
                    text: '今天',
                    onClick(picker) {
                      picker.$emit('pick', new Date())
                    }
                  }
                ]
              }"
              @change="handleCreationTime"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('单据数')"
            prop="docNumber"
            :rules="{
              required: true,
              message: '单据数必填',
              trigger: ['blur', 'change']
            }"
          >
            <gever-input-number
              v-model="applicationInfo.docNumber"
              style="width: 100%"
              :max="999"
              type="natural"
              :disabled="isDisabled"
              :controls="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="orgName" label="申请单位">
            <gever-input
              v-model="applicationInfo.orgName"
              disabled
              class="del-arrow"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="submitUserName" label="申请人">
            <gever-input
              v-model="applicationInfo.submitUserName"
              disabled
              class="del-arrow"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('是否使用预算')" prop="useBudgetFlag">
            <gever-select
              v-model="applicationInfo.useBudgetFlag"
              :options="[
                {
                  id: 1,
                  text: '是'
                },
                {
                  id: 0,
                  text: '否'
                }
              ]"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('业务年份')" prop="businessYear">
            <el-date-picker
              v-model="applicationInfo.businessYear"
              :disabled="true"
              type="year"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('支出类型')"
            prop="expenditureType"
            :rules="{
              required: true,
              message: '支出类型必填',
              trigger: ['blur', 'change']
            }"
          >
            <el-row :gutter="20" type="flex">
              <gever-select
                v-model="applicationInfo.expenditureType"
                style="flex: 1"
                collapse-tags
                :options="mapOptins.expenditureType"
                :disabled="isDisabled || isRefund"
                @change="getApprovalSetting"
                @current-change="handleExpenseTypeChange"
              />
              <el-button
                v-if="
                  applicationInfo.expenditureType ==
                    '909440E63C6D11B2B92E7263D79FDBFF' &&
                    !['view', 'refund', 'invalid'].includes(loadType)
                "
                type="primary"
                @click="handleDialog"
              >
                选择
              </el-button>
              <el-button
                v-if="
                  applicationInfo.expenditureType ==
                    '909440E63C6D11B2B92E7263D79FDBFF' &&
                    ['view', 'refund', 'invalid'].includes(loadType)
                "
                type="primary"
                @click="handleDialog"
              >
                查看
              </el-button>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('金额')"
            prop="amount"
            :rules="{
              required: true,
              message: '金额必填',
              trigger: ['blur', 'change']
            }"
          >
            <gever-input-number
              v-model="applicationInfo.amount"
              style="width: 100%"
              :controls="false"
              :min="
                isRefund || applicationInfo.returnFlag
                  ? -applicationInfo.sourceAmount
                  : 0
              "
              :max="isRefund || applicationInfo.returnFlag ? -1 : 9999999999.99"
              :disabled="isDisabled || amountDisable"
              @blur="getApprovalSetting"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('结算方式')"
            prop="settlementMethod"
            :rules="{
              required: true,
              message: '请选择结算方式',
              trigger: ['blur', 'change']
            }"
          >
            <gever-select
              v-model="applicationInfo.settlementMethod"
              label-prop="settleName"
              :clearable="false"
              :options="mapOptins.billingMethod"
              :disabled="isDisabled || isRefund"
              @optionClick="handletMethod"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('付款银行账号')"
            prop="payAccountId"
            :rules="{
              required: true,
              message: '请选择付款银行账号',
              trigger: ['blur', 'change']
            }"
          >
            <gever-select
              v-if="loadType!='view'"
              v-model="applicationInfo.payAccountId"
              :clearable="false"
              label-prop="accountNumber"
              :options="accountList"
              :disabled="
                disabledPayAccoun ||
                  applicationInfo.payAccountId === '0' ||
                  isDisabled
              "
              @optionClick="handlePayAccoun"
              @change="handlePayAccountChange"
            />
            <gever-input
              v-if="loadType=='view'"
              v-model="applicationInfo.payAccountNumber"
              class="100%"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('审批事项名称')"
            prop="matter"
            :rules="{
              required: true,
              validator: validateSetting,
              trigger: 'change'
            }"
          >
            <gever-select
              v-model="applicationInfo.matter"
              v-loading="matterLoading"
              clearable
              placeholder="填写金额、支出类型、及付款银行账号后获取"
              :options="matterOptions"
              label-prop="matter"
              :disabled="
                !accountType || !applicationInfo.expenditureType || isDisabled
              "
              @change="handleChangeMatter"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('支付模式')"
            prop="payMethod"
            :rules="{
              required: true,
              message: '请选择支付模式',
              trigger: ['blur', 'change']
            }">
            <gever-select
              v-model="applicationInfo.payMethod"
              :options="mapOptins.payMethod"
              :disabled="isDisabled || curMethod.businessType != 1 || bankType != '************' || ![0, 3].includes(applicationInfo.applicationType)"
              number-key
              :clearable="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('资金来源')"
            prop="fundSrc"
            :rules="{
              required: this.applicationInfo.expenditureType == '909440E63C6D11B2B92E7263D79FDBFF',
              message: '请选择资金来源',
              trigger: ['blur', 'change']
              }"
            >
            <gever-select
              v-model="applicationInfo.fundSrc"
              :options="mapOptins.fundSrc"
              :disabled="isDisabled || isRefund"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支出申请单" prop="requisitionExpenditure">
            <gever-select
              v-model="applicationInfo.requisitionExpenditure"
              :options="requisitionExpenditureOptions"
              :disabled="isDisabled"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('报销凭证类别')" prop="reimVoucherType">
            <gever-select
              v-model="applicationInfo.reimVoucherType"
              style="flex: 1"
              :options="mapOptins.reimVoucherType"
              :disabled="isDisabled || isRefund"
              @current-change="handleReimVoucherType"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="billNo" label="发票号码">
            <gever-input
              v-model="applicationInfo.billNo"
              :disabled="
                isDisabled ||
                  !['1', '2'].includes(applicationInfo.reimVoucherType)
              "
              class="del-arrow"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('申请类型')" prop="applicationType">
            <gever-select
              ref="_applicationTypeRef"
              v-model="applicationInfo.applicationType"
              style="flex: 1"
              :options="mapOptins.applicationType"
              :disabled="isRefund || isDisabled || budgetLines"
              :clearable="false"
              @change="handleApplicationType"
              @optionClick="clickApplicationType"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('相抵票据类型')"
            prop="offsetNoteType"
            :rules="[
              {
                required: true,
                message: this.$t('请选择相抵票据类型'),
                trigger: ['blur']
              }
            ]"
          >
            <gever-select
              ref="_offsetNoteTypeRef"
              v-model="applicationInfo.offsetNoteType"
              style="flex: 1"
              :options="mapOptins.offsetNoteType"
              :clearable="false"
              :disabled="isDisabled || isOffsetNoteType"
              @optionClick="clickOffsetNoteType"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            :label="$t('资金用途')"
            prop="fundsUse"
            :rules="[
              {
                required: true,
                message: this.$t('请输入资金用途'),
                trigger: ['blur', 'change']
              }
            ]"
          >
            <el-autocomplete
              v-model.trim="applicationInfo.fundsUse"
              :title="applicationInfo.fundsUse"
              :fetch-suggestions="
                (queryString, callback) =>
                  handleGetInfoFundsUse(queryString, callback)
              "
              style="width: 100%"
              :disabled="isDisabled"
              :placeholder="isDisabled ? '' : $t('请输入资金用途')"
              :debounce="300"
              :maxlength="300"
              show-word-limit
              type="textarea"
              rows="3"
            >
              <template #default="{ item }">
                <el-row type="flex" justify="space-between" :title="item.name">
                  <span class="overflow-hidden">{{ item.name }}</span>
                  <span>
                    <el-button
                      type="text"
                      @click.stop="handleRemoveInfoFundsUse(item.id)"
                    >
                      {{ $t('删除') }}
                    </el-button>
                  </span>
                </el-row>
              </template>
            </el-autocomplete>
            <el-button
              v-if="loadType!='view'"
              :disabled="!applicationInfo.fundsUse"
              @click="handleSaveInfoFundsUse"
            >
              <span class="blue">
                {{ $t('保存') }}
              </span>
            </el-button>
            <!-- <el-autocomplete
              v-model.trim="applicationInfo.fundsUse"
              :disabled="isDisabled"
              style="width: 100%; color: #000000"
              type="textarea"
              :rows="2"
              class="inline-input"
              :fetch-suggestions="querySearch"
              placeholder="请输入内容"
              :trigger-on-focus="false"
              :maxlength="300"
              show-word-limit
              @select="handleSelectFundsUse"
            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            :label="$t('申请详情')"
            :rules="[
              {
                required: true,
                message: this.$t('请输入申请详情'),
                trigger: ['blur', 'change']
              }
            ]"
            prop="details"
          >
            <el-autocomplete
              v-model.trim="applicationInfo.details"
              :title="applicationInfo.details"
              :fetch-suggestions="
                (queryString, callback) =>
                  handleGetInfoDetails(queryString, callback)
              "
              style="width: 100%"
              :disabled="isDisabled"
              :placeholder="isDisabled ? '' : $t('请输入申请详情')"
              :debounce="300"
              :maxlength="300"
              show-word-limit
              type="textarea"
              rows="3"
            >
              <template #default="{ item }">
                <el-row type="flex" justify="space-between" :title="item.name">
                  <span class="overflow-hidden">{{ item.name }}</span>
                  <span>
                    <el-button
                      type="text"
                      @click.stop="handleRemoveInfoDetails(item.id)"
                    >
                      {{ $t('删除') }}
                    </el-button>
                  </span>
                </el-row>
              </template>
            </el-autocomplete>
            <el-button
              v-if="loadType!='view'"
              :disabled="!applicationInfo.details"
              @click="handleSaveInfoDetails"
            >
              <span class="blue">
                {{ $t('保存') }}
              </span>
            </el-button>
            <!-- <el-input
              v-model.trim="applicationInfo.details"
              type="textarea"
              show-word-limit
              style="width: 100%; color: #000000"
              :maxlength="300"
              :rows="2"
              :disabled="isDisabled"
            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('经手人')"
            :rules="[
              {
                required: true,
                message: this.$t('请输入经手人'),
                trigger: ['blur', 'change']
              }
            ]"
            prop="staffHandler"
          >
            <el-autocomplete
              v-model.trim="applicationInfo.staffHandler"
              :title="applicationInfo.staffHandler"
              :fetch-suggestions="
                (queryString, callback) =>
                  handleGetInfoStaff(queryString, '1', callback)
              "
              style="width: 100%"
              :disabled="isDisabled"
              :placeholder="isDisabled ? '' : $t('请输入经手人')"
              :debounce="300"
              :maxlength="30"
              show-word-limit
            >
              <template #default="{ item }">
                <el-row type="flex" justify="space-between" :title="item.name">
                  <span class="overflow-hidden">{{ item.name }}</span>
                  <span>
                    <el-button
                      type="text"
                      @click.stop="handleRemoveInfoStaff(item.id)"
                    >
                      {{ $t('删除') }}
                    </el-button>
                  </span>
                </el-row>
              </template>
              <template #append>
                <el-button
                  v-if="loadType!='view'"
                  type="primary"
                  :disabled="!applicationInfo.staffHandler"
                  @click="handleSaveInfoStaff('1')"
                >
                  <span class="blue">
                    {{ $t('保存') }}
                  </span>
                </el-button>
              </template>
            </el-autocomplete>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('审核人')"
            :rules="[
              {
                required: true,
                message: this.$t('请输入审核人'),
                trigger: ['blur', 'change']
              }
            ]"
            prop="staffReviewer"
          >
            <el-autocomplete
              v-model.trim="applicationInfo.staffReviewer"
              :title="applicationInfo.staffReviewer"
              :fetch-suggestions="
                (queryString, callback) =>
                  handleGetInfoStaff(queryString, '3', callback)
              "
              style="width: 100%"
              :disabled="isDisabled"
              :placeholder="isDisabled ? '' : $t('请输入审核人')"
              :debounce="300"
              :maxlength="30"
              show-word-limit
            >
              <template #default="{ item }">
                <el-row type="flex" justify="space-between" :title="item.name">
                  <span class="overflow-hidden">{{ item.name }}</span>
                  <span>
                    <el-button
                      type="text"
                      @click.stop="handleRemoveInfoStaff(item.id)"
                    >
                      {{ $t('删除') }}
                    </el-button>
                  </span>
                </el-row>
              </template>
              <template #append>
                <el-button
                  v-if="loadType!='view'"
                  type="primary"
                  :disabled="!applicationInfo.staffReviewer"
                  @click="handleSaveInfoStaff('3')"
                >
                  <span class="blue">
                    {{ $t('保存') }}
                  </span>
                </el-button>
              </template>
            </el-autocomplete>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('证明人')" prop="staffCertifier">
            <el-autocomplete
              v-model.trim="applicationInfo.staffCertifier"
              :title="applicationInfo.staffCertifier"
              :fetch-suggestions="
                (queryString, callback) =>
                  handleGetInfoStaff(queryString, '2', callback)
              "
              style="width: 100%"
              :disabled="isDisabled"
              :placeholder="isDisabled ? '' : $t('请输入证明人')"
              :debounce="300"
              :maxlength="30"
              show-word-limit
            >
              <template #default="{ item }">
                <el-row type="flex" justify="space-between" :title="item.name">
                  <span class="overflow-hidden">{{ item.name }}</span>
                  <span>
                    <el-button
                      type="text"
                      @click.stop="handleRemoveInfoStaff(item.id)"
                    >
                      {{ $t('删除') }}
                    </el-button>
                  </span>
                </el-row>
              </template>
              <template #append>
                <el-button
                  v-if="loadType!='view'"
                  type="primary"
                  :disabled="!applicationInfo.staffCertifier"
                  @click="handleSaveInfoStaff('2')"
                >
                  <span class="blue">
                    {{ $t('保存') }}
                  </span>
                </el-button>
              </template>
            </el-autocomplete>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('验收人')"
            prop="staffAccepter"
          >
            <el-autocomplete
              v-model.trim="applicationInfo.staffAccepter"
              :title="applicationInfo.staffAccepter"
              :fetch-suggestions="
                (queryString, callback) =>
                  handleGetInfoStaff(queryString, '4', callback)
              "
              style="width: 100%"
              :disabled="isDisabled"
              :placeholder="isDisabled ? '' : $t('请输入验收人')"
              :debounce="300"
              :maxlength="30"
              show-word-limit
            >
              <template #default="{ item }">
                <el-row type="flex" justify="space-between" :title="item.name">
                  <span class="overflow-hidden">{{ item.name }}</span>
                  <span>
                    <el-button
                      type="text"
                      @click.stop="handleRemoveInfoStaff(item.id)"
                    >
                      {{ $t('删除') }}
                    </el-button>
                  </span>
                </el-row>
              </template>
              <template #append>
                <el-button
                  v-if="loadType!='view'"
                  type="primary"
                  :disabled="!applicationInfo.staffAccepter"
                  @click="handleSaveInfoStaff('4')"
                >
                  <span class="blue">
                    {{ $t('保存') }}
                  </span>
                </el-button>
              </template>
            </el-autocomplete>
          </el-form-item>
        </el-col>
<!--        <el-col :span="12">
          <el-form-item :label="$t('是否大批量支付')" prop="isBigBatch">
            <el-select
              v-model="applicationInfo.isBigBatch"
              :disabled="isDisabled || curMethod.businessType != 1 || (applicationInfo.applicationType != 0 && applicationInfo.applicationType != 3)"
              @change="handleSwitchBigBatch"
            >
              <el-option
                v-for="item in mapOptins.isBigBatchOptions"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>-->
        <!-- <el-col :span="24">
          <el-form-item :label="$t('应付费用')">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-input v-model.trim="applicationInfo.accruedExpenseList" disabled />
              </el-col>
              <el-col :span="10">
                <el-button plain type="primary" icon="el-icon-plus" :disabled="!(applicationInfo.expenditureType && applicationInfo.fundSrc)" @click="handleSelectReceiver">
                  {{ $t('关联应付费用') }}
                </el-button>
                <el-button plain icon="el-icon-minus" :disabled="!applicationInfo.details" @click="handleSelectReceiver">
                  {{ $t('取消关联') }}
                </el-button>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <div class="module-title">
      <span class="label-title">{{ $t('收款方信息') }}</span>
    </div>
    <div
      v-if="!['refund', 'add', 'edit'].includes(loadType)"
      class="btn-container"
      style="margin-bottom: 5px"
    >
      <el-button
        v-if="applicationInfo.applicationType == 1"
        round
        plain
        type="primary"
        @click="handleOpenBill"
      >
        {{ $t('查看票据') }}
      </el-button>
    </div>
    <div
      v-if="['refund', 'add', 'edit'].includes(loadType) && applicationInfo.isBigBatch != 1"
      class="btn-container"
      style="margin-bottom: 5px"
    >
      <el-button
        v-if="['add', 'edit'].includes(loadType)"
        round
        plain
        type="primary"
        icon="el-icon-plus"
        :disabled="disabledReceiver"
        @click="handleSelectReceiver"
      >
        {{ $t('选择收款方') }}
      </el-button>
      <el-button
        v-if="['add', 'edit'].includes(loadType)"
        round
        plain
        type="primary"
        icon="el-icon-plus"
        :disabled="disabledReceiver"
        @click="handleAddReceiver"
      >
        {{ $t('新增收款方') }}
      </el-button>
      <el-button
        round
        plain
        type="primary"
        icon="el-icon-delete"
        @click="handleDeleteReceiver"
      >
        {{ $t('删除收款方') }}
      </el-button>

      <el-button
        round
        plain
        type="primary"
        icon="el-icon-plus"
        :disabled="disabledAddProjectObj"
        @click="handleBudget"
      >
        {{ $t('选择预算') }}
      </el-button>
      <el-button
        v-if="!['view', 'refund', 'invalid'].includes(loadType)"
        round
        plain
        type="primary"
        icon="el-icon-plus"
        :disabled="applicationInfo.applicationType != 1"
        @click="handleOpenBill"
      >
        {{ $t('关联票据') }}
      </el-button>
      <el-button round plain icon="el-icon-upload2" @click="handleImport">
        {{ $t('导入收款方') }}
      </el-button>
      <span class="ml20 font-danger">
        注: 如果是银行转账，收款方必须与银行账户户名一致
      </span>
      <!-- <el-button round plain type="primary" icon="el-customIcon-upload" @click="batchImportVisible = true">
        {{ $t('导入') }}
      </el-button> -->
    </div>
    <div
      v-if="applicationInfo.receiverSettlementType === '1'"
      class="alarm-msg"
    >
      <i class="el-icon-warning" />
      <span>{{ $t('温馨提示:') }}</span>
      <h2>{{ $t('该模式下，暂仅支持银行卡账号') }}</h2>
    </div>
    <el-form
      v-if="applicationInfo.isBigBatch != 1"
      class="add-receiver"
      :disabled="(loadType === 'view' || loadType === 'invalid') && applicationInfo.applicationType != 1"
    >
      <el-table
        ref="_tableRef"
        :data="receiverList"
        show-summary
        border
        row-key="id"
        :span-method="spanMethod"
        :summary-method="getSummaries"
        @cell-click="handleCellClick"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
          :selectable="selectable"
        />
        <el-table-column
          :show-overflow-tooltip="false"
          :label="$t('预算项目')"
          prop="budgetItemId"
          align="center"
          min-width="80"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.parentKey"
              type="danger"
              class="el-icon-minus font20"
              plain
              round
              :disabled="isDisabled"
              @click="handleDeleteProject(scope.$index, scope.row)"
            ></el-button>
            <el-button
              v-else
              type="primary"
              class="el-icon-plus font20"
              plain
              round
              :disabled="disabledAddProjectObj || disabledAddProject(scope.row)"
              @click="
                handleAddProject({ row: scope.row, $index: scope.$index })
              "
            ></el-button>
          </template>
        </el-table-column>
        <el-table-column
          :show-overflow-tooltip="false"
          :label="$t('收款方*')"
          prop="receiverAccount"
          align="center"
          min-width="200"
        >
          <template slot-scope="scope">
            <gever-input
              v-if="!scope.row.isEdit && scope.row.parentKey"
              v-model="scope.row.fullAccountingItemName"
              disabled
            >
              <template slot="prepend">项目名称:</template>
              <el-button
                slot="append"
                v-hasPermi="
                  'financial.payment.approvalManager.approvalApplication.choice'
                "
                :disabled="isRefund || isDisabled"
                @click="handleShowBudgetDialog(scope.$index)"
              >
                选择项目
              </el-button>
            </gever-input>
            <el-input
              v-else-if="!scope.row.isEdit && !scope.row.parentKey && !isRefund"
              v-model.trim="scope.row.receiverAccount"
              class="w100%"
              :maxlength="70"
              :title="scope.row.receiverAccount"
              :disabled="isDisabled"
              @blur="scope.row.isEdit = true"
              @change="handleChangeReceiverAccount(scope.row)"
            />
            <span
              v-else-if="scope.row.isEdit && scope.row.parentKey"
              :title="scope.row.fullAccountingItemName"
            >
              {{ scope.row.fullAccountingItemName }}
            </span>
            <span v-else :title="scope.row.receiverAccount">
              {{ scope.row.receiverAccount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :show-overflow-tooltip="false"
          :label="$t(`收款方银行账号${!cashCollection && applicationInfo.payMethod != 2 ? '*' : ''}`)"
          prop="receiverBankAccount"
          align="center"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-input
              v-if="!scope.row.isEdit && !isRefund"
              v-model="scope.row.receiverBankAccount"
              class="100%"
              :minlength="14"
              :maxlength="30"
              :title="scope.row.receiverBankAccount"
              :disabled="isDisabled"
              @blur="handleBlur(scope.row.receiverBankAccount, scope.row)"
            />

            <span v-else :title="scope.row.receiverBankAccount">
              {{ scope.row.receiverBankAccount }}
            </span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          v-if="applicationInfo.receiverSettlementType !== '1'"
          :show-overflow-tooltip="false"
          :label="`收款方银行联行号${curMethod.businessType === 1 ? '*' : ''}`"
          prop="receiverOpenAccountNumber"
          align="center"
          min-width="200"
        >
          <template slot-scope="scope">
            <span :title="scope.row.receiverOpenAccountNumber">
              {{ scope.row.receiverOpenAccountNumber }}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column
          v-if="applicationInfo.receiverSettlementType !== '1'"
          :show-overflow-tooltip="false"
          :label="$t(`收款方开户银行${!cashCollection && applicationInfo.payMethod != 2 ? '*' : ''}`)"
          prop="receiverOpenAccount"
          align="center"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-select
              v-if="
                curMethod.businessType === 1 && !scope.row.isEdit && !isRefund
              "
              v-model="scope.row.receiverOpenAccount"
              class="100%"
              filterable
              remote
              :remote-method="handleSearchBankType"
              :loading="bankLoading"
              :title="scope.row.receiverOpenAccount"
              :disabled="isDisabled"
              @change="handleSelectChange($event, scope.row)"
            >
              <el-option
                v-for="item in selectOptions"
                :key="item.number"
                :title="item.bankName"
                :label="item.bankName"
                :value="item.bankName"
              />
            </el-select>
            <el-input
              v-else-if="
                curMethod.businessType !== 1 && !scope.row.isEdit && !isRefund
              "
              v-model="scope.row.receiverOpenAccount"
              class="100%"
              :title="scope.row.receiverOpenAccount"
              :disabled="isDisabled"
            />
            <span v-else>{{ scope.row.receiverOpenAccount }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="applicationInfo.settlementMethod === '1701539261882900482'"
          :show-overflow-tooltip="false"
          :label="$t('开户银行联行号')"
          prop="receiverOpenAccountNumber"
          align="center"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.receiverOpenAccountNumber"
              class="w100%"
              :maxlength="70"
              :title="scope.row.receiverOpenAccountNumber"
              disabled
            />
          </template>
        </el-table-column>
        <el-table-column
          :show-overflow-tooltip="false"
          :label="$t('金额*')"
          prop="amountPayable"
          align="center"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-input-number
              v-if="applicationInfo.applicationType === 3 || applicationInfo.applicationType === 2"
              v-model="scope.row.amountPayable"
              class="w100%"
              :controls="false"
              :precision="2"
              :min="isRefund ? -scope.row.sourceAmount : 0"
              :max="isRefund ? -1 : *************"
              :title="scope.row.amountPayable"
              :disabled="isDisabled"
              @blur="amountPayableBlur(scope.row)"
            />
            <span
              v-else-if="
                !applicationInfo.useBudgetFlag && !scope.row.projectPerformance
                  ? false
                  : !scope.row.parentKey || scope.row.isEdit
              "
              :title="scope.row.amountPayable"
            >
              {{ comma(scope.row.amountPayable, 2) }}
            </span>
            <el-input-number
              v-else
              v-model="scope.row.amountPayable"
              class="w100%"
              :controls="false"
              :precision="2"
              :min="isRefund ? -scope.row.sourceAmount : 0"
              :max="isRefund ? -1 : *************"
              :title="scope.row.amountPayable"
              :disabled="isDisabled"
              @blur="amountPayableBlur(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="applicationInfo.settlementMethod === '1701539261882900482'"
          :show-overflow-tooltip="false"
          :label="$t(`转账类型`)"
          prop="transferType"
          align="center"
          min-width="200"
        >
          <template slot-scope="scope">
            <gever-select
              v-if="!scope.row.isEdit"
              v-model="scope.row.transferType"
              style="flex: 1"
              collapse-tags
              :options="mapOptins.transferType"
              :disabled="scope.row.isEdit || isDisabled"
            />
            <span v-else>
              {{ scope.row.transferType | transferTypeFilter }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :show-overflow-tooltip="false"
          :label="$t('备注')"
          prop="remark"
          align="center"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-input
              v-if="!scope.row.isEdit"
              v-model="scope.row.remark"
              class="w100%"
              :maxlength="70"
              :title="scope.row.remark"
              :disabled="isDisabled"
              @blur="scope.row.isEdit = true"
            />
            <span v-else>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <el-form
      v-if="applicationInfo.isBigBatch == 1"
      class="form-sg"
    >
      <div class="form-sg">
        <el-form-item :label="$t('')" prop="fileId">
          <gever-upload
            ref="_importReceiverRef"
            :photographic="false"
            :accept="'.xlsx'"
            :business-id="applicationInfo.id"
            file-classification="importPayReceiver"
            :disabled="isDisabled"
            :list-type="'picture-card'"
            :limit="1"
            @batch-change="changeImportFileBatch"
          />
        </el-form-item>
      </div>
    </el-form>
    <el-form  v-if="applicationInfo.isBigBatch == 1" class="form-sg">
      <div>
        <span style="font-weight: 400">注：请在模板中填写收款人信息，再导入到本系统（只能上传一份文件）。标准模板: </span>
          <a
          href="/excel/payment/batchImportReceiverTemplate.xlsx"
          target="_blank"
          download
        >
            {{ $t('收款人信息模板.xlsx') }}
          </a>
      </div>
    </el-form>
<!--    委托支付-->
    <div v-if="applicationInfo.payMethod == 2 && !isRefund" class="module-title">
      <span class="label-title">{{ $t('委托支付清单') }}</span>
    </div>
      <el-form  v-if="applicationInfo.payMethod == 2  && !isRefund" ref="_entrustformRef" :model="applicationInfo" label-width="120px">
        <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
      <el-col :span="12">
        <el-form-item
          :label="$t('同行/跨行')"
          prop="interOrOut"
          :rules="[
              {
                required: true,
                message: this.$t('请输入同行/跨行'),
                trigger: ['blur', 'change']
              }
            ]"
        >
          <gever-select
            v-model="applicationInfo.interOrOut"
            :options="mapOptins.interOrOutOptions"
            :disabled="isDisabled"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          :label="$t('委托业务类型')"
          prop="entrustType"
          :rules="[
              {
                required: true,
                message: this.$t('请输入委托业务类型'),
                trigger: ['blur', 'change']
              }
            ]"
        >
          <gever-select
            v-model="applicationInfo.entrustType"
            :options="entrustTypeList"
            :disabled="isDisabled"
          />
        </el-form-item>
      </el-col>
        <el-form-item :label="$t('')" prop="fileId">
          <gever-upload
            ref="_importEntrustReceiverRef"
            :photographic="false"
            :accept="'.xlsx'"
            :business-id="applicationInfo.id"
            file-classification="importEntrustPayReceiver"
            :disabled="isDisabled"
            :list-type="'picture-card'"
            :limit="1"
            :delete-message="deleteEntrustFileHandle"
            @batch-change="changeImportFileBatch"
          />
        </el-form-item>
        </el-row>
    </el-form>
    <el-form  v-if="applicationInfo.payMethod == 2  && !isRefund" class="form-sg">
      <div>
        <span style="font-weight: 400">注：请在模板中填写收款人信息，再导入到本系统（只能上传一份文件）。标准模板: </span>
        <a
          href="/excel/payment/entrustImportReceiverTemplate.xlsx"
          target="_blank"
          download
        >
          {{ $t('收款人信息模板.xlsx') }}
        </a>
      </div>
    </el-form>
    <el-form class="form-sg">
      <div style="width: 100%">
        <attachment
          v-for="(attachment, key) in currentAttachmentSettings"
          :key="key"
          ref="_attachmentRef"
          :attachment="attachment"
          :business-id="
            isRefund && !applicationInfo.returnFlag ? '' : applicationInfo.id
          "
          :batch-id="
            (attachment.fileList && attachment.fileList[0].batchId) || ''
          "
          :is-view="loadType === 'view' || loadType === 'invalid'"
          @loading="(loading) => $emit('loading', loading)"
        />
      </div>

      <public-drawer
        :visible.sync="dialogVisible"
        :title="$t('收款人信息')"
        :size="70"
        :append-to-body="true"
        destroy-on-close
        :buttons="[
          {
            type: '',
            text: $t('关 闭'),
            buttonStatus: true,
            callback: () => {
              dialogVisible = false
            }
          },
          {
            type: 'primary',
            text: $t('确 定'),
            buttonStatus: true,
            callback: handleConfirmSelectReceiver
          }
        ]"
      >
        <receiver-list
          v-if="dialogVisible"
          ref="_receiverListRef"
          :org-code="application.orgCode"
        />
      </public-drawer>

      <public-drawer
        :visible.sync="isShowContract"
        :title="$t('关联工程合同')"
        :size="70"
        :append-to-body="true"
        :buttons="[
          {
            type: '',
            text: $t('关 闭'),
            buttonStatus: true,
            callback: () => {
              isShowContract = false
            }
          },
          {
            type: 'primary',
            text: $t('确 定'),
            buttonStatus: loadType === 'view' ? false : true,
            callback: handleConfirmContractReceiver
          }
        ]"
      >
        <contract-list
          v-if="isShowContract"
          ref="_receiverListRef"
          :org-code="books.orgId"
          :is-view="loadType === 'view'"
          :applay-id="
            loadType === 'view' || loadType === 'edit' ? applicationInfo.id : ''
          "
          :select-list="
            loadType === 'view' || loadType === 'edit'
              ? applicationInfo.projectPayRecordDTOList
              : []
          "
          @confirm="handleRelatedConstructContract"
        />
      </public-drawer>

      <!-- <public-drawer :visible.sync="batchImportVisible" title="批量导入" :size="60" :buttons="buttons">
        <batch-import ref="_importRef" />
      </public-drawer> -->
    </el-form>

    <el-form
      v-if="loadType === 'invalid'"
      ref="_validRef"
      :model="inValid"
      label-width="110px"
      class="invalid-ele"
    >
      <el-form-item
        label="作废原因"
        prop="reason"
        :rules="{
          required: true,
          message: '请输入作废原因',
          trigger: ['blur', 'change']
        }"
      >
        <gever-input
          v-model="inValid.reason"
          type="textarea"
          :maxlength="100"
        />
      </el-form-item>
    </el-form>

    <public-drawer
      class="budget-dialog"
      :title="$t('关联预算项目')"
      :visible.sync="budgetVisible"
      append-to-body
      perch-height="500"
      width="65%"
      :buttons="[
        {
          type: '',
          text: '取 消',
          buttonStatus: true,
          callback: () => {
            budgetVisible = false
          }
        },
        {
          type: 'primary',
          text: '确 定',
          buttonStatus: true,
          callback: handleSelectBudget
        }
      ]"
    >
      <el-form inline :model="queryParams">
        <el-form-item :label="$t('预算类型')">
          <gever-select
            v-model="queryParams.budgetTypes"
            class="w150"
            :clearable="false"
            :options="budgetTypeOptions"
          />
        </el-form-item>
        <el-form-item :label="$t('项目名称')">
          <el-input v-model="queryParams.projectName" class="w150" />
        </el-form-item>
        <el-form-item :label="$t('会计科目')">
          <gever-select
            v-model="queryParams.accountingItemId"
            clearable
            class="w150"
            label-prop="itemTitle"
            :options="accountingItemOptions"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            round
            plain
            type="primary"
            icon="el-icon-search"
            @click="getBudgetItemList"
          >
            {{ $t('搜索') }}
          </el-button>
        </el-form-item>
      </el-form>
      <gever-table
        ref="_budgetTableRef"
        :columns="budgetColumns"
        :data="budgetList"
        :total="budgetList.length"
        :pagination="false"
        class="budget-table"
      >
        <template #accountingItemTitle="{ row }">
          <span :style="{ 'margin-left': `${(row.leafItem - 1) * 10 || 0}px` }">
            {{ row.accountingItemTitle }}
          </span>
        </template>
        <template #budgetAmount="{ row }">
          {{ row.budgetAmount | formatMoney }}
        </template>
        <template #surplusAmount="{ row }">
          {{ (row.budgetAmount - row.applyAmount) | formatMoney }}
        </template>
      </gever-table>
    </public-drawer>

    <public-drawer
      class="budget-dialog"
      :title="$t('关联预算项目')"
      :visible.sync="budgetMultipleVisible"
      append-to-body
      perch-height="500"
      width="65%"
      :buttons="[
        {
          type: '',
          text: '取 消',
          buttonStatus: true,
          callback: () => {
            budgetMultipleVisible = false
          }
        },
        {
          type: 'primary',
          text: '确 定',
          buttonStatus: true,
          callback: handleSelectBudgetMultiple
        }
      ]"
    >
      <el-form inline :model="queryParams">
        <el-form-item :label="$t('预算类型')">
          <gever-select
            v-model="queryParams.budgetTypes"
            class="w150"
            :clearable="false"
            :options="budgetTypeOptions"
          />
        </el-form-item>
        <el-form-item :label="$t('项目名称')">
          <el-input v-model="queryParams.projectName" class="w150" />
        </el-form-item>
        <el-form-item :label="$t('会计科目')">
          <gever-select
            v-model="queryParams.accountingItemId"
            clearable
            class="w150"
            label-prop="itemTitle"
            :options="accountingItemOptions"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            round
            plain
            type="primary"
            icon="el-icon-search"
            @click="getBudgetItemList"
          >
            {{ $t('搜索') }}
          </el-button>
        </el-form-item>
      </el-form>
      <gever-table
        ref="_budgetMultipleTableRef"
        :columns="budgetColumns"
        :data="budgetList"
        :total="budgetList.length"
        :pagination="false"
        height="calc(100vh - 200px)"
      >
        <template #accountingItemTitle="{ row }">
          <span :style="{ 'margin-left': `${(row.leafItem - 1) * 10 || 0}px` }">
            {{ row.accountingItemTitle }}
          </span>
        </template>
        <template #budgetAmount="{ row }">
          {{ row.budgetAmount | formatMoney }}
        </template>
        <template #surplusAmount="{ row }">
          {{ (row.budgetAmount - row.applyAmount) | formatMoney }}
        </template>
      </gever-table>
    </public-drawer>

    <!-- 批量导入收款人 -->
    <uploadDrawer
      :visible.sync="uploadVisible"
      :params="{
        orgCode: this.application.orgCode
          ? this.application.orgCode
          : this.paymentLogin.code,
        businessType: curMethod.businessType
      }"
      :import-project="importProject"
      :mulpitle="false"
      :uuid="batchId"
      :disabled-add-project="disabledAddProjectObj"
      :accept="'.xls,.xlsx'"
      @selectProject="handleShowBudgetDialog"
      @reload="readImportData"
    />

    <!-- 选择履约 -->
    <el-dialog
      :title="
        ['view', 'refund', 'invalid'].includes(this.loadType)
          ? $t('查看履约')
          : $t('选择履约')
      "
      append-to-body
      :visible.sync="performanceVisible"
      width="1200px"
      top="5vh"
      custom-class="process-dialog"
    >
      <performance
        v-if="performanceVisible"
        :id="applicationInfo.id"
        ref="_performanceRef"
        :row-id-arr="rowIdsArr"
        :selected-rows="selectedRows"
        :load-type="loadType"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="performanceVisible = false">取 消</el-button>
        <el-button
          v-if="loadType != 'view'"
          type="primary"
          @click="handleDetermine"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>

    <!-- 选择票据 -->
    <el-dialog
      :title="
        ['view', 'refund', 'invalid'].includes(this.loadType)
          ? $t('查看票据')
          : $t('选择票据')
      "
      append-to-body
      :visible.sync="billVisible"
      width="1200px"
      top="5vh"
      custom-class="process-dialog"
    >
      <bill-list
        v-if="billVisible"
        :id="applicationInfo.id"
        ref="_billRef"
        :row-id-arr="rowIdsArr"
        :row-data="selectionData[0]"
        :load-type="loadType"
        :creation-time="applicationInfo.creationTime"
        :offset-note-type="applicationInfo.offsetNoteType.toString()"
        :select-bill-ids="selectBillIds"
        :rel-bill-ids="relBillIds"
        :other-select-bill-ids="otherSelectBillIds"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="billVisible = false">取 消</el-button>
        <el-button
          v-if="loadType != 'view'"
          type="primary"
          @click="handleBillSelect"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { formatMoney } from '../../utils/index.js'
import { loadAccountList } from '../../api/payment/capital.js'
import { getToken } from '../../utils/auth'
import Attachment from './components/attachment.vue'
import performance from './components/performance.vue'
import ReceiverList from './components/receiverList.vue'
import contractList from './components/contractList.vue'
import billList from './components/billList.vue'
import { receiverTableColumns } from '../../views/payment/approval/approvalApplication/config'
import {
  searchBankNumber,
  loadApprovalSetting,
  loadAttachmentList,
  saveApprovalApplication,
  submitApprovalApplication,
  tovoid,
  budgetItemList,
  selectFundsUse,
  infoDetailsSave,
  infoDetailsPage,
  infoDetailsDelete,
  infoFundsUseSave,
  infoFundsUsePage,
  infoFundsUseDelete,
  infoStaffSave,
  infoStaffPage,
  infoStaffDelete,
  getAccountingItem
} from '../../api/payment/approval.js'
import uploadDrawer from './components/uploadDrawer'
import { getBudgetAmount } from '../../api/financial/cashier/voucher'
import { getDictionary } from '../../api/gever/common.js'
import { mapState } from 'vuex'
import { paymentExpenditureTypeTree as expenseTypePage } from '../../api/payment/expenseType'
import { page as billingMethodPage } from '../../api/payment/billingMethod'
import _ from 'lodash'
import moment from 'moment'
import { uuid } from '../../utils/gever.js'
import { listEntrustType } from '../../api/payment/capital.js'
var that = null
export default {
  name: 'PaymentApplication',
  components: {
    // BatchImport,
    Attachment,
    ReceiverList,
    contractList,
    uploadDrawer,
    performance,
    billList
  },
  props: {
    loadType: { type: String, default: '' },
    infoType: { type: Boolean, default: false },
    showProcessBtn: { type: Boolean, default: true },
    visible: { type: Boolean, default: true },
    receiver: { type: Array, default: () => [] },
    application: {
      type: Object,
      default: () => {}
    },
    treeData: { type: Array, default: () => [] },
    accountingItemId: { type: String, default: '' },
    viewState: { type: Number, default: 0 },
    applicationBooksId: { type: String, default: '' }
  },
  data() {
    return {
      rowIdsArr: [],
      selectedRows: [],
      performanceVisible: false,
      uploadVisible: false,
      loadingInfoDetails: false,
      loadingInfoFundsUse: false,
      loadingInfoStaffHandler: false,
      loadingInfoStaffCertifier: false,
      loadingInfoStaffReviewer: false,
      loadingInfoStaffAccepter: false,
      batchId: uuid(),
      importProject: {},
      accountingItemOptions: [],
      queryParams: {
        budgetTypes: '',
        projectName: '',
        accountingItemId: ''
      },
      loading: false,
      disabledPayAccoun: false,
      ids: [2, 4, 5, 6],
      accountList: [],
      ZcOptions: [],
      budgetControl: 0,
      orgIdLoading: false,
      expenditureTypeLoading: false,
      matterLoading: false,
      treeNodeData: {},
      matterOptions: [],
      restaurants: [],
      requisitionExpenditureOptions: [],
      receiverSettlementTypeOptions: [
        { id: '3', text: '默认模式' },
        { id: '2', text: '模拟批量模式' },
        { id: '1', text: '超级网银模式' }
      ],
      tableColumns: receiverTableColumns,
      bankLoading: false,
      selectOptions: [],
      dialogVisible: false,
      batchImportVisible: false,
      currentApprovalSetting: {},
      currentAttachmentSettings: [],
      buttons: [
        {
          type: 'primary',
          text: this.$t('导 入'),
          buttonStatus: true,
          callback: this.handleConfirmImport
        }
      ],
      sum: 0,
      batchIds: [],
      initInfo: {
        orgName: ''
        // submitUserName: this.$store.state.user.name
      }, // 初始化信息
      currentTransformItem: {}, // 当前互转科目信息,
      currentBudgetItem: {}, // 当前预算科目
      applicationInfo: {
        applicationType: '',
        offsetNoteType: 0,
        isBigBatch: 0,
        payMethod: 1
      },
      inValid: {
        reason: '',
        id: ''
      },
      budgetVisible: false,
      budgetMultipleVisible: false,
      settlementMethodOptions: [],
      budgetList: [],
      budgetColumns: [
        { type: 'selection', width: 50 },
        {
          label: '年份',
          prop: 'year',
          minWidth: 100,
          align: 'center'
        },
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 150,
          align: 'left'
        },
        {
          label: '会计科目',
          prop: 'accountingItemName',
          minWidth: 150,
          align: 'left'
        },
        {
          label: '本年计划数',
          prop: 'budgetAmount',
          minWidth: 150,
          align: 'right',
          filter: 'money'
        },
        {
          label: '已使用金额',
          prop: 'applyAmount',
          minWidth: 150,
          align: 'right',
          filter: 'money'
        },
        {
          label: '剩余可用金额',
          prop: 'surplusAmount',
          minWidth: 150,
          align: 'right',
          filter: 'money'
        },
        { label: '备注', prop: 'remark', minWidth: 150, align: 'left' }
      ],
      budgetAmount: 0, // 当前预算项目预算额
      usedAmount: 0,
      originAmount: 0, // 编辑时原来的金额
      isShowContract: false,
      amountDisable: false,
      selection: [],
      receiverList: [],
      currentExpenseType: {},
      relatedBankFlows: [], // 已关联的银行流水
      relatedConstructContract: [], // 已关联的工程合同
      mapOptins: {
        expenditureType: [],
        matter: [],
        settlementMethod: [],
        fundSrc: [],
        reimVoucherType: [],
        payMethod: [],
        applicationType: [
          { id: 0, text: '常规用款' },
          { id: 1, text: '收支相抵' },
          { id: 2, text: '冲减收入' },
          { id: 3, text: '非预算项目支出' },
          { id: 4, text: '银行代扣' },
          { id: 5, text: '扣减预算' },
          { id: 6, text: '非预算收支相抵' }
        ],
        offsetNoteType: [
          { id: 0, text: '不冲销票据' },
          { id: 1, text: '冲销收款收据' },
          { id: 2, text: '冲销发票' },
          { id: 3, text: '冲销非电子票据' }
        ],
        isBigBatchOptions: [
          { id: 1, text: '是' },
          { id: 0, text: '否' }
        ],
        interOrOutOptions: [
          { id: 1, text: '同行' },
          { id: 2, text: '跨行' }
        ],
        entrustTypeOptions: [],
        transferType: []
      },
      entrustTypeList: [],
      selectionData: [],
      editIndex: null,
      isCashPay: false,
      isOffsetNoteType: true,
      cashCollection: true,
      bocBankFlag: false,
      curMethod: {},
      accountType: '',
      settlementType: '',
      bankType: '',
      applicationTypeVal: {
        val: '',
        old: ''
      },
      offsetNoteTypeVal: {
        val: '',
        old: ''
      },
      isProject: '',
      billVisible: false,
      creationTime: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
      // 当前收款人选择的票据ID集合
      selectBillIds: [],
      // 当前支付申请已关联的票据ID集合（指已保存在数据库的）
      relBillIds: [],
      // 其他收款人已选择的票据ID
      otherSelectBillIds: [],
      importFileBatchId: ''
    }
  },
  computed: {
    ...mapState('financial', ['books', 'year', 'period', 'booksId']),
    budgetTypeOptions() {
      const budgetTypeMap = {
        2: [{ id: '1', text: '收入' }],
        4: [
          { id: '2,3', text: '全部' },
          { id: '2', text: '支出' },
          { id: '3', text: '非损益类' }
        ],
        0: [
          { id: '2,3', text: '全部' },
          { id: '2', text: '支出' },
          { id: '3', text: '非损益类' }
        ],
        1: [
          { id: '2,3', text: '全部' },
          { id: '2', text: '支出' },
          { id: '3', text: '非损益类' }
        ]
      }
      return budgetTypeMap?.[this.applicationInfo.applicationType] ?? []
    },
    disabledAddProjectObj() {
      return (
        this.applicationInfo.applicationType == 3 ||
        this.applicationInfo.applicationType == 2 ||
        !this.applicationInfo.useBudgetFlag ||
        this.isRefund ||
        this.isDisabled
      )
    },
    parentLen() {
      return this.receiverList.filter((cur) => !cur.parentKey)
    },
    showReceiverSettlementType() {
      return this.receiverList.length > 1
    },
    contractIds() {
      return this.relatedConstructContract.map(({ payscheduleId: id }) => id)
    },
    constructionContract() {
      const { text = '' } = this.currentExpenseType
      return text === '工程支出'
    },
    isDisabled() {
      return ['view', 'invalid'].includes(this.loadType)
    },
    isRefund() {
      return this.loadType === 'refund' || !!this.applicationInfo.returnFlag
    },
    disabledReceiver() {
      const receiverData = this.receiverList.filter((cur) => !cur.parentKey)
      return receiverData?.length == 10
    },
    paymentLogin() {
      return this.$store.state.area.areaLogin ?? {}
    },
    budgetLines() {
      return !this.receiverList.every((item) => !item.budgetItemId)
    }
  },
  filters: {
    transferTypeFilter(val) {
      const type = that.mapOptins.transferType.find((item) => item.id == val)
      return type ? type.text : ''
    }
  },
  watch: {
    'applicationInfo.applicationType'(val, old) {
      this.applicationTypeVal = {
        val,
        old
      }
    },
    'applicationInfo.offsetNoteType'(val, old) {
      this.offsetNoteTypeVal = {
        val,
        old
      }
    },
    accountType(val) {
      if (val) {
        this.getApprovalSetting()
      }
    },
    'applicationInfo.settlementMethod'(val) {
      if (val && this.mapOptins?.billingMethod?.length) {
        const item = (this.mapOptins.billingMethod || []).find(
          (cur) => cur.id === val
        )

        this.cashCollection = ![
          '1701539261882900482',
          '9797d83952d711eea8160242ac120002'
        ].includes(item?.id)
      }
      if (
        ['1701539261882900482', '9797d83952d711eea8160242ac120002'].includes(
          val
        ) &&
        !this.mapOptins?.billingMethod?.length
      ) {
        this.cashCollection = false
      }
      if (
        !['1701539261882900482', '9797d83952d711eea8160242ac120002'].includes(
          val
        ) &&
        !this.mapOptins?.billingMethod?.length
      ) {
        this.cashCollection = true
      }
    },
/*    'applicationInfo.isBigBatch'(val) {
      console.log("---" + val)
      if (val == '0') {
        this.$set(this.applicationInfo, 'isBigBatch', 0)
      } else {
        this.$set(this.applicationInfo, 'isBigBatch', 1)
      }
    },*/
    application: {
      handler: function (val = {}) {
        const { id, amount, businessType } = val
        this.originAmount = id ? amount : 0

        val?.applicationType
          ? this.handleApplicationType(val.applicationType, 'yes')
          : this.handleApplicationType(0)
        this.applicationInfo = {
          applicationType: 0,
          offsetNoteType: 0,
          isBigBatch: 0,
          payMethod: 1,
          businessYear: moment().format('YYYY'),
          ..._.cloneDeep(val)
        }
        // this.setSwitchBigBatch()
        // 获取付款账号
        if (businessType != 2 && val.payAccountId) {
          this.handleAccountList()
        }
        this.initInfo.orgName = this.application.orgName
        const { projectPayRecordDTOList = [] } = val
        this.relatedConstructContract = projectPayRecordDTOList
        if (val.approvalSettingsId) {
          // 回填审批事项
          this.getApprovalSetting()
          // 回填附件信息
          const params = { settingId: this.application.approvalSettingsId }
          loadAttachmentList(params).then((res) => {
            this.currentAttachmentSettings = res.data.fileSettings.map(
              (cur, i) => {
                if (this.application?.approvalFileSettingInsts?.[i]?.fileList) {
                  cur.fileList =
                    this.application?.approvalFileSettingInsts?.[i]?.fileList
                }
                return cur
              }
            )
            this.currentApprovalSetting = res.data.setting
            this.$forceUpdate()
          })
        }
        if (businessType == 2) {
          this.accountType = '1'
        }
        if (this.applicationInfo.payAccountId) {
          this.listEntrustType()
        }
      },
      immediate: true,
      deep: true
    },
    receiver: {
      handler(val) {
        this.receiverList = val.map((cur) => {
          cur.isEdit = true
          return cur
        })
      },
      immediate: true,
      deep: true
    },
    'receiverList.length': {
      handler: function () {
        if (this.parentLen?.length > 1) {
          this.$set(
            this.applicationInfo,
            'receiverSettlementType',
            this.applicationInfo.receiverSettlementType || '3'
          )
        } else {
          this.$set(this.applicationInfo, 'receiverSettlementType', '')
        }
      },
      immediate: true,
      deep: true
    },
    booksId: {
      handler: function (newVal) {
        if (newVal) {
          this.initInfo.orgName = this.application.orgName
          this.applicationInfo.orgId = this.books.orgCode
          // this.getAddInitInfo()
        }
      },
      immediate: true
    }
  },
  mounted() {
    that = this
    this.getOptions()
    // this.setSwitchBigBatch()
  },
  methods: {
    getToken,
    moment,
    // 工程支出选择
    handleDialog() {
      this.selectedRows = []
      for (const receiver of this.receiverList) {
        if (receiver && receiver.id) {
          this.rowIdsArr.push(receiver.id)
        }
        this.selectedRows.push(
          receiver.projectPerformance ? receiver.projectPerformance : 'NULL'
        )
      }
      this.performanceVisible = true
    },
    // 选择履约确定
    handleDetermine() {
      const selected = this.$refs['_performanceRef'].selectArr
      if (!selected || !selected.length) {
        return this.$message.warning(this.$t('请选择履约明细'))
      }
      const receiverData = this.receiverList.filter((cur) => !cur.parentKey)
      const receiverKeys = receiverData.map((cur) => cur.key)
      if (
        selected.length == 1 &&
        selected.some((cur) => receiverKeys.includes(cur.id))
      ) {
        return this.$message.warning(this.$t('选择重复的履约明细，请重新选择'))
      }
      if (selected.length > 1) {
        const curPerformances = selected.filter((cur) => cur.performanceCode)
        if (curPerformances && curPerformances.length > 0) {
          const performanceCode = selected[0].performanceCode
          if (
            curPerformances.length > 1 &&
            curPerformances.some(
              (cur) => performanceCode !== cur.performanceCode
            )
          ) {
            return this.$message.warning(
              this.$t('请选择相同履约编号的履约明细')
            )
          }
        }
      }
      if (selected.length + receiverData.length > 10) {
        return this.$message.warning(
          this.$t(`最多可设置10个收款人(已设置${receiverData.length}人)`)
        )
      }
      // 编辑时，原有的收款人
      const oldRelIds = this.receiverList.map((cur) => cur.projectPerformance)
      const newList = []
      const keys = []
      this.receiverList.forEach((cur) => {
        // 保留相同的收款人信息
        if (
          selected.some(
            (item) =>
              !cur.budgetItemId &&
              (!cur.projectPerformance || item.id == cur.projectPerformance)
          )
        ) {
          keys.push(cur.key)
          newList.push(cur)
        }
        // 保留预算
        if (cur.parentKey && keys.includes(cur.parentKey)) {
          newList.push(cur)
        }
      })
      this.receiverList = newList
      const result = []
      selected.forEach((item) => {
        if (oldRelIds && oldRelIds.includes(item.id)) {
          return
        }
        result.push({
          receiverAccount: item.pyeName,
          receiverBankAccount: item.pyeBankCode,
          receiverBank: item.payType,
          receiverOpenAccountNumber: '',
          receiverOpenAccount: item.pyeBankName,
          amountPayable: item.payAmount || undefined,
          remark: `付款条件：【${item.payCondition}】`,
          key: item.id,
          projectPerformance: item.id,
          performanceCode: item.performanceCode,
          isEdit: true
        })
      })
      // 选择完履约总计申请信息金额
      this.receiverList.push(...result)
      this.autoSetBankNumber()
      this.performanceVisible = false
      // const num = this.receiverList.reduce(
      //   (sum, item) => sum + item.amountPayable * 1,
      //   0
      // )
      // this.applicationInfo.amount = num
      // this.getApprovalSetting()
    },
    autoSetBankNumber() {
      this.receiverList.forEach((item) => {
        if (!item.receiverOpenAccountNumber && item.receiverOpenAccount) {
          const params = {
            keyword: item.receiverOpenAccount
          }
          searchBankNumber(params).then((res) => {
            const bankInfo = res.data.rows.find((bank) => bank.bankName === item.receiverOpenAccount)
            console.log(bankInfo)
            if (bankInfo.number) {
              item.receiverOpenAccountNumber = bankInfo.number
            }
          })
        }
      })
    },
    readImportData(list) {
      this.receiverList = list
      // list.forEach((cur) => {
      //   if (
      //     this.receiverList.some(
      //       (item) =>
      //         !item.parentKey &&
      //         item.receiverAccount === cur.receiverAccount &&
      //         item.receiverBankAccount === cur.receiverBankAccount &&
      //         item.receiverOpenAccount === cur.receiverOpenAccount &&
      //         item.amountPayable === cur.amountPayable &&
      //         item.remark === cur.remark
      //     )
      //   ) {
      //     return
      //   }
      //   this.receiverList.push(cur)
      // })
    },
    handleImport() {
      if (!this.applicationInfo.settlementMethod) {
        return this.$message.warning('请先选择结算方式')
      }
      this.batchId = uuid()
      this.uploadVisible = true
    },
    handleBudget() {
      if (!this.selectionData.length) {
        return this.$message.warning(this.$t('请选择收款方！'))
      }
      this.budgetMultipleVisible = true
      this.getBudgetItemList()
    },
    handleAccountList() {
      loadAccountList({
        orgCode:
          this.loadType !== 'add'
            ? this.applicationInfo.orgId
            : this.paymentLogin.code,
        businessType: this.curMethod.businessType,
        state: ['add', 'edit'].includes(this.loadType) ? 1 : null
      }).then((res) => {
        this.accountList = res.data.rows.map((cur) => ({
          ...cur,
          accountNumber:
            cur.accountNumber +
            (cur.accountTypeName ? `（${cur.accountTypeName}）` : '')
        }))
        if (this.applicationInfo.matter && this.applicationInfo.payAccountId) {
          const curPayAccoun = this.accountList.find(
            (cur) => cur.id === this.applicationInfo.payAccountId
          )
          if (!curPayAccoun && this.curMethod.businessType !== 2) {
            this.$set(this.applicationInfo, 'payAccountId', '')
            this.$set(this.applicationInfo, 'payAccountNumber', '')
            this.$set(this.applicationInfo, 'payAccountOwner', '')
          }
          this.accountType = curPayAccoun.accountType
          this.bankType = curPayAccoun.bankType
        }
      })
    },
    handlePayAccoun(option) {
      this.applicationInfo.matter = ''
      this.matterOptions = []
      this.$set(this.applicationInfo, 'payAccountNumber', option.accountNumber)
      this.$set(this.applicationInfo, 'payAccountOwner', option.ownerName)
      this.accountType = option?.accountType ?? ''
      this.bankType = option?.bankType ?? ''
      if (option?.bankType === '104') {
        this.bocBankFlag = true
      } else {
        this.bocBankFlag = false
      }
      if (this.bankType != '************') {
        // 不是顺德农商行的不能选择委托支付
        this.$set(this.applicationInfo, 'payMethod', 1)
      }
    },
    handletMethod(option) {
      this.curMethod = option
      if (option.settleCode == '12') {
        console.log(this.applicationInfo.applicationType)
        if (this.applicationInfo.applicationType == 1) {
          this.receiverList.forEach((cur) => {
            this.getOldRelBillIds()
            cur.deductRelList = []
            cur.isClean = 1
          })
        }
        this.$set(this.applicationInfo, 'applicationType', 4)
        this.handleApplicationType(4)
      }
      if (option.businessType != 1) {
        this.$set(this.applicationInfo, 'isBigBatch', 0)
        this.$set(this.applicationInfo, 'payMethod', 1)
      }
      if (option?.businessType === 2) {
        this.disabledPayAccoun = true
        this.$set(this.applicationInfo, 'payAccountId', '0')
        this.$set(this.applicationInfo, 'payAccountNumber', '0')
        this.$set(this.applicationInfo, 'payAccountOwner', 'orgName')
        this.accountType = '1'
        this.getApprovalSetting()
        return
      }
      this.disabledPayAccoun = false
      this.$set(this.applicationInfo, 'payAccountId', '')
      this.$set(this.applicationInfo, 'payAccountNumber', '')
      this.$set(this.applicationInfo, 'payAccountOwner', '')
      this.$set(this.applicationInfo, 'matter', '')
      this.matterOptions = []
      this.handleAccountList()
    },
    handleSaveAndSubmit() {
      this.handleSaveApplication(1)
    },
    clickApplicationType(cur) {
      const { val, old } = this.applicationTypeVal
      this.$refs._applicationTypeRef.blur()
      if (old == 1 && val != 1) {
        this.$confirm(
          `申请类型切换为【${cur.text}】后将清空收款人关联票据数据,请确认是否继续?`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.receiverList.forEach((cur) => {
              this.getOldRelBillIds()
              cur.deductRelList = []
              cur.isClean = 1
            })
          })
          .catch(() => {
            this.$set(this.applicationInfo, 'applicationType', old)
          })
      }
      if (
        ([2, 3].includes(val) && [0, 1, 4].includes(old)) ||
        ([0, 1, 4].includes(val) && [2, 3].includes(old))
      ) {
        this.$confirm(
          `申请类型切换为【${cur.text}】后将清空收款人明细数据,请确认是否继续?`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.receiverList = []
          })
          .catch(() => {
            this.$set(this.applicationInfo, 'applicationType', old)
          })
      }
    },
    clickOffsetNoteType(cur) {
      const { val, old } = this.offsetNoteTypeVal
      this.$refs._applicationTypeRef.blur()
      if (this.applicationInfo.applicationType == 1 && val != old
        && this.receiverList && this.receiverList.length > 0) {
        this.$confirm(
          `相抵票据类型切换为【${cur.text}】后将清空收款人关联票据数据,请确认是否继续?`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.$set(this.applicationInfo, 'offsetNoteType', val)
            this.receiverList.forEach((cur) => {
              this.getOldRelBillIds()
              cur.deductRelList = []
              cur.isClean = 1
            })
            this.$refs._offsetNoteTypeRef.blur()
          })
          .catch(() => {
            this.$set(this.applicationInfo, 'offsetNoteType', old)
            this.$refs._offsetNoteTypeRef.blur()
          })
      }
    },
    handleApplicationType(val, text = '') {
      const budgetTypesMap = {
        0: '2,3',
        1: '2,3',
        2: '1',
        4: '2,3',
        5: '2,3'
      }
      if (!['0', '3'].includes(val)) {
        this.$set(this.applicationInfo, 'isBigBatch', 0)
        this.$set(this.applicationInfo, 'payMethod', 1)
      }
      this.$set(this.queryParams, 'budgetTypes', budgetTypesMap[val])
      if (val === 4) {
        const cur = (this.mapOptins.billingMethod || []).find(
          (item) => item.settleCode == '12'
        )
        if (cur) {
          this.$set(this.applicationInfo, 'settlementMethod', cur.id)
          this.curMethod = cur
          this.handleAccountList()
        }
      }
      // 申请类型是冲减收入-2或常规申请-0，相抵票据类型就是不冲销票据-0；申请类型是收支相抵-1，相抵票据类型就是可选那两种
      if ([0, 2, 3, 4, 5].includes(val)) {
        this.isOffsetNoteType = true
        this.$set(this.applicationInfo, 'offsetNoteType', 0)
        this.mapOptins.offsetNoteType = [
          { id: 0, text: '不冲销票据' },
          { id: 1, text: '冲销收款收据' },
          { id: 2, text: '冲销发票' },
          { id: 3, text: '冲销非电子票据' }
        ]
        return
      }
      this.isOffsetNoteType = false
      this.$set(this.applicationInfo, 'offsetNoteType', undefined)
      this.mapOptins.offsetNoteType = [
        { id: 1, text: '冲销收款收据' },
        { id: 2, text: '冲销发票' },
        { id: 3, text: '冲销非电子票据' }
      ]
    },
    async handleGetInfoDetails(queryString, callback) {
      const { data } = await infoDetailsPage({
        orgCode: this.application.orgCode
          ? this.application.orgCode
          : this.paymentLogin.code
      })
      const result = queryString
        ? data.filter(({ name = '' }) => name.indexOf(queryString) > -1)
        : data
      result.forEach((data) => {
        data.value = data.name || ''
      })
      callback(result)
    },
    handleRemoveInfoFundsUse(id) {
      this.$confirm('确认删除该条资金用途吗', this.$t('提示'), {
        confirmButtonText: '确认',
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(async () => {
          try {
            await infoFundsUseDelete({ id })
          } catch (error) {
            console.log(error)
          }
        })
        .catch(() => {})
    },
    handleSaveInfoFundsUse() {
      infoFundsUseSave({
        orgCode:
          this.applicationInfo.orgCode === undefined
            ? this.applicationInfo.orgId
            : this.applicationInfo.orgCode,
        name: this.applicationInfo.fundsUse
      })
        .then((res) => {
          this.$message.success(res.message)
        })
        .finally(() => {
          this.loadingInfoFundsUse = false
        })
    },
    async handleGetInfoFundsUse(queryString, callback) {
      const { data } = await infoFundsUsePage({
        orgCode: this.application.orgCode
          ? this.application.orgCode
          : this.paymentLogin.code
      })
      const result = queryString
        ? data.filter(({ name = '' }) => name.indexOf(queryString) > -1)
        : data
      result.forEach((data) => {
        data.value = data.name || ''
      })
      callback(result)
    },
    handleRemoveInfoDetails(id) {
      this.$confirm('确认删除该条申请详情吗', this.$t('提示'), {
        confirmButtonText: '确认',
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(async () => {
          try {
            await infoDetailsDelete({ id })
          } catch (error) {
            console.log(error)
          }
        })
        .catch(() => {})
    },
    handleSaveInfoDetails() {
      this.loadingInfoDetails = true
      infoDetailsSave({
        orgCode:
          this.applicationInfo.orgCode === undefined
            ? this.applicationInfo.orgId
            : this.applicationInfo.orgCode,
        name: this.applicationInfo.details
      })
        .then((res) => {
          this.$message.success(res.message)
        })
        .finally(() => {
          this.loadingInfoDetails = false
        })
    },
    async handleGetInfoStaff(queryString, type, callback) {
      const { data } = await infoStaffPage({
        orgCode: this.application.orgCode
          ? this.application.orgCode
          : this.paymentLogin.code,
        type: type
      })
      const result = queryString
        ? data.filter(({ name = '' }) => name.indexOf(queryString) > -1)
        : data
      result.forEach((data) => {
        data.value = data.name || ''
      })
      callback(result)
    },
    handleRemoveInfoStaff(id) {
      this.$confirm('确认删除该人员名称吗', this.$t('提示'), {
        confirmButtonText: '确认',
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      })
        .then(async () => {
          try {
            await infoStaffDelete({ id })
          } catch (error) {
            console.log(error)
          }
        })
        .catch(() => {})
    },
    handleSaveInfoStaff(type) {
      let name = ''
      if (type === '1') {
        this.loadingInfoStaffHandler = true
        name = this.applicationInfo.staffHandler
      }
      if (type === '2') {
        this.loadingInfoStaffCertifier = true
        name = this.applicationInfo.staffCertifier
      }
      if (type === '3') {
        this.loadingInfoStaffReviewer = true
        name = this.applicationInfo.staffReviewer
      }
      if (type === '4') {
        this.loadingInfoStaffAccepter = true
        name = this.applicationInfo.staffAccepter
      }
      infoStaffSave({
        orgCode:
          this.applicationInfo.orgCode === undefined
            ? this.applicationInfo.orgId
            : this.applicationInfo.orgCode,
        name: name,
        type: type
      })
        .then((res) => {
          this.$message.success(res.message)
        })
        .finally(() => {
          if (type === '1') {
            this.loadingInfoStaffHandler = false
          }
          if (type === '2') {
            this.loadingInfoStaffCertifier = false
          }
          if (type === '3') {
            this.loadingInfoStaffReviewer = false
          }
          if (type === '4') {
            this.loadingInfoStaffAccepter = false
          }
        })
    },
    async getOptions() {
      this.loading = true
      try {
        const { data } = await getDictionary(
          '/payment/requisition_expenditure/'
        )
        this.requisitionExpenditureOptions = data
        // 资金来源
        const fundSrc = await getDictionary('/payment/source_of_funds/')
        if (fundSrc.returnCode === '0') {
          this.mapOptins.fundSrc = fundSrc.data
        }
        // 报销凭证类别
        const reimVoucherType = await getDictionary(
          '/payment/reimbursement_type/'
        )
        if (reimVoucherType.returnCode === '0') {
          this.mapOptins.reimVoucherType = reimVoucherType.data
        }
        // 转账类型
        const transferTypeDirData = await getDictionary(
          '/payment/transferType/'
        )
        if (transferTypeDirData.returnCode === '0') {
          this.mapOptins.transferType = transferTypeDirData.data
        }
        // 支出类型
        const expenditureType = await expenseTypePage({ page: 1, rows: 100 })
        if (expenditureType.returnCode === '0') {
          this.mapOptins.expenditureType = expenditureType.data.filter(
            (cur) => cur.id !== '0'
          )
        }
        // 结算方式
        const billingMethod = await billingMethodPage({
          page: 1,
          rows: 100,
          parentId: '0'
        })

        if (billingMethod.returnCode === '0') {
          this.mapOptins.billingMethod = billingMethod.data.rows
        }
        if (this.application?.settlementMethod) {
          const cur = billingMethod.data.rows.find(
            (item) => item.id === this.application.settlementMethod
          )
          this.curMethod = cur
          this.handleAccountList()
        }
        // 支付模式
        const payMethod = await getDictionary('/payment/payMethod/')
        if (payMethod.returnCode === '0') {
          this.mapOptins.payMethod = payMethod.data
        }
        // 委托业务类型
        const entrustTypeOptions = await getDictionary('/payment/entrustType/')
        if (entrustTypeOptions.returnCode === '0') {
          this.mapOptins.entrustTypeOptions = entrustTypeOptions.data
          this.listEntrustType()
        }
        this.loading = false
      } catch (error) {
        this.loading = false
      }
    },
    // 是否超出预算
    infoOverBudget() {
      const { budgetItemCode, amount, id = '' } = this.applicationInfo
      if (budgetItemCode && amount && this.loadType !== 'view') {
        getBudgetAmount({
          booksId: this.booksId,
          accountingItemCode: budgetItemCode,
          paymentId: id
        })
          .then(({ data = 0 }) => {
            // 剩余值减去当前值 小于 总值*阈值
            const { budgetPlait = {}, budgetItem = [] } = this.initInfo
            const { budgetThreshold } = budgetPlait // 阈值百分比
            this.currentBudgetItem = budgetItem.find(
              ({ accountingItemCode: code }) => budgetItemCode === code
            )
            const { budgetAmount = 0, accountingItemTitle = '' } =
              this.currentBudgetItem
            const isOverBudget =
              budgetAmount - data + amount >
              (budgetAmount * budgetThreshold) / 100
            if (amount > data) {
              this.$message.error({
                duration: 6000,
                message: `预算项目（${accountingItemTitle}）可用余额为：${data}，已超支，请调整支出单金额`
              })
            } else if (budgetThreshold && isOverBudget) {
              this.$message.info({
                duration: 6000,
                message: `支出后预算项目的已使用金额将超出预算项目设置的阈值比例(${budgetThreshold}%)！`
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
      }
    },
    handleExpenseTypeChange(current) {
      this.currentExpenseType = current
      this.cleanReceiverForProject()
    },
    handleReimVoucherType(current) {
      if (current?.id && !['1', '2'].includes(current?.id)) {
        this.applicationInfo.billNo = ''
      }
    },
    getBudgetItemList() {
      const { id = '', creationTime = new Date() } = this.applicationInfo
      const budgetTypes =
        this.applicationInfo.applicationType === 2 ? '1' : '2,3'
      budgetItemList({
        orgId: this.paymentLogin.id,
        year: moment(creationTime).format('yyyy'),
        paymentId: id,
        budgetTypes,
        ...this.queryParams
      }).then((res) => {
        this.budgetList = res.data
        this.$nextTick(() => {
          setTimeout(() => {
            if (this.applicationInfo.budgetItemId) {
              // 预算科目回显
              const row = this.budgetList.find(
                (item) => item.id === this.applicationInfo.budgetItemId
              )
              this.$refs['_budgetTableRef'].toggleRowSelection(row, true)
            }
          }, 1000)
        })
      })
    },
    async handleShowBudgetDialog(index) {
      if (!this.applicationInfo.creationTime) {
        return this.$message.warning('请先选择日期再选项目')
      }
      if (!this.applicationInfo.applicationType.toString()) {
        return this.$message.warning('请先选择申请类型')
      }
      this.budgetVisible = true
      if (index) {
        this.editIndex = index
      }
      this.getBudgetItemList()
      const accountingItemOptions = await getAccountingItem({
        orgId: this.paymentLogin.id,
        year: moment(this.applicationInfo.creationTime).format('yyyy')
      })
      this.accountingItemOptions = accountingItemOptions.data.map((cur) => {
        cur.itemTitle = `${cur.itemCode}-${cur.itemTitle}`
        return cur
      })
    },
    handleSelectBudget() {
      const selection = this.$refs['_budgetTableRef'].$refs['elTable'].selection
      if (selection.length != 1) {
        return this.$message.warning(this.$t('请选择一条预算项目'))
      }
      if (selection[0].budgetAmount < selection[0].executeAmount) {
        return this.$message.warning(
          this.$t('所选预算项目预算金额小于已执行金额，请选择其他预算项目!')
        )
      }
      if (this.uploadVisible) {
        this.importProject = selection[0]
        this.budgetVisible = false
        return
      }
      const index = this.editIndex
      const getCurProjectBrothers = this.receiverList.filter(
        (cur) => cur.parentKey === this.receiverList[index].parentKey
      )
      if (
        getCurProjectBrothers.some(
          (item) => item.budgetItemId === selection[0].id
        )
      ) {
        return this.$message.warning(
          this.$t('同一收款人下不能有相同预算项目，请合并相同项!')
        )
      }
      const row = {
        ...this.receiverList[index],
        budgetItemName: selection[0].projectName,
        fullAccountingItemName:
          selection[0].projectName +
          ' (' +
          selection[0].accountingItemName +
          ')',
        budgetItemId: selection[0].id,
        accountingItemId: selection[0].accountingItemId,
        accountingItemCode: selection[0].accountingItemCode,
        accountingItemType: selection[0].accountingItemType,
        amountPayable: 0
      }
      if (
        this.applicationInfo.expenditureType ===
        '909440E63C6D11B2B92E7263D79FDBFF'
      ) {
        // 查询到预算项目对应的收款方
        const obj = this.receiverList.find((item) => item.key === row.parentKey)
        if (obj.projectPerformance) {
          row.isProject = '1'
        }
        if (obj.amountPayable) {
          row.amountPayable = Number(0)
        }
      }
      this.receiverList.splice(index, 1, row)
      if (!row.isProject || row.isProject !== '1') {
        this.countAmountPayable(row.parentKey)
      }
      this.budgetVisible = false
    },
    handleSelectBudgetMultiple() {
      // console.log('handleSelectBudgetMultiple')
      const selection =
        this.$refs['_budgetMultipleTableRef'].$refs['elTable'].selection

      if (selection.length < 1) {
        return this.$message.warning(this.$t('请至少选择一条预算项目'))
      }

      // 选择的预算
      // console.log('selection', selection)
      // 外面的收款方信息(包含预算项目)
      // console.log('receiverList', this.receiverList)
      // 外面选择的收款方
      // console.log('selectionData', this.selectionData)
      const addData = []

      this.receiverList.forEach((item, i) => {
        const receiver = item
        let isProject = '0'
        if (
          this.applicationInfo.expenditureType ===
          '909440E63C6D11B2B92E7263D79FDBFF'
        ) {
          // 查询到预算项目对应的收款方
          if (receiver.projectPerformance) {
            isProject = '1'
          }
        }

        const receiverChildren = this.receiverList.filter(
          (cur) => cur.parentKey === receiver.key
        )
        const receiverChildrenLastIndex = this.receiverList.findLastIndex(
          (cur) => cur.parentKey === receiver.key
        )
        const filterBudgetData = []
        selection.forEach((cur) => {
          const receiverChildrenIds = receiverChildren.map(
            (row) => row.budgetItemId
          )
          if (!receiverChildrenIds.includes(cur.id)) {
            filterBudgetData.push({
              ...cur,
              id: uuid(),
              isEdit: true,
              budgetItemName: cur.projectName,
              fullAccountingItemName:
                cur.projectName + ' (' + cur.accountingItemName + ')',
              budgetItemId: cur.id,
              accountingItemId: cur.accountingItemId,
              accountingItemCode: cur.accountingItemCode,
              accountingItemType: cur.accountingItemType,
              amountPayable: 0,
              parentKey: receiver.key,
              isProject: isProject
            })
          }
        })
        if (
          this.selectionData.some((cur) => cur.key === receiver.key) &&
          filterBudgetData.length
        ) {
          const addIndex =
            receiverChildrenLastIndex === -1 ? i : receiverChildrenLastIndex
          addData.push({
            addIndex: addIndex + 1,
            rows: filterBudgetData
          })
        }
      })

      this.budgetMultipleVisible = false
      if (addData.length) {
        addData.forEach((cur, i) => {
          this.receiverList.splice(cur.addIndex, 0, ...cur.rows)
          const lastIndex = this.receiverList.findLastIndex(
            (item) => item.parentKey === cur.rows[0].parentKey
          )
          addData[i + 1].addIndex = lastIndex + 2
        })
      }
      this.countAmountPayable(row.parentKey)
    },
    handleTransformItemChange(current) {
      this.currentTransformItem = { ...current }
      const { bankAccountNo, transferAccountingItemCode } = current
      this.$set(this.applicationInfo, 'bankAccount', bankAccountNo)
      this.applicationInfo.transferAccountingItemCode =
        transferAccountingItemCode
      if (bankAccountNo) {
        this.$nextTick(() => {
          this.$refs['_formRef'].validateField('bankAccount')
        })
      }
    },
    // 添加银行格式校验
    handleBlur(event, row) {
      if (row) {
        row.isEdit = true
      } else {
        return true
      }
    },
    handlePayAccountChange() {
      this.getApprovalSetting()
      this.listEntrustType()
    },
    // 获取当前地区的审批事项设置 机构 金额 支出类型
    getApprovalSetting() {
      const { expenditureType = '', amount } = this.applicationInfo
      const accountType = this.accountType
      const settlementType = this.curMethod.settlementType
        ? this.curMethod.settlementType
        : this.applicationInfo.payAccountNumber == '0'
        ? 1
        : 2
      const orgId =
        this.loadType !== 'add'
          ? this.applicationInfo.orgId
          : this.paymentLogin.code
      if (
        typeof amount === 'number' &&
        ((orgId && expenditureType) || (expenditureType && amount > 0)) &&
        accountType
      ) {
        this.matterLoading = true
        const params = {
          id:
            this.loadType === 'view'
              ? this.applicationInfo.approvalSettingsId
              : '',
          orgId,
          expenditureType,
          amount: Math.abs(amount),
          accountType: settlementType == 1 ? '' : accountType,
          settlementType
        }
        loadApprovalSetting(params)
          .then(({ data = [] }) => {
            this.matterOptions = data
            if (!this.matterOptions.length) {
              this.$message.warning('未获取到当前机构的审批设置!')
            }
            const notExist =
              this.matterOptions.filter(
                ({ id }) => id === this.applicationInfo.approvalSettingsId
              ).length === 0
            if (notExist) {
              this.applicationInfo.matter = ''
            }
            this.matterLoading = false
          })
          .catch((error) => {
            console.log(error)
            this.matterOptions = []
            this.matterLoading = false
          })
      }
    },
    handleSelectFundsUse(item) {},
    handlePreviewProcess() {
      if (!this.application.procInstId) {
        return this.$message.warning(
          this.$t('只有已提交的记录才能预览审批流程')
        )
      }
      const url =
        process.env.VUE_APP_BASE_API +
        '/workflow/genProcessDiagram?processInsId=' +
        this.application.procInstId +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      window.open(url)
    },
    handleChangeMatter(val) {
      this.$forceUpdate()
      if (!val || !val.length) return
      const currentSetting = this.matterOptions.find((item) => item.id === val)
      if (currentSetting) {
        const params = { settingId: currentSetting.id }
        loadAttachmentList(params).then((res) => {
          this.currentAttachmentSettings = []
          this.$set(this, 'currentAttachmentSettings', res.data.fileSettings)
          this.$set(this, 'currentApprovalSetting', res.data.setting)
          this.$forceUpdate()
        })
      }
    },
    validateSetting(rule, value, callback) {
      if (this.applicationInfo.matter) {
        callback()
      } else {
        callback(new Error(this.$t('审批事项名称必填')))
      }
    },
    validateBankAccount(rule, value = '', callback) {
      // if (![0, 14, 15, 16, 17, 18, 19].includes(value.length)) {
      //   callback(new Error(this.$t('请输入正确的银行账号')))
      // }
      callback()
    },
    handleSelectReceiver() {
      this.dialogVisible = true
    },
    handleConfirmSelectReceiver() {
      const selected =
        this.$refs['_receiverListRef'].$refs['_geverTableRef'].$refs['elTable']
          .selection
      if (!selected || !selected.length) {
        return this.$message.warning(this.$t('请选择收款人'))
      }
      const receiverData = this.receiverList.filter((cur) => !cur.parentKey)
      const receiverKeys = receiverData.map((cur) => cur.key)
      if (selected.some((cur) => receiverKeys.includes(cur.id))) {
        return this.$message.warning(this.$t('不能重复添加相同收款方'))
      }
      if (selected.length + receiverData.length > 10) {
        return this.$message.warning(
          this.$t(`最多可设置10个收款人(已设置${receiverData.length}人)`)
        )
      }
      const result = []
      selected.forEach((item, key) => {
        result.push({
          receiverAccount: item.receiverName,
          receiverBankAccount: item.bankAccount,
          receiverBank: item.bankType,
          receiverOpenAccountNumber: item.bankNumber,
          receiverOpenAccount: item.bankName,
          isBankAccount: item.isBankAccount,
          amountPayable: undefined,
          remark: '',
          key: item.id,
          isEdit: true
        })
      })
      this.receiverList.push(...result)
      this.dialogVisible = false
    },
    spanMethod({ row, columnIndex }) {
      if (row.parentKey && columnIndex === 2) {
        return {
          rowspan: 1,
          colspan: 3
        }
      }
      if (row.parentKey && [2, 3, 4].includes(columnIndex)) {
        return {
          rowspan: 1,
          colspan: 0
        }
      }
    },
    disabledAddProject(row) {
      const parentProject = this.receiverList.filter(
        (cur) => cur.parentKey === row.key
      )
      return parentProject?.length === 20
    },
    handleDeleteProject(index, row) {
      const brothers = this.receiverList.filter(
        (cur) => cur.parentKey === row.parentKey
      )
      if (brothers.length <= 1) {
        return this.$message.warning(
          '删除操作无效, 每个收款方下需至少保留一个项目'
        )
      }
      // this.receiverList.splice(index, 1)
      this.$delete(this.receiverList, index)
      this.countAmountPayable(row.parentKey)
    },
    selectable(row) {
      return !row.parentKey
    },
    handleAddProject({ row, $index }) {
      const emptyRow = {
        receiverAccount: '',
        receiverBankAccount: '',
        amountPayable: undefined,
        remark: '',
        key: `${new Date().getTime()}`,
        parentKey: row.key,
        isEdit: true
      }
      const parentProject = this.receiverList.filter(
        (cur) => cur.parentKey === row.key
      )
      const parentProjectLastIndex = !parentProject.length
        ? $index
        : this.receiverList.findIndex(
            (cur) => cur.key === parentProject[parentProject.length - 1].key
          )
      this.receiverList.splice(parentProjectLastIndex + 1, 0, emptyRow)
    },
    handleAddReceiver() {
      const emptyRow = {
        receiverAccount: '',
        receiverBankAccount: '',
        receiverOpenAccountNumber: '',
        receiverOpenAccount: '',
        amountPayable: undefined,
        receiverBank: '',
        remark: '',
        key: `${new Date().getTime()}`,
        isEdit: true
      }
      this.receiverList.push(emptyRow)
    },
    amountPayableBlur(row) {
      row.isEdit = true
      if (
        this.applicationInfo.expenditureType ===
          '909440E63C6D11B2B92E7263D79FDBFF' &&
        row.isProject &&
        row.isProject === '1'
      ) {
        // 查询到和收款方一致的预算项目
        const newArr = []
        this.receiverList.forEach((item) => {
          if (item.parentKey && item.parentKey == row.parentKey) {
            newArr.push(item)
          }
        })
        // 查询到预算项目对应的收款方
        const obj = this.receiverList.find((item) => item.key == row.parentKey)
        const num = newArr.reduce(
          (sum, item) => sum + (item.amountPayable ? item.amountPayable : 0),
          0
        )
        if (num != obj.amountPayable * 1) {
          return this.$message.warning(
            `请修正预算项目金额值总计保持与收款方【${obj.receiverAccount}】金额一致！`
          )
        }
      } else {
        this.countAmountPayable(row.parentKey)
      }
      // this.countAmountPayable(row.parentKey)
    },
    // 计算父级合计金额
    countAmountPayable(parentKey) {
      if (!parentKey) return
      if (
        this.applicationInfo.expenditureType ===
        '909440E63C6D11B2B92E7263D79FDBFF'
      ) {
        // 查询到预算项目对应的收款方
        const obj = this.receiverList.find((item) => item.key === parentKey)
        if (obj.projectPerformance) {
          return
        }
      }

      const parentIndex = this.receiverList.findIndex(
        (cur) => cur.key === parentKey
      )
      const tableData = _.cloneDeep(this.receiverList)

      let amountPayable = 0
      tableData.forEach((item, i) => {
        if (item.parentKey === parentKey) {
          amountPayable += Number(!item.amountPayable ? 0 : item.amountPayable) ?? 0
        }
      })
      // if (
      //   this.applicationInfo.expenditureType !== '909440E63C6D11B2B92E7263D79FDBFF'
      // ) {
      //   tableData[parentIndex].amountPayable = amountPayable.toFixed(2) * 1
      // }
      tableData[parentIndex].amountPayable = amountPayable.toFixed(2) * 1
      this.receiverList = tableData
    },
    cleanReceiverForProject() {
      if (this.loadType != 'view' && this.isProject && this.isProject == '1' && this.receiverList.length > 0 ) {
        const selectKeys = this.receiverList
          .filter((cur) => cur.projectPerformance)
          .map((cur) => cur.id || cur.key)
        this.handleDeleteReceiverByKey(selectKeys)
      }
      if (
        this.applicationInfo.expenditureType ==
        '909440E63C6D11B2B92E7263D79FDBFF'
      ) {
        this.isProject = '1'
      } else {
        this.isProject = '0'
      }
    },
    handleDeleteReceiver() {
      if (!this.selectionData.length) {
        return this.$message.warning(this.$t('请选择要删除的收款方！'))
      }
      const parents = this.receiverList.filter((cur) => !cur.budgetItemId)
      if (
        this.selectionData.length >= parents.length &&
        this.applicationInfo.expenditureType !=
          '909440E63C6D11B2B92E7263D79FDBFF'
      ) {
        return this.$message.warning(this.$t('请至少保留一个收款方！'))
      }
      // const parentId = this.loadType === 'edit' ? 'id' : 'key'
      const selectKeys = this.selectionData.map((cur) => cur.id || cur.key)
      this.handleDeleteReceiverByKey(selectKeys)
    },
    handleDeleteReceiverByKey(selectKeys) {
      const newList = []
      this.receiverList.forEach((cur, index) => {
        if (
          !(
            selectKeys.includes(cur.id || cur.key) ||
            selectKeys.includes(cur.parentKey)
          )
        ) {
          newList.push(cur)
        }
      })
      this.receiverList = _.cloneDeep(newList)
      // 修改总金额
      // if (
      //  this.applicationInfo.expenditureType ==
      //  '909440E63C6D11B2B92E7263D79FDBFF'
      // ) {
      //   const num = this.receiverList.reduce(
      //     (sum, item) => sum + item.amountPayable * 1,
      //     0
      //   )
      //   this.applicationInfo.amount = num
      // }
    },
    handleSelectionChange(selection) {
      this.selectionData = selection
    },
    editActived({ row, column }) {
      if (
        (!row.parentKey &&
          [
            'receiverAccount',
            'receiverBankAccount',
            'receiverOpenAccount',
            'remark'
          ].includes(column.field)) ||
        row.parentKey
      ) {
        return true
      }
      return false
    },
    handleCellClick(row, column, cell, event) {
      if (column.property === 'budgetItemId') return
      const tableData = _.cloneDeep(this.$refs._tableRef.tableData)
      this.receiverList = tableData.map((item) => {
        item.isEdit = item.key !== row.key
        return item
      })
      // this.$refs._tableRef.doLayout()
    },
    handleSearchBankType(query) {
      if (query !== '') {
        this.bankLoading = true
        const params = {
          keyword: query
        }
        searchBankNumber(params).then((res) => {
          this.selectOptions = res.data.rows
          this.bankLoading = false
        })
      } else {
        this.selectOptions = []
      }
    },
    handleInvalid() {
      this.$refs['_validRef'].validate((valid) => {
        if (valid) {
          this.inValid.id = this.application.id
          tovoid(this.inValid)
            .then(({ message }) => {
              console.log('作废完成5555')
              this.$message.success(message)
              this.$emit('success')
              this.$emit('Invalid', 'true')
              this.$emit('closeDrawer')
            })
            .finally(() => {
              this.$emit('Invalid', 'true')
            })
        } else {
          this.$emit('Invalid', 'true')
        }
      })
    },
    handleSelectChange(val, row) {
      const type = this.selectOptions.find((item) => item.bankName === val)
      row.receiverBank = type.bankType
      row.receiverOpenAccountNumber = type.number
    },
    handleSaveApplication(isCommit) {
      const {
        settlementMethod,
        useBudgetFlag,
        businessYear,
        applicationType,
        staffHandler,
        staffReviewer,
        staffCertifier,
        staffAccepter
      } = this.applicationInfo
      this.applicationInfo.projectPayRecordDTOList =
        this.relatedConstructContract
      if (
        staffHandler &&
        staffReviewer &&
        (
          staffHandler === staffReviewer ||
          staffHandler === staffCertifier ||
          staffReviewer === staffCertifier ||
          staffAccepter === staffReviewer ||
          staffHandler === staffAccepter ||
          (staffAccepter && staffCertifier && staffAccepter === staffCertifier)
        )
      ) {
        return this.$message.warning(
          this.$t('证明人,经手人,审核人,验收人必须是四个不同的人')
        )
      }
      if (this.applicationInfo.fundsUse.length > 300) {
        return this.$message.warning(this.$t('资金用途不能大于300个中文字符'))
      }
      if (this.applicationInfo.payMethod == 2 && !this.isRefund) {
        this.$refs['_entrustformRef'].validate((valid) => {
          if (!valid) return
        })
        if (!this.applicationInfo.interOrOut) {
          return
        }
        if (!this.applicationInfo.entrustType) {
          return
        }
      }
      if (this.curMethod.businessType == 1 && !this.isRefund) {
/*        if (this.applicationInfo.fundsUse.length > 20 && this.applicationInfo.payMethod == 2) {
          return this.$message.warning(this.$t('使用委托支付时，资金用途不能大于20个中文字符'))
        }
        if (this.applicationInfo.fundsUse.length > 30) {
          return this.$message.warning(this.$t('使用银农直连时，资金用途不能大于30个中文字符'))
        }*/
        // 定义正则表达式，匹配英文符号
        const regex = /[!@#$%^&*()_+\-=$${};':"\\|,<>/?]+/
        if (regex.test(this.applicationInfo.fundsUse)) {
          return this.$message.warning(this.$t('使用银农直连时，资金用途不能包含英文符号'))
        }
      }
      // eslint-disable-next-line complexity
      this.$refs['_formRef'].validate((valid) => {
        if (!valid) return
        if (
          applicationType == 4 &&
          settlementMethod !== '1747078583725600770'
        ) {
          return this.$message.warning(
            this.$t('结算方式、申请类型必须同时为“银行代扣”')
          )
        }
        if (this.applicationInfo.isBigBatch == 1) {
          if (this.$refs._importReceiverRef.fileList.length === 0) {
            return this.$message.warning(this.$t('大批量支付必须上传数据文件'))
          }
          this.applicationInfo.importFileBatchId = this.importFileBatchId
        } else {
          if (
            settlementMethod === 1 &&
            this.sum !== this.applicationInfo.amount
          ) {
            return this.$message.warning(
              this.$t('支出申请金额与收款方合计金额不一致')
            )
          }
          if (!this.receiverList?.length) {
            return this.$message.warning(this.$t('请至少设置一个收款方'))
          }
          if (this.applicationInfo.payMethod == 2 && this.receiverList.filter(r => !r.parentKey).length > 1) {
            return this.$message.warning(this.$t('委托支付模式只需填写一个收款方,实际收款方信息通过附件导入'))
          }
          if (this.curMethod.businessType == 1 && this.receiverList.filter(r => !r.parentKey).length > 50) {
            return this.$message.warning(this.$t('银农直连普通模式最多只能50个收款方'))
          }
          let paymentAmount = 0

          for (let index = 0; index < this.receiverList.length; index++) {
            const item = this.receiverList[index]
            // 处理工程
            // if (item.projectPerformance && this.applicationInfo.expenditureType !=
            //   '909440E63C6D11B2B92E7263D79FDBFF') {
            //   this.receiverList[index].projectPerformance = ''
            // }
            if (!item.parentKey && !this.isRefund && item.amountPayable <= 0) {
              return this.$message.warning(
                this.$t(`第${index + 1}个收款方金额不能小于等于0`)
              )
            }
            // 收支相抵 票据关联校验
            if (this.applicationInfo.applicationType == 1 && !item.parentKey) {
              // if (item.isClean == 1) {
              //   return this.$message.warning(this.$t(`第${index + 1}行收款方，申请类型为收支相抵，必须关联票据`))
              // }
              // if (!item.relBillList || item.relBillList.length === 0) {
              //   item.relBillList = item.deductRelList
              // }
              if (!item.deductRelList || item.deductRelList.length === 0) {
                return this.$message.warning(this.$t(`第${index + 1}行收款方，申请类型为收支相抵，必须关联票据`))
              }
              const deductBusinessAmount = item.deductRelList.map(
                (item) => item.deductBusinessAmount
              )
              if (
                item.amountPayable !=
                deductBusinessAmount.reduce((acc, curr) => acc + curr, 0).toFixed(2)
              ) {
                return this.$message.warning(this.$t(`第${index + 1}行收款方票据的金额合计与收款方信息的金额必须一致`))
              }
              if (item.deductRelList.some(bill => bill.deductDrawee && bill.deductDrawee != item.receiverAccount)) {
                return this.$message.warning(this.$t(`第${index + 1}行收款方票据的缴款人与收款方信息必须一致`))
              }
            }
            if (
              (item.parentKey && !item.budgetItemId) ||
              (applicationType !== 3 &&
                applicationType !== 2 &&
                useBudgetFlag &&
                !item.parentKey &&
                this.receiverList.every((cur) => cur.parentKey !== item.key))
            ) {
              return this.$message.warning(this.$t('收款人必须关联项目'))
            }
            if (!item.parentKey) {
              paymentAmount =
                Number(paymentAmount).toFixed(2) * 1 + Number(item.amountPayable)
            }
              if (!this.isRefund && !item.parentKey && !item.receiverAccount) {
              return this.$message.warning(
                this.$t(`第${index + 1}行收款方银行账户名称必填`)
              )
            }
            if (
              this.curMethod.businessType != 1 &&
              this.applicationInfo.payMethod != 2 &&
              !this.isRefund &&
              !this.cashCollection &&
              !item.parentKey &&
              !item.receiverBankAccount
            ) {
              return this.$message.warning(
                this.$t(`第${index + 1}行收款方银行账号必填`)
              )
            }
            if (
              this.curMethod.businessType != 1 &&
              this.applicationInfo.payMethod != 2 &&
              !this.isRefund &&
              !this.cashCollection &&
              !item.parentKey &&
              !item.receiverOpenAccount
            ) {
              return this.$message.warning(
                this.$t(`第${index + 1}行收款方开户银行必填`)
              )
            }
            if (this.curMethod.businessType == 1 && this.applicationInfo.payMethod != 2 && !item.parentKey && !item.receiverOpenAccountNumber) {
              return this.$message.warning(
                this.$t(`第${index + 1}行收款方开户行联行号不能为空，请重新选择开户行`)
              )
            }
            if (
              this.bocBankFlag &&
              !this.cashCollection &&
              !item.parentKey &&
              !item.transferType
            ) {
              return this.$message.warning(
                this.$t(`第${index + 1}行转账类型必填`)
              )
            }
          }
          // if (
          //   !this.applicationInfo.expenditureType ==
          //   '909440E63C6D11B2B92E7263D79FDBFF'
          // ) {
          if (
            paymentAmount.toFixed(2) !==
            Number(this.applicationInfo.amount).toFixed(2)
          ) {
            return this.$message.warning(
              this.$t('申请金额与收款方合计金额不一致')
            )
          }
          // }
        }
        if (!this.attachmentValid()) {
          return this.$message.warning(this.$t('请上传附件'))
        }
        const params = {
          useBudgetFlag,
          businessYear,
          businessType: this.curMethod.businessType,
          payAccountId: this.applicationInfo.payAccountId,
          payAccountNumber: this.applicationInfo.payAccountNumber,
          payAccountOwner: this.applicationInfo.payAccountOwner,
          returnFlag: this.isRefund ? 1 : 0,
          matter: this.applicationInfo.matter,
          docNumber: this.applicationInfo.docNumber,
          settlementMethod: this.applicationInfo.settlementMethod,
          id:
            this.isRefund && !this.applicationInfo.returnFlag
              ? ''
              : this.applicationInfo.id || '',
          batchIds: this.batchIds,
          amount: this.applicationInfo.amount,
          applicationType: this.applicationInfo.applicationType,
          offsetNoteType: this.applicationInfo.offsetNoteType,
          expenditureType: this.applicationInfo.expenditureType,
          fundsUse: this.applicationInfo.fundsUse,
          fundSrc: this.applicationInfo.fundSrc,
          details: this.applicationInfo.details,
          staffHandler: this.applicationInfo.staffHandler,
          staffCertifier: this.applicationInfo.staffCertifier,
          staffReviewer: this.applicationInfo.staffReviewer,
          staffAccepter: this.applicationInfo.staffAccepter,
          orgName: this.initInfo.orgName,
          submitUserName: this.applicationInfo.submitUserName,
          creationTime: moment(this.applicationInfo.creationTime).format(
            'YYYY-MM-DD 00:00:00'
          ),
          reimVoucherType: this.applicationInfo.reimVoucherType,
          requisitionExpenditure: this.applicationInfo.requisitionExpenditure,
          billNo: this.applicationInfo.billNo,
          payMethod: this.applicationInfo.payMethod,
          entrustType: this.applicationInfo.entrustType,
          interOrOut: this.applicationInfo.interOrOut,
          accruedExpenseList: [], // 应付费用关联
          orgId:
            this.loadType !== 'add'
              ? this.applicationInfo.orgId
              : this.paymentLogin.code,
          receiverSettlementType: this.applicationInfo.receiverSettlementType,
          isBigBatch: this.applicationInfo.isBigBatch,
          importFileBatchId: this.importFileBatchId,
          rows: this.receiverList.map((cur) => {
            if (this.isRefund) {
              cur.returnFlag = this.isRefund ? 1 : 0
              if (!this.applicationInfo.returnFlag) {
                cur.sourceId = cur.id || cur.sourceId
                cur.id = ''
              }
            }
            return cur
          })
        }
        if (this.bankType === '************' && this.applicationInfo.isBigBatch != 1) {
          /* 验证顺德农商行明细是否超过1000行*/
          if (this.receiverList.length > 1000) {
            return this.$message.warning(
              this.$t('收款方信息数量超过500，请修改！')
            )
          }
        }
        if (this.isRefund) {
          params.sourceId = this.applicationInfo.returnFlag
            ? this.applicationInfo.sourceId
            : this.applicationInfo.id
        }
        if (this.applicationInfo.amount > this.initInfo.balance) {
          this.$confirm('当前金额不足，仍要继续提交支出申请吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.handleSureSave(params, isCommit)
          })
        } else {
          this.handleSureSave(params, isCommit)
        }
      })
    },
    handleSureSave(params, isCommit) {
      this.$emit('loading', true)
      this.loading = true
      saveApprovalApplication(params)
        .then((res) => {
          if (res.returnCode === '0') {
            if (isCommit) {
              this.handleSureCommit(res.data)
              return
            }
            this.loading = false
            this.$message.success(res.message)
            this.$emit('closeDrawer')
            this.$emit('loading', false)
          } else {
            this.$message.error(res.message)
            this.$emit('loading', false)
          }
        })
        .catch(() => {
          this.loading = false
          this.$emit('loading', false)
        })
    },
    handleSureCommit(id) {
      submitApprovalApplication({ id })
        .then((res) => {
          if (res.returnCode === '0') {
            this.loading = false
            this.$message.success(res.message)
            this.$emit('closeDrawer')
            this.$emit('loading', false)
          } else {
            this.loading = false
            this.$message.error(res.message)
            this.$emit('loading', false)
          }
        })
        .finally(() => {
          this.loading = false
          this.$emit('loading', false)
        })
    },
    filterSelection(list) {
      let arr = []
      list.forEach((item, index) => {
        const obj = {
          payscheduleId: item.paymentId,
          payamount: item.nowPayMoney
        }
        arr.push(obj)
      })
      if (arr.length === 0 && this.loadType === 'edit') {
        arr = this.applicationInfo.projectPayRecordDTOList
      }

      return JSON.stringify(arr)
    },
    // eslint-disable-next-line complexity
    handleConfirmImport() {
      const rowsNum = this.$refs['_importRef'].table.countRows()
      if (rowsNum > 1) {
        const data = JSON.parse(
          JSON.stringify(this.$refs['_importRef'].table.getSourceData())
        )
        const message = {}
        for (const i in data) {
          message[i] = []
          const index = parseInt(i) + 1
          if (index === 1) continue
          if (
            data[i].receiverAccount == null ||
            !/^.{1,70}$/.test(data[i].receiverAccount)
          ) {
            message[i].push('收款方银行账户名称不能为空且长度不能大于70个字符')
          }

          for (let j = 0; j < data.length; j++) {
            if (j != i && data[i] == data[j]) {
              message[i].push('收款方银行账号不能重复')
            }
          }

          if (!data[i].receiverBankAccount) {
            message[i].push('收款方银行账号不能为空')
          }

          // if (
          //   data[i].receiverBankAccount &&
          //   !/^([1-9]{1})(\d{14}|\d{16}|\d{18})$/.test(
          //     data[i].receiverBankAccount
          //   )
          // ) {
          //   message[i].push('收款方银行账号应为15位或17位或19位数字')
          // }

          if (
            !data[i].receiverOpenAccount ||
            !data[i].receiverOpenAccountNumber
          ) {
            message[i].push('收款方开户银行不能为空')
          }

          if (data[i].amountPayable === 0) {
            message[i].push('金额不能为0')
          } else if (!data[i].amountPayable) {
            message[i].push('金额不能为空')
          }

          data[i].key = new Date().getTime() + i
        }
        if (JSON.stringify(message) !== '{}') {
          let msg = ''
          for (const key in message) {
            if (message[key].length) {
              msg += message[key].reduce((total, curr) => {
                if (curr) {
                  const temp = `第${key}条数据, ${curr}<br />`
                  return total + temp
                } else {
                  return total
                }
              }, '')
            }
          }
          if (msg) {
            this.$confirm(msg, {
              dangerouslyUseHTMLString: true,
              confirmButtonText: this.$t('确定'),
              showClose: false,
              showCancelButton: false,
              customClass: 'receiver-alert',
              type: 'warning'
            })
          } else {
            data.shift()
            this.receiverList.push(...data)
            this.batchImportVisible = false
          }
        } else {
          data.shift()
          this.receiverList.push(...data)
          this.batchImportVisible = false
        }
      } else {
        this.$message.warning(this.$t('请先填充数据'))
      }
    },
    attachmentValid() {
      let valid = true
      const attachmentArr = this.$refs['_attachmentRef']
      this.batchIds = []
      for (let i = 0; i < attachmentArr.length; i++) {
        this.batchIds.push(attachmentArr[i].uuid)
        const required = attachmentArr[i].attachment.notNull
        if (
          required &&
          attachmentArr[i].$refs['_uploadRef'].uploadFiles.length <= 0
        ) {
          valid = false
          break
        }
      }
      return valid
    },
    // eslint-disable-next-line complexity
    tableDataValid() {
      let valid = true
      let sum = 0
      if (this.applicationInfo.receiverSettlementType === '1') {
        console.log('this.applicationInfo.receiverSettlementTyp')
        for (let i = 0; i < this.receiverList.length; i++) {
          const rowData = this.receiverList[i]
          valid =
            valid &&
            rowData.receiverAccount.length &&
            rowData.receiverBankAccount.length &&
            rowData.receiverOpenAccountNumber.length &&
            rowData.receiverOpenAccount.length &&
            (rowData.amountPayable + '').length &&
            this.handleBlur(rowData.receiverBankAccount)
          sum =
            sum +
            Number(isNaN(rowData.amountPayable) ? 0 : rowData.amountPayable)
        }
        console.log('---', valid)
      } else {
        for (let j = 0; j < this.receiverList.length; j++) {
          const rowDatas = this.receiverList[j]
          valid =
            valid &&
            rowDatas.receiverAccount.length &&
            rowDatas.receiverBankAccount.length &&
            (rowDatas.amountPayable + '').length &&
            this.handleBlur(rowDatas.receiverBankAccount)
          sum =
            sum +
            Number(isNaN(rowDatas.amountPayable) ? 0 : rowDatas.amountPayable)
        }
      }
      this.sum = Number((Math.round(sum * 100) / 100).toFixed(2))
      return valid
    },
    failure(index) {
      const message = this.$refs['_importRef'].validateMessage
      if (message.length) {
        let msg = ''
        message.forEach((item) => {
          msg += `第${Object.keys(item)[0]}条数据, ${
            item[Object.keys(item)[0]]
          }<br />`
        })
        this.$confirm(msg, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('确定'),
          showClose: false,
          showCancelButton: false,
          type: 'warning'
        })
      }
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }
        if (index !== 5) {
          sums[index] = ''
          return
        }
        const parentData = data.filter((cur) => !cur.parentKey)
        const values = parentData.map((item) => Number(item.amountPayable || 0))
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] = formatMoney(sums[index].toFixed(2)) + '元'
        } else {
          sums[index] = '0'
        }
        // }
      })
      return sums
    },

    querySearch(queryString, cb) {
      const data = {
        orgId: this.applicationInfo.orgId,
        fundsUse: this.applicationInfo.fundsUse
      }
      selectFundsUse(data)
        .then((res) => {
          this.restaurants = res.data
          var results = queryString
            ? res.data.filter(this.createFilter(queryString))
            : res.data
          console.log(results, 'results')
          // 调用 callback 返回建议列表的数据
          cb(results.map((c) => ({ value: c })))
        })
        .catch((err) => {
          console.log(err)
        })
    },
    createFilter(queryString) {
      return (restaurant) => {
        console.log(restaurant, 'restaurant')
        console.log(
          restaurant.toLowerCase().indexOf(queryString.toLowerCase()) === 0,
          '111'
        )
        return restaurant.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    toDecimal(x) {
      let f = parseFloat(x)
      if (isNaN(f)) {
        return
      }
      f = Math.round(x * 100) / 100
      return f
    },
    handleRelatedConstructContract(selection = []) {
      this.selection = selection
    },
    handleContract() {
      this.isShowContract = true
    },
    handleConfirmContractReceiver() {
      const valid = this.selection.every(
        ({ nowPayMoney = 0 }) => nowPayMoney > 0
      )
      if (this.selection.length === 0) {
        this.$message.warning('请选择要关联的数据')
        return
      }
      if (valid) {
        const total =
          this.selection.reduce(
            (total, { nowPayMoney = 0 }) => total + 100 * nowPayMoney,
            0
          ) / 100
        const { credit } = this.applicationInfo
        const { length } = this.receiverList
        // 编辑不与关联流水总金额比较
        if (length) {
          this.relatedConstructContract = this.selection.map(
            ({ paymentId = '', nowPayMoney = 0 }) => ({
              payscheduleId: paymentId,
              payamount: nowPayMoney
            })
          )
          this.applicationInfo.amount = total
          this.isShowContract = false
        }
        if (!length || credit === total) {
          this.relatedConstructContract = this.selection.map(
            ({ paymentId = '', nowPayMoney = 0 }) => ({
              payscheduleId: paymentId,
              payamount: nowPayMoney
            })
          )
          this.applicationInfo.amount = total
          this.isShowContract = false
        }
      } else {
        this.$message.warning('本次支付金额必须大于零!')
      }
    },
    handleChangeReceiverAccount(row) {
      this.receiverList.forEach((item) => {
        if ((item.key && item.key === row.key)
          || (item.id && item.id === row.id)) {
          this.getOldRelBillIds()
          if (this.applicationInfo.applicationType == 1 && item.deductRelList && item.deductRelList.length > 0) {
            this.$message.warning(this.$t('收款人信息已变更，请重新关联票据！'))
            item.deductRelList = []
            item.isClean = 1
          }
        }
      })
    },
    /* 关联票据 */
    handleOpenBill() {
      if (!this.selectionData.length || this.selectionData.length !== 1) {
        return this.$message.warning(this.$t('请选择1个收款方！'))
      }
      if (!this.selectionData[0].receiverAccount) {
        return this.$message.warning(this.$t('请先填写收款方信息！'))
      }
      if (!this.selectionData[0].amountPayable) {
        return this.$message.warning(this.$t('请先输入金额！'))
      }
      // if (this.selectionData[0].isClean == 1) {
      //   this.selectionData[0].relBillList = []
      // } else if (!this.selectionData[0].relBillList || this.selectionData[0].relBillList.length == 0) {
      //   this.selectionData[0].relBillList = this.selectionData[0].deductRelList
      // }
      this.selectBillIds = []
      if (this.selectionData[0].deductRelList) {
        this.selectBillIds = this.selectionData[0].deductRelList.map(
          (item) => item.deductBusinessId
        )
      }
      console.log(this.receiverList)
      // 获取所有已关联保存的id
      this.relBillIds = []
      for (let index = 0; index < this.receiverList.length; index++) {
        const receiver = this.receiverList[index]
        if (this.applicationInfo.applicationType == 1 && !receiver.parentKey
          && this.selectionData[0].receiverAccount == receiver.receiverAccount) {
          if (!receiver.hasOld) {
            if (this.loadType == 'edit' && (!receiver.oldRelBillList)) {
              this.receiverList[index].oldRelBillList = receiver.deductRelList
            } else {
              this.receiverList[index].oldRelBillList = []
            }
            this.receiverList[index].hasOld = true
          }
          if (this.receiverList[index].oldRelBillList && this.receiverList[index].oldRelBillList.length > 0) {
            this.relBillIds = this.relBillIds.concat(
              this.receiverList[index].oldRelBillList.map(
                (item) => item.deductBusinessId
              )
            )
          }
        }
      }

      // 其他收款人关联的票据
      this.otherSelectBillIds = []
      for (let index = 0; index < this.receiverList.length; index++) {
        const receiver = this.receiverList[index]
        // if (receiver.isClean == 1) {
        //   receiver.relBillList = []
        // } else if (!receiver.relBillList || receiver.relBillList.length === 0) {
        //   receiver.relBillList = receiver.deductRelList
        // }
        if (this.applicationInfo.applicationType == 1 && !receiver.parentKey
          && receiver.key != this.selectionData[0].key
          && receiver.deductRelList && receiver.deductRelList.length > 0) {
          this.otherSelectBillIds = this.otherSelectBillIds.concat(
            receiver.deductRelList.map(
              (item) => item.deductBusinessId
            )
          )
        }
      }
      this.billVisible = true
    },
    // 选择票据确定
    handleBillSelect() {
      const selected = this.$refs['_billRef'].nowSelectData
      if (!selected || !selected.length) {
        return this.$message.warning(this.$t('请选择需要冲销的票据'))
      }
      const deductBusinessAmount = selected.map(
        (item) => item.totalAmount
      )
      if (
        this.selectionData[0].amountPayable !=
        deductBusinessAmount.reduce((acc, curr) => acc + curr, 0).toFixed(2)
      ) {
        return this.$message.warning(
          '票据的金额合计与收款方信息的金额必须一致'
        )
      }
/*      for (let index = 0; index < this.receiverList.length; index++) {
        const receiver = this.receiverList[index]
        if (!receiver.relBillList || receiver.relBillList.length == 0) {
          receiver.relBillList = receiver.deductRelList
        }
        if (this.applicationInfo.applicationType == 1 && !receiver.parentKey
          && receiver.relBillList && receiver.relBillList.length > 0) {
          selected.forEach(item => {
            if (receiver.relBillList.some(cur => cur.deductBusinessId === item.id)) {
              return this.$message.warning(
                '请勿重复选择'
              )
            }
          })
        }
      }*/
      const relBillList = []
      selected.forEach((cur) => {
        const relBill = {}
        relBill.deductBusinessId = cur.id
        relBill.deductBusinessNo = [1, 3].includes(this.applicationInfo.offsetNoteType)
          ? cur.billNo
          : cur.invoiceCode + cur.invoiceNo
        console.log(cur)
        relBill.deductBusinessAmount = cur.totalAmount
        relBill.deductDrawee = [1, 3].includes(this.applicationInfo.offsetNoteType)
          ? cur.otherSide
          : cur.payerName
        relBillList.push(relBill)
      })
      this.receiverList.forEach((item) => {
        if ((item.key && item.key === this.selectionData[0].key)
          || (item.id && item.id === this.selectionData[0].id)) {
          item.deductRelList = relBillList
          item.isClean = 0
        }
      })
      this.billVisible = false
    },
    getOldRelBillIds() {
      for (let index = 0; index < this.receiverList.length; index++) {
        const receiver = this.receiverList[index]
        if (this.applicationInfo.applicationType == 1 && !receiver.parentKey) {
          if (!receiver.hasOld) {
            if (this.loadType == 'edit' && (!receiver.oldRelBillList)) {
              this.receiverList[index].oldRelBillList = receiver.deductRelList
            } else {
              this.receiverList[index].oldRelBillList = []
            }
            this.receiverList[index].hasOld = true
          }
        }
      }
    },
    listEntrustType() {
      this.entrustTypeList = []
      // this.entrustTypeList = []
      listEntrustType(this.applicationInfo.payAccountId).then((res) => {
        // 获取已关联的数据列表
        const entrusTypeRel = res.data
        const entrustTypeValue = new Set()
        for (const item of entrusTypeRel) {
          entrustTypeValue.add(item.entrustType)
        }
        for (const dirt of this.mapOptins.entrustTypeOptions) {
            if (entrustTypeValue.has(dirt.id)) {
              this.entrustTypeList.push(dirt)
            }
        }
      })
    },
    /**
     * 数字保留小数位显示
     * @param {*} val 待转量(Number/String)
     * @param {*} keep_dec 小数点位数
     * @returns String
     */
    comma(val, keep_dec) {
      if (val == undefined) {
        return ''
      }
      if (keep_dec) {
        val = Number(val).toFixed(keep_dec)
      }
      val = String(val)
      if (val.indexOf('.') == -1) {
        return val.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,')
      }
      return (
        val.split('.')[0].replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,') +
        '.' +
        val.split('.')[1]
      )
    },
    handleCreationTime() {
      if (this.applicationInfo.creationTime) {
        if (new Date(this.applicationInfo.creationTime).getTime() > new Date().getTime()) {
          this.$confirm('所选日期为一个未到的日期，是否继续选择', this.$t('提示'), {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          }).then(() => {
              this.creationTime = this.applicationInfo.creationTime
              // this.$set(this.applicationInfo, 'businessYear', moment(this.applicationInfo.creationTime).format('yyyy'))
            })
            .catch(() => {
              this.applicationInfo.creationTime = this.creationTime
            })
        } else {
          this.creationTime = this.applicationInfo.creationTime
          // this.$set(this.applicationInfo, 'businessYear', moment(this.applicationInfo.creationTime).format('yyyy'))
        }
      }
    },
    changeImportFileBatch(batchId) {
      this.importFileBatchId = batchId
    },
    handleSwitchBigBatch(val) {
      if (val == 0 && this.$refs._importReceiverRef.fileList.length != 0) {
        this.receiverList = []
      }
    },
    setSwitchBigBatch() {
      this.$set(this.applicationInfo, 'isBigBatch', this.applicationInfo.isBigBatch)
    },
    deleteEntrustFileHandle(file) {
      return '删除委托清单附件后会清空之前上传的数据，<br>确定删除？'
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-textarea__inner {
  font-size: 15px;
}
::v-deep .invalid-ele {
  position: fixed;
  bottom: 38px;
  right: 30px;
  left: 30%;
  background-color: #fff;
  z-index: 9;
}
::v-deep .my-search {
  .vxe-input--suffix {
    padding: 0 20px;
    border-left: 1px solid #dcdfe6;
  }
}
::v-deep form.add-receiver.el-form {
  max-height: 300px;
  overflow-y: auto;
}
::v-deep .del-arrow input::-webkit-outer-spin-button,
::v-deep .del-arrow input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
.el-select-dropdown li.el-select-dropdown__item.tree-select {
  padding: 0;
}
::v-deep
  .gever-form
  .form-sg
  .el-form-item
  .el-form-item__content
  .el-textarea__inner {
  min-height: auto !important;
}

// ::v-deep .el-table__body-wrapper {
//   height: auto !important;
// }
::v-deep .el-input-number {
  .el-input-number__decrease,
  .el-input-number__increase {
    display: none;
  }
  input {
    text-align: left;
  }
}
.el-table .error-row {
  background: red;
}
::v-deep .el-table .el-input__inner {
  // border-radius: 0;
  // border-top-width: 0px;
  // border-left-width: 0px;
  // border-right-width: 0px;
  // border-bottom-width: 1px;
}
::v-deep .el-input-group__append {
  // border-left: 1px solid #dcdfe6;
  // border-top-left-radius: 4px;
}
::v-deep .el-table tbody tr:hover > td {
  background-color: #ffffff !important;
}
.alarm-msg {
  border: 1px #ffdb47 solid;
  background-color: #fff7d3;
  padding: 5px 10px;
  i {
    color: #ffdb47;
    font-size: 16px;
  }
  h2 {
    font-weight: bold;
    margin-bottom: 5px;
  }
  .contract {
    color: #1890ff;
    width: 40px;
  }
}
</style>

<style lang="scss">
.receiver-alert {
  width: unset !important;
}
::v-deep .budget-dialog {
  z-index: 1001;
  .el-dialog {
    width: fit-content !important;
  }
  .el-table__body-wrapper {
    height: 459px !important;
  }
}
.el-row--flex {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.el-textarea.is-disabled .el-textarea__inner {
  color: #000000;
}

.budget-dialog {
  .el-main {
    display: flex;
    flex-direction: column;
    .budget-table {
      flex: 1;
      height: 0;
      .table-box {
        height: 100% !important;
        .el-table {
          height: 100% !important;
          .el-table__body-wrapper {
            height: calc(100% - 40px) !important;
          }
        }
      }
    }
  }
}
.blue {
  color: blue !important;
}
</style>
