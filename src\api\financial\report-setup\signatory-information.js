/*
 * @Descripttion:结算方式接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-29 16:40:25
 * @LastEditors: Andy
 * @LastEditTime: 2023-07-27 14:30:59
 */

import request from '@/utils/request'

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/report/reportPersonnel/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}


/**
 * @name:人员管理——新增/修改
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/report/reportPersonnel/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:人员管理-检查重复
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function checkData(data) {
  return request({
    url: `/financial/report/reportPersonnel/checkData`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:机构类型
 * @param {*} param
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function signer() {
  return request({
    url: `/system/dictionary/combotree?path=/financial/settings/reportSettings/signer/`,
    method: 'get'
  })
}

/**
 * @name:落款人类型
 * @param {*} param
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function signerType(data) {
  return request({
    url: `/system/dictionary/combotree?path=/financial/settings/reportSettings/signer/${data}/`,
    method: 'get'
  })
}

/**
 * @name:删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteData(data) {
  return request({
    url: `/financial/report/reportPersonnel/delete`,
    data,
    method: 'post',
  })
}

/**
 * 复制到下年
 * @param {*} data 
 * @returns 
 */
export function copyToNext(data) {
  return request({
    url: `/financial/report/reportPersonnel/copyToNext`,
    method: "post",
    data,
    withCredentials: true,
  })
}