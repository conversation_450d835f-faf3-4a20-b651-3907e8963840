/*
 * @Descripttion: 
 * @version: 
 * @Author: 未知
 * @Date: 2023-10-09 16:59:46
 * @LastEditors: Andy
 * @LastEditTime: 2023-10-12 09:06:56
 */
import request from '@/utils/request'

/* 列表 */
export function page(data) {
  return request({
    url: '/bill/sundryBillType/page',
    method: 'post',
    data: data,
  })
}

/* 保存/编辑 */
export function save(data) {
  return request({
    url: '/bill/sundryBillType/save',
    method: 'post',
    data: data,
    // payload: true,
    // withCredentials: true
  })
}

/* 查看 */
export function load(id) {
  return request({
    url: `/bill/sundryBillType/get/${id}`,
    method: 'get'
  })
}

/* 删除 */
export function deleteData(data) {
  return request({
    url: `/bill/sundryBillType/delete`,
    method: 'post',
    data
  })
}

/* 获取模板列表 */
export function loadTemplateList() {
  return request({
    url: `/invoice/invoiceTemplate/loadTemplateList`,
    method: 'get'
  })
}
