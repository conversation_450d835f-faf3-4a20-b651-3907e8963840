/*
 * @Descripttion:GEVER公共通用方法
 * @version:
 * @Author: Lantresor
 * @Date: 2021-10-13 13:46:21
 * @LastEditors: Peng Xiao
 * @LastEditTime: 2022-05-11 14:39:16
 */
import request from '@base/src/utils/request'

/**
 * @name:获取字典
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getDictionary(path) {
  return request({
    url: `/system/dictionary/combotree`,
    method: 'get',
    params: { path: path },
    withCredentials: true
  })
}

/**
 * @name: 获取流程图
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function genFlowChart(data) {
  return request({
    url: 'workflow/genFlowChart',
    method: 'get',
    params: data,
    header: {
      'Content-Type': 'application/json'
    },
    responseType: 'arraybuffer',
    withCredentials: true
  })
}

/**
 * @name: 获取流程日志
 * @param  {
 *   traceType : 0
 *   procInstId: 流程Id
 *   bussinessKey: 流程业务key
 *   page: 页
 *   rows: 每页条数
 * }
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getProcessTraceList(data) {
  return request({
    url: '/workflow/getProcessTraceList',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
/**
 * @name:获取文件上传的地址
 * @param {Object} params
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getUrlCfg(params) {
  return request({
    url: `/filesystem/fileInfo/getUrlCfg`,
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:文件上传
 * @param {*} data 数据
 * @param {*} url 文件上传的路径
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function upload(data, url) {
  return request({
    url,
    method: 'post',
    data,
    // headers: {
    //   'content-type': 'multipart/form-data; boundary=----WebKitFormBoundarygBeYHPUsMiM6EGGl'
    // },
    withCredentials: true

  })
}
// 下载文件
export function download(url, data) {
  return request({
    url,
    method: 'get',
    data,
    responseType: 'blob',
    withCredentials: true

  })
}

/**
 * @name:
 * @param {String} serviceId
 * @param {String} path 文件的路径
 * @param {String} fileId 文件的id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteFile(data) {
  return request({
    url: '/filesystem/fileInfo/deleteFile',
    method: 'post',
    data,
    withCredentials: true

  })
}

/**
 * @name:根据业务id获取文件列表
 * @param {String} bussinessId 业务id
 * @param {String} serviceId 后端服务id
 * @param {String} fileClassification 文件分类
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getList(params) {
  return request({
    url: '/filesystem/fileInfo/getList',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:根据业务id、批次号复制返回临时文件列表
 * @param {String} batchId 业务id,必填
 * @param {String} srcBusinessId 业务id（源）,必填
 * @param {String} srcFileClassification 文件类型（源）,必填
 * @param {String} tarFileClassification 文件类型（目标）,必填
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getCopyList(params) {
  return request({
    url: '/filesystem/fileInfo/entry/copyFileByBusinessId',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:获取地区
 * @param {String}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function authAreaTreeDataAll(data) {
  return request({
    url: '/system/area/authTreeDataAll',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

/**
 * @name:获取当前有权限的地区（只获取地区）
 * @param {String}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function authTreeData(data) {
  return request({
    url: '/system/area/authTreeData',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

/**
 * @name:懒加载地区、机构
 * @param {String}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function loadAuthTreeData(data) {
  return request({
    url: '/system/organization/authTreeData',
    // url: '/authority/organization/authTreeData',
    method: 'get',
    params: data,
    // mock: true,
    withCredentials: true
  })
}

/**
 * @name:获取地区、机构树
 * @param {String}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function authTreeDataAll(data) {
  return request({
    url: '/system/organization/authTreeDataAll',
    method: 'get',
    params: data,
    withCredentials: true
  })
}
/**
 * @name:获取JSON
 * @param {String} path 文件的路径
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function comboJson(data) {
  return request({
    url: '/system/dictionary/combotree',
    method: 'get',
    params: data
  })
}

/**
 * @param {*} data  areaCode
 */
export function loginAreaOrg(data) {
  return request({
    url: '/system/organization/loginAreaOrg',
    method: 'get',
    params: data
  })
}

/**
 * @returns uploadConfig
 */
export function loadUploadUrl() {
  return request({
    url: '/filesystem/fileInfo/getUrlCfg',
    method: 'get'
  })
}

/**
 * @param {*} 文件路径
 * @returns 文件信息
 */
export function loadFileList(data) {
  return request({
    url: '/filesystem/fileInfo/getList',
    method: 'get',
    params: data
  })
}

/**
 * 根据batchId获取已上传文件列表
 * @param {*} 文件路径
 * @returns 文件信息
 */
export function getBatchList(data) {
  return request({
    url: '/filesystem/fileInfo/getBatchList',
    method: 'get',
    params: data
  })
}
/**
 * 获取待选角色列表
 * @param {*} data 查询参数
 */
export function roles(data) {
  return request({
    url: '/system/role/roles',
    method: 'get',
    params: data
  })
}
/**
 * 获取选中角色列表
 * @param {*} data 查询参数
 */
export function getUserRole(data) {
  return request({
    url: `/system/userRole/getUserRole/${data}`,
    method: 'get'
  })
}
/**
 * 角色分配保存
 * @param {*} data 查询参数
 */
export function batchSave(pamers, data) {
  return request({
    url: `/system/userRole/batchSave/${pamers}`,
    method: 'post',
    data: data,
    payload: true
  })
}
/**
 * 禁用无效接口
 * @param {*} data 查询参数
 */
export function setIsNotValid(data) {
  return request({
    url: '/system/user/setIsNotValid',
    method: 'post',
    data: data
  })
}
/**
 * 启用有效接口
 * @param {*} data 查询参数
 */
export function setIsValid(data) {
  return request({
    url: '/system/user/setIsValid',
    method: 'post',
    data: data
  })
}
// 角色
/**
 * 禁用无效接口
 * @param {*} data 查询参数
 */
export function roleSetIsNotValid(data) {
  return request({
    url: '/system/role/setIsNotValid',
    method: 'post',
    data: data
  })
}
/**
 * 启用有效接口
 * @param {*} data 查询参数
 */
export function roleSetIsValid(data) {
  return request({
    url: '/system/role/setIsValid',
    method: 'post',
    data: data
  })
}
/**
 * 获取角色权限请求
 * @param {*} data 查询参数
 */
export function roleInit() {
  return request({
    url: '/contract/contractSetting/list/init',
    method: 'get'
  })
}

/**
 * 手机扫码通知
 */
export function sendScanNotice(params, token) {
  return request({
    url: '/filesystem/fileInfo/sendScanNotice',
    method: 'get',
    params: params,
    headers: {
      'X-user-token-header': token
    }
  })
}

/**
 * 手机上传完成通知
 */
export function sendUploadNotic(params, token) {
  return request({
    url: '/filesystem/fileInfo/sendUploadNotic',
    method: 'get',
    params: params,
    headers: {
      'X-user-token-header': token
    }
  })
}
/**
 * YesAndNo
 * @param {*} data
 * @returns
 */
export function YesAndNo(data) {
  return request({
    url: '/json/financial/public/YesAndNo.json',
    method: 'get',
    data: data
  })
}

/**
 * 保存暂存数据
 * key:暂存数据key(使用相同的key会覆盖之前的暂存数据,建议为:本项业务标识+用户标识)
 * data:暂存数据(建议为json字符串)
 */
export function saveTempData(key, data) {
  return request({
    url: '/tempstore/entry/save',
    method: 'post',
    data: {
      key,
      data
    }
  })
}

/**
 * 获取暂存数据
 * key:暂存数据key
 */
export function getTempData(key) {
  return request({
    url: '/tempstore/entry/get?key=' + key,
    method: 'get'
  })
}