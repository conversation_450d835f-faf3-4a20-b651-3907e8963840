<template>
  <div class="right-box" style="height: 100%;">
    <el-form inline @submit.native.prevent>
      <el-form-item :label="$t('交易号')" prop="orderNo">
        <gever-input v-model="queryParams.orderNo" />
      </el-form-item>
      <el-form-item :label="$t('说明')" prop="description">
        <gever-input v-model="queryParams.description" />
      </el-form-item>
      <el-form-item :label="$t('交易时间')" prop="transactionTimeStart">
        <el-date-picker
          v-model="queryParams.transactionTimeStart"
          class="w45%"
          type="date"
          value-format="yyyy-MM-dd"
        /> -
        <el-date-picker
          v-model="queryParams.transactionTimeEnd"
          class="w45%"
          type="date"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item :label="$t('交易金额')" prop="transactionAmountMin">
        <gever-input 
          v-model="queryParams.transactionAmountMin" 
          style="width:45%" 
          @input="handleAmountInput('transactionAmountMin', $event)" 
        /> -
        <gever-input 
          v-model="queryParams.transactionAmountMax" 
          style="width:45%" 
          @input="handleAmountInput('transactionAmountMax', $event)" 
        />
      </el-form-item>
      <el-form-item :label="$t('订单类型')" prop="orderType">
        <el-select v-model="queryParams.orderType" clearable placeholder="请选择">
          <el-option
            v-for="item in orderTypeOptions"
            :key="item.id"
            :label="item.text"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" round @click="handleSearch">
          {{ $t('搜索') }}
        </el-button>
      </el-form-item>
    </el-form>
    <gever-table
      :loading="tableLoading"
      ref="_geverTableRef"
      :columns="table.columns"
      :data="table.tableList"
      :total="table.total"
      :pagi="queryParams"
      @pagination-change="getList"
      @selection-change="handleSelectionChange"
      :row-class-name="setRowStyle"
      style="clear: both;"
      height="calc(95% - 50px)"
    >
    </gever-table>
  </div>
</template>

<script>
import { loadSettleListApi } from '@/api/paymentQr/qrPaymentSettle.js'
import { getDictionary } from '@/api/gever/common.js'
export default {
  name: 'SelectTransaction',
  props: {
    detailVisible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'view'
    },
    accountNumber: {
      type: [String, Number],
      default: null
    },
    activeMerchantNo: {
      type: String,
      default: ''
    },
    id: {
      type: [String, Number],
      default: null
    },
    initialSelectedData: { 
      type: Array,
      default: () => []
    },
    tradeTime: {
      type: String,
      default: ''
    }
  },
  data() {
    // console.log(new Date(this.tradeTime).setDate(new Date(this.tradeTime).getDate() - 1))
    const startDate = new Date(this.tradeTime);
    startDate.setDate(startDate.getDate() - 1);
    return {
      tableLoading: false,
      queryParams: {
        page: 1,
        rows: 20,
        // this.tradeTime当天的前一天
        transactionTimeStart: this.formatDate(startDate), 
        // this.tradeTime当天
        transactionTimeEnd: this.formatDate(new Date(this.tradeTime)), 
        orderType: '',
        merchantNo: '',
        customerAccount: '',
        transactionAmountMin: '',
        transactionAmountMax: '',
        reconciliation : 0,
        accountDetailId : this.id
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50' },
          { type: 'index', label: '序号', width: '50' },
          { label: '商户号', prop: 'merchantNo', minWidth: '150', resizable: true },
          { label: '交易号', prop: 'orderNo', minWidth: '150', resizable: true },
          { label: '交易时间', prop: 'transactionTime', minWidth: '150', resizable: true },
          { label: '交易金额(元)', prop: 'transactionAmount', minWidth: '100', resizable: true },
          { label: '客户账号', prop: 'customerAccount', minWidth: '150', resizable: true },
          { label: '说明', prop: 'description', minWidth: '200', resizable: true }
        ],
        tableList: [],
        total: 0
      },
      orderTypeOptions: [
        { text: '平台订单', id: '01' },
        { text: '非平台订单', id: '02'}
      ],
      showDetail: false,
      currentRow: null,
      selectedRows: [...this.initialSelectedData],
      isChangingPage: false 
    }
  },
  created() {
    window.addEventListener('resize', () => {
      this.$nextTick(() => {
        this.$refs._geverTableRef.setHeight()
      })
    })
    this.getList()
  },
  methods: {
    getList() {
      this.isChangingPage = true 
      this.tableLoading = true
      if (!this.queryParams.transactionTimeStart) {
        this.$set(this.queryParams, 'transactionTimeStart', '')
      }
      if (!this.queryParams.transactionTimeEnd) {
        this.$set(this.queryParams, 'transactionTimeEnd', '')
      }
      this.queryParams.merchantNo = this.activeMerchantNo
      loadSettleListApi(this.queryParams).then(res => {
        this.table.tableList = res.data.rows
        this.table.total = res.data.total
        // 在数据加载完成后自动勾选对应行
        this.autoSelectRows()
      })
      .catch(err => {
          this.table.tableList = []
          this.table.total = 0
        })
      .finally(() => {
        this.tableLoading = false
        this.isChangingPage = false 
      })
    },
    autoSelectRows() {
      this.$nextTick(() => {
        if (this.$refs._geverTableRef) {
          console.log('autoSelectRows',this.selectedRows)
          const selectedRowsCopy = [...this.selectedRows]
          this.table.tableList.forEach(row => {
            const isSelected = selectedRowsCopy.some(selectedRow => selectedRow.id == row.id)
            this.$refs._geverTableRef.toggleRowSelection(row, isSelected)
          })
        }
      })
    },
    handleSearch() {
      this.getList()
    },
    // 收入金额输入处理（限制两位小数）
    handleAmountInput(field, value) {
      // 1. 移除非数字和非小数点字符
      let sanitized = value.replace(/[^0-9.]/g, '');
      
      // 2. 限制只能有一个小数点（移除第一个小数点后的所有其他小数点）
      const firstDotIndex = sanitized.indexOf('.');
      if (firstDotIndex !== -1) {
        // 保留第一个小数点前的部分 + 第一个小数点 + 后续部分移除所有小数点
        sanitized = sanitized.slice(0, firstDotIndex + 1) + 
                    sanitized.slice(firstDotIndex + 1).replace(/\./g, '');
      }
      
      // 3. 限制小数点后最多两位
      sanitized = sanitized.replace(/\.(\d{2})\d*/g, '.$1');
      
      // 4. 移除开头的小数点（如：".12" 转为 "0.12"）
      if (sanitized.startsWith('.')) {
        sanitized = '0' + sanitized;
      }
      
      // 更新绑定值
      this.queryParams[field] = sanitized;
    },
    getFirstDayOfMonth() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = '01';
      return `${year}-${month}-${day}`;
    },
    // 新增：获取前一天日期的方法
    getYesterday() {
      const date = new Date();
      date.setDate(date.getDate() - 1); // 当前日期减1天
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份补零
      const day = String(date.getDate()).padStart(2, '0'); // 日期补零
      return `${year}-${month}-${day}`;
    },
    getToday() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    handleView(row) {
      this.currentRow = row
      this.showDetail = true
    },
    handleCloseDetail() {
      this.showDetail = false
    },
    getOrderNoPrefix(row) {
      if (row && row.orderNo) {
        return row.orderNo.split('-')[0] || row.orderNo;
      }
      return '';
    },
    handleSelectionChange(selection) {
      // 如果正在换页，不处理选中状态变化
      if (this.isChangingPage) return; 

      // 获取当前页所有行的唯一标识
      const currentPageRowIds = this.table.tableList.map(row => row.id);
      // 获取当前选中数据的唯一标识数组
      const currentSelectionIds = selection.map(item => item.id);
      // 获取之前选中数据的唯一标识数组
      const previousSelectionIds = this.selectedRows.map(item => item.id);

      // 找出当前页被取消选中的行的唯一标识
      const deselectedIds = currentPageRowIds.filter(id => 
        previousSelectionIds.includes(id) && !currentSelectionIds.includes(id)
      );

      // 处理新增选中的数据
      selection.forEach(item => {
        if (!previousSelectionIds.includes(item.id)) {
          this.selectedRows.push(item);
        }
      });

      // 处理取消选中的数据，移除当前页被取消选中的行
      this.selectedRows = this.selectedRows.filter(item => 
        !deselectedIds.includes(item.id)
      );

      console.log('handleSelectionChange',this.selectedRows)
      // 将选中的数据传递给上级组件
      this.$emit('selection-change', this.selectedRows);
    },
    // 新增格式化时间的方法
    formatDate(date) {
      console.log('formatDate',date)
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      // return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      return `${year}-${month}-${day}`;
    },
    setRowStyle( row ) {
      if (this.selectedRows.some(selectedRow => selectedRow.id === row.row.id)) {
        return 'bg_gray'
      }
      return '';
    }
  }
}
</script>

<style scoped>
/* 样式后续补充 */
.gever-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}
.form-db {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}
</style>

<style>
.bg_gray{
  background-color: #f8f8f9 !important;
}
</style>