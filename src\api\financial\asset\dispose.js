/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-11-24 09:38:03
 * @LastEditTime: 2022-09-28 15:52:46
 * @LastEditors: Pengxiao
 * @Description:
 * @FilePath: \b-ui\src\api\financial\asset\dispose.js
 * ^-^
 */

import request from '@/utils/request'

/**
 * @name:获取资产入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/asset/assetDispose/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/asset/assetDispose/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:保存编辑结果
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/asset/assetDispose/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:保存并入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveAndPosting({ params, data }) {
  return request({
    url: `/financial/asset/assetDispose/saveAndPosting`,
    method: 'post',
    params,
    data,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteWithBooks(data) {
  return request({
    url: `/financial/asset/assetDispose/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:审核
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function approval(data) {
  return request({
    url: `/financial/asset/assetDispose/submit`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消审核
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unApproval(data) {
  return request({
    url: `/financial/asset/assetDispose/unSubmit`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function posting(data) {
  return request({
    url: `/financial/asset/assetDispose/posting`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消资产入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unPosting(data) {
  return request({
    url: `/financial/asset/assetDispose/unPosting`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消凭证
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancelGenerateVoucher(data) {
  return request({
    url: `/financial/asset/assetDispose/cancelGenerateVoucher`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function addInit(data) {
  return request({
    url: `/financial/asset/assetDispose/addInit`,
    method: 'post',
    data,
    withCredentials: true
  })
}
