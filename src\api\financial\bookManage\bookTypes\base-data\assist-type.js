/*
 * @Descripttion:辅助核算类型接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-27 17:48:37
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-02 15:50:39
 */

import request from '@/utils/request'

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/booktypes/basedata/assistType/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取辅助核算类型
 * @param {String} booksId 登陆的账套id
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listByBooksTypeId(data) {
  return request({
    url: '/financial/booktypes/basedata/assistType/listByBooksTypeId',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存辅助核算类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/booktypes/basedata/assistType/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:删除辅助核算类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteWithBooksType(data) {
  return request({
    url: `/financial/booktypes/basedata/assistType/deleteWithBooksType`,
    method: 'post',
    data,
    withCredentials: true
  })
}

export function saveImportTemp(data) {
  return request({
    url: `/financial/booktypes/basedata/assistType/saveImportTemp`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取辅助核算树形数据
 * @param {*} data booksId
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function tree(data) {
  return request({
    url: `/financial/booktypes/basedata/assistType/tree`,
    method: 'post',
    data,
    withCredentials: true
  })
}
