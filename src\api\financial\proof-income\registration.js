/*
 * @Author: 未知
 * @Date: 2022-08-30 11:30:02
 * @LastEditTime: 2022-08-30 16:16:58
 * @LastEditors: Pengxiao
 * @Description:
 * @FilePath: \b-ui\src\api\financial\proof-income\registration.js
 * ^-^
 */
import request from '@/utils/request'

export const baseUrl = '/financial/proofIncome'

/**
 * @name:
 * @param {*} invoiceNumber
 * @param {*} balanceStatus
 * @param {*} drawer
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: `${baseUrl}/page`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `${baseUrl}/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `${baseUrl}/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(params) {
  return request({
    url: `${baseUrl}/delete`,
    method: 'post',
    params,
    withCredentials: true
  })
}

/**
 * @name:收款合同数据查询
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getContractCollection(data) {
  return request({
    url: `${baseUrl}/getContractCollection`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:发票登记-作废
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function invalid(data) {
  return request({
    url: '/financial/proofIncome/invalid',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:发票登记-作废
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
 export function invalidCancel(data) {
  return request({
    url: '/financial/proofIncome/invalidCancel',
    method: 'post',
    data,
    withCredentials: true
  })
}
