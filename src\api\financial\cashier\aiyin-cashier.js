/*
 * @Descripttion: 
 * @version: 
 * @Author: 未知
 * @Date: 2023-07-27 15:02:00
 * @LastEditors: Andy
 * @LastEditTime: 2023-07-31 16:36:30
 */
import request from '@/utils/request'


/**
 * @name:出纳列表
 * @param {*} data
 * @description:参数
 * @return {*}
 * @test:
 * @msg:
 */
export function pageListCashier(data) {
  return request({
    url: `/financial/cashier/ayCashierTransfer/pageList`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:出纳收入或者支出详情
 * @param {*} data
 * @description:参数
 * @return {*}
 * @test:
 * @msg:
 */
export function getDetailCashier(data) {
  return request({
    url: `/financial/cashier/ayCashierTransfer/getDetail`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:修改是否保存
 * @param {*} data
 * @description:参数
 * @return {*}
 * @test:
 * @msg:
 */
export function updateAyCashier (data) {
  return request({
    url: `/financial/cashier/cashierVoucher/updateAyCashier`,
    method: 'post',
    data,
    withCredentials: true
  })
}