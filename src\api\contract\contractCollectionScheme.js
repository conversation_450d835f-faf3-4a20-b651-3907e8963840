/*
 * @Descripttion:
 * @version:
 * @Author: Tanronglong
 * @Date: 2021-10-28 12:46:55
 * @LastEditors: Tanronglong
 * @LastEditTime: 2021-12-03 11:40:55
 */
import request from '@/utils/request'
// 收款事项条数
export function count(data) {
  return request({
    url: '/contract/collectionPlanQuery/count',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 收款计划查询
export function list(data) {
  return request({
    url: '/contract/collectionPlanQuery/list',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 付款计划查询
export function planQuery(data) {
  return request({
    url: '/contract/paymentPlanQuery/planQuery',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同收款到期查询列表
export function expireQuery(data) {
  return request({
    url: '/contract/collectionExpireQuery/expireQuery',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同付款到期查询列表
export function payExpireQuery(data) {
  return request({
    url: '/contract/paymentExpireQuery/expireQuery',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 收款合同台账/contract/contractStatements/collectionContractLedgerBookSheet/get
export function contractStatements(data) {
  return request({
    url: '/contract/contractStatements/collectionContractLedgerBookSheet/get',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 收款合同统计表
export function collectionContractCounttingSheet(data) {
  return request({
    url: '/contract/contractStatements/collectionContractCounttingSheet/get',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 在管收款合同统计表
export function collectionContractInManagerSheet(data) {
  return request({
    url: '/contract/contractStatements/collectionContractInManagerSheet/get',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 收款合同欠款查询表
export function collectionContractArrearsSheet(data) {
  return request({
    url: '/contract/contractStatements/collectionContractArrearsSheet/get',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 打印样式调取接口
export function contractCoverPrint(data) {
  return request({
    url: '/contract/contractStatements/css/contractCoverPrint.css',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
export function contractTablePrint(data) {
  return request({
    url: '/contract/contractStatements/css/contractTablePrint.css',
    method: 'get',
    data: data,
    withCredentials: true
  })
}
