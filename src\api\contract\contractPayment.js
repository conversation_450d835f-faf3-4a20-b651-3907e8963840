/*
 * @Descripttion:
 * @version:
 * @Author: Tanronglong
 * @Date: 2021-10-25 09:29:04
 * @LastEditors: Tanronglong
 * @LastEditTime: 2021-11-09 15:53:55
 */

import request from '@/utils/request'
// 查询合同收款数据
export function list(data) {
  return request({
    url: '/contract/contractCollection/list',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 合同选择 /contract/contractCollection/regisitrationList?receiveStatus=0
export function regisitrationList(data) {
  return request({
    url: '/contract/contractCollection/regisitrationList?receiveStatus=0',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
export function regisitrationSearch(data) {
  return request({
    url: '/contract/contractCollection/regisitrationList?receiveStatus=1',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 收款合同详情 /contract/contractCollection/collectionInitData?contractId=2104b7e0c5ba4734b6b2f7f8e6d0ff37
export function collectionInitData(data) {
  return request({
    url: `/contract/contractCollection/collectionInitData?contractId=${data.contractId}`,
    method: 'post',
    data: data._ood_,
    withCredentials: true
  })
}
// 收款项目
export function contractCollection(data) {
  return request({
    url: `/contract/contractCollection/combobox?contractId=${data.contractId}&orgCode=${data.orgCode}`,
    method: 'get',
    data: data,
    withCredentials: true
  })
}
// 每期应收款明细
export function loadReceivalbesDetails(data) {
  return request({
    url: '/contract/contractCollection/loadReceivalbesDetails',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 收款合同保存按钮  /contract/contractCollection/saveCollection
export function saveCollection(data) {
  return request({
    url: '/contract/contractCollection/saveCollection',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 删除 /contract/contractCollection/delete
export function deleteRemove(data) {
  return request({
    url: '/contract/contractCollection/delete',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 历史收款信息 /contract/contractCollection/historyCollection
export function historyCollection(data) {
  return request({
    url: '/contract/contractCollection/historyCollection',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
// 退款保存 /contract/contractCollection/saveRefund
export function saveRefund(data) {
  return request({
    url: '/contract/contractCollection/saveRefund',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
