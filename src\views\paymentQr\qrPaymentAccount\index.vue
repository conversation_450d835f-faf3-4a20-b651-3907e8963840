<template>
  <div>
    <fold-box right-title="收款账号管理">
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('户主名称')" prop="accountName">
              <gever-input v-model="queryParams.accountName"  />
            </el-form-item>
            <el-form-item :label="$t('账户类型')" prop="accountType">
              <el-select v-model="queryParams.accountType" clearable  placeholder="请选择">
                <el-option
                  v-for="item in optionsMap.acountType"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('银行类型')" prop="bankType">
              <el-select v-model="queryParams.bankType" filterable="true" clearable  placeholder="请选择">
                <el-option
                  v-for="item in optionsMap.bankType"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('状态')" prop="status">
              <el-select v-model="queryParams.status" clearable  placeholder="请选择">
                <el-option
                  v-for="item in vaildOptions"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button plain icon="el-icon-search" type="primary" round @click="handleSearch">
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
            <div style="float: right;">
              <el-form-item>
                <el-button
                  v-hasPermi="'financial.qrPayment.acount.save'"
                  type="primary"
                  round
                  icon="el-icon-plus"
                  @click="handleAdd"
                >
                  {{ $t('新增') }}
                </el-button>
              </el-form-item>
            </div>
          </el-form>
          <gever-table
		    :loading="tableLoading"
            ref="_geverTableRef"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
            style="clear: both;"
          >
            <template #status="{ row }">
              <el-switch
              v-hasPermi="'financial.qrPayment.acount.save'"
              v-model="row.status"
              @change="handleVoidChange(row)"
              >
              </el-switch>
            </template>
            <template #bankType="{ row }">
              {{ getTitle(row.bankType, optionsMap.bankType) }}
            </template>
            <template #accountType="{ row }"> 
              {{ getTitle(row.accountType, optionsMap.acountType) }}
            </template>
            <template #operation="{ row }">
              <el-button v-hasPermi="'financial.qrPayment.acount.view'" type="text" @click="handleView(row)">{{ $t('查看') }}</el-button>
              <el-button v-hasPermi="'financial.qrPayment.acount.save'" type="text" @click="handleEdit(row)" >{{ $t('编辑') }}</el-button>
              <el-button v-hasPermi="'financial.qrPayment.acount.delete'" type="text" @click="handleRemove(row,true)" >{{ $t('删除') }}</el-button>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <public-drawer 
        :visible.sync="detailVisible" 
        :title="detailTitle" 
        :size="70" 
        :buttons="type == 'view' ? viewButtons : detailButtons" 
        @close="handleCloseDetail"
    >
      <detail
        v-if="detailVisible"
        :detail-visible.sync="detailVisible"
        ref="_detailContentRef"
        :type="type"
        :id="currentId"
        @refreshTable="getList"
      />
    </public-drawer>
  </div>
</template>

<script>
import { loadQrPaymentAccountListApi, deleteQrPaymentAccountApi, setVoidApi } from '@/api/paymentQr/qrPaymentAccount.js'
import detail from './components/detail'
import {vaildOptions} from '../config'
import { getDictionary } from '@/api/gever/common.js'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')

export default {
  name: 'QrPaymentAccount',
  components: {
    detail
  },
  data() {
    return {
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      vaildOptions,
      optionsMap: {
        bankType: [],
        acountType: []
      },
      queryParams: {
        page: 1,
        rows: 20,
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50' },
          { type: 'index', label: '序号', width: '50' },
          { label: '组织机构', prop: 'orgName' , minWidth: '350' ,resizable: true},
          { label: '户主名称', prop: 'accountName' , minWidth: '350' ,resizable: true},
          { label: '账户类型', prop: 'accountType' , minWidth: '150' ,resizable: true},
          { label: '银行账号', prop: 'bankAccountNo' , minWidth: '200' ,resizable: true},
          { label: '银行类型', prop: 'bankType' , minWidth: '220' ,resizable: true},
          { label: '开户银行', prop: 'bankName' , minWidth: '200' ,resizable: true},
          { label: '状态', prop: 'status' , minWidth: '80' ,resizable: true},
          { label: '操作', slotName: 'operation', width: '130', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
	  currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ],
      viewButtons: [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        }
      ]
    }
  },
  computed: {
    ...mapState(['areaLogin']),
  },
  watch: {
    areaLogin: {
      handler: function (val) {
        console.log("初始化", val);
        this.getList()
        this.getOptions()
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
  },
  created() {
    // this.getList()
    // this.getOptions()
  },
  methods: {
    getList() {
      console.log(this.areaLogin)
      const areaCode = this.areaLogin.areaCode || this.areaLogin.code
      const orgCode = this.areaLogin.code
      const orgId = this.areaLogin.id
      if(this.areaLogin.type == 'Organization'){
        this.queryParams.orgCode = orgCode
        this.queryParams.areaCode = ''
      }else{
        this.queryParams.areaCode = areaCode
        this.queryParams.orgCode = ''
      }
      if(!areaCode){
        return
      }

      this.tableLoading = true
      
      loadQrPaymentAccountListApi(this.queryParams).then(res => {
        res.data.rows.forEach(v=>{
          if(v.status){
            v.status = true
          }else{
            v.status = false
          }
        })
        this.table.tableList = res.data.rows
        this.table.total = res.data.total
      })
      .catch(err => {
          this.table.tableList = []
          this.table.total = 0
        })
      .finally(() => {
        this.tableLoading = false
      })
    },
    async getOptions () {
      const { data: bankType = [] } = await getDictionary('/payment/pay_bank_type/')
      this.optionsMap.bankType = bankType

      const { data: acountType = [] } = await getDictionary('/financial/bank_account_type/')
      this.optionsMap.acountType = acountType
    },
    handleSearch() {
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleAdd() {
      if (this.areaLogin.type !== 'Organization') {
        return this.$message.warning('请选择机构')
      }
      this.detailTitle = '新增收款账号'
      this.type = 'add'
	    this.currentId = ''
      this.detailVisible = true
    },
    handleView(row) {
      this.detailTitle = '查看收款账号'
      this.type = 'view'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleEdit(row) {
      // if (this.areaLogin.type !== 'Organization') {
      //   return this.$message.warning('请选择机构')
      // }
      this.detailTitle = '编辑收款账号'
      this.type = 'edit'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleRemove(row, batchFlag) {
      // if (this.areaLogin.type !== 'Organization') {
      //   return this.$message.warning('请选择机构')
      // }
      let ids
      if (batchFlag) {
        ids = [row.id]
      } else {
        if (this.tableIdSelection.length == 0) {
          return this.$message.warning(this.$t('请先选择要删除的数据！'))
        }
        ids = this.tableIdSelection
      }
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        deleteQrPaymentAccountApi(ids).then(res => {
          if (res) {
            this.$message.success(res.message)
            this.getList()
          }
        })
      }).catch(() => {})
    },
    handleCloseDetail() {
      this.detailVisible = false
    },
    handleSave() {
      this.$refs._detailContentRef.save()
    },
    handleVoidChange(row) {
      let voidName = row.status ? "生效" : "失效"
      setVoidApi( row.id ).then(res => {
          if (res) {
            this.$message.success("设置" + voidName + "成功！")
            // this.getList()
          }else{
            this.getList()
          }
      })
    }
  }
}
</script>