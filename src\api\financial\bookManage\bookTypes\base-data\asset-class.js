/*
 * @Descripttion:会计凭证类型
 * @version:
 * @Author: 未知
 * @Date: 2021-10-29 16:40:25
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-04 16:05:41
 */

import request from '@/utils/request'

/**
 * @name:
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/booktypes/basedata/baseAssetClass/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:保存出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save(data) {
  return request({
    url: `/financial/booktypes/basedata/baseAssetClass/save`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:保存出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load({ id }) {
  return request({
    url: `/financial/booktypes/basedata/baseAssetClass/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:获取资产类型树
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function tree({ data, params }) {
  return request({
    url: `/financial/booktypes/basedata/baseAssetClass/tree`,
    method: 'post',
    params,
    data,
    withCredentials: true
  })
}
/**
 * @name:删除出纳凭证类型类型
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteX(data) {
  return request({
    url: `/financial/booktypes/basedata/baseAssetClass/deleteWithBooks`,
    method: 'post',
    data,
    withCredentials: true
  })
}

