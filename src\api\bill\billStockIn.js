import request from '@/utils/request'

// 获取表格内容
export function billStockInList(data) {
  return request({
    url: '/bill/billStockIn/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 查看详情
export function viewDetails(id) {
  return request({
    url: `/bill/billStockIn/load/${id}`,
    method: 'get'
  })
}

// 获取票据类型信息
export function getTicketType(data) {
  return request({
    url: '/bill/billType/getBillTypeList',
    method: 'post',
    data:data,
    withCredentials: true
  })
}

// 获取入库修改信息
export function changeInformation(id) {
  return request({
    url: `/bill/billStockIn/load/${id}`,
    method: 'get'
  })
}

// 删除
export function storeRemove(data) {
  return request({
    url: '/bill/billStockIn/delete',
    method: 'post',
    data: data
  })
}

// 新增
export function storeAdd(data) {
  return request({
    url: '/bill/billStockIn/save',
    method: 'post',
    data: data
  })
}

