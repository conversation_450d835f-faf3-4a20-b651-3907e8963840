/*
 * @Descripttion:渠道商户
 * @version:
 * @Author: 未知
 * @Date: 2021-01-12 13:46:21
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-10-15 16:41:47
 */

import request from '@/utils/request'

/**
 * @name:获取数据页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/payment/manager/maintain/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:启用
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function enable(params) {
  return request({
    url: '/payment/manager/maintain/enabled',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:停用
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function disable(params) {
  return request({
    url: '/payment/manager/maintain/disabled',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:停用
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveX(data) {
  return request({
    url: '/payment/manager/maintain/saveX',
    method: 'post',
    data,
    // payload: true,
    withCredentials: true
  })
}

/**
 * @name:是否需要验证码
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function frameInit(params) {
  return request({
    url: '/payment/manager/maintain/frame/init',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:是否需要验证码
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function addInit(params) {
  return request({
    url: '/payment/manager/maintain/add/init',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:获取支付管理员
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function paymentManager(params) {
  return request({
    url: '/payment/manager/admin/paymentManager',
    method: 'get',
    params,
    withCredentials: true
  })
}

export function sendSmsCode(data) {
  return request({
    url: '/payment/manager/authorize/sendSmsCode',
    method: 'post',
    data,
    withCredentials: true
  })
}
