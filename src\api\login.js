import request from "@/utils/request";

export function getRsaPublicKey() {
  return request({
    url: "/asymmetricCrypto/getPublicKey",
    method: "get",
    withCredentials: true,
  });
}

export function getValidateCode(codeId) {
  return request({
    url: "/validate/imageCode/" + codeId,
    method: "get",
    withCredentials: true,
  });
}

// 登录方法
export function login(data) {
  return request({
    url:
      process.env.VUE_APP_IS_BACKEND_MONOLITH === "true"
        ? "/oauth/token"
        : "/pc/oauth/token",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

//登录跳转
export function authorize(data) {
  return request({
    url: "/oauth2/authorize",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },

  });
}

// 注册方法
export function register(data) {
  return request({
    url: "/register",
    headers: {
      isToken: false,
      "Content-Type": "application/json;charset=utf-8",
    },
    method: "post",
    data: data,
  });
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: "/system/user/current",
    method: "get",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

// 退出方法
export function logout() {
  return request({
    url: "/logout",
    method: "get",
    headers: {
      "Content-Type": "application/json;charset=utf-8",
    },
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: "/captchaImage",
    method: "get",
    timeout: 20000,
    headers: {
      "Content-Type": "application/json;charset=utf-8",
    },
  });
}

// 获取主办单位、技术支持
export function loginConfig() {
  return request({
    url: "/system/config/loginConfig",
    method: "get",
  });
}

// 是否强制修改密码 forceChange 0不需要强制修改 1需要， id字段是用户id值
export function forceChangePwd() {
  return request({
    url: "/system/user/forceChangePwd",
    method: "get",
  });
}

/**
 * 用户修改密码保存
 * @param {*} data 查询参数
 */
export function update_my_password(data) {
  return request({
    url: "/system/user/update_my_password",
    method: "post",
    data: data,
  });
}

// 获取系统初始化接口 forceInit：1（需要初始化） forceInit：0（不需要初始化）
export function InitForce() {
  return request({
    url: "/system/init/force",
    method: "get",
  });
}

// 获取系统初始化接口 forceInit：1（需要初始化） forceInit：0（不需要初始化）
export function doInit(data) {
  return request({
    url: "/system/init/doInit",
    method: "post",
    payload: true,
    data: data,
  });
}
