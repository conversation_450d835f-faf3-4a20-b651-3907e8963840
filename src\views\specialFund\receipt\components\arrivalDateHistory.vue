<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-08-07 15:51:20
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-14 17:27:19
-->
<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-21 10:52:13
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-07 15:49:57
-->
<template>
  <!-- <el-dialog :title="title" width="30%" :visible.sync="dialogVisible" :before-close="handleClose"> -->
  <div class="gever-form detail">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="100px"
      :label-position="'left'"
      style="margin:20px"
    >
      <el-form-item :label="$t('金额')">
        <el-input v-model="contentForm.totalAmountStr" disabled style="width:240px" />
      </el-form-item>
      <el-form-item :label="$t('入账日期')" prop="arrivalDate">
        <el-date-picker
          v-model="contentForm.arrivalDate"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="date"
          style="width:240px"
        />
        <div style="color:red;font-size:10px">说明：入账日期为这笔专项资金收入的出纳日期</div>
      </el-form-item>
    </el-form>
  </div>
  <!-- <span slot="footer" class="dialog-footer" style="text-align: center;">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </span>
  </el-dialog>-->
</template>

<script>
import { updateArrivalDate } from '@/api/specialFund/receipt.js'
export default {
  components: {

  },
  props: {
    ids: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    totalAmount: {
      type: Number,
      default: 0.00
    },
    orgId: {
      type: String,
      default: ''
    },
    bankAccountNumber: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dialogVisible: false,
      contentForm: {
        arrivalDate: '',
        id: ''
      },
      rules: {
        arrivalDate: [{ required: true, message: '请选择达账日期', trigger: 'change' }]
      },
      totalAmountStr: '',
      cashierVisible: false,
      cashierButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('确 定'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
    }
  },
  created () {
    this.contentForm.arrivalDate = ''
    this.contentForm.totalAmountStr = this.totalAmount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },
  methods: {
    save () {
      this.$refs['_geverFormRef'].validate(valid => {
        if (!valid) {
          return this.$message.warning(this.$t('请完善表单信息'))
        }
        const data = {
          ids: this.ids,
          arrivalDate: this.contentForm.arrivalDate,
          isHistory: 0
        }
        updateArrivalDate(data).then(res => {
          this.$message.success(this.$t('操作成功'))
          this.$emit('refreshTable')
          this.$emit('close')
          // this.handleClose()
        }).finally(() => {
        })
      })
    },
    handleClose () {
      // this.contentForm.arrivalDate = ''
      this.$refs['_geverFormRef'].resetFields()
      this.dialogVisible = false
    },
    cashierClick () {
      this.cashierVisible = true
    },
    handleCloseDetail () {
      this.cashierVisible = false
    },
    handleSave () {
      console.log('222')
      this.$refs.cashierRef.save()
    },
    cashierSave (val) {
      console.log('1111', val)
      // this.contentForm.cashierVoucherNumber = val.typeNumber
      // this.contentForm.arrivalDate = val.businessDate
      this.$set(this.contentForm, 'cashierVoucherNumber', val[0].typeNumber)
      this.$set(this.contentForm, 'arrivalDate', val[0].businessDate)
      this.$set(this.contentForm, 'debit', val[0].debit)
      this.$set(this.contentForm, 'cashierVoucherId', val[0].id)
      console.log('this.contentForm', this.contentForm)

      this.cashierVisible = false
    }
  }
}
</script>

<style>
