/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-09-07 16:53:08
 * @LastEditors: Andy
 * @LastEditTime: 2023-09-12 14:33:27
 * @FilePath: \rural-financial\src\api\financial\custom-report\settingParameter.js
 */
import request from '@/utils/request'

// 查询列表 {defineId}参数可以指定不同的报表体
export function query(data) {
  return request({
    url: '/financial/customReport/customReportParam/page',
    method: 'post',
    data,
    withCredentials: true
  })
}
// insert插入单个
export function insert(data) {
  return request({
    url: '/financial/customReport/customReportParam/insert',
    method: 'post',
    data,
    withCredentials: true
  })
}

// post  或者 get 查询单个，id参数1，2，3。。。。。。。分别表示不同报表组
export function detail(id) {
  return request({
    url: `/financial/customReport/customReportParam/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}

// 更新
export function update(data) {
  return request({
    url: `/financial/customReport/customReportParam/update`,
    method: 'post',
    data,
    withCredentials: true
  })
}

// 删除
export function del(data) {
  return request({
    url: `/financial/customReport/customReportParam/delete`,
    method: 'post',
    data,
    withCredentials: true
  })
}
