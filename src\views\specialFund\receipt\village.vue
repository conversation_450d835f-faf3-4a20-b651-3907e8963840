<!--
 * @Description: 专项资金入账管理  村级编制
 * @Author: liuwf
 * @Date: 2025-05-19 09:37:40
 * @LastEditors: liuwf
 * @LastEditTime: 2025-09-12 16:22:05
-->
<template>
  <div>
    <fold-box right-title="村级入账">
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('专项资金编号')">
              <el-input v-model="queryParams.receiptCode" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金类别')">
              <!-- @getValue="handleCategoryValue"
              :show-all-levels="false"-->
              <categoryTreeSelect v-model="queryParams.categoryId" class="w200" placeholder="请选择"></categoryTreeSelect>
            </el-form-item>
            <el-form-item :label="$t('专项资金名称')">
              <el-input v-model="queryParams.receiptName" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金来源')">
              <gever-select
                v-model="queryParams.specialSource"
                class="w200"
                path="/financial/specialFund/receipt/specialSource/"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item :label="$t('状态')">
              <gever-select
                v-model="queryParams.status"
                class="w200"
                :options="optionsMap.villagetSstatus"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item label="金额">
              <div style="display: flex;justify-content:space-between">
                <div>
                  <el-input
                    v-model="queryParams.amountMin"
                    type="text"
                    class="w150"
                    clearable
                    @input="handleAmountInput('amountMin')"
                    @blur="formatDecimal('amountMin')"
                    @change="validateAmountRange"
                  />
                </div>
                <span style="text-align: center;margin-left:3px;margin-right:3px;">-</span>
                <div>
                  <el-input
                    v-model="queryParams.amountMax"
                    type="text"
                    class="w150"
                    clearable
                    @input="handleAmountInput('amountMax')"
                    @blur="formatDecimal('amountMax')"
                    @change="validateAmountRange"
                  />
                </div>
              </div>
            </el-form-item>
            <el-form-item label="入账日期">
              <div style="display: flex;justify-content:space-between">
                <div>
                  <el-date-picker
                    v-model="queryParams.arrivalDateStart"
                    type="date"
                    class="w150"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :picker-options="startPickerOptions"
                    style="width: 100%;"
                  ></el-date-picker>
                </div>
                <span style="text-align: center;margin-left:3px;margin-right:3px;">-</span>
                <div>
                  <el-date-picker
                    v-model="queryParams.arrivalDateEnd"
                    type="date"
                    class="w150"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :picker-options="endPickerOptions"
                    style="width: 100%;"
                  ></el-date-picker>
                </div>
              </div>
            </el-form-item>
            <el-form-item :label="$t('是否历史数据')">
              <gever-select
                v-model="queryParams.isHistory"
                class="w200"
                :options="optionsMap.isHistory"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item :label="$t('出纳凭证号')">
              <el-input v-model="queryParams.cashierVoucherNumber" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item>
              <el-button
                plain
                icon="el-icon-search"
                type="primary"
                round
                @click="handleSearch"
              >{{ $t('搜索') }}</el-button>
            </el-form-item>
          </el-form>
          <el-tabs
            v-model="activeName"
            type="card"
            style="height: calc(100% - 215px) !important;margin-top:15px"
            @tab-click="handleClick"
          >
            <el-tab-pane label="镇" name="first">
              <div style="float: right;padding-bottom: 10px;">
                <el-button
                  v-hasPermi="'specialFund:receipt:village:examine'"
                  type="primary"
                  round
                  @click="handleExamine(1)"
                >{{ $t('入账') }}</el-button>
                <el-button
                  v-hasPermi="'specialFund:receipt:village:cancelAudit'"
                  type="primary"
                  round
                  @click="handleCancelExamine"
                >{{ $t('取消入账') }}</el-button>
                <!-- <el-button
                  v-hasPermi="'specialFund:receipt:village:updateArrivalDate'"
                  type="primary"
                  round
                  @click="handleExamine(2)"
                >{{ $t('修改入账日期') }}</el-button>-->
              </div>
              <gever-table
                ref="_geverTableRef"
                style="height: 480px"
                :loading="tableLoading"
                :columns="table.columns"
                :data="table.tableList"
                :total="table.total"
                :pagi="queryParams"
                :row-class-name="getRowClassName"
                @pagination-change="getList"
                @selection-change="handleSelectionChange"
              >
                <template
                  #isHistory="{ row }"
                >{{ getTitle(row.isHistory+'', optionsMap.isHistory) }}</template>
                <template
                  #specialType="{ row }"
                >{{ getTitle(row.specialType+'', optionsMap.specialType) }}</template>
                <template
                  #specialSource="{ row }"
                >{{ getTitle(row.specialSource+'', optionsMap.specialSource) }}</template>
                <template #status="{ row }">{{ getTitle(row.status+'', optionsMap.status) }}</template>
                <template
                  #reviewerTime="{ row }"
                >{{ row.reviewerTime?row.reviewerTime.substring(0,10):"" }}</template>
                <template
                  #createTime="{ row }"
                >{{ row.createTime?row.createTime.substring(0,10):"" }}</template>
                <template #categoryId="{ row }">
                  <span
                    v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
                  >{{ row.categoryId != undefined && row.categoryId != ''? row.categoryCode+'-'+row.categoryName :'' }}</span>
                </template>
                <template #rowIndex="{row, index }">
                  <span
                    v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
                  >{{ (queryParams.page - 1) * queryParams.rows + index + 1 }}</span>
                </template>
                <template #operation="{ row }">
                  <span v-if="row.orgName != '本页合计' && row.orgName != '总合计'">
                    <el-button
                      v-hasPermi="'specialFund:receipt:village:view'"
                      type="text"
                      @click="handleView(row)"
                    >{{ $t('查看') }}</el-button>
                    <!-- <el-button
                      v-hasPermi="'specialFund:receipt:village:edit'"
                      :disabled="row.status != 1 && row.status != 0"
                      type="text"
                      @click="handleEdit(row)"
                    >{{ $t('编辑') }}</el-button>
                    <el-button
                      v-hasPermi="'specialFund:receipt:village:delete'"
                      :disabled="row.status != 1 && row.status != 0"
                      type="text"
                      @click="handleRemove(row,true)"
                    >{{ $t('删除') }}</el-button>-->
                  </span>
                </template>
              </gever-table>
            </el-tab-pane>
            <el-tab-pane label="村" name="second">
              <div style="float: right;padding-bottom: 10px;">
                <el-button
                  v-hasPermi="'specialFund.receipt.excelBatchImportHistory'"
                  type="primary"
                  round
                  @click="handleBatchImport(1)"
                >{{ $t('初始化导入') }}</el-button>
                <el-button
                  v-hasPermi="'specialFund:receipt:village:save'"
                  type="primary"
                  round
                  @click="handleAdd"
                >{{ $t('新增') }}</el-button>
                <el-button
                  v-hasPermi="'specialFund:receipt:village:examine'"
                  type="primary"
                  round
                  @click="handleExamine(1)"
                >{{ $t('入账') }}</el-button>
                <el-button
                  v-hasPermi="'specialFund:receipt:village:cancelAudit'"
                  type="primary"
                  round
                  @click="handleCancelExamine"
                >{{ $t('取消入账') }}</el-button>
                <!-- <el-button
                  v-hasPermi="'specialFund:receipt:village:excelBatchImport'"
                  type="primary"
                  round
                  @click="handleExamine(2)"
                >{{ $t('修改入账日期') }}</el-button>-->
                <el-button plain type="primary" round @click="handleBatchImport(2)">{{ $t('批量导入') }}</el-button>
                <el-button
                  v-hasPermi="'specialFund:receipt:village:batchFileImport'"
                  plain
                  type="primary"
                  round
                  @click="handleBatchUploadAttachment"
                >{{ $t('批量上传附件') }}</el-button>
              </div>
              <div>
                <gever-table
                  ref="_geverVillagetTableRef"
                  style="height: 480px"
                  :loading="villagetTableLoading"
                  :columns="villagetTable.columns"
                  :data="villagetTable.tableList"
                  :total="villagetTable.total"
                  :pagi="queryParams"
                  width="100%"
                  :row-class-name="getRowClassName"
                  @pagination-change="getList"
                  @selection-change="handleSelectionChange"
                >
                  <template
                    #isHistory="{ row }"
                  >{{ getTitle(row.isHistory+'', optionsMap.isHistory) }}</template>
                  <template
                    #specialType="{ row }"
                  >{{ getTitle(row.specialType+'', optionsMap.specialType) }}</template>
                  <template
                    #specialSource="{ row }"
                  >{{ getTitle(row.specialSource+'', optionsMap.specialSource) }}</template>
                  <template
                    #status="{ row }"
                  >{{ getTitle(row.status+'', optionsMap.villagetSstatus) }}</template>
                  <template
                    #createTime="{ row }"
                  >{{ row.createTime?row.createTime.substring(0,10):"" }}</template>
                  <template
                    #reviewerTime="{ row }"
                  >{{ row.reviewerTime?row.reviewerTime.substring(0,10):"" }}</template>
                  <template #categoryId="{ row }">
                    <span
                      v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
                    >{{ row.categoryId != undefined && row.categoryId != ''? row.categoryCode+'-'+row.categoryName:'' }}</span>
                  </template>
                  <template #rowIndex="{row, index }">
                    <span
                      v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
                    >{{ (queryParams.page - 1) * queryParams.rows + index + 1 }}</span>
                  </template>
                  <template #operation="{ row }">
                    <span v-if="row.orgName != '本页合计' && row.orgName != '总合计'">
                      <el-button
                        v-hasPermi="'specialFund:receipt:village:view'"
                        type="text"
                        @click="handleView(row)"
                      >{{ $t('查看') }}</el-button>
                      <el-button
                        v-hasPermi="'specialFund:receipt:village:edit'"
                        :disabled="(row.status != 1 && row.status != 0) || row.isHistory == 1"
                        type="text"
                        @click="handleEdit(row)"
                      >{{ $t('编辑') }}</el-button>
                      <el-button
                        v-hasPermi="'specialFund:receipt:village:delete'"
                        :disabled="row.status != 1 && row.status != 0"
                        type="text"
                        @click="handleRemove(row,true)"
                      >{{ $t('删除') }}</el-button>
                    </span>
                  </template>
                </gever-table>
              </div>
            </el-tab-pane>
            <el-tab-pane label="全部" name="all">
              <div style="float: right;padding-bottom: 10px;">
                <el-button
                  v-hasPermi="'specialFund:receipt:village:examine'"
                  type="primary"
                  round
                  @click="handleExamine(1)"
                >{{ $t('入账') }}</el-button>
                <el-button
                  v-hasPermi="'specialFund:receipt:village:cancelAudit'"
                  type="primary"
                  round
                  @click="batchCancelAccounting()"
                >{{ $t('取消入账') }}</el-button>
              </div>
              <div>
                <gever-table
                  ref="_geverAllTableRef"
                  style="height: 480px"
                  :loading="villagetTableLoading"
                  :columns="allTable.columns"
                  :data="allTable.tableList"
                  :total="allTable.total"
                  :pagi="queryParams"
                  width="100%"
                  :row-class-name="getRowClassName"
                  @pagination-change="getList"
                  @selection-change="handleSelectionChange"
                >
                  <template
                    #isHistory="{ row }"
                  >{{ getTitle(row.isHistory+'', optionsMap.isHistory) }}</template>
                  <template
                    #specialType="{ row }"
                  >{{ getTitle(row.specialType+'', optionsMap.specialType) }}</template>
                  <template
                    #specialSource="{ row }"
                  >{{ getTitle(row.specialSource+'', optionsMap.specialSource) }}</template>
                  <template
                    #status="{ row }"
                  >{{ getTitle(row.status+'',(row.specialType==1? optionsMap.villagetSstatus:optionsMap.status)) }}</template>
                  <template
                    #createTime="{ row }"
                  >{{ row.createTime?row.createTime.substring(0,10):"" }}</template>
                  <template
                    #reviewerTime="{ row }"
                  >{{ row.reviewerTime?row.reviewerTime.substring(0,10):"" }}</template>
                  <template #categoryId="{ row }">
                    <span
                      v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
                    >{{ row.categoryId != undefined && row.categoryId != ''? row.categoryCode+'-'+row.categoryName:'' }}</span>
                  </template>
                  <template #rowIndex="{row, index }">
                    <span
                      v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
                    >{{ (queryParams.page - 1) * queryParams.rows + index + 1 }}</span>
                  </template>
                  <template #operation="{ row }">
                    <span v-if="row.orgName != '本页合计' && row.orgName != '总合计'">
                      <el-button
                        v-hasPermi="'specialFund:receipt:village:view'"
                        type="text"
                        @click="handleView(row)"
                      >{{ $t('查看') }}</el-button>
                    </span>
                  </template>
                </gever-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </template>
    </fold-box>
    <public-drawer
      :visible.sync="detailVisible"
      :title="detailTitle"
      :size="60"
      :buttons="detailButtons"
      @close="handleCloseDetail"
    >
      <detail
        v-if="detailVisible"
        :id="currentId"
        ref="_detailContentRef"
        :detail-visible.sync="detailVisible"
        :area="areaLogin"
        :type="type"
        :special-type="queryParams.specialType"
        @refreshTable="getList"
      />
    </public-drawer>
    <batch-import
      ref="batchImportRef"
      :org-id="areaLogin.id"
      :special-type="queryParams.specialType"
      :area="areaLogin"
      @refreshTable="getList"
      @close="handleCloseDetail"
    />
    <public-drawer
      :visible.sync="batchFileVisible"
      title="上传附件"
      :size="40"
      :buttons="batchFileButtons"
      @close="handleCloseDetail"
    >
      <batchFile
        v-if="batchFileVisible"
        ref="_batchFileRef"
        :ids="batchFileIds"
        :detail-visible.sync="batchFileVisible"
        @refreshTable="getList"
        @close="handleCloseDetail"
      />
    </public-drawer>

    <public-drawer
      :visible.sync="arrivalDateVisible"
      :title="'入账登记'"
      :size="30"
      :buttons="arrivalDateButtons"
      @close="handleCloseDetail"
    >
      <arrival-date
        v-if="arrivalDateVisible"
        ref="arrivalDateRef"
        :visible.sync="arrivalDateVisible"
        :ids="arrivalDateIds"
        :form="form"
        :total-amount="totalAmount"
        :title="arrivalDateTitle"
        :org-id="arrivalDateOrgId"
        :bank-account-number="bankAccountNumber"
        @refreshTable="getList"
        @close="handleCloseDetail"
      />
    </public-drawer>
    <public-drawer
      :visible.sync="arrivalDateHistoryVisible"
      :title="'入账登记'"
      :size="30"
      :buttons="arrivalDateButtons"
      @close="handleCloseDetail"
    >
      <arrivalDateHistory
        v-if="arrivalDateHistoryVisible"
        ref="arrivalDateHistoryRef"
        :visible.sync="arrivalDateHistoryVisible"
        :ids="arrivalDateIds"
        :form="form"
        :total-amount="totalAmount"
        :title="arrivalDateTitle"
        :org-id="arrivalDateOrgId"
        :bank-account-number="bankAccountNumber"
        @refreshTable="getList"
        @close="handleCloseDetail"
      />
    </public-drawer>
  </div>
</template>

<script>
import { loadReceiptListApi, deleteReceiptApi, updateStatus, batchCancelAccounting } from '@/api/specialFund/receipt.js'
import detail from './components/detail'
import categoryTreeSelect from './components/categoryTreeSelect'
import batchImport from './components/batchImport'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
import batchFile from './components/batchFile'
import arrivalDate from './components/arrivalDate'
import arrivalDateHistory from './components/arrivalDateHistory.vue'
export default {
  name: 'Receipt',
  components: {
    detail,
    categoryTreeSelect,
    batchImport,
    batchFile,
    arrivalDate,
    arrivalDateHistory
  },
  data () {
    return {
      arrivalDateHistoryVisible: false,
      arrivalDateIds: '',
      arrivalDateTitle: '',
      arrivalDateVisible: false,
      villagetTable: {
        columns: [
          { type: 'selection', align: 'center', width: '50', selectable: this.selectEnable, fixed: 'left' },
          { prop: 'rowIndex', label: '序号', width: '50' },
          { label: '编制方', prop: 'specialType', width: '80' },
          { label: '单位名称', prop: 'orgName', width: '300' },
          { label: '专项资金编号', prop: 'receiptCode', width: '150' },
          { label: '专项资金类别', prop: 'categoryId', width: '200' },
          { label: '专项资金来源', prop: 'specialSource', width: '150' },
          { label: '专项资金名称', prop: 'receiptName', width: '150' },
          // { label: '收款账户名称', prop: 'bankAccountName', width: '240' },
          { label: '金额', prop: 'amountStr', width: '150' },
          { label: '入账日期', prop: 'arrivalDate', width: '150' },
          { label: '期限', prop: 'term', width: '100' },
          { label: '出纳凭证号', prop: 'cashierVoucherNumber', width: '100' },
          { label: '状态', prop: 'status', width: '80' },
          { label: '是否历史数据', prop: 'isHistory', width: '120' },
          { label: '创建人', prop: 'creatorName', width: '120' },
          { label: '创建日期', prop: 'createTime', minWidth: '120' },
          { label: '操作', slotName: 'operation', width: '130', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      queryParams: {
        page: 1,
        rows: 20,
        specialType: 2,
        arrivalDateEnd: '',
        arrivalDateStart: '',
        statusList: [2, 3]
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50', selectable: this.selectEnable, fixed: 'left' },
          { prop: 'rowIndex', label: '序号', width: '50' },
          { label: '编制方', prop: 'specialType', width: '80' },
          { label: '单位名称', prop: 'orgName', width: '300' },
          { label: '专项资金编号', prop: 'receiptCode', width: '150' },
          { label: '专项资金类别', prop: 'categoryId', width: '200' },
          { label: '专项资金来源', prop: 'specialSource', width: '150' },
          { label: '专项资金名称', prop: 'receiptName', width: '150' },
          // { label: '收款账户名称', prop: 'bankAccountName', width: '240' },
          { label: '金额', prop: 'amountStr', width: '150', align: 'right' },
          { label: '入账日期', prop: 'arrivalDate', width: '150' },
          { label: '期限', prop: 'term', width: '100' },
          { label: '出纳凭证号', prop: 'cashierVoucherNumber', width: '100' },
          { label: '状态', prop: 'status', width: '80' },
          { label: '是否历史数据', prop: 'isHistory', width: '120' },
          { label: '创建人', prop: 'creatorName', width: '120' },
          { label: '创建日期', prop: 'createTime', minWidth: '120' },
          { label: '审核人', prop: 'reviewerName', minWidth: '120' },
          { label: '审核日期', prop: 'reviewerTime', minWidth: '120' },
          { label: '操作', slotName: 'operation', width: '130', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('暂 存'),
          buttonStatus: true,
          callback: this.handleSaveDraft
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ],
      // 按钮
      arrivalDateButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handlArrivalDateSave
        }
      ],
      batchFileButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('确 定'),
          buttonStatus: true,
          callback: this.handleBatchFileSave
        }
      ],
      treeData: [],
      props: {
        value: 'id', // ID字段名
        label: 'text', // 显示名称
        children: 'children', // 子级字段名
        isLeaf: 'isLeaf'
      },
      optionsMap: {
        status: [],
        specialSource: [],
        specialType: [],
        villagetSstatus: [
          {
            text: '暂存',
            id: '0'
          },
          {
            text: '村编制',
            id: '1'
          },
          {
            text: '镇已审核',
            id: '2'
          },
          {
            text: '已入账',
            id: '3'
          }
        ],
        isHistory: [
          {
            text: '否',
            id: '0'
          },
          {
            text: '是',
            id: '1'
          }
        ]
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      activeName: 'first',
      villagetTableLoading: false,
      form: {},
      totalAmount: '',
      arrivalDateOrgId: '',
      bankAccountNumber: '',
      allTable: {
        columns: [
          { type: 'selection', align: 'center', width: '50', selectable: this.selectEnable, fixed: 'left' },
          { prop: 'rowIndex', label: '序号', width: '50' },
          { label: '编制方', prop: 'specialType', width: '80' },
          { label: '单位名称', prop: 'orgName', width: '300' },
          { label: '专项资金编号', prop: 'receiptCode', width: '150' },
          { label: '专项资金类别', prop: 'categoryId', width: '200' },
          { label: '专项资金来源', prop: 'specialSource', width: '150' },
          { label: '专项资金名称', prop: 'receiptName', width: '150' },
          // { label: '收款账户名称', prop: 'bankAccountName', width: '240' },
          { label: '金额', prop: 'amountStr', width: '150' },
          { label: '入账日期', prop: 'arrivalDate', width: '150' },
          { label: '期限', prop: 'term', width: '100' },
          { label: '出纳凭证号', prop: 'cashierVoucherNumber', width: '100' },
          { label: '状态', prop: 'status', width: '80' },
          { label: '是否历史数据', prop: 'isHistory', width: '120' },
          { label: '创建人', prop: 'creatorName', width: '120' },
          { label: '创建日期', prop: 'createTime', minWidth: '120' },
          { label: '操作', slotName: 'operation', width: '130', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      }
    }
  },
  computed: {
    ...mapState(['areaLogin']), // 地区机构数据
    startPickerOptions () {
      return {
        disabledDate: time => {
          if (this.queryParams.arrivalDateEnd) {
            return (
              time.getTime() >= new Date(this.queryParams.arrivalDateEnd).getTime()
            )
          }
          return false
        }
      }
    },
    endPickerOptions () {
      return {
        disabledDate: time => {
          if (this.queryParams.arrivalDateStart) {
            return (
              time.getTime() <= new Date(this.queryParams.arrivalDateStart).getTime() - 8.64e7
            )
          }
          return false
        }
      }
    }
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler () {
        this.getList()
      }
    }
  },
  created () {
    // this.getList()
    this.getOptions()
  },
  methods: {
    getRowClassName ({ row, rowIndex }) {
      if (row.orgName == '总合计') { // 例如，让第一行的字体加粗
        return 'row-bold'
      }
      return ''
    },
    handleAmountInput (field) {
      let value = this.queryParams[field]
      value = value.replace(/[^\d.]/g, '')// 只允许数字和小数点
        .replace(/^\./g, '')// 不能以小数点开头
        .replace(/\.{2,}/g, '.')// 不能有多个小数点
        .replace('.', '$#$').replace(/\./g, '').replace('$#$', '.') // 只保留第一个小数点

      // 限制小数点后最多两位
      if (value.indexOf('.') > -1) {
        const parts = value.split('.')
        if (parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2)
        }
      }
      this.queryParams[field] = value
    },
    // 失去焦点时格式化显示两位小数
    formatDecimal (field) {
      if (this.queryParams[field]) {
        const num = parseFloat(this.queryParams[field])
        if (!isNaN(num)) {
          this.queryParams[field] = num.toFixed(2)
        }
      }
    },
    validateAmountRange () {
      if (this.queryParams.amountMin && this.queryParams.amountMax) {
        if (Number(this.queryParams.amountMin) > Number(this.queryParams.amountMax)) {
          // this.$message.error('开始金额不能大于结束金额');
          // 可以清空其中一个值或进行其他处理
          this.queryParams.amountMax = ''
        }
      }
    },
    getList () {
      if (this.areaLogin.type == 'Organization') {
        this.queryParams.orgId = this.areaLogin.id
        this.queryParams.areaCode = ''
      } else {
        this.$message.warning(this.$t('请选择机构！'))
        return
        // this.queryParams.areaCode = this.areaLogin.code
        // this.queryParams.orgId = ''
      }
      console.log('this.queryParams', this.areaLogin)
      console.log('this.queryParams', this.queryParams)
      this.villagetTableLoading = true
      this.tableLoading = true
      loadReceiptListApi(this.queryParams).then(res => {
        if (this.activeName === 'first') {
          this.table.tableList = res.data.rows
          this.table.total = res.data.total
        } else if (this.activeName === 'all') {
          this.allTable.tableList = res.data.rows
          this.allTable.total = res.data.total
        } else {
          this.villagetTable.tableList = res.data.rows
          this.villagetTable.total = res.data.total
        }
      }).finally(() => {
        this.tableLoading = false
        this.villagetTableLoading = false
      })
    },
    async getOptions () {
      const { data: status = [] } = await getDictionary('/financial/specialFund/receipt/status/')
      this.optionsMap.status = status
      const { data: specialSource = [] } = await getDictionary('/financial/specialFund/receipt/specialSource/')
      this.optionsMap.specialSource = specialSource
      const { data: specialType = [] } = await getDictionary('/financial/specialFund/receipt/specialType/')
      this.optionsMap.specialType = specialType
    },
    handleCategoryValue (value) {
      this.queryParams.categoryId = value
    },
    handleSearch () {
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleAdd () {
      if (this.areaLogin.type != 'Organization') {
        this.$message.warning(this.$t('请先选择机构！'))
        return
      }
      this.detailButtons = [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('暂 存'),
          buttonStatus: true,
          callback: this.handleSaveDraft
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
      this.detailTitle = '新增'
      this.type = 'add'
      this.currentId = ''
      this.detailVisible = true
    },
    handleView (row) {
      this.detailButtons = [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        }
      ]
      this.detailTitle = '查看'
      this.type = 'view'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleEdit (row) {
      this.detailButtons = [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('暂 存'),
          buttonStatus: true,
          callback: this.handleSaveDraft
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
      this.detailTitle = '编辑'
      this.type = 'edit'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleRemove (row, batchFlag) {
      let ids
      if (batchFlag) {
        ids = row.id
      } else {
        if (this.tableIdSelection.length == 0) {
          return this.$message.warning(this.$t('请先选择要删除的数据！'))
        }
        ids = this.tableIdSelection.join(',')
      }
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        deleteReceiptApi({ ids }).then(res => {
          if (res) {
            this.$message.success(res.message)
            this.getList()
          }
        })
      }).catch(() => { })
    },
    handleCloseDetail () {
      this.detailVisible = false
      this.batchFileVisible = false
      this.arrivalDateVisible = false
      this.arrivalDateHistoryVisible = false
    },
    handleSave () {
      this.$refs._detailContentRef.save()
    },
    handleSaveDraft () {
      this.$refs._detailContentRef.saveDraft()
    },
    handleExamine (val) {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要入账的数据！'))
      }
      if (this.tableIdSelection.length != 1) {
        return this.$message.warning(this.$t('只能选择一条数据进行入账！'))
      }
      if (val == 1) {
        if (this.tableSelection[0].specialType == 2) {
          const data = this.tableSelection.filter(item => item.status != 2)
          if (data.length != 0) {
            return this.$message.warning(this.$t('请选择状态为"镇已审核"的数据！'))
          }
        } else {
          const data = this.tableSelection.filter(item => item.status != 1)
          if (data.length != 0) {
            return this.$message.warning(this.$t('请选择状态为"村编制"的数据！'))
          }
        }
      } else {
        const data = this.tableSelection.filter(item => item.status != 3)
        if (data.length != 0) {
          return this.$message.warning(this.$t('请选择状态为"已入账"的数据！'))
        }
      }
      const isHistory = this.tableSelection.every(item =>
        item.isHistory === this.tableSelection[0].isHistory
      )
      if (!isHistory) {
        return this.$message.warning(this.$t('是否是历史数据不一致，请选择相同类型'))
      }
      const allSame = this.tableSelection.every(item =>
        item.bankAccountNumber === this.tableSelection[0].bankAccountNumber
      )
      if (!allSame) {
        return this.$message.warning(this.$t('请选择同一个银行账号数据'))
      }
      const allOrgId = this.tableSelection.every(item =>
        item.orgId === this.tableSelection[0].orgId
      )
      if (!allOrgId) {
        return this.$message.warning(this.$t('请选择同一个机构数据'))
      }
      const totalAmount = this.tableSelection.reduce((sum, item) => {
        return sum + (item.amount || 0) // 使用 || 0 处理可能的 undefined 或 null 值
      }, 0)
      this.totalAmount = totalAmount
      const ids = this.tableIdSelection.join(',')
      this.arrivalDateIds = ids
      this.arrivalDateOrgId = this.tableSelection[0].orgId
      this.bankAccountNumber = this.tableSelection[0].bankAccountNumber
      this.arrivalDateTitle = val == 1 ? '入账' : '修改入账日期'
      if (this.tableSelection[0].isHistory == 1) {
        this.arrivalDateHistoryVisible = true
      } else {
        this.arrivalDateVisible = true
      }

      // this.$refs.arrivalDateRef.dialogVisible = true
    },
    handleCancelExamine () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要取消入账的数据！'))
      }
      const data = this.tableSelection.filter(item => item.status != 3)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择状态为"已入账"的数据！'))
      }
      this.$confirm(this.$t('是否确认对选中数据取消入账？'), '取消入账', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.tableIdSelection.join(',')
        if (this.activeName === 'first') {
          this.updateStatus(ids, 2, '')
        } else {
          this.updateStatus(ids, 1, '')
        }
      }).catch(() => {
        // 用户点击取消后的操作
      })
    },
    batchCancelAccounting() {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要取消入账的数据！'))
      }
      const data = this.tableSelection.filter(item => item.status != 3)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择状态为"已入账"的数据！'))
      }
      this.$confirm(this.$t('是否确认对选中数据取消入账？'), '取消入账', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(11111, this.tableSelection)

        batchCancelAccounting(this.tableSelection).then(res => {
          if (res) {
            this.$message.success(res.message)
            this.getList()
          }
        })
      }).catch((res) => {
        console.log(22222, res)
        // 用户点击取消后的操作
      })
    },
    handleBatchImport (val) {
      if (this.areaLogin.type != 'Organization') {
        this.$message.warning(this.$t('请先选择机构！'))
        return
      }
      this.$refs.batchImportRef.isHistory = val
      this.$refs.batchImportRef.contentForm.systemBatchId = ''
      this.$refs.batchImportRef.dialogVisible = true
    },
    handleBatchUploadAttachment () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要上传附件的数据！'))
      }
      const data = this.tableSelection.filter(item => item.status == 2 || item.status == 3)
      if (data.length != 0) {
        return this.$message.warning(this.$t('入账后不可再进行此操作。'))
      }
      this.batchFileIds = this.tableIdSelection.join(',')
      this.batchFileVisible = true
    },
    updateStatus (ids, status, specialType) {
      const data = {
        ids,
        status,
        specialType
      }
      updateStatus(data).then(res => {
        this.$message.success(res.message)
        this.getList()
      })
    },
    selectEnable (row) {
      return row.orgName != '本页合计' && row.orgName != '总合计'
    },
    handleBatchFileSave () {
      this.$refs._batchFileRef.save()
    },
    handleClick (tab, event) {
      this.queryParams.queryType = ''
      if (tab.name === 'first') {
        this.activeName = 'first'
        this.$set(this, 'activeName', 'first')
        this.queryParams.specialType = 2
        this.queryParams.statusList = [2, 3]
      } else if (tab.name === 'all') {
         this.activeName = 'all'
        this.$set(this, 'activeName', 'all')
        this.queryParams.specialType = ''
        this.queryParams.queryType = '3'
        this.queryParams.statusList = []
      } else {
        this.activeName = 'second'
        this.$set(this, 'activeName', 'second')
        this.queryParams.specialType = 1
        this.queryParams.statusList = []
      }
      this.queryParams.page = 1
      this.getList()
      this.$nextTick(() => {
        if (this.activeName === 'first' && this.$refs._geverTableRef && this.$refs._geverTableRef.resize) {
          this.$refs._geverTableRef.resize()
        }
        if (this.activeName === 'second' && this.$refs._geverVillagetTableRef && this.$refs._geverVillagetTableRef.resize) {
          this.$refs._geverVillagetTableRef.resize()
        }
        if (this.activeName === 'all' && this.$refs._geverAllTableRef && this.$refs._geverAllTableRef.resize) {
          this.$refs._geverAllTableRef.resize()
        }
      })
    },
    handleExport () {
      console.log('导出')
    },
    handlArrivalDateSave () {
      if (this.$refs.arrivalDateRef != undefined) {
        this.$refs.arrivalDateRef.save()
      } else {
        this.$refs.arrivalDateHistoryRef.save()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__item {
  padding: 0px !important;
  width: 110px;
  text-align: center;
  color: #1890ff;
}
::v-deep .el-tabs__content {
  overflow: auto;
  height: 100%;
  // max-height: 700px;
}
::v-deep .row-bold {
  font-weight: bold !important;
  background-color: #f8f8f9;
}
::v-deep .el-tabs__item.is-active {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #f8f8f9;
}
</style>
