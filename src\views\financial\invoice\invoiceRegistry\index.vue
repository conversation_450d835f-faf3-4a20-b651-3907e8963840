<template>
  <div>
    <fold-box :right-title="$t('注册订购')">
      <template #right>
        <div class="right-box">
          <div class="btnHead">
            <el-button
              v-hasPermi="`financial.invoice.invoiceRegistry.registry`"
              icon="el-icon-plus"
              type="primary"
              round
              :loading="loading"
              @click="handleDG()"
            >
              {{ '订 购' }}
            </el-button>
          </div>
          <div
            v-for="item in formList"
            :key="item.registryType"
            class="registryList"
            :style="total === 1 ? { width: '99%' } : {}"
          >
            <div>
              <div class="zcdgxx">
                <div class="zcdg_t">
                  <span>注册订购信息</span>
                </div>
                <table class="cTable" style="width: 500px; margin: 10px auto 0">
                  <tr>
                    <td class="rTitle">集体经济组织名称：</td>
                    <td>{{ item.orgName }}</td>
                  </tr>
                  <tr>
                    <td class="rTitle">发票服务商名称：</td>
                    <td>
                      {{
                        (item.registryTypeName || '') +
                          '（' +
                          item.remark +
                          '）'
                      }}
                    </td>
                  </tr>
                  <tr>
                    <td class="rTitle">有效期间：</td>
                    <td>{{ item.validPeriod }}</td>
                  </tr>
                </table>
              </div>
            </div>
            <div style="margin-top: 20px">
              <div class="zcdgxx">
                <div class="zcdg_t">
                  <span>收款账户信息</span>
                </div>
                <div
                  class="account-info"
                  style="width: 500px; margin: 2px auto"
                >
                  <div class="info-item">
                    <span class="label">户 名：</span>
                    <span class="value">{{ item.applierAccountName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">账 号：</span>
                    <span class="value">{{ item.applierAccountNo }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">开 户 行：</span>
                    <span class="value">{{ item.applierBankName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">联 系 人：</span>
                    <span class="value">{{ item.applierContact }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">服务金额：</span>
                    <span class="value">{{ item.applierAmount }}</span>
                  </div>
                  <div class="info-item">
                    <p>备注：转账时需要备注单位名称，转账后第二天10点前开通使用。</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="btnGroup">
              <el-button
                v-if="
                  hasPermission('financial.invoice.invoiceRegistry.renew') &&
                    item.registryFlag === 1 &&
                    (item.validBeginDate || '') !== '' &&
                    (item.validEndDate || '') !== ''
                "
                type="default"
                icon="el-icon-s-operation"
                round
                :disabled="item.registryFlag !== 1"
                :loading="loading"
                @click="handleXY(item)"
              >
                {{ '续 约' }}
              </el-button>
              <el-button
                v-if="
                  hasPermission('financial.invoice.invoiceRegistry.update') &&
                    (item.id || '') !== ''
                "
                type="default"
                icon="el-icon-document-checked"
                round
                :disabled="(item.id || '') === ''"
                :loading="loading"
                @click="handleGX(item)"
              >
                {{ '更新信息' }}
              </el-button>
              <el-button
                v-if="
                  hasPermission('financial.invoice.invoiceRegistry.record') &&
                    item.registryFlag === 1
                "
                type="default"
                icon="el-icon-document"
                round
                :disabled="item.registryFlag !== 1"
                :loading="loading"
                @click="handleJL(item)"
              >
                {{ '订购记录' }}
              </el-button>
              <el-button
                v-if="
                  hasPermission('financial.invoice.invoiceRegistry.delete') &&
                    (item.registryFlag || 0) === 0 &&
                    (item.id || '') !== ''
                "
                type="default"
                icon="el-icon-delete"
                round
                :disabled="item.registryFlag === 1 || (item.id || '') === ''"
                :loading="loading"
                @click="handleSC(item)"
              >
                {{ '删 除' }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </fold-box>
    <!-- 订购/续约 -->
    <details-dialog
      v-if="detailsVisible"
      :dialog-visible.sync="detailsVisible"
      :account-visible.sync="accountVisible"
      :type-no.sync="typeNo"
      :area-login="areaLogin"
      :status-type="statusType"
      :form-data="form"
      @updateList="getRegistry"
    ></details-dialog>
    <!-- 订购续约记录 -->
    <gever-dialog
      title="订购/续约记录"
      width="65%"
      :visible.sync="registryLogVisible"
      destroy-on-close
      style="margin-top: 5vh"
      :append-to-body="false"
      @close="registryLogClose"
    >
      <gever-table
        ref="_registryLogRef"
        :columns="registryLogColumns"
        :data="registryLogs"
        :loading="registryLogLoading"
        :pagination="false"
        :height="460"
      >
        <template #logType="{ row }">
          {{ row.logType === 1 ? '续约' : '注册订购' }}
        </template>
        <template #logState="{ row }">
          {{
            row.logState === 1 ? '申请中' : row.logState === 2 ? '已审批' : ''
          }}
        </template>
        <template #options="{ row }">
          <el-button
            type="text"
            :disabled="
              (form.registryFlag === 1 && row.logType === 0) ||
                row.logState === 2
            "
            @click="deleteRegistryLog(row)"
          >
            {{ '删除' }}
          </el-button>
          <el-button
            v-if="areaLogin.areaCode.startsWith('D440605') && row.logType === 0"
            type="text"
            @click="openOriFiles()"
          >
            {{ '附件' }}
          </el-button>
          <el-button
            v-if="
                row.logType === 1 &&
                row.protocolFile
            "
            type="text"
            @click="downloadProtocolFile(row.protocolFile)"
          >
            {{ '下载协议' }}
          </el-button>
        </template>
      </gever-table>
    </gever-dialog>
    <!-- 查看原服务合同附件 -->
    <gever-dialog
      title="查看原服务合同附件"
      width="880px"
      style="margin-top: 10vh"
      destroy-on-close
      :visible.sync="originalFileDetailsVisible"
      :append-to-body="false"
      @close="oriFileClose"
    >
      <el-form
        ref="_fileFormRef"
        :inline="true"
        :model="form"
        label-position="right"
        label-width="125px"
        class="gever-form"
        disabled
      >
        <div class="form-sg">
          <el-form-item :label="$t('单位名称')" prop="orgName">
            <el-input v-model="form.orgName" type="text" />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('纳税人识别号')" prop="taxpayerNo">
            <el-input v-model="form.taxpayerNo" type="text" />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('原合同开始日期')" prop="oriBeginDate">
            <el-date-picker
              v-model="form.oriBeginDate"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item :label="$t('原合同结束日期')" prop="oriEndDate">
            <el-date-picker
              v-model="form.oriEndDate"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </div>
        <div class="form-sg">
          <el-form-item :label="$t('原服务合同附件')">
            <gever-upload
              ref="_bwFilesRef"
              list-type="form-list"
              accept="jpg,jpeg,png,doc,xls,txt,pdf,docx,xlsx,zip,rar"
              file-classification="bwInvoice"
              :disabled="true"
              :business-id="form.id"
            />
          </el-form-item>
        </div>
      </el-form>
    </gever-dialog>
    <!-- 订购成功后提示信息 -->
    <gever-dialog
      title=""
      width="700px"
      :visible.sync="accountVisible"
      destroy-on-close
      style="margin-top: 20vh"
      :append-to-body="false"
      @close="accountClose"
    >
      <div class="account-info">
        <h1 style="color: red">已订购成功！</h1>
        <h3 style="color: #409eff; line-height: 35px">
          请向该发票服务商账户进行费用支付，并备注单位名称和税号，核实后即开通服务。
        </h3>
      </div>
      <div class="account-info" style="margin-left: 100px">
        <div class="info-item">
          <span class="label">户 名：</span>
          <span class="value">{{ form.applierAccountName }}</span>
        </div>
        <div class="info-item">
          <span class="label">账 号：</span>
          <span class="value">{{ form.applierAccountNo }}</span>
        </div>
        <div class="info-item">
          <span class="label">开 户 行：</span>
          <span class="value">{{ form.applierBankName }}</span>
        </div>
        <div class="info-item">
          <span class="label">联 系 人：</span>
          <span class="value">{{ form.applierContact }}</span>
        </div>
        <div class="info-item">
          <span class="label">服务金额：</span>
          <span class="value">{{ form.applierAmount }}</span>
        </div>
      </div>
    </gever-dialog>
  </div>
</template>

<script>
import {
  initLoad,
  deleteByIds,
  loadLogs,
  deleteRegistryLog
} from '@/api/financial/invoice/invoiceRegistry.js'
import detailsDialog from './components/details.vue'
import { createNamespacedHelpers } from 'vuex'
import { getToken } from '@/utils/auth'
import axios from 'axios'

const { mapState } = createNamespacedHelpers('area')
export default {
  name: 'InvoiceRegistry',
  components: { detailsDialog },
  data() {
    return {
      loading: false,
      formList: [],
      total: 0,
      form: {},
      detailsVisible: false,
      statusType: 'view',
      registryLogVisible: false,
      registryLogLoading: false,
      registryLogFromId: '',
      registryLogs: [],
      registryLogColumns: [
        {
          label: '序号',
          type: 'index',
          width: 55,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '服务商',
          prop: 'registryType',
          minWidth: '80',
          align: 'center'
        },
        { label: '类型', prop: 'logType', minWidth: '70', align: 'center' },
        { label: '状态', prop: 'logState', minWidth: '60', align: 'center' },
        {
          label: '申请开始日期',
          prop: 'beginDate',
          minWidth: '100',
          align: 'center'
        },
        {
          label: '申请结束日期',
          prop: 'endDate',
          minWidth: '100',
          align: 'center'
        },
        {
          label: '申请日期',
          prop: 'applyDate',
          minWidth: '100',
          align: 'center'
        },
        { label: '申请人', prop: 'applier', minWidth: '80', align: 'center' },
        { label: '备注', prop: 'remark', minWidth: '70', align: 'center' },
        { label: '操作', prop: 'options', minWidth: '110', align: 'center' }
      ],
      originalFileDetailsVisible: false,
      accountVisible: false,
      typeNo: '' // 注册订购成功后用户显示账户信息的服务商编码
    }
  },
  computed: {
    ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler() {
        this.getRegistry()
      }
    }
  },
  mounted() {},
  methods: {
    getRegistry() {
      if (this.areaLogin.level !== 5) {
        return this.$message.warning('请选择机构')
      }
      this.form = {}
      this.loading = true
      initLoad(this.areaLogin.id)
        .then((res) => {
          this.total = res.data.total
          const returnRows = res.data.rows
          returnRows.forEach((item) => {
            if ((item.orgName || '') === '') {
              item.orgName = this.areaLogin.fname
            }
          })
          this.formList = returnRows
          if (this.typeNo !== '') {
            this.formList.forEach((item) => {
              if (item.registryType === this.typeNo) {
                this.form = item
              }
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 判断是否有权限
    hasPermission(value) {
      const operationCodes = JSON.parse(
        localStorage.getItem('userInfo')
      ).operationCodes
      return operationCodes.indexOf(value) >= 0
    },
    // 订购
    handleDG() {
      this.statusType = 'add'
      this.detailsVisible = true
    },
    // 续约
    handleXY(item) {
      if (!item.registryFlag || item.registryFlag !== 1) {
        return this.$message.warning('该机构还未订购发票')
      }
      this.form = item
      this.statusType = 'renew'
      this.detailsVisible = true
    },
    // 更细信息
    handleGX(item) {
      this.form = item
      this.statusType = 'edit'
      this.detailsVisible = true
    },
    // 删除
    handleSC(item) {
      if (item.registryFlag === 1) {
        return this.$message.warning(
          '服务商' + item.registryTypeName || '' + '已注册订购，不能删除'
        )
      }
      this.$confirm(
        '是否确定删除该服务商（' + item.registryTypeName + '）的记录？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.loading = true
        deleteByIds({ id: item.id })
          .then((res) => {
            if (res.returnCode === '0') {
              this.$message.success(res.message)
              this.getRegistry()
            } else {
              this.$message.error(res.message)
            }
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 订购/续约记录
    handleJL(item) {
      this.form = item
      this.registryLogVisible = true
      this.registryLogFromId = item.id
      this.loadJL()
    },
    loadJL() {
      this.registryLogLoading = true
      loadLogs(this.registryLogFromId)
        .then((res) => {
          this.registryLogs = res.data
        })
        .finally(() => {
          this.registryLogLoading = false
        })
    },
    // 关闭订购/续约记录
    registryLogClose() {
      this.form = {}
      this.registryLogs = []
      this.registryLogFromId = ''
      this.registryLogVisible = false
      this.getRegistry()
    },
    deleteRegistryLog(item) {
      if (item.logState === 2) {
        return this.$message.warning('该记录已审批通过，不能删除')
      }
      this.$confirm('是否确定删除该记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteRegistryLog({ id: item.id })
          .then((res) => {
            if (res.returnCode === '0') {
              this.$message.success(res.message)
              this.loadJL()
            } else {
              this.$message.error(res.message)
            }
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 查看原服务合同附件
    openOriFiles() {
      this.originalFileDetailsVisible = true
    },
    // 关闭原服务合同附件
    oriFileClose() {
      this.originalFileDetailsVisible = false
    },
    // 关闭提示框
    accountClose() {
      this.accountVisible = false
    },
    // 下载协议文件
    downloadProtocolFile(protocolFile) {
      const url = this.getURL(
        '/filesystem/fileInfo/download?path=',
        protocolFile.filePath
      )
      this.loading = true
      axios({
        method: 'get',
        url: url,
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-user-token-header': getToken()
        }
      })
        .then((res) => {
          if (res.status === 200) {
            const blob = new Blob([res.data], {
              type: res.headers['content-type']
            })
            if (blob.size === 0) {
              this.$message.warning('没有数据可下载')
              return
            }
            const fileName = decodeURI(
              res.headers['content-disposition'].split('=')[1]
            ).replace(new RegExp('"', 'g'), '') // 下载时的文件名
            const elink = document.createElement('a')
            elink.download = fileName
            elink.style.display = 'none'
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            elink.click()
            URL.revokeObjectURL(elink.href) // 释放URL 对象
            document.body.removeChild(elink)
            this.$nextTick(() => {
              this.$message.success('下载成功')
              return '下载完成'
            })
          }
        })
        .catch((result) => {
          if (result.returnCode !== '0') {
            this.$message.warning(result.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getURL(serverUrl, path) {
      return `/${
        process.env.VUE_APP_REAL
      }${serverUrl}${path}&access_token=${getToken()}&tenant_id=${this.$Cookies.get(
        'X-tenant-id-header'
      )}`
    }
  }
}
</script>

<style lang="scss" scoped>
.right-box {
  overflow-y: auto;
}

.btnHead {
  width: 99%;
  height: 30px;
  text-align: right;
  clear: both;

  ::v-deep .el-button--mini.is-round {
    padding: 5px 15px;
    font-size: 15px !important;
  }
}

.registryList {
  width: calc(50% - 1px);
  padding: 5px 5px;
  float: left;
}

.registryList:not(:last-child) {
  border-right: 1px solid #c8c8c8;
}

.zcdgxx {
  width: 550px;
  margin: 0 auto;
  padding: 5px 0;
  border: 1px solid #409eff;
  border-radius: 10px;

  .zcdg_t {
    margin-top: 10px;
    padding: 0 22px;
    font-size: 18px;
    font-weight: bold;
    color: #237edc;
  }
}

.cTable {
  width: calc(100% - 12px);
  margin: 0 auto;
  border-collapse: collapse;

  // 注册订购信息行下面的边框
  //tr:not(:last-child) {
  //  border-bottom: 1px solid #ebeef5;
  //}

  td {
    font-size: 16px;
    line-height: 1.5;
    padding: 12px 5px;
  }

  .rTitle {
    width: 180px;
    color: #409eff;
    font-size: 16px;
    font-weight: bold;
    text-align: right;
  }
}

.btnGroup {
  margin: 20px 0 10px 0;
  width: calc(100% - 12px);
  padding: 10px 10px;
  text-align: center;

  ::v-deep .el-button--mini.is-round {
    padding: 10px 15px;
    font-size: 15px !important;
  }
}

.accountBtn {
  font-size: 14px;
  color: #409eff;
  margin-left: 25px;
  cursor: pointer;
}

.account-info {
  padding: 10px 20px 5px;
  color: #333;

  .info-item {
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;

    .label {
      font-weight: bold;
      margin-right: 10px;
      text-align: right;
      display: inline-block;
      width: 100px;
      color: #409eff;
    }

    p {
      width: 100%;
      text-align: center;
    }
  }
}
</style>
