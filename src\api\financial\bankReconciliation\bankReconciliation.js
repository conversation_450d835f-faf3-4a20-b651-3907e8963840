/*
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2023-11-08 17:37:26
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-02-14 16:28:04
 * @FilePath: \rural-financial-fs-master\src\api\financial\bankReconciliation\bankReconciliation.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

/**
 * @name:列表
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/listReconciliation',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:自动对账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function autoReconciliation(data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/autoReconciliation',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:手工对账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function manualReconciliation(data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/manualReconciliation',
    method: 'post',
    data: data
  })
}
/**
 * @name:取消对账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancelReconciliation(data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/cancelReconciliation',
    method: 'post',
    data: data
  })
}
/**
 * @name:获取未达账前置条件控制标识
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBankStatementFlag(params) {
  return request({
    url: '/financial/cashier/cashierBankStatement/getBankStatementFlag',
    method: 'get',
    params,
  })
}