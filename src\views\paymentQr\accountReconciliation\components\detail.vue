<template>
  <div v-loading="loading">
    <el-form
      ref="_formRef"
      class="gever-form"
      :model="detailForm"
      :rules="rules"
      label-width="150px"
    >
      <!-- 账户明细账 -->
      <div class="gever-title">{{ $t('账户明细账') }}</div>
      <el-table :data="accountDetails" border style="width: 100%"> 
        <el-table-column prop="activeAccountName" :label="$t('银行账户名称')" align="center" />
        <el-table-column prop="activeBankAccountNo" :label="$t('银行账号')" align="center" />
        <el-table-column prop="tradeTime" :label="$t('账户明细时间')" align="center" />
        <el-table-column prop="balance" :label="$t('账户余额（元）')" align="center" />
        <el-table-column prop="inAmt" :label="$t('收入金额（元）')" align="center" />
        <el-table-column prop="absInfo" :label="$t('交易摘要')" align="center" />
        <el-table-column prop="postScript" :label="$t('附言')" align="center" />
      </el-table>
      <br>
      <br>
      <!-- 关联银行交易账单 -->
      
      <div class="gever-title">{{ $t('关联银行交易账单') }}</div>
      <el-tabs v-model="activeTab">
        <el-tab-pane :label="$t('交易账单')" name="transaction">
          <div style="float: right;padding: 10px;" >
            <el-button type="primary" @click="autoReconciliation" :disabled="type === 'view'">{{ $t('自动对账') }}</el-button>
            <el-button type="primary" @click="selectTransaction" :disabled="type === 'view'">{{ $t('选择交易账单') }}</el-button>  
          </div>
          <gever-table
            :loading="tableLoading"
            ref="_geverTableRef"
            :columns="table.columns"
            :data="table.selectedData"
            style="clear: both;"
            :height="400"
            :pagination="false"
            show-summary
            :summary-method="getTransactionSummaries"
          >
            <template #operation="{ row }">
              <el-button :disabled="type === 'view'" type="text" @click="handleDelete(row)">{{ $t('删除') }}</el-button>
            </template>
            <template #orderNo="{ row }">
             {{ getOrderNoPrefix(row.orderNo) }}
            </template>
          </gever-table>
        </el-tab-pane>
        <el-tab-pane :label="$t('收款明细')" name="receipt">
          <el-form inline>
            <el-form-item :label="$t('收款项目')">
              <el-input v-model="searchForm.receiveItemName" class="w200" />
            </el-form-item>
            <el-form-item :label="$t('付款方')">
              <el-input v-model="searchForm.payerAccountName" class="w200" />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                plain
                round
                icon="el-icon-search"
                @click="searchReceipt"
              >
                {{ $t('搜索') }}
              </el-button>
            </el-form-item>
          </el-form>
          <gever-table
            :loading="receiptDetailsLoading"
            ref="_receiptTableRef"
            :columns="receiptColumns"
            :data="receiptDetails"
            :pagination="false"
            style="clear: both;"
            :height="400"
            show-summary
            :summary-method="getSummaries"
          >
          </gever-table>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <public-drawer 
      :visible.sync="selectTransactionDialogVisible" 
      title="选择交易账单"
      :size="80" 
      :buttons="detailButtons" 
      @close="handleCloseDetail"
    >
      <select-transaction
        v-if="selectTransactionDialogVisible" 
        :accountNumber="accountNumberForSelect"
        :activeMerchantNo="activeMerchantNo"
        :tradeTime="accountDetail.tradeTime"
        :id="id"
        @selection-change="handleChildSelectionChange"
        :initial-selected-data="table.selectedData"
      />
    </public-drawer>
  </div>
</template>

<script>
// 引入 selectTransaction 组件
import SelectTransaction from './selectTransaction.vue'
// import { getAccountReconciliationDetailApi } from '@/api/paymentQr/accountReconciliation.js' // 假设存在获取详情的 API
import { getDictionary } from '@/api/gever/common.js'
import { loadSettleListApi, reconcile } from '@/api/paymentQr/qrPaymentSettle.js'
import { loadQrPaymentReceiveDetailListApi } from '@/api/paymentQr/qrPaymentReceiveDetail.js'

export default {
  name: 'AccountReconciliationDetail',
  components: {
    SelectTransaction
  },
  props: {
    detailVisible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'view'
    },
    id: {
      type: [String, Number],
      default: null
    },
    activeAccountName: {
      type: String,
      default: ''
    },
    activeBankAccountNo: {
      type: String,
      default: ''
    },
    activeMerchantNo: {
      type: String,
      default: ''
    },
    // 接收从 index.vue 传递过来的当前行数据
    accountDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      detailForm: {
      },
      optionsMap: {

      },
      rules: {

      },
      accountDetails: [],
      relatedTransactions: [],
      receiptDetails: [],
      receiptColumns: [
        { type: 'index', label: this.$t('序号'), width: '50' },
        { label: this.$t('订单号'), prop: 'orderNo', minWidth: '150', resizable: true },
        { label: this.$t('收款项目'), prop: 'receiveItemName', minWidth: '200', resizable: true },
        { label: this.$t('金额'), prop: 'money', minWidth: '100', resizable: true },
        { label: this.$t('付款方'), prop: 'payerAccountName', minWidth: '200', resizable: true }
      ],
      activeTab: 'transaction',
      searchForm: {
        receiptItem: '',
        payer: '',
        receiveItemName:'',
        payerAccountName:''
      },
      // 控制选择交易账单弹窗的显示与隐藏
      selectTransactionDialogVisible: false,
      // 传递给 selectTransaction 组件的 accountNumber
      accountNumberForSelect: '',
      // 定义 detailButtons 数组
      detailButtons: [
        {
          type: '',
          text: this.$t('取消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          text: this.$t('确定'),
          type: 'primary',
          buttonStatus: true,
          callback: this.handleSelectTransactionConfirm
        }
      ],
      // 新增 table 配置
      table: {
        columns: [
          { type: 'index', label: '序号', width: '50' },
          { label: '商户号', prop: 'merchantNo', minWidth: '150', resizable: true },
          { label: '订单号', prop: 'orderNo', minWidth: '150', resizable: true },
          { label: '交易时间', prop: 'transactionTime', minWidth: '150', resizable: true },
          { label: '交易金额', prop: 'transactionAmount', minWidth: '100', resizable: true },
          { label: '客户账号', prop: 'customerAccount', minWidth: '150', resizable: true },
          { label: '说明', prop: 'description', minWidth: '200', resizable: true },
          { label: '操作', prop: 'operation', minWidth: '100', resizable: true }
        ],
        selectedData: []
      },
      // 新增加载状态
      tableLoading: false,
      receiptDetailsLoading: false
    }
  },
  watch: {
    'table.selectedData': {
      handler(newVal) {
        // 当 table.selectedData 变化时调用 searchReceipt 方法
        this.$set(this.searchForm,'payerAccountName', '')
        this.$set(this.searchForm,'receiveItemName', '')
        this.searchReceipt();
      },
      deep: true // 深度监听对象或数组的变化
    },
    detailVisible: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.id) {
          this.getDetail()
          this.getOptions()
          // 当弹窗显示时，更新 accountDetails
          this.accountDetails = [this.accountDetail]
          this.accountDetails[0].activeAccountName = this.activeAccountName
          this.accountDetails[0].activeBankAccountNo = this.activeBankAccountNo
          this.accountDetails[0].activeMerchantNo = this.activeMerchantNo
          this.getReconciliation()
        }
      }
    },
    // 监听 accountDetail 变化，更新 accountDetails 并合并属性
    accountDetail: {
      immediate: true,
      handler(newVal) {
        this.accountDetails = [newVal]
        this.accountDetails[0].activeAccountName = this.activeAccountName
        this.accountDetails[0].activeBankAccountNo = this.activeBankAccountNo
        this.accountDetails[0].activeMerchantNo = this.activeMerchantNo
      }
    }
  },
  methods: {
    async getDetail() {
      this.loading = true
      try {
        // const res = await getAccountReconciliationDetailApi(this.id)
        // this.detailForm = res.data
      } catch (error) {
        console.error('获取账户明细对账详情失败', error)
        this.$message.error(this.$t('获取账户明细对账详情失败'))
      } finally {
        this.loading = false
      }
    },
    async getOptions() {
      // try {
      //   const [bankTypeRes, accountTypeRes] = await Promise.all([
      //     getDictionary('bankType'),
      //     getDictionary('accountType')
      //   ])
      //   this.optionsMap.bankType = bankTypeRes.data
      //   this.optionsMap.accountType = accountTypeRes.data
      // } catch (error) {
      //   console.error('获取字典数据失败', error)
      //   this.$message.error(this.$t('获取字典数据失败'))
      // }
    },
    save() {
      this.$refs._formRef.validate(async (valid) => {
        if (valid) {
          // 假设存在保存的 API
          // await saveAccountReconciliationApi(this.detailForm)
          this.$message.success(this.$t('保存成功'))
          this.$emit('refreshTable')
          this.$emit('update:detailVisible', false)
        }
      })
    },
    // 新增格式化时间的方法
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      // return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      return `${year}-${month}-${day}`;
    },
    async autoReconciliation() {
      console.log(this.accountDetail.tradeTime)
      const endDate = new Date(this.accountDetail.tradeTime);
      const startDate = new Date(this.accountDetail.tradeTime);
      startDate.setDate(startDate.getDate() - 1);
      this.tableLoading = true
      try {
        const queryParams = {
          merchantNo : this.activeMerchantNo,
          transactionTimeEnd : this.formatDate(endDate),
          transactionTimeStart : this.formatDate(startDate),
          reconciliation : 0,
          accountDetailId : this.id,
          rows : 999
        }
        const res = await loadSettleListApi(queryParams)
        if (res.data && res.data.rows) {
          this.table.selectedData = res.data.rows
        }
      } catch (error) {
        console.error('自动对账查询数据失败', error)
        this.$message.error(this.$t('自动对账查询数据失败'))
      } finally {
        this.tableLoading = false
      }
    },
    async getReconciliation() {
      this.tableLoading = true
      try {
        const queryParams = {
          accountDetailId : this.id,
          rows : 999
        }
        const res = await loadSettleListApi(queryParams)
        if (res.data && res.data.rows) {
          this.table.selectedData = res.data.rows
        }
      } catch (error) {
        console.error('交易账单查询数据失败', error)
        this.$message.error(this.$t('交易账单查询数据失败'))
      } finally {
        this.tableLoading = false
      }
    },
    selectTransaction() {
      const accountNumber = this.accountDetail.accountNumber.replace(/-/g, '')
      this.accountNumberForSelect = accountNumber
      this.selectedData = [...this.table.selectedData]
      // 显示选择交易账单弹窗
      this.selectTransactionDialogVisible = true
    },
    cancel() {
      this.$emit('update:detailVisible', false)
    },
    async confirm() {
      try {
        // 计算收款明细总金额
        const totalMoney = this.table.selectedData.reduce((sum, item) => {
          return sum + Number(item.transactionAmount || 0);
        }, 0);
        
        // 获取账户明细收入金额
        const accountInAmt = Number(this.accountDetail.inAmt || 0);
        
        // 判断金额是否一致并显示对应提示
        const isMatch = accountInAmt === totalMoney;
        const confirmMsg = this.$t(isMatch 
          ? '对账结果"金额一致"，是否确认保存？' 
          : '对账结果“金额不一致”，是否确认保存？');
    
        // 显示确认对话框
        await this.$confirm(confirmMsg, this.$t('提示'), {
          confirmButtonText: this.$t('保存'),
          cancelButtonText: this.$t('取消'),
          type: 'warning'
        });
    
        // 用户确认后执行对账逻辑
        const settleDetailIds = this.table.selectedData.map(item => item.id);
        if (settleDetailIds.length === 0) {
          this.$message.warning(this.$t('请选择先选择交易账单'));
          return;
        }
    
        const params = {
          settleDetailIds,
          accountDetailId: this.id
        };
        const res = await reconcile(params);
        
        if (res.returnCode === '0') {
          this.$message.success(this.$t('对账成功'));
          this.$emit('refreshTable');
          this.$emit('update:detailVisible', false);
        } else {
          this.$message.error(this.$t('对账失败') + ': ' + res.message);
        }
      } catch (error) {
        // 忽略用户取消操作的错误
        if (error !== 'cancel') {
          console.error('对账接口调用失败', error);
          this.$message.error(this.$t('对账接口调用失败'));
        }
      }
    },
    searchReceipt() {
      // 如果没有选中的交易账单，直接清空收款明细
      if (this.table.selectedData.length === 0) {
        this.receiptDetails = [];
        return;
      }
      
      // 从 table.selectedData 中提取所有 orderNo 并用逗号分隔
      const orderNos = this.table.selectedData.map(item => this.getOrderNoPrefix(item.orderNo)).join(',');
      this.searchForm.orderNo = orderNos;

      this.receiptDetailsLoading = true;
      loadQrPaymentReceiveDetailListApi(this.searchForm).then(res => {
        if (res.data && res.data.rows) {
          this.receiptDetails = res.data.rows;
        } else {
          this.receiptDetails = []; // 接口无数据时也清空
        }
      }).catch(error => {
        console.error('查询收款明细失败', error);
        this.$message.error(this.$t('查询收款明细失败'));
      }).finally(() => {
        this.receiptDetailsLoading = false;
      });
    },
    handleSelectTransactionConfirm() {
      // 处理选择交易账单确认后的逻辑
      this.table.selectedData = [...this.selectedData]
      console.log('选择的交易账单数据:', this.table.selectedData)
      this.selectTransactionDialogVisible = false
    },
    handleCloseDetail() {
      this.selectTransactionDialogVisible = false
      this.selectedData = []
    },
    handleChildSelectionChange(selectedData) {
      this.selectedData = selectedData
      
    },
    handleDelete(row) {
      this.$confirm(this.$t('确定要删除该条数据吗？'), this.$t('提示'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        this.table.selectedData = this.table.selectedData.filter(item => item !== row);
        this.$message.success(this.$t('删除成功'));
      }).catch(() => {
        this.$message.info(this.$t('已取消删除'));
      });
    },
    getOrderNoPrefix(orderNo) {
      if (orderNo) {
        return orderNo.split('-')[0] || orderNo;
      }
      return '';
    },
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }else if (index === 3) {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            // 使用整数计算避免浮点数精度问题
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                // 转换为分进行计算
                return prev + Math.round(value * 100)
              } else {
                return prev
              }
            }, 0) / 100 // 转换回元
          } else {
            sums[index] = ''
          }
        }else{
          sums[index] = ''
        }
      })

      return sums
    },
    getTransactionSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计'
          return
        }else if (index === 4) {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            // 使用整数计算避免浮点数精度问题
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                // 转换为分进行计算
                return prev + Math.round(value * 100)
              } else {
                return prev
              }
            }, 0) / 100 // 转换回元
          } else {
            sums[index] = ''
          }
        }else{
          sums[index] = ''
        }
      })

      return sums
    }
  }
}
</script>

<style scoped>
/* 样式后续补充 */
.gever-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}
.form-db {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}
</style>
