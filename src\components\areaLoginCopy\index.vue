<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-05-22 11:57:53
 * @LastEditors: liuwf
 * @LastEditTime: 2025-05-22 16:01:55
-->
<template>
  <div>
    <div class="login-info">
      <div class="name">
        {{ $t('地区/机构') + '：' }}
      </div>
      <div class="value" :title="$t(loginInfo.fname)" @click="handleLogin">
        {{ $t(loginInfo.fname) }}
        <i class="el-icon-arrow-down" style="float: right; line-height: 20px" />
      </div>
    </div>

    <el-drawer
      custom-class="loginDrawer"
      :visible.sync="loginVisible"
      append-to-body
      :modal-append-to-body="false"
      :title="$t('地区/机构选择')"
      :wrapper-closable="false"
      :size="500"
    >
      <div class="area-box">
        <div class="area">
          <area-org-selector-copy
            :expand-first="true"
            :type="'area'"
            @current-change="handleNodeChange"
          />
        </div>
        <div class="btn-area">
          <el-button type="primary" @click="handleOrgChange">
            {{ $t('确 定') }}
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { authTreeData, loginAreaOrg } from '@/api/gever/common.js'
import { treeDataAll } from '@/api/system/organization.js'
const IS_USE_DEFAULT_TOP_LEVEL = false // 未登录时默认使用顶层地区作为当前地区登录,为true时开启,其他值或未配置时关闭
const IS_MULTI_MODULE_SHARING = true // 是否多模块公用登录信息,为true时开启,其他值或未配置时关闭
export default {
  name: 'AreaLoginCopy',
  data() {
    return {
      treeData: [],
      loginVisible: false,
      selectedNode: {},
      loginInfo: {}
    }
  },
  computed: {
    module() {
      return ''
    },
    saveKey() {
      return 'areaLoginCopy'
    },
    loginType() {
      // 允许登录的类型。all不限制、area地区、org机构
      return 'area'
    },
    loginLevel() {
      // 允许登录的地区层级。0不限制、1省级、2市级、3区级、4县级、5村级
      return '0'
    }
  },
  watch: {
    '$route.path': {
      handler: function (val) {
        console.log(val)
        this.initLoginInfo()
      },
      immediate: false
    },
    module: {
      handler: function (newVal, oldVal) {
        console.log(newVal)
        console.log(oldVal)
      },
      immediate: true
    }
  },
  created() {
    console.log(this.$route.path)
    authTreeData().then((res) => {
      this.treeData = res.data
      this.initLoginInfo()
    })
  },
  methods: {
    initLoginInfo() {
      this.loginInfo = JSON.parse(sessionStorage.getItem(this.saveKey)) || {}
      if (!this.loginInfo.id) {
        if (IS_USE_DEFAULT_TOP_LEVEL) {
          loginAreaOrg({
            areaOrgId: this.treeData[0].id,
            type: this.treeData[0].type
          }).then((res) => {
            this.loginInfo = res.data
            sessionStorage.setItem(this.saveKey, JSON.stringify(this.loginInfo))
          })
        } else {
          this.loginVisible = true
        }
      } else {
        sessionStorage.setItem(this.saveKey, JSON.stringify(this.loginInfo))
      }
    },
    handleLogin() {
      this.loginVisible = true
    },
    handleNodeChange(data) {
      this.selectedNode = data
    },
    handleOrgChange() {
      const currentNode = this.selectedNode
      if (!currentNode) return
      if (this.loginType === 'org' && currentNode.type !== 'Organization') {
        return this.$message.warning(this.$t('请选择机构！'))
      } else if (this.loginType === 'area' && currentNode.type !== 'Area') {
        return this.$message.warning(this.$t('请选择地区！'))
      }
      if (
        this.loginLevel &&
        this.loginLevel != '0' &&
        currentNode.type === 'Area'
      ) {
        /**
         * 注意：机构的attributes.level都是5
         * 所以暂时不做机构的层级选择限制
         */
        if (currentNode.attributes.level != this.loginLevel) {
          const areaMap = {
            1: '省级',
            2: '市级',
            3: '区级',
            4: '镇级',
            5: '村级'
          }
          return this.$message.warning(
            `${this.$t('请选择')}${areaMap[this.loginLevel]}${this.$t('地区')}`
          )
        }
      }
      const params = {
        areaOrgId: currentNode.id,
        type: currentNode.type
      }
      loginAreaOrg(params).then((res) => {
        this.$message.success(this.$t('地区/机构切换成功！'))
        this.loginInfo = res.data
        sessionStorage.setItem(this.saveKey, JSON.stringify(this.loginInfo))
        this.loginVisible = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-info {
  color: #000;
  font-weight: 500;
  & > div {
    display: inline-block;
    vertical-align: middle;
    height: 20px;
    line-height: 20px;
  }
  .value {
    cursor: pointer;
    width: 300px;
    padding-left: 8px;
    border-bottom: 1px solid #000;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
::v-deep .loginDrawer {
  .el-drawer__header {
    font-size: 16px;
    padding: 10px;
    margin: 0;
  }
  .el-drawer__body {
    padding: 10px;
    display: flex;
    flex-direction: column;
    height: 100%;
    .filter-area {
      height: 30px;
      display: flex;
      .el-input {
        flex: 1;
        margin-right: 5px;
      }
    }
    .area-tree-container {
      flex: 1;
      overflow-y: auto;
    }
    .btn-area {
      width: 100%;
      height: 30px;
      text-align: center;
      .el-button {
        width: 80%;
      }
    }
  }
}
.area-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  .area {
    flex: 1;
    overflow: scroll;
  }
}
</style>
