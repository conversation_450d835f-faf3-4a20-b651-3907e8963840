import request from '@/utils/request'

/**
 * 获取账簿列表
 */
export function loadReportList (data) {
  return request({
    url: '/financial/report/reportList',
    method: 'post',
    data: data
  })
}

/**
 * 获取账套的期间数据
 */
export function loadAccountingPeriod (params) {
  return request({
    url: '/financial/books/basedata/accountingperiod/combotree',
    method: 'get',
    params: params
  })
}

/**
 * yes no配置
 */
export function loadYesAndNo () {
  return request({
    url: '/json/financial/public/YesAndNo.json',
    method: 'get'
  })
}

/**
 * 获取会计科目
 */
export function loadAccountingItemList (params) {
  return request({
    url: '/financial/books/basedata/accountingItem/getAccountingItemList',
    method: 'post',
    params: params
  })
}

/**
 * 获取细科目设置数据
 */
export function loadBooksSetting (params) {
  return request({
    url: '/financial/booksSetting/getSetting',
    method: 'post',
    params: params
  })
}

/**
 * 获取细科目设置数据
 */
export function loadReport (data, params) {
  return request({
    url: '/financial/report/get',
    method: 'post',
    data: data,
    params: params
  })
}

/**
 * 获取meta数据
 */
export function getMeta (data) {
  return request({
    url: '/financial/report/getMeta',
    method: 'post',
    data: data
  })
}

/**
 * 获取辅助核算类型列表
 */
export function loadAssistTypeList (params) {
  return request({
    url: '/financial/books/basedata/assistType/listByBooksId',
    method: 'post',
    params: params
  })
}

/**
 * 获取辅助核算类型对应辅助核算项目
 */
export function loadAssistProjectList (params) {
  return request({
    url: '/financial/books/basedata/assistProject/getAssistPrj',
    method: 'get',
    params: params
  })
}

/**
 * 获取现金科目列表
 */
export function loadCashierItemList (data) {
  return request({
    url: '/financial/books/basedata/accountingItem/getCashierItemList',
    method: 'post',
    data: data
  })
}

/**
 * 获取excel导出路径
 */
export function saveExcelTempFile (data) {
  return request({
    url: '/financial/report/saveExcelTempFile',
    method: 'post',
    data: data,
    payload: true,
    timeout: 180000
  })
}

/**
 * 获取excel导出路径,3分钟超时（与后台相同）
 * 财务账簿页面使用
 */
export function saveExcelTempFileExtra (data) {
  return request({
    url: '/financial/report/saveExcelTempFile',
    method: 'post',
    data: data,
    payload: true,
    timeout: 180000
  })
}

/**
 * 科目辅助余额查看明细
 * 
 */
export function getDetail (data) {
  return request({
    url: '/financial/report/getDetail',
    method: 'post',
    data: data,
    // payload: true,
    timeout: 180000
  })
}

/* 资产对账表-统计 */
export function getListReport (data, params) {
  return request({
    url: '/financial/report/get',
    method: 'post',
    data: data,
    params: params,
    // payload: true,
    timeout: 180000
  })
}

/* 资产对账表-导出 */
export function saveExcelTempFileReport (data) {
  return request({
    url: '/financial/report/saveExcelTempFile',
    method: 'post',
    data: data,
    payload: true,
    timeout: 180000
  })
}

/* 资产对账表-外层信息 */
export function getMetaReport (data) {
  return request({
    url: '/financial/report/getMeta',
    method: 'post',
    data: data,
    // payload: true,
    timeout: 180000
  })
}

/* 基础会计科目查询 */
export function getBaseItemListReport (data) {
  return request({
    url: '/financial/booktypes/basedata/item/getBaseItemList',
    method: 'post',
    data: data,
    // payload: true,
    timeout: 180000
  })
}