import request from '@/utils/request'

//多栏账查询
export function showLedgerList(data) {
  return request({
    url: '/financial/accounting/accountingMultiColumnLedger/showLedgerList',
    method: 'get',
    params: data,
    withCredentials: true
  })
}
//方案下拉查询
export function loadMultiColumnLedgerList(data) {
  return request({
    url: '/financial/accounting/accountingMultiColumnLedger/loadMultiColumnLedgerList',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

// 方案列表查询
export function listLedger(data) {
  return request({
    url: '/financial/accounting/accountingMultiColumnLedger/listLedger',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

//方案详情
export function loadLedger(data) {
  return request({
    url: '/financial/accounting/accountingMultiColumnLedger/loadLedger',
    method: 'get',
    params: data,
    withCredentials: true
  })
}
//方案新增修改
export function saveLedger(params,data) {
  return request({
    url: '/financial/accounting/accountingMultiColumnLedger/saveLedger',
    method: 'post',
    params:params,
    data: data,
    payload:true
  })
}
// 方案删除
export function deleteLedger(data) {
  return request({
    url: '/financial/accounting/accountingMultiColumnLedger/deleteLedger',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 自动编排
export function autoSort(data) {
  return request({
    url: '/financial/accounting/accountingMultiColumnLedger/autoSort',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

//获取核算科目
export function loadItemList(data) {
  return request({
    url: '/financial/accounting/accountingMultiColumnLedger/loadItemList',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

//获取辅助核算类型
export function getItemAssist(data) {
  return request({
    url: '/financial/books/basedata/accountingItem/getItemAssist',
    method: 'get',
    params: data,
    withCredentials: true                
  })
}

//获取核算科目下级
export function loadChildrenItemList(data) {
  return request({
    url: '/financial/accounting/accountingMultiColumnLedger/loadChildrenItemList',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

//获取辅助核算下级
export function getAssistPrj(data) {
  return request({
    url: '/financial/books/basedata/assistProject/getAssistPrj',
    method: 'get',
    params: data,
    withCredentials: true
  })
}