import request from '@/utils/request'

/**
 * 获取凭证审核列表
 */
export function loadAccountingApproveList(data) {
  return request({
    url: '/financial/accounting/accountingApprove/pageVoucherShow',
    method: 'post',
    data: data
  })
}

/**
 * 凭证审核
 */
export function accountingApprove(data) {
  return request({
    url: '/financial/accounting/accountingApprove/approve',
    method: 'post',
    data: data
  })
}

/**
 * 凭证取消审核
 */
export function accountingUnApprove(data) {
  return request({
    url: '/financial/accounting/accountingApprove/unApprove',
    method: 'post',
    data: data
  })
}

/**
 * 凭证全部审核
 */
export function accountingApproveAll(data) {
  return request({
    url: '/financial/accounting/accountingApprove/approveAll',
    method: 'post',
    data: data
  })
}

/**
 * 凭证全部取消审核
 */
export function accountingUnApproveAll(data) {
  return request({
    url: '/financial/accounting/accountingApprove/unApproveAll',
    method: 'post',
    data: data
  })
}

/**
 * 凭证id查询
 */
export function voucherIdQuery(data) {
  return request({
    url: '/financial/accounting/accountingVoucherQuery/voucherIdQuery',
    method: 'post',
    data: data
  })
}

/**
   * 凭证id查询
   */
export function loadPrintData(params) {
  return request({
    url: '/financial/accounting/accountingVoucher/loadPrintData',
    method: 'post',
    params: params
  })
}

/**
   * 凭证机构年份
   */
export function getOrgBooksYear(params) {
  return request({
    url: '/financial/books/orgBooksYear',
    method: 'post',
    params: params
  })
}

/**
   * 凭证机构年份对应的期间
   */
export function getOrgBooksPeriod(params) {
  return request({
    url: '/financial/books/orgBooksPeriod',
    method: 'post',
    params: params
  })
}

/**
   * 获取账表
   */
export function loadListReport(data) {
  return request({
    url: '/financial/accounting/accountingVoucher/listReport',
    method: 'post',
    data: data
  })
}
