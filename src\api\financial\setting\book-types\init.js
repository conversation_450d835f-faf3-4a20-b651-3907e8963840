/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-01-19 16:25:52
 * @LastEditTime: 2022-01-19 16:35:19
 * @LastEditors: Peng <PERSON>
 * @Description:
 * @FilePath: \gever-zhxc-ui\src\api\financial\setting\book-types\init.js
 * ^-^
 */

import request from '@/utils/request'
/**
 * @name:初始化之前检测是否已经初始化
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function checkBookTypes() {
  return request({
    url: '/financial/bjBookTypes/checkBookTypes',
    method: 'get',
    withCredentials: true
  })
}

/**
 * @name:初始化
 * @param {*}
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function initBookTypes() {
  return request({
    url: '/financial/bjBookTypes/initBookTypes',
    method: 'post',
    withCredentials: true
  })
}
