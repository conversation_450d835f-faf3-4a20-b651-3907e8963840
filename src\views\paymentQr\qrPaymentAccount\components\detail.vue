<template>
  <div v-loading="loading" class="gever-form detail">
    <el-form
      ref="_geverFormRef"
      class="gever-form"
      :model="contentForm"
      :rules="rules"
      inline
      label-width="170px"
      :disabled="type == 'view'"
    >
		<div class="gever-title">{{ $t('基本信息') }}</div>
		<div class="form-db">
			<el-form-item :label="$t('组织机构代码')" prop="orgCode">
				<gever-input v-model="contentForm.orgCode" disabled />
			</el-form-item>
      <el-form-item :label="$t('组织机构名称')" prop="orgName">
				<gever-input v-model="contentForm.orgName" disabled />
			</el-form-item>
		</div>
		
		<div class="form-db">
			<el-form-item :label="$t('银行账号')" prop="bankAccountNo" :class="type === 'view' ? 'disableSelectAcount' : 'selectAcount'">
        <gever-input v-model="contentForm.bankAccountNo"  :disabled="true" placeholder="请选择" >
				<template slot="append" >
					<el-button
						type="primary"
						@click="dataAccountClick"
						:disabled="type === 'view'"
					>
						{{ $t('选择') }}
					</el-button>
				</template>
				</gever-input>
			</el-form-item>
      <el-form-item :label="$t('账户类型')" prop="accountType">
        <el-select v-model="contentForm.accountType" disabled  >
          <el-option
            v-for="item in optionsMap.acountType"
            :key="item.id"
            :label="item.text"
            :value="item.id">
          </el-option>
        </el-select>
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('户主名称')" prop="accountName">
				<gever-input v-model="contentForm.accountName" disabled/>
			</el-form-item>
      <el-form-item :label="$t('银行类型')" prop="bankType">
        <el-select v-model="contentForm.bankType" disabled>
                <el-option
                  v-for="item in optionsMap.bankType"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
                </el-option>
              </el-select>
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('开户银行')" prop="bankName">
				<gever-input v-model="contentForm.bankName" disabled />
			</el-form-item>
      <el-form-item :label="$t('商户号')" prop="merchantNo">
				<gever-input v-model="contentForm.merchantNo" maxlength="100" />
			</el-form-item>
		</div>
		<div class="form-db">
			<el-form-item :label="$t('证书密码')" prop="secretKey">
				<gever-input v-model="contentForm.secretKey" maxlength="100"/>
			</el-form-item>
      <el-form-item :label="$t('状态')" prop="status">
				<el-select v-model="contentForm.status" placeholder="请选择" >
					<el-option
					v-for="item in vaildOptions"
					:key="item.id"
					:label="item.text"
					:value="item.id">
					</el-option>
				</el-select>
			</el-form-item>
		</div>

    <div class="gever-title">{{ $t('附件') }}</div>
    <div class="form-sg">
      <el-form-item label="1. 商户证书" prop="qrPatmentAcountCertFile" :class="type == 'view' ? 'required-label' : 'required-label pfx-label'">
        <span class="tooltip-icon" title="注：如上传文件错误，则无法生成收款二维码">
          <i class="el-icon-question"></i>
        </span>
        <span class="red">* (支持.pem/.crt/.cer/.pfx/.p12/.der，单个文件不超过 10MB)</span>
        <gever-upload
          ref="_uploadRef"
          list-type="form-list"
          :business-id="contentForm.id"
          :disabled="type == 'view'"
          :limit="1"
          accept=".pem,.crt,.cer,.pfx,.p12,.der" 
          file-classification="qrPatmentAcountCertFile"
          @batch-change="handleCertBatchIdChange"
        />
        <!-- accept=".pfx" -->
      </el-form-item>
    </div>
    <div class="form-sg">
      <el-form-item :label="$t('2.其他')" prop="qrPatmentAcountFile" >
        <!-- <br> -->
        <gever-upload
				ref="_uploadRef2"
				list-type="form-list"
				:business-id="contentForm.id"
				:disabled="type == 'view'"
				file-classification="qrPatmentAcountFile"
				@batch-change="handleBatchIdChange"
				/>
				<!-- accept=".pdf" -->
			</el-form-item>
		</div>

    </el-form>

    <public-drawer
      :visible.sync="chooseVisible"
      title="选择收款账户"
      :size="95"
      :buttons="chooseButtons"
      @close="handleCloseChoose"
    >
      <chooseAccount v-if="chooseVisible" ref="_chooseRef" />
    </public-drawer>
  </div>
</template>

<script>
import {
  loadQrPaymentAccountInfoApi,saveQrPaymentAccountApi,updateQrPaymentAccountApi} from '@/api/paymentQr/qrPaymentAccount.js'
import chooseAccount from './chooseAccount'
import { mapGetters, createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
import {vaildOptions} from '../../config'
export default {
  components: {
    chooseAccount
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
	    loading: false,
      chooseVisible: false,
      vaildOptions,
      optionsMap: {
        bankType: [],
        acountType: []
      },
      batchId:  '',
      certBatchId:  '',
      chooseButtons: [
        {
          type: '',
          text: this.$t('返 回'),
          buttonStatus: true,
          callback: this.handleCloseChoose
        },
        {
          type: 'primary',
          text: this.$t('确 定'),
          buttonStatus: true,
          callback: this.handleChoose
        }
      ],
      contentForm: {},
      rules: { 
        merchantNo: [
          {
            required: true,
            message: '请输入商户号',
            trigger: ['blur', 'change']
          }
        ],
        secretKey: [
          {
            required: true,
            message: '请输入证书密码',
            trigger: ['blur', 'change']
          }
        ],
        status: [
          {
            required: true,
            message: '请选择状态',
            trigger: ['blur', 'change']
          }
        ],
        
      }
    }
  },
  computed: {
    ...mapState(['areaLogin'])
  },
  created () {
    this.initPage()
    this.getOptions()
  },
  methods: {
    initPage() {
      if (this.type === 'add') {
        // 新增页面初始化
        const orgCode = this.areaLogin.code
        const orgName = this.areaLogin.fname
        const areaCode = this.areaLogin.areaCode
        this.contentForm.orgCode = orgCode
        this.contentForm.orgName = orgName
        this.contentForm.areaCode = areaCode
      } else if (this.type === 'view' || this.type === 'edit') {
        // 查看、编辑页面初始化
        this.loading = true
        loadQrPaymentAccountInfoApi(this.id).then(res => {
          this.contentForm = res.data
        }).finally(() => {
          this.loading = false
        })
      }
    },
    async getOptions () {
      const { data: bankType = [] } = await getDictionary('/payment/pay_bank_type/')
      this.optionsMap.bankType = bankType

      const { data: acountType = [] } = await getDictionary('/financial/bank_account_type/')
      this.optionsMap.acountType = acountType
    },
    handleBatchIdChange(batchId) {
      this.batchId = batchId
    },
    handleCertBatchIdChange(certBatchId) {
      this.certBatchId = certBatchId
    },
    save() {
      this.$refs['_geverFormRef'].validate(valid => {
        if(!this.contentForm.bankAccountNo){
          return this.$message.warning(this.$t('请选择收款账户'))
        }
        if (!valid) {
          return this.$message.warning(this.$t('请完善表单信息'))
        }
        if(this.$refs['_uploadRef'].fileList.length < 1){
          return this.$message.warning('请上传商户证书')
        }
        this.contentForm.certBatchId = this.certBatchId
        this.contentForm.batchId = this.batchId
        this.loading = true
        if (this.contentForm.id) {
          updateQrPaymentAccountApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        } else {
          saveQrPaymentAccountApi(this.contentForm).then(res => {
            if (res.returnCode === '0') {
              this.$message.success(this.$t('操作成功'))
              this.$emit('refreshTable')
              this.$emit('update:detailVisible', false)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    dataAccountClick(){
      this.chooseVisible = true
    },
    handleCloseChoose() {
      this.chooseVisible = false
    },
    handleChoose() {
      if (this.$refs['_chooseRef'].tableSelection.length !== 1) {
        return this.$message.warning(this.$t('请选择一条记录!'))
      }

      const acount = this.$refs['_chooseRef'].tableSelection[0]
      this.$set(this.contentForm, 'bankAccountNo', acount.accountNumber)
      this.$set(this.contentForm, 'accountType', acount.accountType)
      this.$set(this.contentForm, 'accountName', acount.ownerName)
      this.$set(this.contentForm, 'bankType', acount.bankType)
      this.$set(this.contentForm, 'bankName', acount.bankName)
      this.$set(this.contentForm, 'bankAccountId', acount.id)

	    this.handleCloseChoose()
    }
  }
}
</script>


<style scoped>
::v-deep .selectAcount .el-input-group__append {
  background-color: #1890ff;
  color: #FFF;
}

::v-deep .disableSelectAcount .el-input-group__append {
  background-color: #F5F7FA;
  color: rgb(0, 0, 0);
}

.tooltip-icon {
  margin-right: 5px;
  cursor: help;
}

.tooltip-icon i {
  font-size: 16px;
  color: #52abff;
}

::v-deep .btn:active {
  outline: 0;
  background-image: none;
  box-shadow: none !important; 
  cursor: auto;
}

::v-deep .btn {
  cursor: auto;
}
</style>
