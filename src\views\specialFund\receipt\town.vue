<!--
 * @Description: 专项资金入账管理  镇级编制
 * @Author: liuwf
 * @Date: 2025-05-19 09:37:40
 * @LastEditors: liuwf
 * @LastEditTime: 2025-08-12 16:22:38
-->
<template>
  <div>
    <fold-box right-title="镇级下达">
      <template #right>
        <div class="right-box">
          <el-form inline @submit.native.prevent>
            <el-form-item :label="$t('专项资金编号')">
              <el-input v-model="queryParams.receiptCode" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金类别')">
              <!-- @getValue="handleCategoryValue" -->
              <categoryTreeSelect v-model="queryParams.categoryId" class="w200" placeholder="请选择"></categoryTreeSelect>
            </el-form-item>
            <el-form-item :label="$t('专项资金名称')">
              <el-input v-model="queryParams.receiptName" type="text" class="w200" clearable />
            </el-form-item>
            <el-form-item :label="$t('专项资金来源')">
              <gever-select
                v-model="queryParams.specialSource"
                class="w200"
                placeholder="请选择"
                path="/financial/specialFund/receipt/specialSource/"
              />
            </el-form-item>
            <el-form-item :label="$t('状态')">
              <gever-select
                v-model="queryParams.status"
                class="w200"
                placeholder="请选择"
                path="/financial/specialFund/receipt/status/"
              />
            </el-form-item>
            <el-form-item label="金额">
              <div style="display: flex;justify-content:space-between">
                <div>
                  <el-input
                    v-model="queryParams.amountMin"
                    type="text"
                    class="w150"
                    clearable
                    @input="handleAmountInput('amountMin')"
                    @blur="formatDecimal('amountMin')"
                    @change="validateAmountRange"
                  />
                </div>
                <span style="text-align: center;margin-left:3px;margin-right:3px;">-</span>
                <div>
                  <el-input
                    v-model="queryParams.amountMax"
                    type="text"
                    class="w150"
                    clearable
                    @input="handleAmountInput('amountMax')"
                    @blur="formatDecimal('amountMax')"
                    @change="validateAmountRange"
                  />
                </div>
              </div>
            </el-form-item>
            <el-form-item :label="$t('是否历史数据')">
              <gever-select
                v-model="queryParams.isHistory"
                class="w200"
                :options="optionsMap.isHistory"
                placeholder="请选择"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                plain
                icon="el-icon-search"
                type="primary"
                round
                @click="handleSearch"
              >{{ $t('搜索') }}</el-button>
            </el-form-item>
            <div style="float: right;">
              <el-form-item>
                <el-button
                  v-hasPermi="'specialFund:receipt:twon:save'"
                  type="primary"
                  round
                  icon="el-icon-plus"
                  @click="handleAdd"
                >{{ $t('新增') }}</el-button>
              </el-form-item>
              <el-form-item>
                <!-- v-hasPermi="'receipt.receipt.receipt.add'" -->
                <el-button
                  v-hasPermi="'specialFund:receipt:twon:examine'"
                  type="primary"
                  round
                  @click="handleExamine"
                >{{ $t('审核') }}</el-button>
              </el-form-item>
              <el-form-item>
                <!-- v-hasPermi="'receipt.receipt.receipt.add'" -->
                <el-button
                  v-hasPermi="'specialFund:receipt:twon:cancelAudit'"
                  type="primary"
                  round
                  @click="handleCancelExamine"
                >{{ $t('取消审核') }}</el-button>
              </el-form-item>
              <el-form-item>
                <!-- v-hasPermi="'receipt.receipt.receipt.delete'" -->
                <el-button
                  v-hasPermi="'specialFund:receipt:twon:excelBatchImport'"
                  plain
                  type="primary"
                  round
                  @click="handleBatchImport"
                >{{ $t('批量导入') }}</el-button>
              </el-form-item>
              <el-form-item>
                <!-- v-hasPermi="'receipt.receipt.receipt.delete'" -->
                <el-button
                  v-hasPermi="'specialFund:receipt:twon:batchFileImport'"
                  plain
                  type="primary"
                  round
                  @click="handleBatchUploadAttachment"
                >{{ $t('批量上传附件') }}</el-button>
              </el-form-item>
            </div>
          </el-form>
          <gever-table
            ref="_geverTableRef"
            :loading="tableLoading"
            :columns="table.columns"
            :data="table.tableList"
            :total="table.total"
            :pagi="queryParams"
            :row-class-name="getRowClassName"
            @pagination-change="getList"
            @selection-change="handleSelectionChange"
          >
            <template
              #isHistory="{ row }"
            >{{ getTitle(row.isHistory+'', optionsMap.isHistory) }}</template>
            <template
              #specialType="{ row }"
            >{{ getTitle(row.specialType+'', optionsMap.specialType) }}</template>
            <template
              #specialSource="{ row }"
            >{{ getTitle(row.specialSource+'', optionsMap.specialSource) }}</template>
            <template #status="{ row }">{{ getTitle(row.status+'', optionsMap.status) }}</template>
            <template
              #reviewerTime="{ row }"
            >{{ row.reviewerTime?row.reviewerTime.substring(0,10):"" }}</template>
            <template #bankAccountName="{ row }">
              <span
                v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
              >{{ row.bankAccountNumber+'('+getTitle(row.bankAccountType+'', optionsMap.bankAccountType) +')' }}</span>
            </template>
            <template #categoryId="{ row }">
              <span
                v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
              >{{ row.categoryId != undefined && row.categoryId != ''? row.categoryCode+'-'+row.categoryName:'' }}</span>
            </template>
            <template #rowIndex="{row, index }">
              <span
                v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
              >{{ (queryParams.page - 1) * queryParams.rows + index + 1 }}</span>
            </template>
            <template #createTime="{ row }">{{ row.createTime?row.createTime.substring(0,10):"" }}</template>
            <template #operation="{ row }">
              <span v-if="row.orgName != '本页合计' && row.orgName != '总合计'">
                <el-button
                  v-hasPermi="'specialFund:receipt:twon:view'"
                  type="text"
                  @click="handleView(row)"
                >{{ $t('查看') }}</el-button>
                <el-button
                  v-hasPermi="'specialFund:receipt:twon:edit'"
                  :disabled="row.status != 1 && row.status != 0"
                  type="text"
                  @click="handleEdit(row)"
                >{{ $t('编辑') }}</el-button>
                <el-button
                  v-hasPermi="'specialFund:receipt:twon:delete'"
                  type="text"
                  :disabled="row.status != 1 && row.status != 0"
                  @click="handleRemove(row,true)"
                >{{ $t('删除') }}</el-button>
              </span>
            </template>
          </gever-table>
        </div>
      </template>
    </fold-box>
    <public-drawer
      :visible.sync="detailVisible"
      :title="detailTitle"
      :size="60"
      :buttons="detailButtons"
      @close="handleCloseDetail"
    >
      <detail
        v-if="detailVisible"
        :id="currentId"
        ref="_detailContentRef"
        :detail-visible.sync="detailVisible"
        :area="areaLogin"
        :type="type"
        :special-type="2"
        :category-area-code="categoryAreaCode"
        @refreshTable="getList"
      />
    </public-drawer>
    <batch-import
      ref="batchImportRef"
      :org-id="areaLogin.id"
      :special-type="2"
      :area="areaLogin"
      @refreshTable="getList"
      @close="handleCloseDetail"
    />
    <public-drawer
      :visible.sync="batchFileVisible"
      title="上传附件"
      :size="40"
      :buttons="batchFileButtons"
      @close="handleCloseDetail"
    >
      <batchFile
        v-if="batchFileVisible"
        ref="_batchFileRef"
        :ids="batchFileIds"
        :detail-visible.sync="batchFileVisible"
        @refreshTable="getList"
        @close="handleCloseDetail"
      />
    </public-drawer>
  </div>
</template>

<script>
import { loadReceiptListApi, deleteReceiptApi, updateStatus } from '@/api/specialFund/receipt.js'
import detail from './components/detail'
import categoryTreeSelect from './components/categoryTreeSelect'
import batchImport from './components/batchImport'
import { createNamespacedHelpers } from 'vuex'
const { mapState } = createNamespacedHelpers('area')
import { getDictionary } from '@/api/gever/common.js'
import batchFile from './components/batchFile'
export default {
  name: 'Receipt',
  components: {
    detail,
    categoryTreeSelect,
    batchImport,
    batchFile
  },
  data () {
    return {
      tableLoading: false,
      currentSelectedNode: null,
      currentSelectedTreeNode: null,
      queryParams: {
        page: 1,
        rows: 20,
        specialType: 2
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50', selectable: this.selectEnable, fixed: 'left' },
          { prop: 'rowIndex', label: '序号', width: '50' },
          { label: '单位名称', prop: 'orgName', width: '300' },
          { label: '专项资金编号', prop: 'receiptCode', width: '150' },
          { label: '专项资金类别', prop: 'categoryId', width: '200' },
          { label: '专项资金来源', prop: 'specialSource', width: '150' },
          { label: '专项资金名称', prop: 'receiptName', width: '150' },
          // { label: '收款账户名称', prop: 'bankAccountName', width: '240' },
          { label: '金额', prop: 'amountStr', width: '150', align: 'right' },
          { label: '期限', prop: 'term', width: '100' },
          { label: '状态', prop: 'status', width: '80' },
          { label: '是否历史数据', prop: 'isHistory', width: '120' },
          { label: '创建人', prop: 'creatorName', width: '120' },
          { label: '创建日期', prop: 'createTime', minWidth: '120' },
          { label: '审核人', prop: 'reviewerName', minWidth: '120' },
          { label: '审核日期', prop: 'reviewerTime', minWidth: '120' },
          { label: '操作', slotName: 'operation', width: '130', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      tableSelection: [],
      tableIdSelection: [],
      type: '',
      currentId: '',
      detailTitle: '',
      detailVisible: false,
      // 按钮
      detailButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('暂 存'),
          buttonStatus: true,
          callback: this.handleSaveDraft
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ],
      batchFileButtons: [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('确 定'),
          buttonStatus: true,
          callback: this.handleBatchFileSave
        }
      ],
      treeData: [],
      props: {
        value: 'id', // ID字段名
        label: 'text', // 显示名称
        children: 'children', // 子级字段名
        isLeaf: 'isLeaf'
      },
      optionsMap: {
        status: [],
        specialSource: [],
        specialType: [],
        bankAccountType: [],
        isHistory: [
          {
            text: '否',
            id: '0'
          },
          {
            text: '是',
            id: '1'
          }
        ]
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      categoryAreaCode: ''
    }
  },
  computed: {
    ...mapState(['areaLogin']) // 地区机构数据
  },
  watch: {
    areaLogin: {
      deep: true,
      immediate: true,
      handler () {
        this.getList()
      }
    }
  },
  created () {
    // this.getList()
    this.getOptions()
    // this.authTreeData()
  },
  methods: {
  getRowClassName ({ row, rowIndex }) {
      if (row.orgName == '总合计') { // 例如，让第一行的字体加粗
        return 'row-bold'
      }
      return ''
    },
    handleAmountInput (field) {
      let value = this.queryParams[field]
      value = value.replace(/[^\d.]/g, '') // 只允许数字和小数点
        .replace(/^\./g, '') // 不能以小数点开头
        .replace(/\.{2,}/g, '.') // 不能有多个小数点
        .replace('.', '$#$').replace(/\./g, '').replace('$#$', '.') // 只保留第一个小数点

      // 限制小数点后最多两位
      if (value.indexOf('.') > -1) {
        const parts = value.split('.')
        if (parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2)
        }
      }
      this.queryParams[field] = value
    },
    // 失去焦点时格式化显示两位小数
    formatDecimal(field) {
      if (this.queryParams[field]) {
        const num = parseFloat(this.queryParams[field])
        if (!isNaN(num)) {
          this.queryParams[field] = num.toFixed(2)
        }
      }
    },
    validateAmountRange () {
      if (this.queryParams.amountMin && this.queryParams.amountMax) {
        if (Number(this.queryParams.amountMin) > Number(this.queryParams.amountMax)) {
          // this.$message.error('开始金额不能大于结束金额');
          // 可以清空其中一个值或进行其他处理
          this.queryParams.amountMax = ''
        }
      }
    },
    async getOptions () {
      const { data: status = [] } = await getDictionary('/financial/specialFund/receipt/status/')
      this.optionsMap.status = status
      const { data: specialSource = [] } = await getDictionary('/financial/specialFund/receipt/specialSource/')
      this.optionsMap.specialSource = specialSource
      const { data: specialType = [] } = await getDictionary('/financial/specialFund/receipt/specialType/')
      this.optionsMap.specialType = specialType
      const { data: bankAccountType = [] } = await getDictionary('/financial/bank_account_type/')
      this.optionsMap.bankAccountType = bankAccountType
    },
    handleCategoryValue (value) {
      this.queryParams.categoryId = value
    },
    getList () {
      if (this.areaLogin.type == undefined) {
        // this.$message.warning(this.$t('请选择地区/机构！'))
        return
      }
      // if(this.areaLogin.level > 1){
      this.tableLoading = true
      console.log('this.areaLogin', this.areaLogin)
      if (this.areaLogin.type == 'Organization') {
        this.queryParams.orgId = this.areaLogin.id
        this.queryParams.areaCode = ''
      } else {
        this.queryParams.areaCode = this.areaLogin.code
        this.queryParams.orgId = ''
      }
      console.log(this.areaLogin)
      loadReceiptListApi(this.queryParams).then(res => {
        this.table.tableList = res.data.rows
        this.table.total = res.data.total
      }).finally(() => {
        this.tableLoading = false
      })
      // }
    },
    handleSearch () {
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.tableSelection = selection
      this.tableIdSelection = selection.map(item => item.id)
    },
    handleAdd () {
      if (!this.areaLogin.type == 'Organization') {
        this.$message.warning(this.$t('请先选择机构！'))
        return
      }
      this.detailButtons = [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('暂 存'),
          buttonStatus: true,
          callback: this.handleSaveDraft
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
      this.detailTitle = '新增'
      this.type = 'add'
      this.currentId = ''
      this.detailVisible = true
    },
    handleView (row) {
      this.detailButtons = [
        {
          type: '',
          text: this.$t('关 闭'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        }
      ]
      this.detailTitle = '查看'
      this.type = 'view'
      this.currentId = row.id
      this.detailVisible = true
    },
    handleEdit (row) {
      this.detailButtons = [
        {
          type: '',
          text: this.$t('取 消'),
          buttonStatus: true,
          callback: this.handleCloseDetail
        },
        {
          type: 'primary',
          text: this.$t('暂 存'),
          buttonStatus: true,
          callback: this.handleSaveDraft
        },
        {
          type: 'primary',
          text: this.$t('保 存'),
          buttonStatus: true,
          callback: this.handleSave
        }
      ]
      this.detailTitle = '编辑'
      this.type = 'edit'
      this.currentId = row.id
      this.categoryAreaCode = row.areaCode
      this.detailVisible = true
    },
    handleRemove (row, batchFlag) {
      let ids
      if (batchFlag) {
        ids = row.id
      } else {
        if (this.tableIdSelection.length == 0) {
          return this.$message.warning(this.$t('请先选择要删除的数据！'))
        }
        ids = this.tableIdSelection.join(',')
      }
      this.$confirm(this.$t('确定要删除吗?'), {
        confirmButtonText: this.$t('确定'),
        cancelButtonText: this.$t('取消'),
        type: 'warning'
      }).then(() => {
        deleteReceiptApi({ ids }).then(res => {
          if (res) {
            this.$message.success(res.message)
            this.getList()
          }
        })
      }).catch(() => { })
    },
    handleCloseDetail () {
      this.detailVisible = false
      this.batchFileVisible = false
    },
    handleSave () {
      this.$refs._detailContentRef.save()
    },
    handleSaveDraft () {
      this.$refs._detailContentRef.saveDraft()
    },
    handleExamine () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要审核的数据！'))
      }
      const data = this.tableSelection.filter(item => item.status != 1)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择状态为“镇编制”的数据！'))
      }
      this.$confirm(this.$t('审核通过后，由下级单位登记入账。是否确认审核通过？'), '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.tableIdSelection.join(',')
        this.updateStatus(ids, 2, 2)
      }).catch(() => {
        // 用户点击取消后的操作
        console.log('取消删除')
      })
    },
    handleCancelExamine () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要审核的数据！'))
      }
      const data = this.tableSelection.filter(item => item.status != 2)
      if (data.length != 0) {
        return this.$message.warning(this.$t('请选择状态为“镇已审核”的数据！'))
      }
      this.$confirm(this.$t('是否确认取消审核？'), '取消审核', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.tableIdSelection.join(',')
        this.updateStatus(ids, 1, 2)
      }).catch(() => {
        // 用户点击取消后的操作
        console.log('取消删除')
      })
    },
    handleBatchImport () {
      // if (!this.areaLogin.type == 'Organization') {
      //   this.$message.warning(this.$t('请先选择机构！'))
      //   return
      // }
      this.$refs.batchImportRef.contentForm.systemBatchId = ''
      this.$refs.batchImportRef.dialogVisible = true
    },
    handleBatchUploadAttachment () {
      if (this.tableIdSelection.length == 0) {
        return this.$message.warning(this.$t('请先选择要上传附件的数据！'))
      }
      const data = this.tableSelection.filter(item => item.status == 2 || item.status == 3)
      if (data.length != 0) {
        return this.$message.warning(this.$t('审核通过后不可再操作！'))
      }
      this.batchFileIds = this.tableIdSelection.join(',')
      this.batchFileVisible = true
    },
    updateStatus (ids, status, specialType) {
      const data = {
        ids,
        status,
        specialType
      }
      updateStatus(data).then(res => {
        this.$message.success(res.message)
        this.getList()
      })
    },
    selectEnable (row) {
      return row.orgName != '本页合计' && row.orgName != '总合计'
    },
    handleBatchFileSave () {
      this.$refs._batchFileRef.save()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .row-bold {
  font-weight: bold !important;
  background-color: #f8f8f9;
}
</style>
