import request from '@/utils/request'

/**
 * @name:获取数据页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/payment/paymentSettlement/page',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 新增初始化
export function init(params) {
  return request({
    url: '/payment/paymentSettlement/add/init',
    method: 'get',
    params,
    withCredentials: true
  })
}
// 详情
export function load(id) {
  return request({
    url: `/payment/paymentSettlement/load/${id}`,
    method: 'get',
    withCredentials: true
  })
}
// 保存
export function save(data) {
  return request({
    url: '/payment/paymentSettlement/save',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 删除
export function postDeletePost(data) {
  return request({
    url: '/payment/paymentSettlement/delete',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 支出类型树结构
export function paymentSettlementTree(params) {
  return request({
    url: '/payment/paymentSettlement/tree',
    method: 'get',
    params,
    withCredentials: true
  })
}
