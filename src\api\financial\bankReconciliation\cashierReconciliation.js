import request from '@/utils/request'

/**
 * @name:银行接口导入初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function bankInterfaceImportInit (params) {
  return request({
    url: '/financial/cashier/cashierBankStatement/bankInterfaceImportInit',
    method: 'get',
    params,
    withCredentials: true
  })
}
/**
 * @name:文件导入
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function saveFileImport (data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/saveFileImport',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:获取未达账前置条件控制标识
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function getBankStatementFlag (params) {
  return request({
    url: '/financial/cashier/cashierBankStatement/getBankStatementFlag',
    method: 'get',
    params,
  })
}
/**
 * @name:分页
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page (data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/page',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:详情
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function load (params) {
  return request({
    url: `/financial/cashier/cashierBankStatement/loadBankStatement`,
    method: 'get',
    params,
    withCredentials: true
  })
}
/**
 * @name:新增页面初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function addInit (params) {
  return request({
    url: '/financial/cashier/cashierBankStatement/add/init',
    params,
    method: 'get',
    withCredentials: true
  })
}
/**
 * @name:编辑页面初始化
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function editInit (params) {
  return request({
    url: '/financial/cashier/cashierBankStatement/edit/init',
    params,
    method: 'get',
    withCredentials: true
  })
}
/**
 * @name:删除
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function deleteBankStatement (data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/deleteBankStatement',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:保存
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function save (params, data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/saveX',
    method: 'post',
    data,
    params,
    payload: true
  })
}
/**
 * @name:编辑
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function update (data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/update',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:银行接口导入
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function bankInterfaceImport (data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/bankInterfaceImport',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:设置是否对账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function needReconciliation (data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/needReconciliation',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:出纳关联流水对账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cashierBankStatmentReconciliation (data) {
  return request({
    url: '/financial/cashier/cashierBankStatement/cashierBankStatmentReconciliation',
    method: 'post',
    data,
    withCredentials: true
  })
}