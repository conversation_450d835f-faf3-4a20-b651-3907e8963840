<template>
  <div>
    <el-form inline>
      <el-form-item :label="$t('合同名称：')">
        <el-input v-model="contractName" />
      </el-form-item>
      <el-form-item>
        <el-button
          round
          plain
          type="primary"
          icon="el-icon-search"
          @click="handleSearchContractList"
        >
          {{ $t('搜索') }}
        </el-button>
      </el-form-item>
    </el-form>
    <!-- <gever-table
      ref="_geverTableRef"
      :columns="tableColumns"
      :data="receiverList"
      :total="pagiOptions.total"
      :pagi="pagiOptions"
      :height="'100%'"
      @p-current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #bankType="{ row }">
        {{ row.bankType | fillterBankType }}
      </template></gever-table> -->
    <gever-table
      ref="_tableRef"
      :loading="loading"
      :columns="tableColumns"
      :data="receiverList"
      :total="total"
      :pagi.sync="pagiOptions"
      :height="'100%'"
      @selection-change="handleSelectionChange"
      @pagination-change="loadList"
      @header-click="$refs['_tableRef'].setCurrentRow()"
    >
      <template #balance="{ row }">
        {{ ((row.pryMoney || 0) - (row.accountPaidMoney || 0)) | formatterNum }}
      </template>
      <template #nowPayMoney="{ row }">
        <gever-input-number
          v-model="row.nowPayMoney"
          :max="(row.pryMoney || 0) - (row.accountPaidMoney || 0)"
          :disabled="isView"
        />
      </template>
    </gever-table>
  </div>
</template>

<script>
let _this = null
import { comboJson } from '../../../api/gever/common.js'
import { loadContractList } from '../../../api/payment/approval.js'
import { selectContractTableColumns } from '../../../views/payment/approval/approvalApplication/config'

export default {
  filters: {
    fillterBankType(type) {
      const typeItem = _this.bankTypeOptions.find((item) => item.id === type)
      return typeItem ? typeItem.text : ''
    },
    formatterNum(val) {
      if (typeof val === 'number') {
        return (Number(val).toFixed(2) + '').replace(
          /\d{1,3}(?=(\d{3})+(\.\d*)?$)/g,
          '$&,'
        )
      }
    }
  },
  props: {
    orgCode: { type: String, default: '' },
    isView: { type: Boolean, default: () => false },
    selectList: { type: Array, default: () => [] },
    applayId: { type: String, default: '' },
    voucherId: { type: String, default: '' }, // 出纳单id
    contractIds: { type: Array, default: () => [] } // 关联的合同id
  },
  data() {
    return {
      loading: false,
      tableColumns: selectContractTableColumns,
      contractName: '',
      TableColumns: selectContractTableColumns,
      pagiOptions: {
        page: 1,
        rows: 10
      },
      total: 0,
      receiverList: [],
      bankTypeOptions: [],
      selection: []
    }
  },
  created() {
    _this = this
    // this.loadBankTypeOptions()
    this.loadList()
  },
  methods: {
    loadBankTypeOptions() {
      comboJson({ path: '/payment/pay_bank_type/' }).then((res) => {
        this.bankTypeOptions = res.data
      })
    },
    async loadList() {
      try {
        this.loading = true
        const params = {
          name: this.contractName,
          page: this.pagiOptions.page,
          rows: this.pagiOptions.rows,
          orgId: this.orgCode,
          paymentApprovalApplicationId: this.applayId
        }
        const { data } = await loadContractList(params)
        this.receiverList = data.rows
        this.total = data.total
        this.$nextTick(() =>
          this.receiverList.forEach(
            (row) =>
              (row.checkFlag || this.selectList.includes(row.id)) &&
              this.$refs['_tableRef'].toggleRowSelection(row, true)
          )
        )
        this.loading = false
      } catch (error) {
        this.receiverList = []
        this.total = 0
        this.loading = false
      }
    },
    handleSearchContractList() {
      this.pagiOptions.page = 1
      this.loadList()
    },
    handleSelectionChange(selection = []) {
      if (this.isView) {
        this.$refs._tableRef.clearSelection()
        return
      }
      this.selection = selection
      const valid = this.selection.every(
        ({ nowPayMoney = 0 }) => nowPayMoney > 0
      )
      if (!valid) {
        this.$message.warning('本次支付金额必须大于零!')
      }
      this.$emit('confirm', this.selection)
    },
    handleCurrentChange(val) {
      this.pagiOptions.page = val
      this.loadList()
    },
    handleSizeChange(val) {
      this.pagiOptions.page = 1
      this.pagiOptions.rows = val
      this.loadList()
    }
  }
}
</script>

<style></style>
