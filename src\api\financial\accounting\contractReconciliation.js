/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-08-29 09:30:43
 * @LastEditors: Andy
 * @LastEditTime: 2023-09-02 16:24:29
 */
import request from '@/utils/request'

/**
 * 关联凭证-保存
 */
export function relateAccountingEntry (data) {
  return request({
    url: '/financial/accountingContractHangaccount/relateAccountingEntry',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
/**
 * 获取关联凭证初始条件
 */
export function init (data) {
  return request({
    url: '/financial/accounting/business/relateAccounting/init',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
/**
 * 合同挂账-列表
 */
export function listCon (data) {
  return request({
    url: '/financial/accountingContractHangaccount/list',
    method: 'post',
    data: data
  })
}

/**
 * 合同导入
 */
export function contractImportCon (data) {
  return request({
    url: '/financial/accountingContractHangaccount/contractImport',
    method: 'post',
    data: data
  })
}

/**
 * 合同挂账-生成凭证(获取分录信息)
 */
export function creatAccountingVoucherCon (data) {
  return request({
    url: '/financial/accountingContractHangaccount/creatAccountingVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 合同挂账-初始化
 */
export function contractPendingAccountInitCon (data) {
  return request({
    url: '/financial/accountingContractHangaccountDetail/contractPendingAccountInit',
    method: 'post',
    data: data
  })
}

/**
 * 合同挂账-删除
 */
export function deleteCon (data) {
  return request({
    url: '/financial/accountingContractHangaccount/delete',
    method: 'post',
    data: data
  })
}

/**
 * 科目设置-保存/修改
 */
export function saveItemSettingCon (data) {
  return request({
    url: '/financial/accountingContractHangaccountDetail/saveItemSetting',
    method: 'post',
    data: data,
    payload: true
  })
}

/**
 * 科目设置-查看
 */
export function getItemSettingCon (params) {
  return request({
    url: '/financial/accountingContractHangaccountDetail/getItemSetting',
    method: 'get',
    params: params
  })
}

/**
 * 合同挂账-撤销凭证
 */
export function revokeAccountingVoucherCon (data) {
  return request({
    url: '/financial/accountingContractHangaccount/revokeAccountingVoucher',
    method: 'post',
    data: data
  })
}

/**
 * 会计凭证-结转应收款
 */
export function carryoverReceivablesCon (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/carryoverReceivables',
    method: 'post',
    data: data
  })
}


/**
 * 会计科目列表
 */
export function getAccountingItemListCon (data) {
  return request({
    url: '/financial/books/basedata/accountingItem/getAccountingItemList',
    method: 'post',
    data: data
  })
}

// 合同挂账-查看凭证

export function loadVoucher (params) {
  return request({
    url: '/financial/accounting/accountingVoucher/loadVoucher',
    method: 'get',
    params: params
  })
}


//合同挂账-来源查看
export function loadView (data) {
  return request({
    url: '/financial/accountingContractHangaccountDetail/loadView',
    method: 'post',
    data: data
  })
}


//结转应收款

export function carryoverReceivables (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/carryoverReceivables',
    method: 'post',
    data: data
  })
}

/**
 * 会计凭证-结转承包结算
 */
export function carryContractSettlement (data) {
  return request({
    url: '/financial/accounting/accountingVoucher/carryContractSettlement',
    method: 'post',
    data: data
  })
}