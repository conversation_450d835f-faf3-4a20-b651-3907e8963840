import request from '@/utils/request'

/**
 * 查询银行接口信息列表
 * @param {*} data 
 * @returns 
 */
export function getBankInterfaceList(data){
    return request({
        url: '/paymentbill/paymentBankInterface/page',
        method: 'post',
        data
    })
}

/**
 * 查询银行接口详情
 * @returns 
 */
export function getInterfaceDetail(){
    return request({
        url: `/paymentbill/paymentBankInterface/get/${id}`,
        method: 'get'
    })
}

/**
 * 保存
 * @param {*} data 
 * @returns 
 */
export function save(data){
    return request({
        url: '/paymentbill/paymentBankInterface/save',
        method: 'post',
        data,
        payload: true
    })
}

/**
 * 启用/停用
 * @param {*} params 
 * @returns 
 */
export function isEnable(params){
    return request({
        url: '/paymentbill/paymentBankInterface/setIsValid',
        method: 'post',
        params
    })
}

/**
 * 查询接口日志信息列表
 * @param {*} data 
 * @returns 
 */
export function getInterfaceLogList(data){
    return request({
        url: '/paymentbill/paymentBankInterfaceLog/page',
        method: 'post',
        data
    })
}