/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-11-23 20:01:41
 * @LastEditTime: 2021-11-24 09:37:38
 * @LastEditors: Peng Xiao
 * @Description:
 * @FilePath: \gever-zhxc-ui\src\api\financial\asset\posting.js
 * ^-^
 */

import request from '@/utils/request'

/**
 * @name:获取资产入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function page(data) {
  return request({
    url: '/financial/asset/assetPosting/page',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:资产入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function posting(data) {
  return request({
    url: `/financial/asset/assetPosting/posting`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消资产入账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function unPosting(data) {
  return request({
    url: `/financial/asset/assetPosting/unPosting`,
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:取消凭证
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function cancelGenerateVoucher(data) {
  return request({
    url: `/financial/asset/assetPosting/cancelGenerateVoucher`,
    method: 'post',
    data,
    withCredentials: true
  })
}
