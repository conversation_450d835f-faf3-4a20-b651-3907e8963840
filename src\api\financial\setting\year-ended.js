/*
 * @Descripttion:账套年结接口
 * @version:
 * @Author: 未知
 * @Date: 2021-10-25 09:47:29
 * @LastEditors: PengXiao
 * @LastEditTime: 2021-11-02 08:55:34
 */

import request from '@/utils/request'

/**
 * @name:获取账套类型列表
 * @param {Object} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listInit(params) {
  return request({
    url: '/financial/booksYearEnded/list/init',
    method: 'get',
    params,
    withCredentials: true
  })
}

/**
 * @name:获取账套类型列表
 * @param {Object} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listPeriod(data) {
  return request({
    url: '/financial/booksYearEnded/listPeriod',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取出纳对账
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listReconciliation(data) {
  return request({
    url: '/financial/booksYearEnded/listReconciliation',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取会计的日期区间
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function combotree(params) {
  return request({
    url: '/financial/books/basedata/accountingperiod/combotree',
    method: 'get',
    params,
    payload: true,
    withCredentials: true
  })
}

/**
 * @name:年结
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function execYearEnded(data) {
  return request({
    url: '/financial/booksYearEnded/execYearEnded',
    method: 'post',
    data: data,
    withCredentials: true
  })
}

/**
 * @name:获取可以年节的模块
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listYearEndedModule(data) {
  return request({
    url: '/financial/booksYearEnded/listYearEndedModule',
    method: 'post',
    data,
    withCredentials: true
  })
}

/**
 * @name:获取可以反年节的模块
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function listUnYearEndedModule(data) {
  return request({
    url: '/financial/booksYearEnded/listUnYearEndedModule',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name:反年结
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function execUnYearEnded(data) {
  return request({
    url: '/financial/booksYearEnded/execUnYearEnded',
    method: 'post',
    data: data,
    withCredentials: true
  })
}
