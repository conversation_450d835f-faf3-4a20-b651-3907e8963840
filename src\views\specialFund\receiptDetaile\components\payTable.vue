<!--
 * @Description: file content
 * @Author: liuwf
 * @Date: 2025-06-19 11:43:04
 * @LastEditors: liuwf
 * @LastEditTime: 2025-06-26 14:13:07
-->
<template>
  <div>
    <el-form inline @submit.native.prevent>
      <el-form-item :label="$t('专项资金名称')">
        <el-input v-model="queryParams.specialFundName" type="text" class="w200" clearable />
      </el-form-item>
      <el-form-item :label="$t('专项资金编号')">
        <el-input v-model="queryParams.specialFundCode" type="text" class="w200" clearable />
      </el-form-item>
      <el-form-item :label="$t('专项资金来源')">
        <gever-select
          v-model="queryParams.specialFundSource"
          class="w200"
          path="/financial/specialFund/receipt/specialSource/"
        />
      </el-form-item>
      <el-form-item :label="$t('专项资金类别')">
        <!-- :show-all-levels="false" @getValue="handleCategoryValue" -->
        <categoryTreeSelect v-model="queryParams.specialFundCategoryId" class="w200"></categoryTreeSelect>
      </el-form-item>
      <el-form-item :label="$t('收款方')">
        <el-input v-model="queryParams.receiverAccount" type="text" class="w200" clearable />
      </el-form-item>
      <el-form-item :label="$t('专项资金支出类型')">
        <!-- <el-input v-model="queryParams.paymentType" type="text" class="w200" clearable /> -->
        <gever-select
          v-model="queryParams.paymentTypes"
          class="w200"
          path="/payment/specialFundPayType/"
        />
      </el-form-item>
      <el-form-item :label="$t('支出申请单编号')">
        <el-input v-model="queryParams.serialNumber" type="text" class="w200" clearable />
      </el-form-item>
      <el-form-item :label="$t('支出审批状态')">
        <!-- <el-input v-model="queryParams.approvalStatus" type="text" class="w200" clearable /> -->
        <gever-select
          v-model="queryParams.approvalStatusAry"
          class="w200"
          number-key
          multiple
          collapse-tags
          :options="optionsMap.approvalstatus"
        />
      </el-form-item>
      <el-form-item :label="$t('支付状态')">
        <!-- <el-input v-model="queryParams.paymentState" type="text" class="w200" clearable /> -->
        <gever-select
          v-model="queryParams.paymentStateAry"
          class="w200"
          number-key
          multiple
          collapse-tags
          :options="optionsMap.payStatus"
        />
      </el-form-item>
      <el-form-item :label="$t('忽略支付失败')">
        <!-- <el-checkbox v-model="queryParams.payFail">忽略支付失败</el-checkbox> -->
        <gever-select
          v-model="queryParams.noPayFail"
          class="w200"
          number-key
          :options="optionsMap.noPayFail"
        />
      </el-form-item>
      <el-form-item label="支出申请日期">
        <div style="display: flex;justify-content:space-between">
          <div>
            <el-date-picker
              v-model="queryParams.creationTimeStart"
              type="date"
              class="w150"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :picker-options="startPickerOptions"
              style="width: 100%;"
            ></el-date-picker>
          </div>
          <span style="text-align: center;margin-left:3px;margin-right:3px;">-</span>
          <div>
            <el-date-picker
              v-model="queryParams.creationTimeEnd"
              type="date"
              class="w150"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :picker-options="endPickerOptions"
              style="width: 100%;"
            ></el-date-picker>
          </div>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button
          plain
          icon="el-icon-search"
          type="primary"
          round
          @click="handleSearch"
        >{{ $t('搜索') }}</el-button>
        <el-button
          plain
          icon="el-customIcon-baocun"
          type="primary"
          round
          @click="handleExport"
        >{{ $t('导出') }}</el-button>
      </el-form-item>
    </el-form>
    <gever-table
      ref="_geverTableRef"
      :height="500"
      :loading="tableLoading"
      :columns="table.columns"
      :data="table.tableList"
      :total="table.total"
      :pagi="queryParams"
      :row-class-name="getRowClassName"
      @pagination-change="getList"
    >
      <template #specialType="{ row }">{{ getTitle(row.specialType+'', optionsMap.specialType) }}</template>
      <template
        #specialSource="{ row }"
      >{{ getTitle(row.specialSource+'', optionsMap.specialSource) }}</template>
      <template #status="{ row }">{{ getTitle(row.status+'', optionsMap.status) }}</template>
      <template #specialFundCategoryCode="{ row }">
        <span
          v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
        >{{ row.specialFundCategoryCode+'-'+row.specialFundCategoryName }}</span>
      </template>
      <template #creationTime="{ row }">{{ row.creationTime?row.creationTime.substring(0,10):"" }}</template>
      <template #cashierDate="{ row }">{{ row.cashierDate?row.cashierDate.substring(0,10):"" }}</template>
      <template
        #actualPayTime="{ row }"
      >{{ row.actualPayTime?row.actualPayTime.substring(0,10):"" }}</template>
      <template #approvalTime="{ row }">{{ row.approvalTime?row.approvalTime.substring(0,10):"" }}</template>
      <template #rowIndex="{row, index }">
        <span
          v-if="row.orgName != '本页合计' && row.orgName != '总合计'"
        >{{ (queryParams.page - 1) * queryParams.rows + index + 1 }}</span>
      </template>
      <template #operation="{ row }">
        <span v-if="row.orgName != '本页合计' && row.orgName != '总合计'">
          <el-button
            type="text"
            @click="handleView(row)"
          >{{ $t('查看') }}</el-button>
        </span>
      </template>
    </gever-table>
    <public-drawer
      :visible.sync="detailVisible"
      title="支出明细"
      :size="50"
      @close="handleCloseDetail"
    >
      <detail
        v-if="detailVisible"
        ref="_detailContentRef"
        :content-form="contentForm"
        :detail-visible.sync="detailVisible"
      />
    </public-drawer>
  </div>
</template>

<script>
import { pageSpecialFundPay } from '@/api/specialFund/receipt.js'
import { getDictionary } from '@/api/gever/common.js'
import detail from '../../pay/components/detail'
import categoryTreeSelect from '../../receipt/components/categoryTreeSelect'
import { getToken } from '@/utils/auth'
export default {
  name: '',
  components: {
    detail,
    categoryTreeSelect
  },
  props: {
    specialFundId: {
      type: String,
      default: ''
    },
    receiptCode: {
      type: String,
      default: ''
    },
    area: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      tableLoading: false,
      queryParams: {
        page: 1,
        rows: 20,
        specialType: '',
        balance: '',
        isAccountingItem: '',
        specialFundCode: '',
        approvalStatusAry: [2, 3, 4],
        paymentStateAry: [],
        noPayFail: 1
      },
      table: {
        columns: [
          { type: 'selection', align: 'center', width: '50', selectable: this.selectEnable, fixed: 'left' },
          { label: '序号', prop: 'rowIndex', width: '50' },
          { label: '单位名称', prop: 'orgName', width: '300' },
          // { label: '编制方', prop: 'specialFundTypeText', width: '100' },
          { label: '专项资金编号', prop: 'specialFundCode', width: '150' },
          { label: '专项资金名称', prop: 'specialFundName', width: '150' },
          { label: '专项资金类别', prop: 'specialFundCategoryCode', width: '200' },
          { label: '专项资金来源', prop: 'specialFundSourceText', width: '150' },
          { label: '专项支出类型', prop: 'paymentTypeText', width: '150' },
          // { label: '收款账户名称', prop: 'bankAccountName', width: '200' },
          { label: '支出申请单编号', prop: 'serialNumber', width: '150' },
          { label: '支出申请金额（元）', prop: 'payAmount', filter: 'money', width: '150', align: 'right' },
          { label: '实际支出金额（元）', prop: 'actualPayAmount', filter: 'money', width: '150', align: 'right' },
          { label: '支出申请人', prop: 'submitUserName', width: '150' },
          { label: '支出申请日期', prop: 'creationTime', width: '150' },
          { label: '支付日期', prop: 'actualPayTime', width: '150' },
          { label: '出纳日期', prop: 'cashierDate', width: '150' },
          { label: '收款方', prop: 'receiverAccount', width: '100' },
          { label: '支出审批状态', prop: 'approvalStatusText', width: '150' },
          { label: '支付状态', prop: 'payStatusText', width: '150' },
          { label: '操作', slotName: 'operation', width: '180', fixed: 'right' }
        ],
        tableList: [],
        total: 0
      },
      optionsMap: {
        status: [],
        specialSource: [],
        specialType: [],
        approvalstatus: [],
        payStatus: [],
        noPayFail: [
          {
            id: '1',
            text: '是'
          },
          {
            id: '0',
            text: '否'
          }
        ]
      },
      areaCode: '',
      batchFileVisible: false,
      batchFileIds: '',
      contentForm: {},
      detailVisible: false
    }
  },
  computed: {
    startPickerOptions () {
      return {
        disabledDate: time => {
          if (this.queryParams.creationTimeEnd) {
            return (
              time.getTime() >= new Date(this.queryParams.creationTimeEnd).getTime()
            )
          }
          return false
        }
      }
    },
    endPickerOptions () {
      return {
        disabledDate: time => {
          if (this.queryParams.creationTimeStart) {
            return (
              time.getTime() <= new Date(this.queryParams.creationTimeStart).getTime() - 8.64e7
            )
          }
          return false
        }
      }
    }
  },
  created () {
    this.queryParams.specialFundCode = this.receiptCode
    this.getList()
    this.getOptions()
  },
  methods: {
    handleCloseDetail() {
      this.detailVisible = false
    },
    getToken,
    handleView (row) {
      this.detailTitle = '支出明细'
      this.type = 'view'
      this.contentForm = row
      this.detailVisible = true
    },
    handleAmountInput (field) {
      let value = this.queryParams[field]
      value = value.replace(/[^\d.]/g, '') // 只允许数字和小数点
        .replace(/^\./g, '') // 不能以小数点开头
        .replace(/\.{2,}/g, '.') // 不能有多个小数点
        .replace('.', '$#$').replace(/\./g, '').replace('$#$', '.') // 只保留第一个小数点

      // 限制小数点后最多两位
      if (value.indexOf('.') > -1) {
        const parts = value.split('.')
        if (parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2)
        }
      }
      this.queryParams[field] = value
    },
    // 失去焦点时格式化显示两位小数
    formatDecimal (field) {
      if (this.queryParams[field]) {
        const num = parseFloat(this.queryParams[field])
        if (!isNaN(num)) {
          this.queryParams[field] = num.toFixed(2)
        }
      }
    },
    validateAmountRange () {
      if (this.queryParams.payAmountStart && this.queryParams.payAmountEnd) {
        if (Number(this.queryParams.payAmountStart) > Number(this.queryParams.payAmountEnd)) {
          this.$message.warning('开始金额不能大于结束金额')
          // 可以清空其中一个值或进行其他处理
          this.queryParams.payAmountEnd = ''
        }
      }
    },
    handleSearch () {
      this.getList()
    },
    getRowClassName ({ row, rowIndex }) {
      if (row.orgName == '总合计') { // 例如，让第一行的字体加粗
        return 'row-bold'
      }
      return ''
    },
    selectEnable (row) {
      return row.orgName != '本页合计' && row.orgName != '总合计'
    },
    async getOptions () {
      const { data: status = [] } = await getDictionary('/financial/specialFund/receipt/status/')
      this.optionsMap.status = status
      const { data: specialSource = [] } = await getDictionary('/financial/specialFund/receipt/specialSource/')
      this.optionsMap.specialSource = specialSource
      const { data: specialType = [] } = await getDictionary('/financial/specialFund/receipt/specialType/')
      this.optionsMap.specialType = specialType
      const { data: approvalstatus = [] } = await getDictionary('/payment/approvalstatus/')
      this.optionsMap.approvalstatus = approvalstatus
      const { data: payStatus = [] } = await getDictionary('/payment/payStatus/')
      this.optionsMap.payStatus = payStatus
    },
    getList () {
      // if (this.$route.query.specialFundId != undefined) {
      //   this.queryParams.specialFundId = this.$route.query.specialFundId
      // } else {
      this.queryParams.specialFundId = this.specialFundId
      // }
      if (this.area.type == 'Organization') {
        this.queryParams.orgCode = this.area.code
        this.queryParams.areaCode = ''
      } else {
        this.queryParams.areaCode = this.area.code
        this.queryParams.orgCode = ''
      }
      if (this.queryParams.payAmountStart == '') {
        this.queryParams.payAmountStart = null
      }
      if (this.queryParams.payAmountEnd == '') {
        this.queryParams.payAmountEnd = null
      }
      // if (this.queryParams.payFail == true) {
      //   this.queryParams.noPayFail = 1
      // } else {
      //   this.queryParams.noPayFail = ''
      // }
      this.queryParams.approvalStatusList = this.queryParams.approvalStatusAry.join(',')
      this.queryParams.paymentStateList = this.queryParams.paymentStateAry.join(',')
      this.tableLoading = true
      pageSpecialFundPay(this.queryParams).then(res => {
        this.table.tableList = res.data.records
        this.table.total = res.data.total
        if (res.data.total != 0) {
          this.table.tableList.push({
            orgName: '总合计',
            payAmount: res.data.totalAmount,
            actualPayAmount: res.data.totalActualPayAmount
          })
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleExport () {
      let _data_ = ''
      let index = 0
      const param = {
        ...this.queryParams
      }
      const keys = Object.keys(param)
      keys.forEach((d) => {
        if (param[d] != undefined) {
          _data_ += d + '=' + param[d]
          if (Object.keys(param).length - 1 !== index) {
            _data_ += '&'
          }
        }
        index++
      })
      this.exportLoading = true
      window.location.href =
        process.env.VUE_APP_BASE_API +
        '/payment/approval/approvalReceiver/exportSpecialFundPay?' +
        _data_ +
        '&access_token=' +
        this.getToken() +
        '&tenant_id=' +
        this.$Cookies.get('X-tenant-id-header')
      this.exportLoading = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .row-bold {
  font-weight: bold !important;
  background-color: #f8f8f9;
}
</style>
