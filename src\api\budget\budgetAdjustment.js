import request from '@/utils/request'
// 预算调整-导出文件
export function adjustmentExportFile(params) {
  return request({
    params,
    url: '/financial/budget/budgetAdjustment/exportFile',
    method: 'get'
  })
}

// 列表
export function page(data) {
  return request({
    data,
    url: '/financial/budget/budgetAdjustment/page',
    method: 'post',
  })
}
// 详情
export function details(id) {
  return request({
    url: `/financial/budget/budgetAdjustment/get/${id}`,
    method: 'get'
  })
}
// 保存 
export function save(data) {
  return request({
    data,
    url: '/financial/budget/budgetAdjustment/save',
    method: 'post',
    payload: true
  })
}
// 删除 
export function deletePost(data) {
  return request({
    data,
    url: '/financial/budget/budgetAdjustment/delete',
    method: 'post',
  })
}
// 提交 
export function submit(data) {
  return request({
    data,
    url: '/financial/budget/budgetAdjustment/submit',
    method: 'post',
  })
}
// 撤销提交 
export function unSubmit(data) {
  return request({
    data,
    url: '/financial/budget/budgetAdjustment/unSubmit',
    method: 'post',
  })
}
// 审核 
export function verify(data) {
  return request({
    data,
    url: '/financial/budget/budgetAdjustment/verify',
    method: 'post',
  })
}
// 重新提交 
export function reSubmit(data) {
  return request({
    data,
    url: '/financial/budget/budgetPlait/reSubmit',
    method: 'post',
  })
}
// 查询预算编制列表
export function getBudgetSettingList(params) {
  return request({
    params,
    url: '/financial/budget/budgetAdjustment/getBudgetSettingList ',
    method: 'get',
  })
}
// 预算设置-查询子表数据
export function getItemData(params) {
  return request({
    params,
    url: '/financial/budget/budgetAdjustment/getItemData',
    method: 'get',
  })
}
// 查询预算编制列表
export function checkCanAdd(params) {
  return request({
    params,
    url: '/financial/budget/budgetAdjustment/checkCanAdd',
    method: 'get',
  })
}

// 获取预算调整下拉
export function getAdjustmentLeafItem(data) {
  return request({
    params:data,
    url: '/financial/budget/budgetAdjustment/getLeafItem',
    method: 'get'
  })
}