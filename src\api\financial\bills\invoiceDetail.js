
import request from '@/utils/request'

/* 获取合同类型== */
export function getAllContractType(data) {
  return request({
    url: `/invoice/invoiceDetail/getCollectionContractType`,
    method: 'get',
    params: data
  })
}

/* 查询可用于开票的每期应收款记录==  */
export function getInvoicableReceivableAccounts(data) {
  return request({
    url: '/invoice/invoiceDetail/getInvoicableReceivableAccounts',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 查询合同已收款但未开票的实际收款记录== */
export function getReceivabledNoBillAccounts(data) {
  return request({
    url: '/invoice/invoiceDetail/getReceivabledNoBillAccounts',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 删除票据 */
export function deleteBill(data) {
  return request({
    url: '/invoice/invoiceDetail/deleteBill',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 票据收款列表 */
export function page(data) {
  return request({
    url: '/invoice/invoiceDetail/page',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 出纳关联票据== */
export function relationBillCashier(data) {
  return request({
    url: '/invoice/invoiceDetail/relationBillCashier',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 取消出纳关联票据== */
export function cancelCashierRelation(data) {
  return request({
    url: '/invoice/invoiceDetail/cancelCashierRelation',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 校验出纳是否已关联票据== */
export function verifyCashierRelation(data) {
  return request({
    url: '/invoice/invoiceDetail/verifyCashierRelation',
    method: 'get',
    params: data
    // payload: true
  })
}

/* 全部收款== */
export function allCollection(data) {
  return request({
    url: '/invoice/invoiceDetail/allCollection',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 部分收款（非合同）列表== */
export function getPartNonContractList(data) {
  return request({
    url: '/invoice/invoiceDetail/getPartNonContractList',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 部分收款（非合同）保存== */
export function partNonContractCollection(data) {
  return request({
    url: '/invoice/invoiceDetail/partNonContractCollection',
    method: 'post',
    data: data,
    payload: true
  })
}

/* 部分收款（合同）列表== */
export function getPartContractList(data) {
  return request({
    url: '/invoice/invoiceDetail/getPartContractList',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 部分收款（合同）保存== */
export function partContractCollection(data) {
  return request({
    url: '/invoice/invoiceDetail/partContractCollection',
    method: 'post',
    data: data,
    payload: true
  })
}

/* 获取票据收款记录== */
export function getCollectionRecord(data) {
  return request({
    url: '/invoice/invoiceDetail/getCollectionRecord',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 取消收款==   */
export function cancelCollection(data) {
  return request({
    url: '/invoice/invoiceDetail/cancelCollection',
    method: 'post',
    data: data
    // payload: true
  })
}

/* 出纳关联票据列表记录== */
export function getCashierRelationList(data) {
  return request({
    url: '/invoice/invoiceDetail/getCashierRelationList',
    method: 'post',
    data: data
    // payload: true
  })
}
/* 获取合同类型 */
export function getIncomeType(data) {
  return request({
    url: '/bill/billDetail/getIncomeType',
    method: 'get'
    // payload: true
  })
}